name: Deploy to Development

on:
  push:
    branches: [develop]
  workflow_dispatch:
    inputs:
      image_tag:
        description: 'Docker image tag to deploy'
        required: false
        default: 'develop'

env:
  ENVIRONMENT: development
  NAMESPACE: whimlabai-dev
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  pre-deployment-checks:
    runs-on: ubuntu-latest
    outputs:
      proceed: ${{ steps.check.outputs.proceed }}
    
    steps:
    - name: Check deployment window
      id: check
      run: |
        # Check if current time is within deployment window (optional)
        hour=$(date -u +%H)
        if [[ $hour -ge 2 && $hour -le 4 ]]; then
          echo "Warning: Deploying during off-peak hours"
        fi
        echo "proceed=true" >> $GITHUB_OUTPUT

    - name: Verify image exists
      run: |
        # Verify that the Docker images exist
        docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/webapi:${{ github.event.inputs.image_tag || 'develop' }}
        docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/admin:${{ github.event.inputs.image_tag || 'develop' }}
        docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/customer-web:${{ github.event.inputs.image_tag || 'develop' }}

  deploy-database-migrations:
    needs: pre-deployment-checks
    if: needs.pre-deployment-checks.outputs.proceed == 'true'
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Install EF Core tools
      run: dotnet tool install --global dotnet-ef

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Get database connection from Secrets Manager
      id: db_secret
      run: |
        secret=$(aws secretsmanager get-secret-value --secret-id whimlabai/dev/database --query SecretString --output text)
        echo "::add-mask::$secret"
        echo "DB_CONNECTION=$secret" >> $GITHUB_OUTPUT

    - name: Run database migrations
      env:
        ConnectionStrings__DefaultConnection: ${{ steps.db_secret.outputs.DB_CONNECTION }}
      run: |
        cd BackEnd/WhimLabAI.Infrastructure
        dotnet ef database update \
          --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
          --context WhimLabAIDbContext

    - name: Verify migration status
      run: |
        # Add verification logic here
        echo "Database migrations completed successfully"

  deploy-kubernetes:
    needs: [pre-deployment-checks, deploy-database-migrations]
    runs-on: ubuntu-latest
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region us-east-1 --name whimlabai-dev-cluster

    - name: Create namespace if not exists
      run: |
        kubectl create namespace ${{ env.NAMESPACE }} --dry-run=client -o yaml | kubectl apply -f -

    - name: Deploy secrets
      run: |
        # Create secrets from AWS Secrets Manager
        kubectl create secret generic whimlabai-secrets \
          --from-literal=jwt-secret="${{ secrets.JWT_SECRET }}" \
          --from-literal=openai-key="${{ secrets.OPENAI_API_KEY }}" \
          --from-literal=anthropic-key="${{ secrets.ANTHROPIC_API_KEY }}" \
          --namespace=${{ env.NAMESPACE }} \
          --dry-run=client -o yaml | kubectl apply -f -

    - name: Deploy ConfigMaps
      run: |
        kubectl apply -f k8s/dev/configmaps/ --namespace=${{ env.NAMESPACE }}

    - name: Deploy WebAPI
      run: |
        kubectl set image deployment/webapi \
          webapi=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/webapi:${{ github.event.inputs.image_tag || 'develop' }} \
          --namespace=${{ env.NAMESPACE }}
        
        kubectl rollout status deployment/webapi --namespace=${{ env.NAMESPACE }} --timeout=5m

    - name: Deploy Admin Portal
      run: |
        kubectl set image deployment/admin \
          admin=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/admin:${{ github.event.inputs.image_tag || 'develop' }} \
          --namespace=${{ env.NAMESPACE }}
        
        kubectl rollout status deployment/admin --namespace=${{ env.NAMESPACE }} --timeout=5m

    - name: Deploy Customer Web
      run: |
        kubectl set image deployment/customer-web \
          customer-web=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/customer-web:${{ github.event.inputs.image_tag || 'develop' }} \
          --namespace=${{ env.NAMESPACE }}
        
        kubectl rollout status deployment/customer-web --namespace=${{ env.NAMESPACE }} --timeout=5m

    - name: Run smoke tests
      run: |
        # Wait for services to be ready
        sleep 30
        
        # Test WebAPI health
        kubectl port-forward service/webapi 8080:80 --namespace=${{ env.NAMESPACE }} &
        sleep 5
        curl -f http://localhost:8080/health || exit 1
        
        # Test Admin Portal
        kubectl port-forward service/admin 8081:80 --namespace=${{ env.NAMESPACE }} &
        sleep 5
        curl -f http://localhost:8081/ || exit 1
        
        # Kill port forwards
        pkill -f "kubectl port-forward"

  post-deployment:
    needs: deploy-kubernetes
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Send deployment notification
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        text: |
          Development deployment ${{ job.status }}
          Branch: ${{ github.ref }}
          Commit: ${{ github.sha }}
          Author: ${{ github.actor }}
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

    - name: Update deployment tracking
      run: |
        # Record deployment in tracking system
        curl -X POST ${{ secrets.DEPLOYMENT_TRACKER_URL }} \
          -H "Content-Type: application/json" \
          -d '{
            "environment": "development",
            "version": "${{ github.sha }}",
            "status": "${{ job.status }}",
            "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
            "deployed_by": "${{ github.actor }}"
          }'

    - name: Trigger E2E tests
      if: success()
      uses: peter-evans/repository-dispatch@v2
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        event-type: run-e2e-tests
        client-payload: '{"environment": "development", "ref": "${{ github.sha }}"}'

  rollback:
    needs: [deploy-kubernetes]
    runs-on: ubuntu-latest
    if: failure()
    
    steps:
    - name: Configure kubectl
      uses: azure/setup-kubectl@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region us-east-1 --name whimlabai-dev-cluster

    - name: Rollback deployments
      run: |
        kubectl rollout undo deployment/webapi --namespace=${{ env.NAMESPACE }}
        kubectl rollout undo deployment/admin --namespace=${{ env.NAMESPACE }}
        kubectl rollout undo deployment/customer-web --namespace=${{ env.NAMESPACE }}
        
        # Wait for rollback to complete
        kubectl rollout status deployment/webapi --namespace=${{ env.NAMESPACE }} --timeout=5m
        kubectl rollout status deployment/admin --namespace=${{ env.NAMESPACE }} --timeout=5m
        kubectl rollout status deployment/customer-web --namespace=${{ env.NAMESPACE }} --timeout=5m

    - name: Send rollback notification
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        custom_payload: |
          {
            text: "⚠️ Development deployment rolled back",
            color: "warning",
            fields: [{
              title: "Environment",
              value: "Development",
              short: true
            }, {
              title: "Trigger",
              value: "${{ github.actor }}",
              short: true
            }]
          }
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}