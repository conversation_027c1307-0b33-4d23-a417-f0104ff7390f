name: Deploy to Staging

on:
  workflow_dispatch:
    inputs:
      image_tag:
        description: 'Docker image tag to deploy'
        required: true
        default: 'main'
      run_migrations:
        description: 'Run database migrations'
        required: true
        type: boolean
        default: true
      run_tests:
        description: 'Run post-deployment tests'
        required: true
        type: boolean
        default: true

env:
  ENVIRONMENT: staging
  NAMESPACE: whimlabai-staging
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  approval:
    runs-on: ubuntu-latest
    environment: staging-approval
    steps:
    - name: Request deployment approval
      run: echo "Deployment to staging approved"

  pre-deployment:
    needs: approval
    runs-on: ubuntu-latest
    outputs:
      previous_version: ${{ steps.get_version.outputs.version }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/setup-kubectl@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region us-east-1 --name whimlabai-staging-cluster

    - name: Get current version
      id: get_version
      run: |
        version=$(kubectl get deployment webapi -n ${{ env.NAMESPACE }} -o jsonpath='{.spec.template.spec.containers[0].image}' | cut -d: -f2)
        echo "version=$version" >> $GITHUB_OUTPUT
        echo "Current version: $version"

    - name: Create backup
      run: |
        # Backup current state
        kubectl get all -n ${{ env.NAMESPACE }} -o yaml > backup-${{ steps.get_version.outputs.version }}.yaml
        
        # Upload backup to S3
        aws s3 cp backup-${{ steps.get_version.outputs.version }}.yaml \
          s3://whimlabai-backups/staging/deployments/backup-${{ steps.get_version.outputs.version }}-$(date +%Y%m%d%H%M%S).yaml

    - name: Health check
      run: |
        # Check cluster health
        kubectl get nodes
        kubectl top nodes
        kubectl get pods -n ${{ env.NAMESPACE }}

  deploy-infrastructure:
    needs: pre-deployment
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update infrastructure
      run: |
        # Update RDS parameter groups if needed
        aws rds modify-db-parameter-group \
          --db-parameter-group-name whimlabai-staging \
          --parameters "ParameterName=max_connections,ParameterValue=200,ApplyMethod=immediate"
        
        # Update ElastiCache if needed
        # aws elasticache modify-cache-cluster ...

  deploy-database-migrations:
    needs: deploy-infrastructure
    if: inputs.run_migrations
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Backup database
      run: |
        # Create database snapshot
        aws rds create-db-snapshot \
          --db-instance-identifier whimlabai-staging \
          --db-snapshot-identifier whimlabai-staging-$(date +%Y%m%d%H%M%S)

    - name: Get database connection
      id: db_secret
      run: |
        secret=$(aws secretsmanager get-secret-value --secret-id whimlabai/staging/database --query SecretString --output text)
        echo "::add-mask::$secret"
        echo "DB_CONNECTION=$secret" >> $GITHUB_OUTPUT

    - name: Generate migration SQL
      run: |
        cd BackEnd/WhimLabAI.Infrastructure
        dotnet ef migrations script \
          --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
          --context WhimLabAIDbContext \
          --output migrations-staging.sql

    - name: Review migration SQL
      run: |
        echo "Migration SQL to be applied:"
        cat BackEnd/WhimLabAI.Infrastructure/migrations-staging.sql

    - name: Apply migrations
      env:
        ConnectionStrings__DefaultConnection: ${{ steps.db_secret.outputs.DB_CONNECTION }}
      run: |
        cd BackEnd/WhimLabAI.Infrastructure
        dotnet ef database update \
          --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
          --context WhimLabAIDbContext

  deploy-applications:
    needs: [deploy-database-migrations]
    runs-on: ubuntu-latest
    environment: staging
    strategy:
      matrix:
        component: [webapi, admin, customer-web]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/setup-kubectl@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region us-east-1 --name whimlabai-staging-cluster

    - name: Deploy ${{ matrix.component }}
      run: |
        # Update image with canary deployment
        kubectl set image deployment/${{ matrix.component }}-canary \
          ${{ matrix.component }}=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.component }}:${{ inputs.image_tag }} \
          --namespace=${{ env.NAMESPACE }}
        
        # Wait for canary to be ready
        kubectl rollout status deployment/${{ matrix.component }}-canary \
          --namespace=${{ env.NAMESPACE }} \
          --timeout=5m

        # Run canary tests
        ./scripts/canary-test.sh ${{ matrix.component }} ${{ env.NAMESPACE }}

        # If successful, update main deployment
        kubectl set image deployment/${{ matrix.component }} \
          ${{ matrix.component }}=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.component }}:${{ inputs.image_tag }} \
          --namespace=${{ env.NAMESPACE }}
        
        # Wait for main deployment
        kubectl rollout status deployment/${{ matrix.component }} \
          --namespace=${{ env.NAMESPACE }} \
          --timeout=10m

        # Scale down canary
        kubectl scale deployment/${{ matrix.component }}-canary --replicas=0 \
          --namespace=${{ env.NAMESPACE }}

  post-deployment-tests:
    needs: deploy-applications
    if: inputs.run_tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install test dependencies
      run: |
        cd tests/e2e
        npm ci

    - name: Run E2E tests
      env:
        BASE_URL: https://staging.whimlabai.com
        API_URL: https://api-staging.whimlabai.com
      run: |
        cd tests/e2e
        npm run test:staging

    - name: Run performance tests
      run: |
        # Run k6 performance tests
        docker run --rm \
          -v $(pwd)/tests/performance:/scripts \
          grafana/k6 run \
          -e BASE_URL=https://api-staging.whimlabai.com \
          /scripts/load-test.js

    - name: Security scan
      run: |
        # Run OWASP ZAP scan
        docker run --rm \
          -v $(pwd):/zap/wrk/:rw \
          owasp/zap2docker-stable zap-baseline.py \
          -t https://staging.whimlabai.com \
          -r security-report-staging.html

    - name: Upload test results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: staging-test-results
        path: |
          tests/e2e/reports/
          security-report-staging.html

  monitoring-verification:
    needs: post-deployment-tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Verify metrics
      run: |
        # Check Prometheus metrics
        curl -s https://prometheus-staging.whimlabai.com/api/v1/query \
          -d 'query=up{job="whimlabai"}' | jq '.data.result[].value[1]' | grep -q "1"

    - name: Check error rates
      run: |
        # Query error rate from last 15 minutes
        error_rate=$(curl -s https://prometheus-staging.whimlabai.com/api/v1/query \
          -d 'query=rate(http_requests_total{status=~"5.."}[15m])' | jq '.data.result[0].value[1]')
        
        if (( $(echo "$error_rate > 0.01" | bc -l) )); then
          echo "Error rate too high: $error_rate"
          exit 1
        fi

    - name: Verify logging
      run: |
        # Check that logs are flowing to Elasticsearch
        curl -s https://elasticsearch-staging.whimlabai.com/_cluster/health | jq '.status' | grep -q "green"

  notification:
    needs: [deploy-applications, post-deployment-tests, monitoring-verification]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Generate deployment summary
      id: summary
      run: |
        echo "SUMMARY<<EOF" >> $GITHUB_OUTPUT
        echo "Deployment to Staging: ${{ job.status }}" >> $GITHUB_OUTPUT
        echo "Version: ${{ inputs.image_tag }}" >> $GITHUB_OUTPUT
        echo "Migrations: ${{ inputs.run_migrations }}" >> $GITHUB_OUTPUT
        echo "Tests: ${{ inputs.run_tests }}" >> $GITHUB_OUTPUT
        echo "Deployed by: ${{ github.actor }}" >> $GITHUB_OUTPUT
        echo "Time: $(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Send Slack notification
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        text: ${{ steps.summary.outputs.SUMMARY }}
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

    - name: Update deployment board
      run: |
        # Update Jira/Linear ticket
        curl -X POST https://api.linear.app/graphql \
          -H "Authorization: ${{ secrets.LINEAR_API_KEY }}" \
          -H "Content-Type: application/json" \
          -d '{
            "query": "mutation { issueUpdate(id: \"DEPLOY-123\", input: { stateId: \"deployed-staging\" }) { success } }"
          }'

  rollback-on-failure:
    needs: [deploy-applications]
    runs-on: ubuntu-latest
    if: failure()
    
    steps:
    - name: Configure kubectl
      uses: azure/setup-kubectl@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region us-east-1 --name whimlabai-staging-cluster

    - name: Rollback to previous version
      run: |
        echo "Rolling back to version: ${{ needs.pre-deployment.outputs.previous_version }}"
        
        kubectl set image deployment/webapi \
          webapi=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/webapi:${{ needs.pre-deployment.outputs.previous_version }} \
          --namespace=${{ env.NAMESPACE }}
        
        kubectl set image deployment/admin \
          admin=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/admin:${{ needs.pre-deployment.outputs.previous_version }} \
          --namespace=${{ env.NAMESPACE }}
        
        kubectl set image deployment/customer-web \
          customer-web=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/customer-web:${{ needs.pre-deployment.outputs.previous_version }} \
          --namespace=${{ env.NAMESPACE }}

    - name: Verify rollback
      run: |
        kubectl rollout status deployment/webapi --namespace=${{ env.NAMESPACE }}
        kubectl rollout status deployment/admin --namespace=${{ env.NAMESPACE }}
        kubectl rollout status deployment/customer-web --namespace=${{ env.NAMESPACE }}

    - name: Create incident
      run: |
        # Create PagerDuty incident
        curl -X POST https://api.pagerduty.com/incidents \
          -H "Authorization: Token token=${{ secrets.PAGERDUTY_TOKEN }}" \
          -H "Content-Type: application/json" \
          -d '{
            "incident": {
              "type": "incident",
              "title": "Staging deployment failed and rolled back",
              "service": {
                "id": "PXXXXXX",
                "type": "service_reference"
              },
              "urgency": "high"
            }
          }'