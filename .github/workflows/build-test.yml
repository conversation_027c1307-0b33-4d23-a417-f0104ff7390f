name: Build and Test

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:

env:
  DOTNET_VERSION: '9.0.x'
  DOTNET_NOLOGO: true
  DOTNET_CLI_TELEMETRY_OPTOUT: true

jobs:
  build-test:
    runs-on: ubuntu-latest
    
    services:
      ********:
        image: ********:16
        env:
          POSTGRES_USER: ********
          POSTGRES_PASSWORD: ********
          POSTGRES_DB: whimlabai_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore WhimLabAI.sln

    - name: Build solution
      run: dotnet build WhimLabAI.sln --no-restore --configuration Release

    - name: Run architecture checks
      run: |
        chmod +x ./scripts/check-architecture-complete.sh
        ./scripts/check-architecture-complete.sh

    - name: Run unit tests
      run: |
        dotnet test Tests/WhimLabAI.UnitTests/WhimLabAI.UnitTests.csproj \
          --no-build \
          --configuration Release \
          --logger "trx;LogFileName=unit-test-results.trx" \
          --collect:"XPlat Code Coverage" \
          --results-directory ./TestResults/Unit

    - name: Run integration tests
      env:
        ConnectionStrings__DefaultConnection: "Host=localhost;Port=5432;Database=whimlabai_test;Username=********;Password=********"
        ConnectionStrings__Redis: "localhost:6379"
      run: |
        dotnet test Tests/WhimLabAI.IntegrationTests/WhimLabAI.IntegrationTests.csproj \
          --no-build \
          --configuration Release \
          --logger "trx;LogFileName=integration-test-results.trx" \
          --collect:"XPlat Code Coverage" \
          --results-directory ./TestResults/Integration

    - name: Upload test results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: test-results
        path: TestResults/

    - name: Generate code coverage report
      if: always()
      run: |
        dotnet tool install --global dotnet-reportgenerator-globaltool
        reportgenerator \
          -reports:TestResults/**/coverage.cobertura.xml \
          -targetdir:CoverageReport \
          -reporttypes:Html;Cobertura

    - name: Upload coverage reports
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: CoverageReport/

    - name: Check code coverage
      run: |
        # Parse coverage and fail if below threshold
        coverage=$(grep -oP 'line-rate="\K[0-9.]+' CoverageReport/Cobertura.xml | head -1)
        threshold=0.80
        if (( $(echo "$coverage < $threshold" | bc -l) )); then
          echo "Code coverage $coverage is below threshold $threshold"
          exit 1
        fi
        echo "Code coverage $coverage meets threshold $threshold"

    - name: Run security scan
      run: |
        dotnet tool install --global security-scan
        security-scan --path . --format json --output security-report.json

    - name: Upload security report
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: security-report
        path: security-report.json

    - name: Post results to PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const coverage = fs.readFileSync('CoverageReport/Summary.txt', 'utf8');
          
          await github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## Test Results\n\n${coverage}`
          });