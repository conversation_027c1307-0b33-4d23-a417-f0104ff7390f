name: Deploy to Production

on:
  workflow_dispatch:
    inputs:
      image_tag:
        description: 'Docker image tag to deploy (must be from staging)'
        required: true
      deployment_strategy:
        description: 'Deployment strategy'
        required: true
        type: choice
        options:
          - blue-green
          - canary
          - rolling
        default: blue-green
      rollback_plan:
        description: 'Rollback plan confirmed'
        required: true
        type: boolean
      maintenance_window:
        description: 'Maintenance window confirmed'
        required: true
        type: boolean

env:
  ENVIRONMENT: production
  NAMESPACE: whimlabai-prod
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  validation:
    runs-on: ubuntu-latest
    environment: production-approval
    outputs:
      proceed: ${{ steps.validate.outputs.proceed }}
    
    steps:
    - name: Validate deployment prerequisites
      id: validate
      run: |
        # Check that image exists and was tested in staging
        if [[ ! "${{ inputs.image_tag }}" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
          echo "Error: Production deployments must use semantic version tags"
          exit 1
        fi
        
        if [[ "${{ inputs.rollback_plan }}" != "true" ]]; then
          echo "Error: Rollback plan must be confirmed"
          exit 1
        fi
        
        if [[ "${{ inputs.maintenance_window }}" != "true" ]]; then
          echo "Error: Maintenance window must be confirmed"
          exit 1
        fi
        
        echo "proceed=true" >> $GITHUB_OUTPUT

    - name: Verify staging validation
      run: |
        # Check that this version was successfully deployed to staging
        # This would query deployment history API/database
        echo "Verifying ${{ inputs.image_tag }} was tested in staging..."

  pre-deployment:
    needs: validation
    runs-on: ubuntu-latest
    outputs:
      backup_id: ${{ steps.backup.outputs.id }}
      current_version: ${{ steps.current.outputs.version }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Create change record
      run: |
        # Create change management record
        curl -X POST ${{ secrets.CHANGE_MANAGEMENT_API }} \
          -H "Authorization: Bearer ${{ secrets.CHANGE_MANAGEMENT_TOKEN }}" \
          -H "Content-Type: application/json" \
          -d '{
            "title": "Production deployment - ${{ inputs.image_tag }}",
            "type": "standard",
            "risk": "medium",
            "implementation_plan": "GitHub Actions automated deployment",
            "rollback_plan": "Automated rollback to previous version",
            "approvers": ["${{ github.actor }}"],
            "scheduled_start": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
            "estimated_duration": "30 minutes"
          }'

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Get current production version
      id: current
      run: |
        aws eks update-kubeconfig --region us-east-1 --name whimlabai-prod-cluster
        version=$(kubectl get deployment webapi -n ${{ env.NAMESPACE }} -o jsonpath='{.spec.template.spec.containers[0].image}' | cut -d: -f2)
        echo "version=$version" >> $GITHUB_OUTPUT
        echo "Current production version: $version"

    - name: Create comprehensive backup
      id: backup
      run: |
        backup_id="prod-backup-$(date +%Y%m%d%H%M%S)"
        echo "id=$backup_id" >> $GITHUB_OUTPUT
        
        # Backup Kubernetes state
        kubectl get all,configmap,secret,pvc -n ${{ env.NAMESPACE }} -o yaml > k8s-backup-$backup_id.yaml
        aws s3 cp k8s-backup-$backup_id.yaml s3://whimlabai-backups/production/k8s/$backup_id.yaml
        
        # Create RDS snapshot
        aws rds create-db-cluster-snapshot \
          --db-cluster-snapshot-identifier $backup_id \
          --db-cluster-identifier whimlabai-prod-cluster
        
        # Backup Redis state
        aws elasticache create-snapshot \
          --snapshot-name $backup_id \
          --replication-group-id whimlabai-prod-redis
        
        # Export critical data
        ./scripts/export-critical-data.sh --env=production --backup-id=$backup_id

    - name: Notify team
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        custom_payload: |
          {
            text: "🚀 Production deployment starting",
            blocks: [{
              type: "section",
              text: {
                type: "mrkdwn",
                text: "*Production Deployment Starting*\n*Version:* ${{ inputs.image_tag }}\n*Strategy:* ${{ inputs.deployment_strategy }}\n*Initiated by:* ${{ github.actor }}"
              }
            }]
          }
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  database-migration:
    needs: pre-deployment
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Get database connection
      id: db_secret
      run: |
        secret=$(aws secretsmanager get-secret-value --secret-id whimlabai/prod/database --query SecretString --output text)
        echo "::add-mask::$secret"
        echo "DB_CONNECTION=$secret" >> $GITHUB_OUTPUT

    - name: Run migrations with zero-downtime
      env:
        ConnectionStrings__DefaultConnection: ${{ steps.db_secret.outputs.DB_CONNECTION }}
      run: |
        # Generate migration SQL
        cd BackEnd/WhimLabAI.Infrastructure
        dotnet ef migrations script \
          --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
          --context WhimLabAIDbContext \
          --idempotent \
          --output migrations-prod.sql
        
        # Apply migrations in transaction
        psql "${{ steps.db_secret.outputs.DB_CONNECTION }}" -1 -f migrations-prod.sql

  deploy-blue-green:
    needs: database-migration
    if: inputs.deployment_strategy == 'blue-green'
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/setup-kubectl@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region us-east-1 --name whimlabai-prod-cluster

    - name: Deploy to green environment
      run: |
        # Deploy to green environment
        kubectl apply -f k8s/production/green/ -n ${{ env.NAMESPACE }}
        
        # Update green deployments with new images
        for component in webapi admin customer-web; do
          kubectl set image deployment/${component}-green \
            ${component}=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${component}:${{ inputs.image_tag }} \
            --namespace=${{ env.NAMESPACE }}
          
          kubectl rollout status deployment/${component}-green \
            --namespace=${{ env.NAMESPACE }} \
            --timeout=10m
        done

    - name: Run smoke tests on green
      run: |
        # Test green environment through internal service
        ./scripts/smoke-test.sh --env=green --namespace=${{ env.NAMESPACE }}

    - name: Switch traffic to green
      run: |
        # Update service selectors to point to green
        for service in webapi admin customer-web; do
          kubectl patch service ${service} \
            -n ${{ env.NAMESPACE }} \
            -p '{"spec":{"selector":{"deployment":"green"}}}'
        done
        
        # Wait for traffic to stabilize
        sleep 30

    - name: Verify green deployment
      run: |
        # Monitor error rates and performance
        ./scripts/monitor-deployment.sh --duration=300 --threshold=0.01

    - name: Cleanup blue environment
      run: |
        # After successful verification, scale down blue
        for component in webapi admin customer-web; do
          kubectl scale deployment/${component}-blue --replicas=0 \
            --namespace=${{ env.NAMESPACE }}
        done

  deploy-canary:
    needs: database-migration
    if: inputs.deployment_strategy == 'canary'
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/setup-kubectl@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region us-east-1 --name whimlabai-prod-cluster

    - name: Deploy canary (10% traffic)
      run: |
        # Deploy canary instances
        for component in webapi admin customer-web; do
          kubectl apply -f k8s/production/canary/${component}-canary.yaml \
            -n ${{ env.NAMESPACE }}
          
          kubectl set image deployment/${component}-canary \
            ${component}=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${component}:${{ inputs.image_tag }} \
            --namespace=${{ env.NAMESPACE }}
          
          kubectl rollout status deployment/${component}-canary \
            --namespace=${{ env.NAMESPACE }}
        done
        
        # Configure 10% traffic split
        kubectl apply -f k8s/production/canary/traffic-split-10.yaml \
          -n ${{ env.NAMESPACE }}

    - name: Monitor canary metrics (15 min)
      run: |
        ./scripts/canary-analysis.sh \
          --duration=900 \
          --error-threshold=0.02 \
          --latency-threshold=200

    - name: Increase canary traffic (50%)
      run: |
        kubectl apply -f k8s/production/canary/traffic-split-50.yaml \
          -n ${{ env.NAMESPACE }}
        
        # Monitor for another 15 minutes
        ./scripts/canary-analysis.sh \
          --duration=900 \
          --error-threshold=0.02 \
          --latency-threshold=200

    - name: Complete canary deployment (100%)
      run: |
        # Update main deployments
        for component in webapi admin customer-web; do
          kubectl set image deployment/${component} \
            ${component}=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${component}:${{ inputs.image_tag }} \
            --namespace=${{ env.NAMESPACE }}
          
          kubectl rollout status deployment/${component} \
            --namespace=${{ env.NAMESPACE }}
        done
        
        # Remove canary deployments
        kubectl delete -f k8s/production/canary/ -n ${{ env.NAMESPACE }}

  post-deployment:
    needs: [deploy-blue-green, deploy-canary]
    if: always()
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run comprehensive tests
      run: |
        # API contract tests
        newman run tests/postman/production-api-tests.json \
          --environment tests/postman/production-env.json \
          --reporters cli,json \
          --reporter-json-export test-results.json

    - name: Performance validation
      run: |
        # Run load test
        k6 run \
          -e BASE_URL=https://api.whimlabai.com \
          -e DURATION=5m \
          -e VUS=100 \
          tests/performance/production-load-test.js

    - name: Security validation
      run: |
        # Quick security scan
        ./scripts/security-scan.sh --env=production --quick

    - name: Update monitoring
      run: |
        # Update dashboards with new version
        curl -X POST https://grafana.whimlabai.com/api/annotations \
          -H "Authorization: Bearer ${{ secrets.GRAFANA_API_KEY }}" \
          -H "Content-Type: application/json" \
          -d '{
            "dashboardId": 1,
            "tags": ["deployment", "production"],
            "text": "Deployed version ${{ inputs.image_tag }}"
          }'

    - name: Update documentation
      run: |
        # Update API documentation
        curl https://api.whimlabai.com/swagger/v1/swagger.json \
          -o docs/api/swagger-${{ inputs.image_tag }}.json
        
        # Commit to docs repository
        git config --global user.email "<EMAIL>"
        git config --global user.name "WhimLabAI Bot"
        git add docs/api/swagger-${{ inputs.image_tag }}.json
        git commit -m "Update API docs for version ${{ inputs.image_tag }}"
        git push

  notification:
    needs: post-deployment
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Calculate deployment metrics
      id: metrics
      run: |
        start_time=${{ needs.validation.outputs.start_time }}
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "duration=$duration" >> $GITHUB_OUTPUT

    - name: Send deployment report
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        custom_payload: |
          {
            text: "Production Deployment Complete",
            blocks: [{
              type: "header",
              text: {
                type: "plain_text",
                text: "🎉 Production Deployment Complete"
              }
            }, {
              type: "section",
              fields: [{
                type: "mrkdwn",
                text: "*Status:* ${{ job.status == 'success' && '✅ Success' || '❌ Failed' }}"
              }, {
                type: "mrkdwn",
                text: "*Version:* ${{ inputs.image_tag }}"
              }, {
                type: "mrkdwn",
                text: "*Duration:* ${{ steps.metrics.outputs.duration }}s"
              }, {
                type: "mrkdwn",
                text: "*Strategy:* ${{ inputs.deployment_strategy }}"
              }]
            }]
          }
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

    - name: Update status page
      run: |
        # Update public status page
        curl -X POST https://api.statuspage.io/v1/pages/${{ secrets.STATUSPAGE_ID }}/incidents \
          -H "Authorization: OAuth ${{ secrets.STATUSPAGE_API_KEY }}" \
          -H "Content-Type: application/json" \
          -d '{
            "incident": {
              "name": "Scheduled Maintenance - Deployment",
              "status": "resolved",
              "impact": "none",
              "body": "Successfully deployed version ${{ inputs.image_tag }}"
            }
          }'

  rollback:
    needs: [deploy-blue-green, deploy-canary, post-deployment]
    if: failure()
    runs-on: ubuntu-latest
    
    steps:
    - name: Initiate emergency rollback
      run: |
        echo "EMERGENCY ROLLBACK INITIATED"
        
        # Create incident
        curl -X POST https://api.pagerduty.com/incidents \
          -H "Authorization: Token token=${{ secrets.PAGERDUTY_TOKEN }}" \
          -H "Content-Type: application/json" \
          -d '{
            "incident": {
              "type": "incident",
              "title": "Production deployment failed - Emergency rollback",
              "service": {
                "id": "${{ secrets.PAGERDUTY_SERVICE_ID }}",
                "type": "service_reference"
              },
              "urgency": "critical"
            }
          }'

    - name: Configure kubectl
      uses: azure/setup-kubectl@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Execute rollback
      run: |
        aws eks update-kubeconfig --region us-east-1 --name whimlabai-prod-cluster
        
        # Restore from backup
        aws s3 cp s3://whimlabai-backups/production/k8s/${{ needs.pre-deployment.outputs.backup_id }}.yaml ./backup.yaml
        kubectl apply -f backup.yaml -n ${{ env.NAMESPACE }}
        
        # Force update to previous version
        for component in webapi admin customer-web; do
          kubectl set image deployment/${component} \
            ${component}=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${component}:${{ needs.pre-deployment.outputs.current_version }} \
            --namespace=${{ env.NAMESPACE }}
        done

    - name: Verify rollback
      run: |
        # Wait for rollback to complete
        for component in webapi admin customer-web; do
          kubectl rollout status deployment/${component} \
            --namespace=${{ env.NAMESPACE }} \
            --timeout=10m
        done
        
        # Run health checks
        ./scripts/health-check.sh --env=production

    - name: Post-mortem
      run: |
        # Collect logs and metrics for analysis
        ./scripts/collect-failure-data.sh \
          --deployment-id=${{ github.run_id }} \
          --output=post-mortem-${{ github.run_id }}.tar.gz
        
        # Upload to S3
        aws s3 cp post-mortem-${{ github.run_id }}.tar.gz \
          s3://whimlabai-incidents/deployments/