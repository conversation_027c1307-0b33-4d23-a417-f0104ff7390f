# WhimLab AI - 持续部署工作流
name: CD

on:
  push:
    branches: [ main ]
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      version:
        description: '部署版本（留空使用最新）'
        required: false

env:
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  KUBE_NAMESPACE: whimlab

jobs:
  # 准备部署
  prepare:
    name: 准备部署
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      version: ${{ steps.version.outputs.version }}
      
    steps:
    - name: 确定部署环境
      id: env
      run: |
        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
        elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
          echo "environment=staging" >> $GITHUB_OUTPUT
        elif [[ "${{ github.ref }}" == refs/tags/* ]]; then
          echo "environment=production" >> $GITHUB_OUTPUT
        fi
        
    - name: 确定部署版本
      id: version
      run: |
        if [[ "${{ github.event_name }}" == "workflow_dispatch" && -n "${{ github.event.inputs.version }}" ]]; then
          echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
        elif [[ "${{ github.ref }}" == refs/tags/* ]]; then
          echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
        else
          echo "version=${{ github.sha }}" >> $GITHUB_OUTPUT
        fi

  # 部署到 Staging
  deploy-staging:
    name: 部署到 Staging
    runs-on: ubuntu-latest
    needs: prepare
    if: needs.prepare.outputs.environment == 'staging'
    environment:
      name: staging
      url: https://staging.whimlab.ai
      
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
        
    - name: 配置 AWS 凭据
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ap-southeast-1
        
    - name: 更新 kubeconfig
      run: |
        aws eks update-kubeconfig --region ap-southeast-1 --name whimlab-staging
        
    - name: 创建命名空间
      run: |
        kubectl create namespace ${{ env.KUBE_NAMESPACE }}-staging --dry-run=client -o yaml | kubectl apply -f -
        
    - name: 设置镜像密钥
      run: |
        kubectl create secret docker-registry ghcr-secret \
          --docker-server=${{ env.DOCKER_REGISTRY }} \
          --docker-username=${{ github.actor }} \
          --docker-password=${{ secrets.GITHUB_TOKEN }} \
          --namespace=${{ env.KUBE_NAMESPACE }}-staging \
          --dry-run=client -o yaml | kubectl apply -f -
          
    - name: 部署数据库迁移 Job
      run: |
        cat <<EOF | kubectl apply -f -
        apiVersion: batch/v1
        kind: Job
        metadata:
          name: db-migration-${{ github.run_number }}
          namespace: ${{ env.KUBE_NAMESPACE }}-staging
        spec:
          template:
            spec:
              containers:
              - name: migration
                image: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.prepare.outputs.version }}
                command: ["dotnet", "ef", "database", "update"]
                env:
                - name: ASPNETCORE_ENVIRONMENT
                  value: Staging
                envFrom:
                - secretRef:
                    name: whimlab-secrets
              restartPolicy: Never
              imagePullSecrets:
              - name: ghcr-secret
          backoffLimit: 3
        EOF
        
    - name: 等待迁移完成
      run: |
        kubectl wait --for=condition=complete \
          job/db-migration-${{ github.run_number }} \
          --namespace=${{ env.KUBE_NAMESPACE }}-staging \
          --timeout=300s
          
    - name: 部署应用
      run: |
        helm upgrade --install whimlab-staging ./charts/whimlab \
          --namespace=${{ env.KUBE_NAMESPACE }}-staging \
          --set image.repository=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }} \
          --set image.tag=${{ needs.prepare.outputs.version }} \
          --set environment=staging \
          --set ingress.enabled=true \
          --set ingress.hosts[0].host=staging.whimlab.ai \
          --set ingress.hosts[0].paths[0].path=/ \
          --set ingress.hosts[0].paths[0].pathType=Prefix \
          --values ./charts/whimlab/values-staging.yaml \
          --wait
          
    - name: 验证部署
      run: |
        kubectl rollout status deployment/whimlab-api \
          --namespace=${{ env.KUBE_NAMESPACE }}-staging \
          --timeout=300s
          
    - name: 运行健康检查
      run: |
        for i in {1..30}; do
          if curl -f https://staging.whimlab.ai/health; then
            echo "健康检查通过"
            exit 0
          fi
          echo "等待服务启动... ($i/30)"
          sleep 10
        done
        echo "健康检查失败"
        exit 1

  # 部署到 Production
  deploy-production:
    name: 部署到 Production
    runs-on: ubuntu-latest
    needs: prepare
    if: needs.prepare.outputs.environment == 'production'
    environment:
      name: production
      url: https://whimlab.ai
      
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
        
    - name: 配置 AWS 凭据
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
        aws-region: ap-southeast-1
        
    - name: 更新 kubeconfig
      run: |
        aws eks update-kubeconfig --region ap-southeast-1 --name whimlab-production
        
    - name: 创建命名空间
      run: |
        kubectl create namespace ${{ env.KUBE_NAMESPACE }} --dry-run=client -o yaml | kubectl apply -f -
        
    - name: 设置镜像密钥
      run: |
        kubectl create secret docker-registry ghcr-secret \
          --docker-server=${{ env.DOCKER_REGISTRY }} \
          --docker-username=${{ github.actor }} \
          --docker-password=${{ secrets.GITHUB_TOKEN }} \
          --namespace=${{ env.KUBE_NAMESPACE }} \
          --dry-run=client -o yaml | kubectl apply -f -
          
    - name: 备份数据库
      run: |
        kubectl exec -n ${{ env.KUBE_NAMESPACE }} \
          deployment/postgres \
          -- pg_dump -U whimlab whimlab_prod > backup-$(date +%Y%m%d-%H%M%S).sql
          
    - name: 上传备份
      uses: actions/upload-artifact@v4
      with:
        name: db-backup-${{ github.run_number }}
        path: backup-*.sql
        retention-days: 30
        
    - name: 蓝绿部署
      run: |
        # 部署到绿环境
        helm upgrade --install whimlab-green ./charts/whimlab \
          --namespace=${{ env.KUBE_NAMESPACE }} \
          --set image.repository=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }} \
          --set image.tag=${{ needs.prepare.outputs.version }} \
          --set environment=production \
          --set deployment.name=whimlab-green \
          --set service.name=whimlab-green \
          --values ./charts/whimlab/values-production.yaml \
          --wait
          
        # 运行测试
        kubectl run smoke-test-${{ github.run_number }} \
          --namespace=${{ env.KUBE_NAMESPACE }} \
          --image=curlimages/curl:latest \
          --rm -it --restart=Never \
          -- curl -f http://whimlab-green/health
          
        # 切换流量
        kubectl patch service whimlab-api \
          --namespace=${{ env.KUBE_NAMESPACE }} \
          --patch '{"spec":{"selector":{"deployment":"whimlab-green"}}}'
          
        # 等待稳定
        sleep 60
        
        # 删除蓝环境
        helm uninstall whimlab-blue --namespace=${{ env.KUBE_NAMESPACE }} || true
        
        # 重命名绿环境为蓝环境
        helm upgrade whimlab-green ./charts/whimlab \
          --namespace=${{ env.KUBE_NAMESPACE }} \
          --reuse-values \
          --set deployment.name=whimlab-blue \
          --set service.name=whimlab-blue
          
    - name: 验证部署
      run: |
        for i in {1..30}; do
          if curl -f https://whimlab.ai/health; then
            echo "生产环境健康检查通过"
            exit 0
          fi
          echo "等待服务启动... ($i/30)"
          sleep 10
        done
        echo "生产环境健康检查失败"
        exit 1
        
    - name: 创建发布记录
      uses: softprops/action-gh-release@v1
      if: startsWith(github.ref, 'refs/tags/')
      with:
        body: |
          ## WhimLab AI ${{ needs.prepare.outputs.version }}
          
          ### 部署信息
          - 环境: Production
          - 时间: ${{ github.event.head_commit.timestamp }}
          - 提交: ${{ github.sha }}
          
          ### 更新内容
          请查看 [CHANGELOG.md](https://github.com/${{ github.repository }}/blob/main/CHANGELOG.md)

  # 回滚
  rollback:
    name: 回滚部署
    runs-on: ubuntu-latest
    if: failure() && (needs.deploy-staging.result == 'failure' || needs.deploy-production.result == 'failure')
    needs: [deploy-staging, deploy-production]
    
    steps:
    - name: 确定回滚环境
      id: env
      run: |
        if [[ "${{ needs.deploy-staging.result }}" == "failure" ]]; then
          echo "environment=staging" >> $GITHUB_OUTPUT
          echo "namespace=${{ env.KUBE_NAMESPACE }}-staging" >> $GITHUB_OUTPUT
        else
          echo "environment=production" >> $GITHUB_OUTPUT
          echo "namespace=${{ env.KUBE_NAMESPACE }}" >> $GITHUB_OUTPUT
        fi
        
    - name: 配置 AWS 凭据
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ steps.env.outputs.environment == 'production' && secrets.PROD_AWS_ACCESS_KEY_ID || secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ steps.env.outputs.environment == 'production' && secrets.PROD_AWS_SECRET_ACCESS_KEY || secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ap-southeast-1
        
    - name: 执行回滚
      run: |
        aws eks update-kubeconfig --region ap-southeast-1 --name whimlab-${{ steps.env.outputs.environment }}
        helm rollback whimlab-${{ steps.env.outputs.environment }} \
          --namespace=${{ steps.env.outputs.namespace }} \
          --wait
          
    - name: 发送回滚通知
      if: vars.SLACK_WEBHOOK_URL != ''
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        custom_payload: |
          {
            text: "⚠️ 部署失败，已自动回滚",
            attachments: [{
              color: "warning",
              fields: [
                { title: "环境", value: "${{ steps.env.outputs.environment }}", short: true },
                { title: "版本", value: "${{ needs.prepare.outputs.version }}", short: true },
                { title: "触发者", value: "${{ github.actor }}", short: true },
                { title: "工作流", value: "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}", short: true }
              ]
            }]
          }
        webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}