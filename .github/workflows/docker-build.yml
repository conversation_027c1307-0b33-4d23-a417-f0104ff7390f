name: Docker Build and Push

on:
  push:
    branches: [main, develop]
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      tag:
        description: 'Docker image tag'
        required: false
        default: 'latest'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-webapi:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/webapi
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=raw,value=${{ github.event.inputs.tag || 'latest' }}
          type=sha,prefix={{branch}}-

    - name: Build and push WebAPI Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./FrontEnd/WebApi/WhimLabAI.WebApi/Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          BUILD_VERSION=${{ github.sha }}
          BUILD_DATE=${{ github.event.repository.updated_at }}

    - name: Generate SBOM
      uses: anchore/sbom-action@v0
      with:
        image: ${{ steps.meta.outputs.tags }}
        format: spdx-json
        output-file: sbom-webapi.json

    - name: Upload SBOM
      uses: actions/upload-artifact@v4
      with:
        name: sbom-webapi
        path: sbom-webapi.json

  build-admin:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/admin
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=raw,value=${{ github.event.inputs.tag || 'latest' }}
          type=sha,prefix={{branch}}-

    - name: Build and push Admin Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  build-customer-web:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/customer-web
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=raw,value=${{ github.event.inputs.tag || 'latest' }}
          type=sha,prefix={{branch}}-

    - name: Build and push Customer Web Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web/Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  security-scan:
    needs: [build-webapi, build-admin, build-customer-web]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: read
      security-events: write
    
    strategy:
      matrix:
        image: [webapi, admin, customer-web]
    
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.image }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results-${{ matrix.image }}.sarif'
        severity: 'CRITICAL,HIGH'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results-${{ matrix.image }}.sarif'
        category: 'container-scan-${{ matrix.image }}'

  create-release:
    if: startsWith(github.ref, 'refs/tags/v')
    needs: [build-webapi, build-admin, build-customer-web, security-scan]
    runs-on: ubuntu-latest
    permissions:
      contents: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Generate release notes
      id: release_notes
      run: |
        echo "RELEASE_NOTES<<EOF" >> $GITHUB_OUTPUT
        git log --pretty=format:"- %s" $(git describe --tags --abbrev=0 HEAD^)..HEAD >> $GITHUB_OUTPUT
        echo "" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        body: |
          ## Docker Images
          - WebAPI: `${{ needs.build-webapi.outputs.image-tag }}`
          - Admin: `${{ needs.build-admin.outputs.image-tag }}`
          - Customer Web: `${{ needs.build-customer-web.outputs.image-tag }}`
          
          ## Changes
          ${{ steps.release_notes.outputs.RELEASE_NOTES }}
        draft: false
        prerelease: ${{ contains(github.ref, '-rc') || contains(github.ref, '-beta') || contains(github.ref, '-alpha') }}