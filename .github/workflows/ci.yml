# WhimLab AI - 持续集成工作流
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  DOTNET_VERSION: '9.0.x'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        
    - name: 恢复依赖
      run: dotnet restore
      
    - name: 安装代码分析工具
      run: |
        dotnet tool install --global dotnet-format
        dotnet tool install --global dotnet-reportgenerator-globaltool
        dotnet tool install --global coverlet.console
        
    - name: 代码格式检查
      run: dotnet format --verify-no-changes --verbosity diagnostic
      
    - name: 代码分析
      run: dotnet build /p:TreatWarningsAsErrors=true /p:WarningsAsErrors=nullable

  # 单元测试
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: code-quality
    
    strategy:
      matrix:
        project: [
          'WhimLabAI.Domain.Tests',
          'WhimLabAI.Application.Tests',
          'WhimLabAI.Infrastructure.Tests',
          'WhimLabAI.WebApi.Tests'
        ]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        
    - name: 恢复依赖
      run: dotnet restore
      
    - name: 运行测试
      run: |
        dotnet test Tests/${{ matrix.project }}/${{ matrix.project }}.csproj \
          --no-restore \
          --logger "trx;LogFileName=${{ matrix.project }}.trx" \
          --logger "console;verbosity=detailed" \
          --collect:"XPlat Code Coverage" \
          --results-directory ./TestResults
      continue-on-error: true
      
    - name: 上传测试结果
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.project }}
        path: TestResults/
        retention-days: 7
        
    - name: 发布测试报告
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: ${{ matrix.project }} 测试报告
        path: 'TestResults/*.trx'
        reporter: dotnet-trx

  # 集成测试
  integration-tests:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      postgres:
        image: postgres:16-alpine
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: whimlab_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        
    - name: 恢复依赖
      run: dotnet restore
      
    - name: 运行集成测试
      env:
        ConnectionStrings__PostgreSQL: "Host=localhost;Database=whimlab_test;Username=test;Password=test"
        ConnectionStrings__Redis: "localhost:6379"
      run: |
        dotnet test Tests/WhimLabAI.IntegrationTests/WhimLabAI.IntegrationTests.csproj \
          --no-restore \
          --logger "trx" \
          --logger "console;verbosity=detailed"

  # 构建 Docker 镜像
  build-docker:
    name: 构建 Docker 镜像
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: 登录 GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 提取元数据
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
          
    - name: 构建并推送镜像
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
          BUILD_VERSION=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.version'] }}
          VCS_REF=${{ github.sha }}

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: build-docker
    
    steps:
    - name: 运行 Trivy 扫描
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'
        severity: 'CRITICAL,HIGH'
        
    - name: 上传扫描结果到 GitHub Security
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

  # 通知
  notify:
    name: 发送通知
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests, integration-tests, build-docker, security-scan]
    if: always()
    
    steps:
    - name: 检查工作流状态
      id: check
      run: |
        if [[ "${{ needs.code-quality.result }}" == "failure" || \
              "${{ needs.unit-tests.result }}" == "failure" || \
              "${{ needs.integration-tests.result }}" == "failure" || \
              "${{ needs.build-docker.result }}" == "failure" || \
              "${{ needs.security-scan.result }}" == "failure" ]]; then
          echo "status=failure" >> $GITHUB_OUTPUT
        else
          echo "status=success" >> $GITHUB_OUTPUT
        fi
        
    - name: 发送 Slack 通知
      if: vars.SLACK_WEBHOOK_URL != ''
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ steps.check.outputs.status }}
        webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
        text: |
          CI 工作流 ${{ steps.check.outputs.status == 'success' && '成功' || '失败' }}
          仓库: ${{ github.repository }}
          分支: ${{ github.ref_name }}
          提交: ${{ github.sha }}
          作者: ${{ github.actor }}