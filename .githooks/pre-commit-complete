#!/bin/bash

# WhimLabAI 完整 Pre-commit Hook
# 在提交前运行完整的架构合规性检查

echo "🔍 运行完整架构合规性检查..."

# 运行完整架构检查脚本
./scripts/check-architecture-complete.sh

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ 提交被阻止：发现架构或代码规范问题"
    echo ""
    echo "建议："
    echo "1. 修复上述所有错误（红色 ✗ 标记）"
    echo "2. 考虑修复警告（黄色 ⚠ 标记）以提高代码质量"
    echo "3. 查看 CLAUDE_ARCHITECTURE_RULES_COMPLETE.md 了解详细规范"
    echo ""
    echo "如果确实需要跳过检查（不推荐），使用："
    echo "git commit --no-verify"
    echo ""
    exit 1
fi

echo "✅ 架构检查通过"

# 运行其他检查
echo ""
echo "🔍 运行代码格式检查..."

# 检查是否有未格式化的代码
if command -v dotnet &> /dev/null; then
    dotnet format --verify-no-changes --verbosity quiet
    if [ $? -ne 0 ]; then
        echo "❌ 发现未格式化的代码"
        echo "请运行: dotnet format"
        exit 1
    fi
    echo "✅ 代码格式检查通过"
fi

# 检查是否有TODO/FIXME
echo ""
echo "🔍 检查待办事项..."
TODO_COUNT=$(git diff --cached --name-only | xargs grep -l "TODO\|FIXME\|HACK" 2>/dev/null | wc -l)
if [ $TODO_COUNT -gt 0 ]; then
    echo "⚠️  发现 $TODO_COUNT 个文件包含 TODO/FIXME/HACK"
    echo "建议在提交前解决这些待办事项"
fi

echo ""
echo "✅ 所有检查完成，可以提交"

exit 0