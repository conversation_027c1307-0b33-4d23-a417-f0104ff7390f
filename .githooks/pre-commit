#!/bin/bash

# WhimLabAI Pre-commit Hook
# 在提交前自动检查架构合规性

echo "Running architecture compliance checks..."

# 运行架构检查脚本
./scripts/check-architecture.sh

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ 提交被阻止：发现架构违规问题"
    echo "请先修复上述问题再提交代码"
    echo ""
    echo "提示："
    echo "1. 确保所有 Repository 在 Infrastructure/Data/Repositories 目录"
    echo "2. Infrastructure 层不能依赖 Application 层"
    echo "3. Repository 不应返回 DTO，只返回 Domain 实体"
    echo "4. 查看 CLAUDE_ARCHITECTURE_RULES.md 了解详细规则"
    exit 1
fi

echo "✅ 架构检查通过"

# 运行其他检查（如代码格式、测试等）
# dotnet format --verify-no-changes
# dotnet test --no-build

exit 0