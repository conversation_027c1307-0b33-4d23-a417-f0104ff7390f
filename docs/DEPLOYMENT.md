# WhimLab AI 生产环境部署指南

## 目录

1. [系统要求](#系统要求)
2. [环境准备](#环境准备)
3. [部署架构](#部署架构)
4. [部署步骤](#部署步骤)
5. [配置管理](#配置管理)
6. [SSL证书](#ssl证书)
7. [监控配置](#监控配置)
8. [备份策略](#备份策略)
9. [故障恢复](#故障恢复)
10. [性能优化](#性能优化)
11. [安全加固](#安全加固)
12. [运维手册](#运维手册)

## 系统要求

### 硬件要求

**最小配置（支持 100 并发用户）**
- CPU: 8 核心
- 内存: 16GB
- 存储: 200GB SSD
- 网络: 100Mbps

**推荐配置（支持 1000 并发用户）**
- CPU: 16 核心
- 内存: 32GB
- 存储: 500GB SSD (RAID 10)
- 网络: 1Gbps

**高性能配置（支持 5000+ 并发用户）**
- CPU: 32 核心
- 内存: 64GB
- 存储: 1TB NVMe SSD (RAID 10)
- 网络: 10Gbps

### 软件要求

- 操作系统: Ubuntu 22.04 LTS / CentOS 8+
- Docker: 24.0+
- Docker Compose: 2.20+
- Kubernetes: 1.28+ (可选)
- .NET Runtime: 9.0

## 环境准备

### 1. 系统初始化

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y \
    curl wget git vim \
    htop iotop nethogs \
    ufw fail2ban \
    software-properties-common

# 配置时区
sudo timedatectl set-timezone Asia/Shanghai

# 配置系统限制
cat << EOF | sudo tee -a /etc/security/limits.conf
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
EOF

# 配置内核参数
cat << EOF | sudo tee -a /etc/sysctl.conf
# 网络优化
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 15
net.ipv4.tcp_keepalive_time = 300
net.ipv4.tcp_tw_reuse = 1

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 40
vm.dirty_background_ratio = 10
EOF

sudo sysctl -p
```

### 2. 安装 Docker

```bash
# 安装 Docker
curl -fsSL https://get.docker.com | sudo sh

# 添加用户到 docker 组
sudo usermod -aG docker $USER

# 配置 Docker daemon
sudo mkdir -p /etc/docker
cat << EOF | sudo tee /etc/docker/daemon.json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 65536,
      "Soft": 65536
    }
  },
  "storage-driver": "overlay2",
  "metrics-addr": "127.0.0.1:9323",
  "experimental": true
}
EOF

sudo systemctl restart docker
```

### 3. 防火墙配置

```bash
# 配置 UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许 SSH
sudo ufw allow 22/tcp

# 允许 HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 允许内部服务通信
sudo ufw allow from 172.16.0.0/12 to any

# 启用防火墙
sudo ufw --force enable
```

## 部署架构

### 单机部署架构

```
┌─────────────────────────────────────────────────────────┐
│                      负载均衡器                          │
│                    (Nginx/HAProxy)                      │
└─────────────────────────┬───────────────────────────────┘
                          │
┌─────────────────────────┴───────────────────────────────┐
│                     应用服务器                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │  WebAPI     │  │   Admin     │  │  Customer   │    │
│  │  (Port 15800)│  │ (Port 5001) │  │ (Port 5002) │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
│                                                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │ PostgreSQL  │  │    Redis    │  │  RabbitMQ   │    │
│  │ (Port 5432) │  │ (Port 6379) │  │ (Port 5672) │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
│                                                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │   MinIO     │  │ Prometheus  │  │   Grafana   │    │
│  │ (Port 9000) │  │ (Port 9090) │  │ (Port 3000) │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────────────────────────┘
```

### 集群部署架构

```
                        ┌─────────────┐
                        │  云负载均衡  │
                        │  (ALB/NLB)  │
                        └──────┬──────┘
                               │
                ┌──────────────┴──────────────┐
                │                             │
        ┌───────┴────────┐           ┌───────┴────────┐
        │   Kubernetes   │           │   Kubernetes   │
        │   Master 1     │           │   Master 2     │
        └────────────────┘           └────────────────┘
                │                             │
    ┌───────────┴─────────────────────────────┴───────────┐
    │                                                      │
┌───┴──────┐  ┌───────────┐  ┌───────────┐  ┌───────────┐
│  Node 1  │  │  Node 2   │  │  Node 3   │  │  Node N   │
│          │  │           │  │           │  │           │
│ • Pods   │  │ • Pods    │  │ • Pods    │  │ • Pods    │
│ • Storage│  │ • Storage │  │ • Storage │  │ • Storage │
└──────────┘  └───────────┘  └───────────┘  └───────────┘
```

## 部署步骤

### 1. 克隆代码

```bash
git clone https://github.com/whimlab/whimlab-ai.git
cd whimlab-ai
git checkout v1.0.0  # 使用稳定版本标签
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

必须配置的环境变量：

```env
# 应用配置
ASPNETCORE_ENVIRONMENT=Production
APP_NAME=WhimLab AI
APP_URL=https://whimlab.com
ADMIN_URL=https://admin.whimlab.com

# 数据库配置
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=whimlab_prod
POSTGRES_USER=whimlab
POSTGRES_PASSWORD=<强密码>

# Redis 配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=<强密码>

# RabbitMQ 配置
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=whimlab
RABBITMQ_PASSWORD=<强密码>

# MinIO 配置
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=whimlab
MINIO_SECRET_KEY=<强密码>

# JWT 配置
JWT_SECRET=<至少32位的随机字符串>
JWT_ISSUER=https://whimlab.com
JWT_AUDIENCE=https://whimlab.com

# AI 服务配置
OPENAI_API_KEY=<OpenAI API Key>
CLAUDE_API_KEY=<Claude API Key>
DIFY_API_URL=https://api.dify.ai

# 支付配置
ALIPAY_APP_ID=<支付宝应用ID>
ALIPAY_PRIVATE_KEY=<支付宝私钥>
WECHAT_PAY_MCHID=<微信支付商户号>
WECHAT_PAY_KEY=<微信支付密钥>

# 邮件配置
SMTP_HOST=smtp.whimlab.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASSWORD=<邮箱密码>

# 监控配置
GRAFANA_ADMIN_PASSWORD=<Grafana管理员密码>
PROMETHEUS_RETENTION=30d
```

### 3. 准备 SSL 证书

```bash
# 创建证书目录
mkdir -p nginx/ssl

# 方式1: 使用 Let's Encrypt (推荐)
docker run -it --rm \
  -v $(pwd)/nginx/ssl:/etc/letsencrypt \
  -v $(pwd)/nginx/www:/var/www/certbot \
  certbot/certbot certonly \
  --webroot \
  --webroot-path=/var/www/certbot \
  --email <EMAIL> \
  --agree-tos \
  --no-eff-email \
  -d whimlab.com \
  -d www.whimlab.com \
  -d admin.whimlab.com

# 方式2: 使用商业证书
cp /path/to/your/cert.crt nginx/ssl/whimlab.com.crt
cp /path/to/your/cert.key nginx/ssl/whimlab.com.key
```

### 4. 构建和启动服务

```bash
# 构建镜像
docker-compose build

# 启动基础服务
docker-compose up -d postgres redis rabbitmq minio

# 等待服务就绪
sleep 30

# 运行数据库迁移
docker-compose run --rm whimlab-api dotnet ef database update

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 5. 初始化数据

```bash
# 创建管理员账号
docker-compose exec whimlab-api dotnet WhimLabAI.WebApi.dll seed-admin \
  --email <EMAIL> \
  --password <管理员密码>

# 导入初始数据
docker-compose exec postgres psql -U whimlab -d whimlab_prod < scripts/init-data.sql
```

## 配置管理

### 生产环境配置最佳实践

1. **密钥管理**
   - 使用密钥管理服务（如 AWS Secrets Manager、HashiCorp Vault）
   - 定期轮换密钥
   - 不要将密钥提交到代码仓库

2. **配置分离**
   - 将配置与代码分离
   - 使用环境变量或配置服务
   - 为不同环境使用不同的配置文件

3. **配置验证**
   ```bash
   # 验证配置脚本
   ./scripts/validate-config.sh
   ```

## SSL证书

### 自动续期配置

```bash
# 创建续期脚本
cat > /etc/cron.daily/renew-certificates << 'EOF'
#!/bin/bash
docker run --rm \
  -v $(pwd)/nginx/ssl:/etc/letsencrypt \
  -v $(pwd)/nginx/www:/var/www/certbot \
  certbot/certbot renew \
  --webroot \
  --webroot-path=/var/www/certbot

docker-compose restart nginx
EOF

chmod +x /etc/cron.daily/renew-certificates
```

## 监控配置

### 1. 访问监控面板

- Grafana: https://your-domain:3000
  - 默认账号: admin
  - 默认密码: 查看环境变量 GRAFANA_ADMIN_PASSWORD

- Prometheus: https://your-domain:9090

### 2. 导入仪表板

```bash
# 导入预设仪表板
./scripts/import-dashboards.sh
```

### 3. 配置告警

```bash
# 编辑告警配置
vim monitoring/alertmanager/config.yml

# 重启告警服务
docker-compose restart alertmanager
```

## 备份策略

### 自动备份配置

```bash
# 配置每日备份
crontab -e

# 添加以下行
0 2 * * * /opt/whimlab/scripts/backup.sh all s3 >> /var/log/whimlab-backup.log 2>&1
```

### 备份验证

```bash
# 每周验证备份
0 10 * * 1 /opt/whimlab/scripts/verify-backup.sh >> /var/log/whimlab-backup-verify.log 2>&1
```

## 故障恢复

### 1. 服务故障

```bash
# 检查服务状态
docker-compose ps

# 重启故障服务
docker-compose restart <service-name>

# 查看服务日志
docker-compose logs -f <service-name> --tail=100
```

### 2. 数据恢复

```bash
# 从最新备份恢复
./scripts/restore.sh s3://whimlab-backups/latest/backup.tar.gz all
```

### 3. 紧急回滚

```bash
# 回滚到上一个版本
docker-compose down
git checkout <previous-version-tag>
docker-compose up -d
```

## 性能优化

### 1. 数据库优化

```sql
-- 定期执行
VACUUM ANALYZE;

-- 检查慢查询
SELECT query, calls, total_time, mean_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
```

### 2. Redis 优化

```bash
# 配置持久化策略
redis-cli CONFIG SET save "900 1 300 10 60 10000"

# 设置最大内存
redis-cli CONFIG SET maxmemory 4gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

### 3. 应用优化

```bash
# 调整应用并发数
vim docker-compose.yml

# 修改环境变量
ASPNETCORE_Kestrel__Limits__MaxConcurrentConnections=10000
ASPNETCORE_Kestrel__Limits__MaxConcurrentUpgradedConnections=10000
```

## 安全加固

### 1. 网络安全

```bash
# 配置 fail2ban
sudo cp configs/fail2ban/whimlab.conf /etc/fail2ban/filter.d/
sudo systemctl restart fail2ban
```

### 2. 应用安全

- 启用 HTTPS 强制重定向
- 配置 CORS 策略
- 实施速率限制
- 启用 WAF（Web 应用防火墙）

### 3. 数据安全

- 加密敏感数据
- 定期安全审计
- 实施访问控制
- 配置审计日志

## 运维手册

### 日常运维任务

1. **健康检查** (每小时)
   ```bash
   curl https://whimlab.com/health
   ```

2. **日志检查** (每天)
   ```bash
   ./scripts/check-logs.sh
   ```

3. **性能监控** (实时)
   - 查看 Grafana 仪表板
   - 检查告警通知

4. **备份验证** (每周)
   ```bash
   ./scripts/verify-backup.sh
   ```

5. **安全更新** (每月)
   ```bash
   ./scripts/security-update.sh
   ```

### 故障处理流程

1. **识别问题**
   - 检查监控告警
   - 查看错误日志
   - 验证服务状态

2. **隔离问题**
   - 确定影响范围
   - 隔离故障组件

3. **解决问题**
   - 执行恢复程序
   - 验证修复结果

4. **事后分析**
   - 记录问题原因
   - 更新运维文档
   - 改进监控策略

### 联系方式

- 技术支持: <EMAIL>
- 紧急联系: +86-xxx-xxxx-xxxx
- 值班工程师: <EMAIL>

---

最后更新: 2024-01-01
