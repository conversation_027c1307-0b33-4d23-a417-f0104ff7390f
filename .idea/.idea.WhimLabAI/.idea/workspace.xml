<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile>FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/WhimLabAI.Client.Admin.Client.csproj</projectFile>
    <projectFile profileName="https">FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj</projectFile>
    <projectFile>FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web.Client/WhimLabAI.Client.Customer.Web.Client.csproj</projectFile>
    <projectFile profileName="http">FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web/WhimLabAI.Client.Customer.Web.csproj</projectFile>
    <projectFile profileName="https">FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web/WhimLabAI.Client.Customer.Web.csproj</projectFile>
    <projectFile kind="XamarinAndroid">FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.csproj</projectFile>
    <projectFile kind="XamarinIOS">FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.csproj</projectFile>
    <projectFile kind="XamarinMacOS">FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.csproj</projectFile>
    <projectFile profileName="https">FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4debc459-43c5-4cfc-bdca-5d90a4a69ee1" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Abstractions/Application/IPaymentService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Abstractions/Application/IPaymentService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Abstractions/Infrastructure/IPaymentGateway.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Abstractions/Infrastructure/IPaymentGateway.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Application/Services/Payment/PaymentService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Application/Services/Payment/PaymentService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Entities/Conversation/Conversation.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Entities/Conversation/Conversation.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Entities/Payment/Order.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Entities/Payment/Order.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/IConversationRepository.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/IConversationRepository.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/INotificationRepository.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/INotificationRepository.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/IOrderRepository.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/IOrderRepository.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/IPaymentRepository.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/IPaymentRepository.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/ISubscriptionRepository.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/ISubscriptionRepository.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/IUnitOfWork.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Domain/Repositories/IUnitOfWork.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Infrastructure/PaymentGateways/AlipayGateway.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Infrastructure/PaymentGateways/AlipayGateway.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackEnd/WhimLabAI.Infrastructure/PaymentGateways/WeChatPayGateway.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackEnd/WhimLabAI.Infrastructure/PaymentGateways/WeChatPayGateway.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/Controllers/Payment/PaymentController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/Controllers/Payment/PaymentController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/Controllers/Payment/PaymentExtensionController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/Controllers/Payment/PaymentExtensionController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/Program.cs" beforeDir="false" afterPath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/Program.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/appsettings.json" beforeDir="false" afterPath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/appsettings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/logs/whimlabai-20250710.txt" beforeDir="false" afterPath="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/logs/whimlabai-20250710.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="InvalidFacetManager">
    <ignored-facets>
      <facet id="WhimLabAI.Client.Customer/invalid/Android Facet - WhimLabAI.Client.Customer" />
    </ignored-facets>
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="Toolset" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2zTeEP1D7ZVR7OfEfw25IS7pfFD" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET 启动设置配置文件.WhimLabAI.Client.Admin.executor&quot;: &quot;Run&quot;,
    &quot;.NET 启动设置配置文件.WhimLabAI.Client.Customer.Web: https.executor&quot;: &quot;Run&quot;,
    &quot;.NET 启动设置配置文件.WhimLabAI.WebApi.executor&quot;: &quot;Run&quot;,
    &quot;Device#class com.jetbrains.rider.run.multiPlatform.ios.devices.IOSConnectedDeviceKind#com.jetbrains.rider.run.multiPlatform.ios.devices.IOSDevicesProvider#.NET 启动设置配置文件: WhimLabAI.Client.Customer.Web: https&quot;: &quot;LewvaniPhone&quot;,
    &quot;Device#class com.jetbrains.rider.run.multiPlatform.ios.devices.IOSConnectedDeviceKind#com.jetbrains.rider.run.multiPlatform.ios.devices.IOSDevicesProvider#iOS: WhimLabAI.Client.Customer&quot;: &quot;LewvaniPhone&quot;,
    &quot;Device#class com.jetbrains.rider.run.multiPlatform.ios.devices.IOSSimulatorKind#com.jetbrains.rider.run.multiPlatform.ios.devices.IOSDevicesProvider#iOS: WhimLabAI.Client.Customer&quot;: &quot;iPhone 16 Pro Max (iOS 18.5)&quot;,
    &quot;DeviceKind#com.jetbrains.rider.run.multiPlatform.ios.devices.IOSDevicesProvider&quot;: &quot;模拟器&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;iOS.WhimLabAI.Client.Customer.executor&quot;: &quot;Run&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_interpreter_path&quot;: &quot;node&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;yarn&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected=".NET 启动设置配置文件.WhimLabAI.Client.Customer.Web: https">
    <configuration name="WhimLabAI.Client.Admin.Client" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/WhimLabAI.Client.Admin.Client.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="WhimLabAI.Client.Customer.Web.Client" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="$USER_HOME$/.nuget/packages/microsoft.net.sdk.webassembly.pack/9.0.6/WasmAppHost/WasmAppHost.dll" />
      <option name="PROGRAM_PARAMETERS" value="--use-staticwebassets --runtime-config $PROJECT_DIR$/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web.Client/bin/Debug/net9.0/WhimLabAI.Client.Customer.Web.Client.runtimeconfig.json" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web.Client/bin/Debug/net9.0" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web.Client/WhimLabAI.Client.Customer.Web.Client.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="net9.0" />
      <browser name="98ca6316-2f89-46d9-a9e5-fa9e2b0625b3" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="WhimLabAI.Client.Admin" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="98ca6316-2f89-46d9-a9e5-fa9e2b0625b3" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="WhimLabAI.Client.Admin: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="WhimLabAI.Client.Customer.Web: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web/WhimLabAI.Client.Customer.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="98ca6316-2f89-46d9-a9e5-fa9e2b0625b3" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="WhimLabAI.Client.Customer.Web: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web/WhimLabAI.Client.Customer.Web.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="98ca6316-2f89-46d9-a9e5-fa9e2b0625b3" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="WhimLabAI.WebApi" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <browser name="98ca6316-2f89-46d9-a9e5-fa9e2b0625b3" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="WhimLabAI.WebApi: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="WhimLabAI.Client.Customer" type="XamarinAndroidProject" factoryName="Xamarin.Android">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="DEPLOY_BEHAVIOUR_NAME" value="Default" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="WhimLabAI.Client.Customer" type="XamarinIOSProject" factoryName="Xamarin.iOS">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="IS_PASS_PARENT_ENVS" value="false" />
      <option name="EXTRA_MLAUNCH_PARAMETERS" value="" />
      <option name="PLATFORM_TYPE" value="IOS" />
      <method v="2" />
    </configuration>
    <configuration name="WhimLabAI.Client.Customer" type="XamarinMacProject" factoryName="Xamarin.Mac">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer/bin/Debug/net9.0-maccatalyst/maccatalyst-arm64" />
      <option name="PASS_PARENT_ENVS_MAC" value="1" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <list>
      <item itemvalue=".NET 启动设置配置文件.WhimLabAI.Client.Admin: https" />
      <item itemvalue=".NET 启动设置配置文件.WhimLabAI.WebApi: https" />
      <item itemvalue=".NET 启动设置配置文件.WhimLabAI.Client.Admin" />
      <item itemvalue=".NET 启动设置配置文件.WhimLabAI.WebApi" />
      <item itemvalue=".NET 启动设置配置文件.WhimLabAI.Client.Customer.Web: http" />
      <item itemvalue=".NET 启动设置配置文件.WhimLabAI.Client.Customer.Web: https" />
      <item itemvalue=".NET 项目.WhimLabAI.Client.Admin.Client" />
      <item itemvalue=".NET 项目.WhimLabAI.Client.Customer.Web.Client" />
      <item itemvalue="Android.WhimLabAI.Client.Customer" />
      <item itemvalue="iOS.WhimLabAI.Client.Customer" />
      <item itemvalue="macOS.WhimLabAI.Client.Customer" />
    </list>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4debc459-43c5-4cfc-bdca-5d90a4a69ee1" name="更改" comment="" />
      <created>1734162909730</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1734162909730</updated>
      <workItem from="1734162910850" duration="11000" />
      <workItem from="1734421666929" duration="402000" />
      <workItem from="1737302224997" duration="83000" />
      <workItem from="1747361557497" duration="17000" />
      <workItem from="1751759379083" duration="7492000" />
      <workItem from="1752132160591" duration="38000" />
    </task>
    <task id="LOCAL-00001" summary="init">
      <option name="closed" value="true" />
      <created>1751761707370</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751761707370</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="init" />
    <option name="LAST_COMMIT_MESSAGE" value="init" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>