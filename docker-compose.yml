# WhimLabAI Docker Compose Configuration
# 用于在项目根目录直接运行: docker compose -p whimlabai up -d

services:
  # PostgreSQL 数据库 with pgvector
  postgres:
    image: pgvector/pgvector:pg16
    container_name: whimlab-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      POSTGRES_DB: ${POSTGRES_DB:-whimlabai}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: whimlab-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: whimlab-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-whimlab}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASS:-rabbitmq123}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: whimlab-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-admin123}
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"   # API
      - "9001:9001"   # Console
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "mc", "ready", "local"]
      interval: 10s
      timeout: 5s
      retries: 5

  # WhimLab AI 主应用
  whimlab-api:
    build:
      context: .
      dockerfile: deploy/docker/Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=1
    image: whimlab-api:latest
    container_name: whimlab-api
    restart: unless-stopped
    environment:
      # 数据库连接
      ConnectionStrings__DefaultConnection: "Host=postgres;Port=5432;Database=${POSTGRES_DB:-whimlabai};Username=${POSTGRES_USER:-postgres};Password=${POSTGRES_PASSWORD:-postgres123};MinPoolSize=5;MaxPoolSize=100"
      # Redis连接
      ConnectionStrings__Redis: "redis:6379,password=${REDIS_PASSWORD:-redis123}"
      # RabbitMQ连接
      ConnectionStrings__RabbitMQ: "amqp://${RABBITMQ_USER:-whimlab}:${RABBITMQ_PASS:-rabbitmq123}@rabbitmq:5672"
      # MinIO连接
      Storage__MinIO__Endpoint: "minio:9000"
      Storage__MinIO__AccessKey: ${MINIO_ROOT_USER:-admin}
      Storage__MinIO__SecretKey: ${MINIO_ROOT_PASSWORD:-admin123}
      Storage__MinIO__UseSSL: "false"
      # JWT设置
      Jwt__Secret: ${JWT_SECRET:-ThisIsAVeryLongSecretKeyForJWT123456789012345678901234567890}
      Jwt__Issuer: "WhimLabAI"
      Jwt__Audience: "WhimLabAIUsers"
      # 支付配置（开发环境使用测试密钥）
      Payment__Alipay__AppId: ${ALIPAY_APP_ID:-test-appid}
      Payment__Alipay__PrivateKey: ${ALIPAY_PRIVATE_KEY:-test-private-key}
      Payment__Alipay__PublicKey: ${ALIPAY_PUBLIC_KEY:-test-public-key}
      Payment__WechatPay__AppId: ${WECHAT_APP_ID:-test-appid}
      Payment__WechatPay__MchId: ${WECHAT_MCH_ID:-test-mchid}
      Payment__WechatPay__ApiKey: ${WECHAT_API_KEY:-test-apikey}
      # 邮件服务（开发环境可选）
      Email__Smtp__Host: ${SMTP_HOST:-smtp.example.com}
      Email__Smtp__Port: ${SMTP_PORT:-465}
      Email__Smtp__UseSsl: "true"
      Email__Smtp__Username: ${SMTP_USERNAME:-<EMAIL>}
      Email__Smtp__Password: ${SMTP_PASSWORD:-password}
      Email__From__Name: "WhimLab AI"
      Email__From__Address: ${SMTP_USERNAME:-<EMAIL>}
      # 应用设置
      ASPNETCORE_ENVIRONMENT: ${ASPNETCORE_ENVIRONMENT:-Development}
      ASPNETCORE_URLS: "http://+:15800;https://+:15801"
      # 性能优化
      DOTNET_TieredPGO: 1
      DOTNET_TC_QuickJitForLoops: 1
      DOTNET_ReadyToRun: 1
    volumes:
      - ./logs:/app/logs:rw
      - ./uploads:/app/uploads:rw
      - ./temp:/app/temp:rw
    ports:
      - "5010:15800"
      - "5011:15801"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:15800/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # WhimLab AI Admin Frontend
  whimlab-admin:
    build:
      context: .
      dockerfile: deploy/docker/Dockerfile.admin
      args:
        - BUILDKIT_INLINE_CACHE=1
    image: whimlab-admin:latest
    container_name: whimlab-admin
    restart: unless-stopped
    environment:
      # 应用设置
      ASPNETCORE_ENVIRONMENT: ${ASPNETCORE_ENVIRONMENT:-Development}
      ASPNETCORE_URLS: "http://+:7216;https://+:7217"
      # API配置
      Api__BaseUrl: "https://whimlab-api:15801"
      # JWT设置（与API保持一致）
      Jwt__Secret: ${JWT_SECRET:-ThisIsAVeryLongSecretKeyForJWT123456789012345678901234567890}
      Jwt__Issuer: "WhimLabAI"
      Jwt__Audience: "WhimLabAIUsers"
      # 性能优化
      DOTNET_TieredPGO: 1
      DOTNET_TC_QuickJitForLoops: 1
      DOTNET_ReadyToRun: 1
    volumes:
      - ./logs/admin:/app/logs:rw
    ports:
      - "7216:7216"
      - "7217:7217"
    depends_on:
      whimlab-api:
        condition: service_healthy
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7216/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

  # WhimLab AI Customer Frontend
  whimlab-customer:
    build:
      context: .
      dockerfile: deploy/docker/Dockerfile.customer
      args:
        - BUILDKIT_INLINE_CACHE=1
    image: whimlab-customer:latest
    container_name: whimlab-customer
    restart: unless-stopped
    environment:
      # 应用设置
      ASPNETCORE_ENVIRONMENT: ${ASPNETCORE_ENVIRONMENT:-Development}
      ASPNETCORE_URLS: "http://+:7040;https://+:7041"
      # API配置
      Api__BaseUrl: "https://whimlab-api:15801"
      # JWT设置（与API保持一致）
      Jwt__Secret: ${JWT_SECRET:-ThisIsAVeryLongSecretKeyForJWT123456789012345678901234567890}
      Jwt__Issuer: "WhimLabAI"
      Jwt__Audience: "WhimLabAIUsers"
      # 性能优化
      DOTNET_TieredPGO: 1
      DOTNET_TC_QuickJitForLoops: 1
      DOTNET_ReadyToRun: 1
    volumes:
      - ./logs/customer:/app/logs:rw
    ports:
      - "7040:7040"
      - "7041:7041"
    depends_on:
      whimlab-api:
        condition: service_healthy
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7040/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

  # Nginx 反向代理（生产环境可选）
  nginx:
    image: nginx:alpine
    container_name: whimlab-nginx
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - whimlab-api
      - whimlab-admin
      - whimlab-customer
    networks:
      - whimlab-network

  # Prometheus 监控（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: whimlab-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - whimlab-network
    profiles:
      - monitoring

  # Grafana 可视化（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: whimlab-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - whimlab-network
    profiles:
      - monitoring

  # Elasticsearch 日志存储（可选）
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: whimlab-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
    profiles:
      - logging

  # Kibana 日志可视化（可选）
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: whimlab-kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - whimlab-network
    profiles:
      - logging

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  minio_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  whimlab-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
