2025-07-22 00:00:10.471 +08:00 [INF] Starting payment timeout check job
2025-07-22 00:00:10.475 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT o."Id", o."CouponCode", o."CouponId", o."CreatedAt", o."CustomerUserId", o."DeletedAt", o."ExpireAt", o."IsDeleted", o."Metadata", o."OrderNo", o."PaidAt", o."PaymentMethod", o."ProductId", o."ProductName", o."RefundedAt", o."Remark", o."Status", o."Type", o."UpdatedAt", o."UpdatedBy", o."Amount", o."AmountCurrency", o."DiscountAmount", o."DiscountCurrency", o."FinalAmount", o."FinalCurrency", o."PayableAmount", o."PayableCurrency"
FROM "Orders" AS o
WHERE o."Status" = 0 AND o."ExpireAt" < now()
2025-07-22 00:00:10.476 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__expiryTime_0='2025-07-21T15:30:10.4755570Z' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."CompletedAt", p."CreatedAt", p."ExpireAt", p."FailReason", p."FailedAt", p."OrderId", p."PayerAccount", p."PayerName", p."PaymentMethod", p."PaymentNo", p."RawData", p."Status", p."TransactionId", p."UpdatedAt", p."UpdatedBy", p.amount, p.currency
FROM "PaymentTransactions" AS p
WHERE p."Status" = 0 AND p."CreatedAt" < @__expiryTime_0
2025-07-22 00:00:10.477 +08:00 [INF] Payment timeout check job completed. Cancelled orders: 0, Errors: 0
