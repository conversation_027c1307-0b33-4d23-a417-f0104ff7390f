using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Admin.Customer;
using WhimLabAI.Shared.Results;
using WhimLabAI.WebApi.Extensions;

namespace WhimLabAI.WebApi.Controllers.Admin;

/// <summary>
/// Admin端客户用户管理控制器
/// </summary>
[ApiController]
[Route("api/v1/admin/customers")]
[Authorize]
[Produces("application/json")]
public class AdminCustomerController : ControllerBase
{
    private readonly ICustomerUserService _customerUserService;
    private readonly ILogger<AdminCustomerController> _logger;

    public AdminCustomerController(
        ICustomerUserService customerUserService,
        ILogger<AdminCustomerController> logger)
    {
        _customerUserService = customerUserService;
        _logger = logger;
    }

    /// <summary>
    /// 搜索客户用户
    /// </summary>
    /// <param name="request">搜索条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户用户列表</returns>
    [HttpPost("search")]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<AdminCustomerListDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SearchCustomers(
        [FromBody] AdminCustomerSearchDto request,
        CancellationToken cancellationToken = default)
    {
        var result = await _customerUserService.SearchCustomersAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<PagedResult<AdminCustomerListDto>>.Ok(result.Value, "搜索成功"));
    }

    /// <summary>
    /// 获取客户用户详情
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户详情</returns>
    [HttpGet("{customerId:guid}")]
    [ProducesResponseType(typeof(ApiResponse<AdminCustomerDetailDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetCustomerDetail(
        Guid customerId,
        CancellationToken cancellationToken = default)
    {
        var result = await _customerUserService.GetCustomerDetailAsync(customerId, cancellationToken);

        if (!result.IsSuccess)
        {
            return NotFound(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<AdminCustomerDetailDto>.Ok(result.Value, "获取成功"));
    }

    /// <summary>
    /// 更新客户用户状态
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="request">状态更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPut("{customerId:guid}/status")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> UpdateCustomerStatus(
        Guid customerId,
        [FromBody] AdminUpdateCustomerStatusDto request,
        CancellationToken cancellationToken = default)
    {
        var adminId = User.GetUserId();
        var result = await _customerUserService.UpdateCustomerStatusAsync(customerId, request, adminId, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<object>.Ok(null, "状态更新成功"));
    }

    /// <summary>
    /// 重置客户用户密码
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="request">重置密码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>新密码</returns>
    [HttpPost("{customerId:guid}/reset-password")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ResetCustomerPassword(
        Guid customerId,
        [FromBody] AdminResetCustomerPasswordDto request,
        CancellationToken cancellationToken = default)
    {
        var adminId = User.GetUserId();
        var result = await _customerUserService.ResetCustomerPasswordAsync(customerId, request, adminId, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        // 出于安全考虑，只在响应中返回成功消息，实际密码通过其他方式（如邮件）发送
        return Ok(ApiResponse<object>.Ok(new { message = "密码重置成功" }, "密码重置成功"));
    }

    /// <summary>
    /// 解锁客户账号
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("{customerId:guid}/unlock")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> UnlockCustomerAccount(
        Guid customerId,
        CancellationToken cancellationToken = default)
    {
        var adminId = User.GetUserId();
        var result = await _customerUserService.UnlockCustomerAccountAsync(customerId, adminId, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<object>.Ok(null, "账号解锁成功"));
    }

    /// <summary>
    /// 重新发送邮箱验证邮件
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("{customerId:guid}/resend-email-verification")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ResendVerificationEmail(
        Guid customerId,
        CancellationToken cancellationToken = default)
    {
        var adminId = User.GetUserId();
        var result = await _customerUserService.ResendVerificationEmailAsync(customerId, adminId, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<object>.Ok(null, "验证邮件已发送"));
    }

    /// <summary>
    /// 重新发送手机验证短信
    /// </summary>
    /// <param name="customerId">客户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("{customerId:guid}/resend-sms-verification")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ResendVerificationSms(
        Guid customerId,
        CancellationToken cancellationToken = default)
    {
        var adminId = User.GetUserId();
        var result = await _customerUserService.ResendVerificationSmsAsync(customerId, adminId, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<object>.Ok(null, "验证短信已发送"));
    }

    /// <summary>
    /// 获取客户用户统计概览
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计概览</returns>
    [HttpGet("statistics")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetStatistics(CancellationToken cancellationToken = default)
    {
        // TODO: 实现统计功能
        var statistics = new
        {
            TotalUsers = 0,
            ActiveUsers = 0,
            VerifiedUsers = 0,
            PaidUsers = 0,
            TotalRevenue = 0m,
            NewUsersToday = 0,
            NewUsersThisMonth = 0
        };

        return Ok(ApiResponse<object>.Ok(statistics, "获取成功"));
    }
}