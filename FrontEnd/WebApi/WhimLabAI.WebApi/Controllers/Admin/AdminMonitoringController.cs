using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Infrastructure.BackgroundServices;
using WhimLabAI.Shared.Dtos.Performance;
using WhimLabAI.WebApi.Attributes;
using WhimLabAI.WebApi.Authorization;

namespace WhimLabAI.WebApi.Controllers.Admin;

/// <summary>
/// 管理员监控控制器
/// </summary>
[ApiController]
[Route("api/v1/admin/monitoring")]
[Authorize(Roles = "Admin,SuperAdmin")]
public class AdminMonitoringController : ControllerBase
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<AdminMonitoringController> _logger;

    public AdminMonitoringController(
        ICacheService cacheService,
        ILogger<AdminMonitoringController> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    /// <summary>
    /// 获取最新的性能指标
    /// </summary>
    [HttpGet("performance/latest")]
    [RequirePermission("monitoring.view")]
    [ProducesResponseType(typeof(PerformanceMetrics), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetLatestPerformanceMetrics()
    {
        var metrics = await _cacheService.GetAsync<PerformanceMetrics>("performance:metrics:latest");
        
        if (metrics == null)
        {
            return NotFound(new { error = "No performance metrics available" });
        }
        
        return Ok(metrics);
    }

    /// <summary>
    /// 获取历史性能指标
    /// </summary>
    [HttpGet("performance/history")]
    [RequirePermission("monitoring.view")]
    [ProducesResponseType(typeof(List<PerformanceMetrics>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetPerformanceHistory([FromQuery] int hours = 1)
    {
        var history = new List<PerformanceMetrics>();
        var now = DateTime.UtcNow;
        
        // 获取过去N小时的指标
        for (int i = 0; i < hours * 60; i++) // 每分钟一个数据点
        {
            var timestamp = now.AddMinutes(-i);
            var cacheKey = $"performance:metrics:{timestamp:yyyyMMddHHmm}00";
            var metrics = await _cacheService.GetAsync<PerformanceMetrics>(cacheKey);
            
            if (metrics != null)
            {
                history.Add(metrics);
            }
        }
        
        return Ok(history.OrderBy(m => m.Timestamp));
    }

    /// <summary>
    /// 获取性能警报
    /// </summary>
    [HttpGet("performance/alerts")]
    [RequirePermission("monitoring.view")]
    [ProducesResponseType(typeof(List<PerformanceAlert>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetPerformanceAlerts()
    {
        var alerts = await _cacheService.GetAsync<List<PerformanceAlert>>("performance:alerts:latest");
        return Ok(alerts ?? new List<PerformanceAlert>());
    }

    /// <summary>
    /// 获取系统健康状态
    /// </summary>
    [HttpGet("health/detailed")]
    [RequirePermission("monitoring.view")]
    [ProducesResponseType(typeof(SystemHealthStatus), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetDetailedHealthStatus()
    {
        var metrics = await _cacheService.GetAsync<PerformanceMetrics>("performance:metrics:latest");
        var alerts = await _cacheService.GetAsync<List<PerformanceAlert>>("performance:alerts:latest");
        
        var healthStatus = new SystemHealthStatus
        {
            Timestamp = DateTime.UtcNow,
            Status = DetermineHealthStatus(metrics, alerts),
            Metrics = metrics,
            ActiveAlerts = alerts ?? new List<PerformanceAlert>(),
            Services = await CheckServiceHealth()
        };
        
        return Ok(healthStatus);
    }

    /// <summary>
    /// 获取实时性能统计
    /// </summary>
    [HttpGet("performance/realtime")]
    [RequirePermission("monitoring.view")]
    [ProducesResponseType(typeof(RealtimePerformanceStats), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetRealtimeStats()
    {
        var stats = new RealtimePerformanceStats
        {
            Timestamp = DateTime.UtcNow
        };

        // 获取各种实时统计
        // 获取缓存数据，如果为null则使用默认值
        stats.ActiveRequests = (await _cacheService.GetAsync<int?>("stats:active_requests")) ?? 0;
        stats.RequestsPerMinute = (await _cacheService.GetAsync<double?>("stats:requests_per_minute")) ?? 0.0;
        stats.AverageResponseTime = (await _cacheService.GetAsync<double?>("stats:avg_response_time")) ?? 0.0;
        stats.ErrorRate = (await _cacheService.GetAsync<double?>("stats:error_rate")) ?? 0.0;
        stats.ActiveUsers = (await _cacheService.GetAsync<int?>("stats:active_users")) ?? 0;
        stats.CacheHitRate = (await _cacheService.GetAsync<double?>("stats:cache_hit_rate")) ?? 0.0;
        
        return Ok(stats);
    }

    /// <summary>
    /// 清除性能警报
    /// </summary>
    [HttpDelete("performance/alerts")]
    [RequirePermission("monitoring.manage")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<IActionResult> ClearPerformanceAlerts()
    {
        await _cacheService.RemoveAsync("performance:alerts:latest");
        
        _logger.LogInformation("Performance alerts cleared by {AdminId}", User.Identity?.Name);
        
        return NoContent();
    }

    /// <summary>
    /// 触发手动性能收集
    /// </summary>
    [HttpPost("performance/collect")]
    [RequirePermission("monitoring.manage")]
    [ProducesResponseType(typeof(PerformanceMetrics), StatusCodes.Status200OK)]
    public async Task<IActionResult> TriggerPerformanceCollection()
    {
        var collector = new PerformanceMetricsCollector();
        var metrics = collector.CollectMetrics();
        
        // 存储指标
        var cacheKey = $"performance:metrics:{DateTime.UtcNow:yyyyMMddHHmmss}";
        await _cacheService.SetAsync(cacheKey, metrics, TimeSpan.FromHours(1));
        await _cacheService.SetAsync("performance:metrics:latest", metrics, TimeSpan.FromDays(1));
        
        _logger.LogInformation("Manual performance collection triggered by {AdminId}", User.Identity?.Name);
        
        return Ok(metrics);
    }

    #region Private Methods

    private string DetermineHealthStatus(PerformanceMetrics? metrics, List<PerformanceAlert>? alerts)
    {
        if (metrics == null)
            return "Unknown";

        if (alerts != null && alerts.Any(a => a.Severity == "Critical"))
            return "Critical";

        if (alerts != null && alerts.Any(a => a.Severity == "Warning"))
            return "Warning";

        if (metrics.CpuUsagePercent > 90 || metrics.MemoryUsagePercent > 90)
            return "Degraded";

        return "Healthy";
    }

    private async Task<List<ServiceHealthCheck>> CheckServiceHealth()
    {
        var services = new List<ServiceHealthCheck>();

        // 检查数据库连接
        services.Add(new ServiceHealthCheck
        {
            ServiceName = "Database",
            Status = await CheckDatabaseHealth() ? "Healthy" : "Unhealthy",
            LastCheckTime = DateTime.UtcNow
        });

        // 检查Redis连接
        services.Add(new ServiceHealthCheck
        {
            ServiceName = "Redis Cache",
            Status = await CheckRedisHealth() ? "Healthy" : "Unhealthy",
            LastCheckTime = DateTime.UtcNow
        });

        // 检查后台服务
        services.Add(new ServiceHealthCheck
        {
            ServiceName = "Background Services",
            Status = "Healthy", // 简化实现
            LastCheckTime = DateTime.UtcNow
        });

        return services;
    }

    private async Task<bool> CheckDatabaseHealth()
    {
        try
        {
            // 尝试从缓存获取数据库健康状态
            var dbHealth = await _cacheService.GetAsync<bool?>("health:database");
            return dbHealth ?? true;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> CheckRedisHealth()
    {
        try
        {
            // 尝试设置和获取测试值
            var testKey = "health:redis:test";
            await _cacheService.SetAsync(testKey, true, TimeSpan.FromSeconds(10));
            return true;
        }
        catch
        {
            return false;
        }
    }

    #endregion
}

#region DTOs

/// <summary>
/// 系统健康状态
/// </summary>
public class SystemHealthStatus
{
    public DateTime Timestamp { get; set; }
    public string Status { get; set; } = string.Empty; // Healthy, Warning, Degraded, Critical, Unknown
    public PerformanceMetrics? Metrics { get; set; }
    public List<PerformanceAlert> ActiveAlerts { get; set; } = new();
    public List<ServiceHealthCheck> Services { get; set; } = new();
}

/// <summary>
/// 服务健康检查
/// </summary>
public class ServiceHealthCheck
{
    public string ServiceName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime LastCheckTime { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 实时性能统计
/// </summary>
public class RealtimePerformanceStats
{
    public DateTime Timestamp { get; set; }
    public int ActiveRequests { get; set; }
    public double RequestsPerMinute { get; set; }
    public double AverageResponseTime { get; set; }
    public double ErrorRate { get; set; }
    public int ActiveUsers { get; set; }
    public double CacheHitRate { get; set; }
}

#endregion