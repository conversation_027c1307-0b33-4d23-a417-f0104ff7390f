using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Entities.Compliance;
using WhimLabAI.Shared.Dtos.Compliance;
using WhimLabAI.WebApi.Attributes;
using WhimLabAI.WebApi.Authorization;

namespace WhimLabAI.WebApi.Controllers.Admin;

/// <summary>
/// 管理员合规性管理控制器
/// </summary>
[ApiController]
[Route("api/v1/admin/compliance")]
[Authorize(Roles = "Admin,SuperAdmin")]
public class AdminComplianceController : ControllerBase
{
    private readonly IComplianceService _complianceService;
    private readonly ILogger<AdminComplianceController> _logger;

    public AdminComplianceController(
        IComplianceService complianceService,
        ILogger<AdminComplianceController> logger)
    {
        _complianceService = complianceService;
        _logger = logger;
    }

    /// <summary>
    /// 获取数据保留策略
    /// </summary>
    [HttpGet("retention-policies")]
    [RequirePermission("compliance.view")]
    [ProducesResponseType(typeof(List<DataRetentionPolicy>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetDataRetentionPolicies()
    {
        var result = await _complianceService.GetDataRetentionPoliciesAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 应用数据保留策略
    /// </summary>
    [HttpPost("retention-policies/apply")]
    [RequirePermission("compliance.manage")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ApplyDataRetentionPolicies()
    {
        var result = await _complianceService.ApplyDataRetentionPoliciesAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Data retention policies applied by admin {AdminId}", User.Identity?.Name);
        
        return NoContent();
    }

    /// <summary>
    /// 生成合规性报告
    /// </summary>
    [HttpPost("reports/compliance")]
    [RequirePermission("compliance.reports")]
    [ProducesResponseType(typeof(ComplianceReport), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GenerateComplianceReport([FromBody] GenerateReportDto request)
    {
        var result = await _complianceService.GenerateComplianceReportAsync(
            request.StartDate, 
            request.EndDate);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Compliance report generated for period {StartDate} to {EndDate}", 
            request.StartDate, request.EndDate);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 运行安全合规检查
    /// </summary>
    [HttpPost("checks/security")]
    [RequirePermission("compliance.security")]
    [ProducesResponseType(typeof(SecurityComplianceCheck), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RunSecurityComplianceCheck()
    {
        var result = await _complianceService.RunSecurityComplianceCheckAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Security compliance check completed. Compliant: {IsCompliant}", 
            result.Value!.IsCompliant);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 运行隐私合规检查
    /// </summary>
    [HttpPost("checks/privacy")]
    [RequirePermission("compliance.privacy")]
    [ProducesResponseType(typeof(PrivacyComplianceCheck), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RunPrivacyComplianceCheck()
    {
        var result = await _complianceService.RunPrivacyComplianceCheckAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Privacy compliance check completed. Compliant: {IsCompliant}", 
            result.Value!.IsCompliant);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 生成特定用户的数据报告
    /// </summary>
    [HttpGet("reports/user/{userId:guid}")]
    [RequirePermission("compliance.user_data")]
    [ProducesResponseType(typeof(UserDataReport), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GenerateUserDataReport(Guid userId)
    {
        var result = await _complianceService.GenerateUserDataReportAsync(userId);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("User data report generated for user {UserId} by admin {AdminId}", 
            userId, User.Identity?.Name);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 强制删除用户数据
    /// </summary>
    [HttpDelete("user-data/{userId:guid}")]
    [RequirePermission("compliance.delete_user_data")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ForceDeleteUserData(Guid userId, [FromBody] ForceDeleteDto request)
    {
        // 需要超级管理员权限
        if (!User.IsInRole("SuperAdmin"))
        {
            return Forbid();
        }
        
        // 需要二次确认
        if (request.ConfirmationCode != $"DELETE-USER-{userId}")
        {
            return BadRequest(new { error = "Invalid confirmation code" });
        }
        
        var result = await _complianceService.DeleteUserDataAsync(userId, request.Reason);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogWarning("User data forcefully deleted for user {UserId} by admin {AdminId}, reason: {Reason}", 
            userId, User.Identity?.Name, request.Reason);
        
        return NoContent();
    }

    /// <summary>
    /// 强制匿名化用户数据
    /// </summary>
    [HttpPost("user-data/{userId:guid}/anonymize")]
    [RequirePermission("compliance.anonymize_user_data")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ForceAnonymizeUserData(Guid userId)
    {
        var result = await _complianceService.AnonymizeUserDataAsync(userId);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogWarning("User data anonymized for user {UserId} by admin {AdminId}", 
            userId, User.Identity?.Name);
        
        return NoContent();
    }
}

#region DTOs

/// <summary>
/// 生成报告DTO
/// </summary>
public class GenerateReportDto
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
}

/// <summary>
/// 强制删除DTO
/// </summary>
public class ForceDeleteDto
{
    public string Reason { get; set; } = string.Empty;
    public string ConfirmationCode { get; set; } = string.Empty;
}

#endregion