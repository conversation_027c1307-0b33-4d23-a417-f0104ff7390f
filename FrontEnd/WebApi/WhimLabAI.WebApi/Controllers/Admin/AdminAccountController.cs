using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Admin;
using WhimLabAI.Shared.Dtos.Auth;

namespace WhimLabAI.WebApi.Controllers.Admin;

[ApiController]
[Route("api/v1/admin/account")]
[Authorize]
public class AdminAccountController : ControllerBase
{
    private readonly IAdminUserRepository _adminRepository;
    private readonly IAdminAuthService _adminAuthService;
    private readonly IAdminUserService _adminUserService;
    private readonly IAdminSessionRepository _sessionRepository;
    private readonly IUnitOfWork _unitOfWork;
    
    public AdminAccountController(
        IAdminUserRepository adminRepository,
        IAdminAuthService adminAuthService,
        IAdminUserService adminUserService,
        IAdminSessionRepository sessionRepository,
        IUnitOfWork unitOfWork)
    {
        _adminRepository = adminRepository;
        _adminAuthService = adminAuthService;
        _adminUserService = adminUserService;
        _sessionRepository = sessionRepository;
        _unitOfWork = unitOfWork;
    }
    
    /// <summary>
    /// 获取个人资料
    /// </summary>
    [HttpGet("profile")]
    public async Task<IActionResult> GetProfile(CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var admin = await _adminRepository.GetWithRolesAsync(adminId, cancellationToken);
        
        if (admin == null)
        {
            return NotFound(new ApiResponse
            {
                Success = false,
                Message = "用户不存在"
            });
        }
        
        var profile = new AdminProfileDto
        {
            Id = admin.Id,
            Username = admin.Username,
            Nickname = admin.Nickname,
            Email = admin.Email?.Value ?? "",
            Phone = admin.Phone?.Value ?? "",
            TwoFactorEnabled = admin.TwoFactorEnabled,
            Roles = admin.UserRoles.Where(ur => ur.Role != null).Select(ur => ur.Role.Name).ToList(),
            LastLoginAt = admin.LastLoginAt,
            LastLoginIp = admin.LastLoginIp,
            LoginFailedCount = admin.FailedLoginAttempts
        };
        
        return Ok(new ApiResponse<AdminProfileDto>
        {
            Success = true,
            Data = profile
        });
    }
    
    /// <summary>
    /// 更新个人资料
    /// </summary>
    [HttpPut("profile")]
    public async Task<IActionResult> UpdateProfile([FromBody] UpdateProfileRequest request, CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var admin = await _adminRepository.GetByIdAsync(adminId, cancellationToken);
        
        if (admin == null)
        {
            return NotFound(new ApiResponse
            {
                Success = false,
                Message = "用户不存在"
            });
        }
        
        // Update profile using the entity method
        admin.UpdateProfile(
            nickname: request.Nickname,
            email: request.Email,
            phone: request.Phone,
            avatar: null // Avatar not included in UpdateProfileRequest
        );
        
        _adminRepository.Update(admin);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Ok(new ApiResponse
        {
            Success = true,
            Message = "更新成功"
        });
    }
    
    /// <summary>
    /// 获取活动会话
    /// </summary>
    [HttpGet("sessions")]
    public async Task<IActionResult> GetSessions(CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var sessions = await _sessionRepository.GetActiveSessionsAsync(adminId, cancellationToken);
        
        var sessionDtos = sessions.Select(s => new SessionDto
        {
            Id = s.Id,
            DeviceName = s.UserAgent ?? "Unknown Device",
            IpAddress = s.IpAddress,
            LastActivityAt = s.LastActivityAt,
            IsCurrent = s.RefreshToken == HttpContext.Request.Headers["Authorization"].ToString()?.Replace("Bearer ", "")
        }).ToList();
        
        return Ok(new ApiResponse<List<SessionDto>>
        {
            Success = true,
            Data = sessionDtos
        });
    }
    
    /// <summary>
    /// 终止会话
    /// </summary>
    [HttpDelete("sessions/{sessionId}")]
    public async Task<IActionResult> TerminateSession(Guid sessionId, CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var session = await _sessionRepository.GetByIdAsync(sessionId, cancellationToken);
        
        if (session == null || session.AdminUserId != adminId)
        {
            return NotFound(new ApiResponse
            {
                Success = false,
                Message = "会话不存在"
            });
        }
        
        session.InvalidateSession();
        _sessionRepository.Update(session);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Ok(new ApiResponse
        {
            Success = true,
            Message = "会话已终止"
        });
    }
    
    /// <summary>
    /// 启用双因素认证
    /// </summary>
    [HttpPost("two-factor/enable")]
    public async Task<IActionResult> EnableTwoFactor(CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var result = await _adminAuthService.EnableTwoFactorAsync(adminId, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse<object>
            {
                Success = true,
                Data = result.Value
            });
        }
        
        return BadRequest(new ApiResponse
        {
            Success = false,
            Message = result.Error
        });
    }
    
    /// <summary>
    /// 确认双因素认证
    /// </summary>
    [HttpPost("two-factor/confirm")]
    public async Task<IActionResult> ConfirmTwoFactor([FromBody] ConfirmTwoFactorRequest request, CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var result = await _adminAuthService.ConfirmTwoFactorAsync(adminId, request.Code, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse
            {
                Success = true,
                Message = "双因素认证已启用"
            });
        }
        
        return BadRequest(new ApiResponse
        {
            Success = false,
            Message = result.Error
        });
    }
    
    /// <summary>
    /// 禁用双因素认证
    /// </summary>
    [HttpPost("two-factor/disable")]
    public async Task<IActionResult> DisableTwoFactor([FromBody] DisableTwoFactorRequest request, CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var result = await _adminAuthService.DisableTwoFactorAsync(adminId, request.Password, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse
            {
                Success = true,
                Message = "双因素认证已禁用"
            });
        }
        
        return BadRequest(new ApiResponse
        {
            Success = false,
            Message = result.Error
        });
    }
    
    /// <summary>
    /// 获取IP白名单设置
    /// </summary>
    [HttpGet("ip-whitelist")]
    public async Task<IActionResult> GetIpWhitelist(CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var currentIp = HttpContext.Connection.RemoteIpAddress?.ToString();
        
        var result = await _adminUserService.GetIpWhitelistAsync(adminId, currentIp, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse<IpWhitelistResponseDto>
            {
                Success = true,
                Data = result.Value
            });
        }
        
        return BadRequest(new ApiResponse
        {
            Success = false,
            Message = result.Error
        });
    }
    
    /// <summary>
    /// 更新IP白名单
    /// </summary>
    [HttpPut("ip-whitelist")]
    public async Task<IActionResult> UpdateIpWhitelist([FromBody] UpdateIpWhitelistDto request, CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var result = await _adminUserService.UpdateIpWhitelistAsync(adminId, request, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse
            {
                Success = true,
                Message = "IP白名单已更新"
            });
        }
        
        return BadRequest(new ApiResponse
        {
            Success = false,
            Message = result.Error
        });
    }
    
    /// <summary>
    /// 添加IP到白名单
    /// </summary>
    [HttpPost("ip-whitelist/add")]
    public async Task<IActionResult> AddIpToWhitelist([FromBody] AddIpToWhitelistDto request, CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var result = await _adminUserService.AddIpToWhitelistAsync(adminId, request.IpAddress, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse
            {
                Success = true,
                Message = $"IP地址 {request.IpAddress} 已添加到白名单"
            });
        }
        
        return BadRequest(new ApiResponse
        {
            Success = false,
            Message = result.Error
        });
    }
    
    /// <summary>
    /// 从白名单移除IP
    /// </summary>
    [HttpPost("ip-whitelist/remove")]
    public async Task<IActionResult> RemoveIpFromWhitelist([FromBody] RemoveIpFromWhitelistDto request, CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var result = await _adminUserService.RemoveIpFromWhitelistAsync(adminId, request.IpAddress, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse
            {
                Success = true,
                Message = $"IP地址 {request.IpAddress} 已从白名单移除"
            });
        }
        
        return BadRequest(new ApiResponse
        {
            Success = false,
            Message = result.Error
        });
    }
    
    /// <summary>
    /// 切换IP白名单状态
    /// </summary>
    [HttpPost("ip-whitelist/toggle")]
    public async Task<IActionResult> ToggleIpWhitelist([FromBody] ToggleIpWhitelistRequest request, CancellationToken cancellationToken = default)
    {
        var adminId = Guid.Parse(User.Identity!.Name!);
        var result = await _adminUserService.ToggleIpWhitelistAsync(adminId, request.Enable, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(new ApiResponse
            {
                Success = true,
                Message = request.Enable ? "IP白名单已启用" : "IP白名单已禁用"
            });
        }
        
        return BadRequest(new ApiResponse
        {
            Success = false,
            Message = result.Error
        });
    }
}

public class UpdateProfileRequest
{
    public string Nickname { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
}

public class ConfirmTwoFactorRequest
{
    public string Code { get; set; } = string.Empty;
}

public class DisableTwoFactorRequest
{
    public string Password { get; set; } = string.Empty;
}

public class AdminProfileDto
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Nickname { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public bool TwoFactorEnabled { get; set; }
    public List<string> Roles { get; set; } = new();
    public DateTime? LastLoginAt { get; set; }
    public string? LastLoginIp { get; set; }
    public int LoginFailedCount { get; set; }
}

public class SessionDto
{
    public Guid Id { get; set; }
    public string DeviceName { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public DateTime LastActivityAt { get; set; }
    public bool IsCurrent { get; set; }
}

public class ToggleIpWhitelistRequest
{
    public bool Enable { get; set; }
}