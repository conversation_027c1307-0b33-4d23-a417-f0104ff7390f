using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Constants;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Admin.User;
using WhimLabAI.Shared.Results;
using WhimLabAI.WebApi.Attributes;
using WhimLabAI.WebApi.Extensions;

namespace WhimLabAI.WebApi.Controllers.Admin;

[ApiController]
[Route("api/v1/admin/users/admins")]
[Authorize]
[Produces("application/json")]
public class AdminUserController : ControllerBase
{
    private readonly IAdminUserService _adminUserService;
    private readonly ILogger<AdminUserController> _logger;

    public AdminUserController(
        IAdminUserService adminUserService,
        ILogger<AdminUserController> logger)
    {
        _adminUserService = adminUserService;
        _logger = logger;
    }

    /// <summary>
    /// 获取管理员列表
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="role">角色筛选</param>
    /// <param name="isActive">状态筛选</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>管理员列表</returns>
    [HttpGet]
    [RequirePermission(PermissionConstants.User.Admin.View)]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<AdminListDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAdminUsers(
        [FromQuery] string? keyword = null,
        [FromQuery] string? role = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = new AdminQueryDto
        {
            Keyword = keyword,
            Role = role,
            IsActive = isActive,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        var result = await _adminUserService.GetAdminListAsync(query, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error ?? "操作失败"));
        }

        return Ok(ApiResponse<PagedResult<AdminListDto>>.Ok(result.Value));
    }

    /// <summary>
    /// 获取管理员详情
    /// </summary>
    /// <param name="id">管理员ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>管理员详情</returns>
    [HttpGet("{id}")]
    [RequirePermission(PermissionConstants.User.Admin.View)]
    [ProducesResponseType(typeof(ApiResponse<AdminDetailDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetAdminUser(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        var result = await _adminUserService.GetAdminDetailAsync(id, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return NotFound(ApiResponse<ErrorResponse>.Fail(result.Error ?? "未找到"));
        }

        return Ok(ApiResponse<AdminDetailDto>.Ok(result.Value));
    }

    /// <summary>
    /// 创建管理员
    /// </summary>
    /// <param name="request">创建请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    [RequirePermission(PermissionConstants.User.Admin.Create)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateAdminUser(
        [FromBody] CreateAdminDto request,
        CancellationToken cancellationToken = default)
    {
        var result = await _adminUserService.CreateAdminAsync(request, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error ?? "操作失败"));
        }
        
        return StatusCode(StatusCodes.Status201Created, 
            ApiResponse<object>.Ok(new { Id = result.Value }, "创建成功"));
    }

    /// <summary>
    /// 更新管理员信息
    /// </summary>
    /// <param name="id">管理员ID</param>
    /// <param name="request">更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    [RequirePermission(PermissionConstants.User.Admin.Edit)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateAdminUser(
        Guid id,
        [FromBody] UpdateAdminDto request,
        CancellationToken cancellationToken = default)
    {
        var result = await _adminUserService.UpdateAdminAsync(id, request, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return (result.Error ?? "").Contains("不存在") 
                ? NotFound(ApiResponse<ErrorResponse>.Fail(result.Error ?? "未找到"))
                : BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error ?? "操作失败"));
        }

        return Ok(ApiResponse<object>.Ok(null, "更新成功"));
    }

    /// <summary>
    /// 分配角色
    /// </summary>
    /// <param name="id">管理员ID</param>
    /// <param name="request">角色分配请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分配结果</returns>
    [HttpPost("{id}/roles")]
    [RequirePermission(PermissionConstants.User.Admin.AssignRole)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> AssignRoles(
        Guid id,
        [FromBody] AssignRolesDto request,
        CancellationToken cancellationToken = default)
    {
        var currentUserId = User.Identity?.Name != null ? Guid.Parse(User.Identity.Name) : (Guid?)null;
        var result = await _adminUserService.AssignRolesAsync(id, request.RoleIds, currentUserId, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return (result.Error ?? "").Contains("不存在") 
                ? NotFound(ApiResponse<ErrorResponse>.Fail(result.Error ?? "未找到"))
                : BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error ?? "操作失败"));
        }

        return Ok(ApiResponse<object>.Ok(null, "角色分配成功"));
    }

    /// <summary>
    /// 禁用/启用管理员
    /// </summary>
    /// <param name="id">管理员ID</param>
    /// <param name="request">状态更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPatch("{id}/status")]
    [RequirePermission(PermissionConstants.User.Admin.Edit)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateStatus(
        Guid id,
        [FromBody] UpdateStatusDto request,
        CancellationToken cancellationToken = default)
    {
        var currentUserId = User.GetUserId();
        if (id == currentUserId)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail("不能禁用自己"));
        }

        var updateDto = new UpdateAdminDto { IsActive = request.IsActive };
        var result = await _adminUserService.UpdateAdminAsync(id, updateDto, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return (result.Error ?? "").Contains("不存在") 
                ? NotFound(ApiResponse<ErrorResponse>.Fail(result.Error ?? "未找到"))
                : BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error ?? "操作失败"));
        }

        return Ok(ApiResponse<object>.Ok(null, request.IsActive ? "启用成功" : "禁用成功"));
    }

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="id">管理员ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>新密码</returns>
    [HttpPost("{id}/reset-password")]
    [RequirePermission(PermissionConstants.User.Admin.Edit)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ResetPassword(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        // 生成随机密码
        var newPassword = GenerateRandomPassword();
        
        // TODO: 这里需要调用AdminAuthService的密码重置功能
        // 目前暂时返回生成的密码，实际应该通过邮件或短信发送
        _logger.LogInformation("重置管理员密码: {AdminId}", id);

        return Ok(ApiResponse<object>.Ok(new { Password = newPassword }, "密码重置成功，请通知管理员新密码"));
    }
    
    private string GenerateRandomPassword()
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        var random = new Random();
        var password = new string(Enumerable.Repeat(chars, 12)
            .Select(s => s[random.Next(s.Length)]).ToArray());
        return password;
    }

    /// <summary>
    /// 删除管理员
    /// </summary>
    /// <param name="id">管理员ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    [RequirePermission(PermissionConstants.User.Admin.Delete)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeleteAdminUser(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        var currentUserId = User.GetUserId();
        if (id == currentUserId)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail("不能删除自己"));
        }

        var result = await _adminUserService.DeleteAdminAsync(id, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return (result.Error ?? "").Contains("不存在") 
                ? NotFound(ApiResponse<ErrorResponse>.Fail(result.Error ?? "未找到"))
                : BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error ?? "操作失败"));
        }

        return Ok(ApiResponse<object>.Ok(null, "删除成功"));
    }
}

public class AssignRolesDto
{
    public List<Guid> RoleIds { get; set; } = new();
}

public class UpdateStatusDto
{
    public bool IsActive { get; set; }
}