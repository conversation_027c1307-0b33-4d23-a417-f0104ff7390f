using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Constants;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Admin.Rbac;
using WhimLabAI.Shared.Results;
using WhimLabAI.WebApi.Attributes;

namespace WhimLabAI.WebApi.Controllers.Admin;

/// <summary>
/// 角色管理控制器
/// </summary>
[ApiController]
[Route("api/v1/admin/roles")]
[Authorize]
[ApiExplorerSettings(GroupName = "Admin")]
[Produces("application/json")]
public class RoleController : ControllerBase
{
    private readonly IRoleService _roleService;
    private readonly ILogger<RoleController> _logger;

    public RoleController(
        IRoleService roleService,
        ILogger<RoleController> logger)
    {
        _roleService = roleService;
        _logger = logger;
    }

    /// <summary>
    /// 获取角色列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色列表</returns>
    [HttpGet]
    [Authorize(Policy = "role:view")]
    [ProducesResponseType(typeof(ApiResponse<List<RoleDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetRoles(CancellationToken cancellationToken = default)
    {
        var result = await _roleService.GetRolesAsync(cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<List<RoleDto>>.Ok(result.Value));
    }

    /// <summary>
    /// 获取角色详情（包含权限）
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色详情</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = "role:view")]
    [ProducesResponseType(typeof(ApiResponse<RoleDetailDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetRole(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _roleService.GetRoleDetailAsync(id, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return NotFound(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<RoleDetailDto>.Ok(result.Value));
    }

    /// <summary>
    /// 创建角色
    /// </summary>
    /// <param name="dto">创建角色DTO</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的角色</returns>
    [HttpPost]
    [Authorize(Policy = "role:create")]
    [ProducesResponseType(typeof(ApiResponse<RoleDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateRole(
        [FromBody] CreateRoleDto dto,
        CancellationToken cancellationToken = default)
    {
        var result = await _roleService.CreateRoleAsync(dto, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        _logger.LogInformation("创建角色成功: {RoleCode} ({RoleName})", result.Value.Code, result.Value.Name);
        
        return CreatedAtAction(nameof(GetRole), new { id = result.Value.Id }, 
            ApiResponse<RoleDto>.Ok(result.Value, "角色创建成功"));
    }

    /// <summary>
    /// 更新角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="dto">更新角色DTO</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id}")]
    [Authorize(Policy = "role:update")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateRole(
        Guid id,
        [FromBody] UpdateRoleDto dto,
        CancellationToken cancellationToken = default)
    {
        var result = await _roleService.UpdateRoleAsync(id, dto, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        _logger.LogInformation("更新角色成功: {RoleId}", id);
        
        return Ok(ApiResponse<object>.Ok(null, "角色更新成功"));
    }

    /// <summary>
    /// 删除角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    [Authorize(Policy = "role:delete")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeleteRole(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _roleService.DeleteRoleAsync(id, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        _logger.LogInformation("删除角色成功: {RoleId}", id);
        
        return Ok(ApiResponse<object>.Ok(null, "角色删除成功"));
    }

    /// <summary>
    /// 启用角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id}/enable")]
    [Authorize(Policy = "role:update")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> EnableRole(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _roleService.EnableRoleAsync(id, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<object>.Ok(null, "角色已启用"));
    }

    /// <summary>
    /// 禁用角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id}/disable")]
    [Authorize(Policy = "role:update")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DisableRole(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _roleService.DisableRoleAsync(id, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<object>.Ok(null, "角色已禁用"));
    }

    /// <summary>
    /// 获取启用的角色列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色列表</returns>
    [HttpGet("enabled")]
    [Authorize(Policy = "role:view")]
    [ProducesResponseType(typeof(ApiResponse<List<RoleDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetEnabledRoles(CancellationToken cancellationToken = default)
    {
        var result = await _roleService.GetEnabledRolesAsync(cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<List<RoleDto>>.Ok(result.Value));
    }

    /// <summary>
    /// 更新角色权限
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="dto">更新权限DTO</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id}/permissions")]
    [Authorize(Policy = "role:assign")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateRolePermissions(Guid id, [FromBody] UpdateRolePermissionsDto dto, CancellationToken cancellationToken = default)
    {
        var result = await _roleService.UpdateRolePermissionsAsync(id, dto, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        _logger.LogInformation("更新角色权限成功: RoleId={RoleId}, PermissionCount={Count}", 
            id, dto.PermissionIds.Count);
        
        return Ok(ApiResponse<object>.Ok(null, "权限更新成功"));
    }
}