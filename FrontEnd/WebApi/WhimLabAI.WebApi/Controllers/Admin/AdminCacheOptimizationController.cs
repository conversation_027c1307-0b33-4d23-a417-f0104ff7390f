using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.WebApi.Attributes;
using WhimLabAI.WebApi.Authorization;

namespace WhimLabAI.WebApi.Controllers.Admin;

/// <summary>
/// 管理员缓存优化控制器
/// </summary>
[ApiController]
[Route("api/v1/admin/cache-optimization")]
[Authorize(Roles = "Admin,SuperAdmin")]
public class AdminCacheOptimizationController : ControllerBase
{
    private readonly ICacheOptimizationService _cacheOptimizationService;
    private readonly ILogger<AdminCacheOptimizationController> _logger;

    public AdminCacheOptimizationController(
        ICacheOptimizationService cacheOptimizationService,
        ILogger<AdminCacheOptimizationController> logger)
    {
        _cacheOptimizationService = cacheOptimizationService;
        _logger = logger;
    }

    /// <summary>
    /// 分析缓存性能
    /// </summary>
    [HttpGet("analyze/performance")]
    [RequirePermission("cache.analyze")]
    [ProducesResponseType(typeof(CachePerformanceAnalysis), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AnalyzeCachePerformance()
    {
        _logger.LogInformation("Starting cache performance analysis by {AdminId}", User.Identity?.Name);
        
        var result = await _cacheOptimizationService.AnalyzeCachePerformanceAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Cache performance analysis completed. Health score: {Score}", 
            result.Value!.HealthScore.OverallScore);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    [HttpGet("statistics")]
    [RequirePermission("cache.view")]
    [ProducesResponseType(typeof(CacheStatistics), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetCacheStatistics()
    {
        var result = await _cacheOptimizationService.GetCacheStatisticsAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 分析缓存键
    /// </summary>
    [HttpGet("analyze/keys")]
    [RequirePermission("cache.analyze")]
    [ProducesResponseType(typeof(CacheKeyAnalysis), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AnalyzeCacheKeys(
        [FromQuery] string pattern = "*",
        [FromQuery] int sampleSize = 1000)
    {
        _logger.LogInformation("Analyzing cache keys with pattern {Pattern} by {AdminId}", 
            pattern, User.Identity?.Name);
        
        var result = await _cacheOptimizationService.AnalyzeCacheKeysAsync(pattern, sampleSize);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Analyzed {Count} cache keys", result.Value!.TotalKeysAnalyzed);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 分析缓存内存
    /// </summary>
    [HttpGet("analyze/memory")]
    [RequirePermission("cache.analyze")]
    [ProducesResponseType(typeof(CacheMemoryAnalysis), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AnalyzeCacheMemory()
    {
        _logger.LogInformation("Analyzing cache memory by {AdminId}", User.Identity?.Name);
        
        var result = await _cacheOptimizationService.AnalyzeCacheMemoryAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Cache memory analysis completed. Usage: {Usage}%", 
            result.Value!.CurrentUsage.UsagePercent);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 优化缓存
    /// </summary>
    [HttpPost("optimize")]
    [RequirePermission("cache.optimize")]
    [ProducesResponseType(typeof(CacheOptimizationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> OptimizeCache([FromBody] CacheOptimizationOptions options)
    {
        _logger.LogInformation("Starting cache optimization by {AdminId}", User.Identity?.Name);
        
        var result = await _cacheOptimizationService.OptimizeCacheAsync(options);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Cache optimization completed. Memory reclaimed: {Memory} bytes, Keys optimized: {Keys}",
            result.Value!.MemoryReclaimed, result.Value.KeysOptimized);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 预热缓存
    /// </summary>
    [HttpPost("warm")]
    [RequirePermission("cache.warm")]
    [ProducesResponseType(typeof(CacheWarmingResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> WarmCache([FromBody] CacheWarmingStrategy strategy)
    {
        _logger.LogInformation("Starting cache warming with strategy {Strategy} by {AdminId}", 
            strategy.StrategyName, User.Identity?.Name);
        
        var result = await _cacheOptimizationService.WarmCacheAsync(strategy);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Cache warming completed. Success: {Success}/{Total}", 
            result.Value!.SuccessfulTargets, result.Value.TotalTargets);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 清理过期缓存
    /// </summary>
    [HttpPost("cleanup")]
    [RequirePermission("cache.cleanup")]
    [ProducesResponseType(typeof(CacheCleanupResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CleanupExpiredCache()
    {
        _logger.LogInformation("Starting cache cleanup by {AdminId}", User.Identity?.Name);
        
        var result = await _cacheOptimizationService.CleanupExpiredCacheAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Cache cleanup completed. Keys removed: {Keys}, Memory reclaimed: {Memory} bytes",
            result.Value!.ExpiredKeysRemoved + result.Value.EmptyKeysRemoved + result.Value.DuplicateKeysRemoved,
            result.Value.MemoryReclaimed);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 验证缓存一致性
    /// </summary>
    [HttpPost("validate-consistency")]
    [RequirePermission("cache.validate")]
    [ProducesResponseType(typeof(CacheConsistencyReport), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ValidateCacheConsistency()
    {
        _logger.LogInformation("Starting cache consistency validation by {AdminId}", User.Identity?.Name);
        
        var result = await _cacheOptimizationService.ValidateCacheConsistencyAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Cache consistency validation completed. Score: {Score}%", 
            result.Value!.ConsistencyScore);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 监控缓存性能
    /// </summary>
    [HttpPost("monitor")]
    [RequirePermission("cache.monitor")]
    [ProducesResponseType(typeof(CacheMonitoringResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> MonitorCachePerformance([FromBody] CacheMonitorRequest request)
    {
        _logger.LogInformation("Starting cache performance monitoring for {Duration} minutes by {AdminId}", 
            request.DurationMinutes, User.Identity?.Name);
        
        var result = await _cacheOptimizationService.MonitorCachePerformanceAsync(
            TimeSpan.FromMinutes(request.DurationMinutes));
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Cache monitoring completed. Captured {Count} snapshots", 
            result.Value!.Snapshots.Count);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 生成缓存优化报告
    /// </summary>
    [HttpPost("report/generate")]
    [RequirePermission("cache.reports")]
    [ProducesResponseType(typeof(CacheOptimizationReport), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GenerateOptimizationReport()
    {
        _logger.LogInformation("Generating cache optimization report by {AdminId}", User.Identity?.Name);
        
        var result = await _cacheOptimizationService.GenerateOptimizationReportAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Cache optimization report generated: {ReportId} with score {Score}", 
            result.Value!.ReportId, result.Value.Score.OverallScore);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 获取预定义的缓存预热策略
    /// </summary>
    [HttpGet("warming-strategies")]
    [RequirePermission("cache.view")]
    [ProducesResponseType(typeof(List<PredefinedWarmingStrategy>), StatusCodes.Status200OK)]
    public IActionResult GetPredefinedWarmingStrategies()
    {
        var strategies = new List<PredefinedWarmingStrategy>
        {
            new PredefinedWarmingStrategy
            {
                Id = "common-queries",
                Name = "常用查询预热",
                Description = "预热最常用的查询结果",
                EstimatedDuration = TimeSpan.FromMinutes(5),
                Targets = new List<string> { "User profiles", "Agent listings", "Configuration data" }
            },
            new PredefinedWarmingStrategy
            {
                Id = "user-sessions",
                Name = "用户会话预热",
                Description = "预热活跃用户的会话数据",
                EstimatedDuration = TimeSpan.FromMinutes(3),
                Targets = new List<string> { "Active sessions", "User preferences", "Recent activities" }
            },
            new PredefinedWarmingStrategy
            {
                Id = "hot-data",
                Name = "热点数据预热",
                Description = "基于访问频率预热热点数据",
                EstimatedDuration = TimeSpan.FromMinutes(10),
                Targets = new List<string> { "Popular agents", "Trending conversations", "Frequently accessed resources" }
            },
            new PredefinedWarmingStrategy
            {
                Id = "full-warmup",
                Name = "完整预热",
                Description = "预热所有关键数据",
                EstimatedDuration = TimeSpan.FromMinutes(30),
                Targets = new List<string> { "All user data", "All agent data", "All configuration", "All active sessions" }
            }
        };
        
        return Ok(strategies);
    }

    /// <summary>
    /// 获取快速优化建议
    /// </summary>
    [HttpGet("quick-recommendations")]
    [RequirePermission("cache.view")]
    [ProducesResponseType(typeof(QuickOptimizationRecommendations), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetQuickOptimizationRecommendations()
    {
        // 执行快速分析
        var perfResult = await _cacheOptimizationService.AnalyzeCachePerformanceAsync();
        var statsResult = await _cacheOptimizationService.GetCacheStatisticsAsync();
        
        var recommendations = new QuickOptimizationRecommendations
        {
            GeneratedAt = DateTime.UtcNow,
            HealthScore = perfResult.IsSuccess ? perfResult.Value!.HealthScore.OverallScore : 0,
            CurrentHitRate = statsResult.IsSuccess ? statsResult.Value!.HitRate : 0,
            MemoryUsagePercent = statsResult.IsSuccess ? statsResult.Value!.MemoryUsagePercent : 0,
            Actions = new List<QuickAction>()
        };

        // 生成快速操作建议
        if (recommendations.CurrentHitRate < 0.7)
        {
            recommendations.Actions.Add(new QuickAction
            {
                Action = "Improve Hit Rate",
                Description = $"Cache hit rate is {recommendations.CurrentHitRate:P}. Consider reviewing TTL settings.",
                Priority = "High",
                EstimatedImprovement = "20% performance gain"
            });
        }

        if (recommendations.MemoryUsagePercent > 80)
        {
            recommendations.Actions.Add(new QuickAction
            {
                Action = "Clean Up Memory",
                Description = $"Memory usage at {recommendations.MemoryUsagePercent:F1}%. Run cleanup to free space.",
                Priority = "High",
                EstimatedImprovement = "Free up to 30% memory"
            });
        }

        if (perfResult.IsSuccess && perfResult.Value!.ColdKeys.Count > 20)
        {
            recommendations.Actions.Add(new QuickAction
            {
                Action = "Remove Cold Keys",
                Description = $"Found {perfResult.Value.ColdKeys.Count} cold keys. Remove to optimize memory.",
                Priority = "Medium",
                EstimatedImprovement = "5-10% memory savings"
            });
        }

        return Ok(recommendations);
    }

    /// <summary>
    /// 执行快速优化
    /// </summary>
    [HttpPost("quick-optimize")]
    [RequirePermission("cache.optimize")]
    [ProducesResponseType(typeof(CacheQuickOptimizationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> PerformQuickOptimization()
    {
        _logger.LogInformation("Starting quick cache optimization by {AdminId}", User.Identity?.Name);

        var result = new CacheQuickOptimizationResult
        {
            StartTime = DateTime.UtcNow,
            Actions = new List<string>()
        };

        // 1. 清理过期键
        var cleanupResult = await _cacheOptimizationService.CleanupExpiredCacheAsync();
        if (cleanupResult.IsSuccess)
        {
            result.Actions.Add($"Cleaned up {cleanupResult.Value!.ExpiredKeysRemoved} expired keys");
            result.KeysRemoved += cleanupResult.Value.ExpiredKeysRemoved;
            result.MemoryReclaimed += cleanupResult.Value.MemoryReclaimed;
        }

        // 2. 运行基本优化
        var optimizationOptions = new CacheOptimizationOptions
        {
            OptimizeMemory = true,
            OptimizeTTL = true,
            CompressLargeValues = false, // Skip for quick optimization
            RemoveDuplicates = true,
            CleanupExpired = false, // Already done above
            DefragmentMemory = false, // Skip for quick optimization
            MaxKeysToProcess = 1000 // Limit for quick operation
        };

        var optimizeResult = await _cacheOptimizationService.OptimizeCacheAsync(optimizationOptions);
        if (optimizeResult.IsSuccess)
        {
            result.Actions.Add($"Optimized {optimizeResult.Value!.KeysOptimized} keys");
            result.KeysOptimized = optimizeResult.Value.KeysOptimized;
            result.MemoryReclaimed += optimizeResult.Value.MemoryReclaimed;
            result.PerformanceImprovement = optimizeResult.Value.PerformanceImprovement;
        }

        result.EndTime = DateTime.UtcNow;
        result.Success = true;
        result.Message = "Quick optimization completed successfully";

        _logger.LogInformation("Quick cache optimization completed in {Duration}ms", 
            result.Duration.TotalMilliseconds);

        return Ok(result);
    }
}

#region DTOs

/// <summary>
/// 缓存监控请求
/// </summary>
public class CacheMonitorRequest
{
    public double DurationMinutes { get; set; } = 5;
}

/// <summary>
/// 预定义预热策略
/// </summary>
public class PredefinedWarmingStrategy
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TimeSpan EstimatedDuration { get; set; }
    public List<string> Targets { get; set; } = new();
}

/// <summary>
/// 快速优化建议
/// </summary>
public class QuickOptimizationRecommendations
{
    public DateTime GeneratedAt { get; set; }
    public int HealthScore { get; set; }
    public double CurrentHitRate { get; set; }
    public double MemoryUsagePercent { get; set; }
    public List<QuickAction> Actions { get; set; } = new();
}

/// <summary>
/// 快速操作
/// </summary>
public class QuickAction
{
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string EstimatedImprovement { get; set; } = string.Empty;
}

/// <summary>
/// 快速优化结果
/// </summary>
public class CacheQuickOptimizationResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration => EndTime - StartTime;
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<string> Actions { get; set; } = new();
    public long MemoryReclaimed { get; set; }
    public int KeysOptimized { get; set; }
    public int KeysRemoved { get; set; }
    public double PerformanceImprovement { get; set; }
}

#endregion