using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.WebApi.Attributes;
using WhimLabAI.WebApi.Authorization;

namespace WhimLabAI.WebApi.Controllers.Admin;

/// <summary>
/// 管理员负载测试控制器
/// </summary>
[ApiController]
[Route("api/v1/admin/load-testing")]
[Authorize(Roles = "Admin,SuperAdmin")]
public class AdminLoadTestingController : ControllerBase
{
    private readonly ILoadTestingService _loadTestingService;
    private readonly ILogger<AdminLoadTestingController> _logger;

    public AdminLoadTestingController(
        ILoadTestingService loadTestingService,
        ILogger<AdminLoadTestingController> logger)
    {
        _loadTestingService = loadTestingService;
        _logger = logger;
    }

    /// <summary>
    /// 运行负载测试场景
    /// </summary>
    [HttpPost("scenario/run")]
    [RequirePermission("loadtest.run")]
    [ProducesResponseType(typeof(LoadTestResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RunLoadTestScenario([FromBody] LoadTestScenario scenario)
    {
        _logger.LogInformation("Starting load test scenario: {ScenarioName} by {AdminId}", 
            scenario.ScenarioName, User.Identity?.Name);
        
        var result = await _loadTestingService.RunLoadTestScenarioAsync(scenario);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 运行压力测试
    /// </summary>
    [HttpPost("stress/run")]
    [RequirePermission("loadtest.run")]
    [ProducesResponseType(typeof(StressTestResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RunStressTest([FromBody] StressTestRequest request)
    {
        var configuration = new StressTestConfiguration
        {
            StartingUsers = request.StartingUsers,
            UserIncrement = request.UserIncrement,
            IncrementInterval = TimeSpan.FromMinutes(request.IncrementIntervalMinutes),
            TargetErrorRate = request.TargetErrorRate,
            TargetResponseTime = request.TargetResponseTimeMs,
            MaxDuration = TimeSpan.FromHours(request.MaxDurationHours),
            BaseScenario = CreateBaseScenario(request.ScenarioName)
        };
        
        _logger.LogInformation("Starting stress test: {TestId} by {AdminId}", 
            configuration.TestId, User.Identity?.Name);
        
        var result = await _loadTestingService.RunStressTestAsync(configuration);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 运行尖峰测试
    /// </summary>
    [HttpPost("spike/run")]
    [RequirePermission("loadtest.run")]
    [ProducesResponseType(typeof(SpikeTestResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RunSpikeTest([FromBody] SpikeTestRequest request)
    {
        var configuration = new SpikeTestConfiguration
        {
            BaselineUsers = request.BaselineUsers,
            SpikeUsers = request.SpikeUsers,
            SpikeRampTime = TimeSpan.FromSeconds(request.SpikeRampTimeSeconds),
            SpikeDuration = TimeSpan.FromMinutes(request.SpikeDurationMinutes),
            RecoveryTime = TimeSpan.FromMinutes(request.RecoveryTimeMinutes),
            BaseScenario = CreateBaseScenario(request.ScenarioName)
        };
        
        _logger.LogInformation("Starting spike test: {TestId} by {AdminId}", 
            configuration.TestId, User.Identity?.Name);
        
        var result = await _loadTestingService.RunSpikeTestAsync(configuration);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 运行浸泡测试
    /// </summary>
    [HttpPost("soak/run")]
    [RequirePermission("loadtest.run")]
    [ProducesResponseType(typeof(SoakTestResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RunSoakTest([FromBody] SoakTestRequest request)
    {
        var configuration = new SoakTestConfiguration
        {
            TargetUsers = request.TargetUsers,
            TestDuration = TimeSpan.FromHours(request.TestDurationHours),
            RampUpTime = TimeSpan.FromMinutes(request.RampUpTimeMinutes),
            AcceptableMemoryGrowth = request.AcceptableMemoryGrowthPercent / 100.0,
            AcceptableErrorRate = request.AcceptableErrorRatePercent / 100.0,
            BaseScenario = CreateBaseScenario(request.ScenarioName)
        };
        
        _logger.LogInformation("Starting soak test: {TestId} by {AdminId}", 
            configuration.TestId, User.Identity?.Name);
        
        var result = await _loadTestingService.RunSoakTestAsync(configuration);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 运行容量测试
    /// </summary>
    [HttpPost("capacity/run")]
    [RequirePermission("loadtest.run")]
    [ProducesResponseType(typeof(CapacityTestResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RunCapacityTest([FromBody] CapacityTestRequest request)
    {
        var configuration = new CapacityTestConfiguration
        {
            MinUsers = request.MinUsers,
            MaxUsers = request.MaxUsers,
            UserStep = request.UserStep,
            StepDuration = TimeSpan.FromMinutes(request.StepDurationMinutes),
            TargetResponseTime = request.TargetResponseTimeMs,
            TargetThroughput = request.TargetThroughputRps,
            BaseScenario = CreateBaseScenario(request.ScenarioName)
        };
        
        _logger.LogInformation("Starting capacity test: {TestId} by {AdminId}", 
            configuration.TestId, User.Identity?.Name);
        
        var result = await _loadTestingService.RunCapacityTestAsync(configuration);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 获取测试进度
    /// </summary>
    [HttpGet("progress/{testId}")]
    [RequirePermission("loadtest.view")]
    [ProducesResponseType(typeof(LoadTestProgress), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetTestProgress(string testId)
    {
        var result = await _loadTestingService.GetTestProgressAsync(testId);
        
        if (!result.IsSuccess)
        {
            return NotFound(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 停止运行中的测试
    /// </summary>
    [HttpPost("stop/{testId}")]
    [RequirePermission("loadtest.manage")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> StopTest(string testId)
    {
        var result = await _loadTestingService.StopTestAsync(testId);
        
        if (!result.IsSuccess)
        {
            return NotFound(new { error = result.Error });
        }
        
        _logger.LogInformation("Load test {TestId} stopped by {AdminId}", testId, User.Identity?.Name);
        
        return NoContent();
    }

    /// <summary>
    /// 分析测试结果
    /// </summary>
    [HttpPost("analyze")]
    [RequirePermission("loadtest.view")]
    [ProducesResponseType(typeof(LoadTestAnalysis), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public IActionResult AnalyzeTestResults([FromBody] LoadTestResult result)
    {
        var analysisResult = _loadTestingService.AnalyzeTestResults(result);
        
        if (!analysisResult.IsSuccess)
        {
            return BadRequest(new { error = analysisResult.Error });
        }
        
        return Ok(analysisResult.Value);
    }

    /// <summary>
    /// 生成负载测试报告
    /// </summary>
    [HttpPost("report/generate")]
    [RequirePermission("loadtest.reports")]
    [ProducesResponseType(typeof(LoadTestReport), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GenerateReport([FromBody] LoadTestResult result)
    {
        var reportResult = await _loadTestingService.GenerateLoadTestReportAsync(result);
        
        if (!reportResult.IsSuccess)
        {
            return BadRequest(new { error = reportResult.Error });
        }
        
        _logger.LogInformation("Load test report generated: {ReportId} by {AdminId}", 
            reportResult.Value!.ReportId, User.Identity?.Name);
        
        return Ok(reportResult.Value);
    }

    /// <summary>
    /// 获取预定义的测试场景
    /// </summary>
    [HttpGet("scenarios/predefined")]
    [RequirePermission("loadtest.view")]
    [ProducesResponseType(typeof(List<PredefinedScenario>), StatusCodes.Status200OK)]
    public IActionResult GetPredefinedScenarios()
    {
        var scenarios = new List<PredefinedScenario>
        {
            new PredefinedScenario
            {
                Id = "login-flow",
                Name = "用户登录流程",
                Description = "模拟用户登录、浏览和对话的完整流程",
                Category = "User Journey"
            },
            new PredefinedScenario
            {
                Id = "api-stress",
                Name = "API压力测试",
                Description = "对主要API端点进行压力测试",
                Category = "API Testing"
            },
            new PredefinedScenario
            {
                Id = "concurrent-chat",
                Name = "并发对话测试",
                Description = "模拟多用户同时进行AI对话",
                Category = "AI Testing"
            },
            new PredefinedScenario
            {
                Id = "mixed-workload",
                Name = "混合负载测试",
                Description = "模拟真实的混合用户行为",
                Category = "Realistic"
            }
        };
        
        return Ok(scenarios);
    }

    #region Private Methods

    private LoadTestScenario CreateBaseScenario(string scenarioName)
    {
        // 基于场景名称创建预定义的测试场景
        return scenarioName switch
        {
            "login-flow" => CreateLoginFlowScenario(),
            "api-stress" => CreateApiStressScenario(),
            "concurrent-chat" => CreateConcurrentChatScenario(),
            "mixed-workload" => CreateMixedWorkloadScenario(),
            _ => CreateDefaultScenario()
        };
    }

    private LoadTestScenario CreateLoginFlowScenario()
    {
        return new LoadTestScenario
        {
            ScenarioName = "User Login Flow",
            Description = "Simulates user login, browse agents, and chat",
            LoadPattern = new LoadPattern
            {
                Type = LoadPatternType.Ramp,
                InitialUsers = 10,
                TargetUsers = 100,
                RampUpTime = TimeSpan.FromMinutes(5)
            },
            UserBehaviors = new List<UserBehavior>
            {
                new UserBehavior
                {
                    BehaviorName = "Login and Browse",
                    Weight = 1.0,
                    ThinkTime = TimeSpan.FromSeconds(2),
                    Actions = new List<UserAction>
                    {
                        new UserAction
                        {
                            ActionName = "Login",
                            Type = ActionType.HttpRequest,
                            Target = "/api/auth/login",
                            HttpMethod = HttpMethod.Post,
                            RequestBody = new { username = "testuser", password = "password" },
                            Timeout = TimeSpan.FromSeconds(10),
                            Validations = new List<ResponseValidation>
                            {
                                new ResponseValidation
                                {
                                    ValidationName = "Status OK",
                                    Type = ValidationType.StatusCode,
                                    ExpectedValue = "200"
                                }
                            }
                        },
                        new UserAction
                        {
                            ActionName = "Get Agents",
                            Type = ActionType.HttpRequest,
                            Target = "/api/agents",
                            HttpMethod = HttpMethod.Get,
                            Timeout = TimeSpan.FromSeconds(10)
                        },
                        new UserAction
                        {
                            ActionName = "Start Conversation",
                            Type = ActionType.HttpRequest,
                            Target = "/api/conversations",
                            HttpMethod = HttpMethod.Post,
                            RequestBody = new { agentId = "test-agent-id" },
                            Timeout = TimeSpan.FromSeconds(10)
                        }
                    }
                }
            },
            Duration = new TestDuration
            {
                Type = DurationType.FixedTime,
                FixedDuration = TimeSpan.FromMinutes(10)
            }
        };
    }

    private LoadTestScenario CreateApiStressScenario()
    {
        return new LoadTestScenario
        {
            ScenarioName = "API Stress Test",
            Description = "Stresses main API endpoints",
            LoadPattern = new LoadPattern
            {
                Type = LoadPatternType.Constant,
                TargetUsers = 500
            },
            UserBehaviors = new List<UserBehavior>
            {
                new UserBehavior
                {
                    BehaviorName = "API Calls",
                    Weight = 1.0,
                    ThinkTime = TimeSpan.FromMilliseconds(100),
                    Actions = new List<UserAction>
                    {
                        new UserAction
                        {
                            ActionName = "Health Check",
                            Type = ActionType.HttpRequest,
                            Target = "/health",
                            HttpMethod = HttpMethod.Get,
                            Timeout = TimeSpan.FromSeconds(5)
                        }
                    }
                }
            },
            Duration = new TestDuration
            {
                Type = DurationType.FixedTime,
                FixedDuration = TimeSpan.FromMinutes(5)
            }
        };
    }

    private LoadTestScenario CreateConcurrentChatScenario()
    {
        return new LoadTestScenario
        {
            ScenarioName = "Concurrent Chat",
            Description = "Simulates concurrent AI conversations",
            LoadPattern = new LoadPattern
            {
                Type = LoadPatternType.Step,
                Steps = new List<LoadStep>
                {
                    new LoadStep { UserCount = 50, Duration = TimeSpan.FromMinutes(2) },
                    new LoadStep { UserCount = 100, Duration = TimeSpan.FromMinutes(2) },
                    new LoadStep { UserCount = 200, Duration = TimeSpan.FromMinutes(2) }
                }
            },
            UserBehaviors = new List<UserBehavior>
            {
                new UserBehavior
                {
                    BehaviorName = "Chat Session",
                    Weight = 1.0,
                    ThinkTime = TimeSpan.FromSeconds(5),
                    Actions = new List<UserAction>
                    {
                        new UserAction
                        {
                            ActionName = "Send Message",
                            Type = ActionType.HttpRequest,
                            Target = "/api/conversations/message",
                            HttpMethod = HttpMethod.Post,
                            RequestBody = new { conversationId = "test-conv-id", message = "Hello AI" },
                            Timeout = TimeSpan.FromSeconds(30)
                        }
                    }
                }
            },
            Duration = new TestDuration
            {
                Type = DurationType.FixedTime,
                FixedDuration = TimeSpan.FromMinutes(10)
            }
        };
    }

    private LoadTestScenario CreateMixedWorkloadScenario()
    {
        return new LoadTestScenario
        {
            ScenarioName = "Mixed Workload",
            Description = "Realistic mixed user behaviors",
            LoadPattern = new LoadPattern
            {
                Type = LoadPatternType.Wave,
                InitialUsers = 50,
                TargetUsers = 200,
                RampUpTime = TimeSpan.FromMinutes(3),
                RampDownTime = TimeSpan.FromMinutes(3)
            },
            UserBehaviors = new List<UserBehavior>
            {
                new UserBehavior
                {
                    BehaviorName = "Browser",
                    Weight = 0.6,
                    ThinkTime = TimeSpan.FromSeconds(10),
                    Actions = new List<UserAction>
                    {
                        new UserAction
                        {
                            ActionName = "Browse Agents",
                            Type = ActionType.HttpRequest,
                            Target = "/api/agents",
                            HttpMethod = HttpMethod.Get
                        }
                    }
                },
                new UserBehavior
                {
                    BehaviorName = "Active User",
                    Weight = 0.3,
                    ThinkTime = TimeSpan.FromSeconds(3),
                    Actions = new List<UserAction>
                    {
                        new UserAction
                        {
                            ActionName = "Chat",
                            Type = ActionType.HttpRequest,
                            Target = "/api/conversations/message",
                            HttpMethod = HttpMethod.Post
                        }
                    }
                },
                new UserBehavior
                {
                    BehaviorName = "API User",
                    Weight = 0.1,
                    ThinkTime = TimeSpan.FromMilliseconds(500),
                    Actions = new List<UserAction>
                    {
                        new UserAction
                        {
                            ActionName = "API Call",
                            Type = ActionType.HttpRequest,
                            Target = "/api/agents/search",
                            HttpMethod = HttpMethod.Get
                        }
                    }
                }
            },
            Duration = new TestDuration
            {
                Type = DurationType.FixedTime,
                FixedDuration = TimeSpan.FromMinutes(15)
            }
        };
    }

    private LoadTestScenario CreateDefaultScenario()
    {
        return new LoadTestScenario
        {
            ScenarioName = "Default Test",
            Description = "Basic load test scenario",
            LoadPattern = new LoadPattern
            {
                Type = LoadPatternType.Constant,
                TargetUsers = 100
            },
            UserBehaviors = new List<UserBehavior>
            {
                new UserBehavior
                {
                    BehaviorName = "Default",
                    Weight = 1.0,
                    ThinkTime = TimeSpan.FromSeconds(1),
                    Actions = new List<UserAction>
                    {
                        new UserAction
                        {
                            ActionName = "Health Check",
                            Type = ActionType.HttpRequest,
                            Target = "/health",
                            HttpMethod = HttpMethod.Get
                        }
                    }
                }
            },
            Duration = new TestDuration
            {
                Type = DurationType.FixedTime,
                FixedDuration = TimeSpan.FromMinutes(5)
            }
        };
    }

    #endregion
}

#region DTOs

/// <summary>
/// 压力测试请求
/// </summary>
public class StressTestRequest
{
    public string ScenarioName { get; set; } = "default";
    public int StartingUsers { get; set; } = 10;
    public int UserIncrement { get; set; } = 10;
    public double IncrementIntervalMinutes { get; set; } = 1;
    public double TargetErrorRate { get; set; } = 0.01;
    public double TargetResponseTimeMs { get; set; } = 1000;
    public double MaxDurationHours { get; set; } = 1;
}

/// <summary>
/// 尖峰测试请求
/// </summary>
public class SpikeTestRequest
{
    public string ScenarioName { get; set; } = "default";
    public int BaselineUsers { get; set; } = 100;
    public int SpikeUsers { get; set; } = 1000;
    public double SpikeRampTimeSeconds { get; set; } = 30;
    public double SpikeDurationMinutes { get; set; } = 5;
    public double RecoveryTimeMinutes { get; set; } = 5;
}

/// <summary>
/// 浸泡测试请求
/// </summary>
public class SoakTestRequest
{
    public string ScenarioName { get; set; } = "default";
    public int TargetUsers { get; set; } = 500;
    public double TestDurationHours { get; set; } = 8;
    public double RampUpTimeMinutes { get; set; } = 10;
    public double AcceptableMemoryGrowthPercent { get; set; } = 10;
    public double AcceptableErrorRatePercent { get; set; } = 0.1;
}

/// <summary>
/// 容量测试请求
/// </summary>
public class CapacityTestRequest
{
    public string ScenarioName { get; set; } = "default";
    public int MinUsers { get; set; } = 100;
    public int MaxUsers { get; set; } = 10000;
    public int UserStep { get; set; } = 100;
    public double StepDurationMinutes { get; set; } = 2;
    public double TargetResponseTimeMs { get; set; } = 500;
    public double TargetThroughputRps { get; set; } = 1000;
}

/// <summary>
/// 预定义场景
/// </summary>
public class PredefinedScenario
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
}

#endregion