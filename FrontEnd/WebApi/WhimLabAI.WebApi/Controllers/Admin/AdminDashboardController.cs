using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Constants;
using WhimLabAI.WebApi.Authorization;
using WhimLabAI.WebApi.Attributes;

namespace WhimLabAI.WebApi.Controllers.Admin;

[ApiController]
[Route("api/v1/admin/dashboard")]
[Authorize]
// [RequirePermission(PermissionConstants.Analytics.Dashboard)] // TODO: Enable after seeding analytics permissions
public class AdminDashboardController : ControllerBase
{
    private readonly ILogger<AdminDashboardController> _logger;

    public AdminDashboardController(ILogger<AdminDashboardController> logger)
    {
        _logger = logger;
    }

    [HttpGet("stats")]
    public async Task<IActionResult> GetDashboardStats()
    {
        // TODO: 实现真实的数据统计逻辑
        var stats = new
        {
            TotalUsers = 1234,
            UserGrowthRate = 15.5,
            ActiveAgents = 87,
            TotalAgents = 156,
            MonthlyRevenue = 98765.43m,
            RevenueGrowthRate = 23.7,
            TodayTokens = 125000,
            MonthlyTokens = 3500000
        };

        return Ok(ApiResponse<object>.Ok(stats));
    }

    [HttpGet("recent-orders")]
    public async Task<IActionResult> GetRecentOrders()
    {
        var orders = new[]
        {
            new
            {
                OrderNo = "ORD20250111001",
                UserName = "张三",
                Amount = 199.00m,
                Status = "已完成",
                CreatedAt = DateTime.Now.AddHours(-1)
            },
            new
            {
                OrderNo = "ORD20250111002",
                UserName = "李四",
                Amount = 99.00m,
                Status = "待支付",
                CreatedAt = DateTime.Now.AddHours(-2)
            }
        };

        return Ok(ApiResponse<object>.Ok(orders));
    }

    [HttpGet("popular-agents")]
    public async Task<IActionResult> GetPopularAgents()
    {
        var agents = new[]
        {
            new
            {
                Name = "代码助手",
                UsageCount = 1543,
                Rating = 5
            },
            new
            {
                Name = "翻译专家",
                UsageCount = 1232,
                Rating = 4
            }
        };

        return Ok(ApiResponse<object>.Ok(agents));
    }

    [HttpGet("subscription-distribution")]
    public async Task<IActionResult> GetSubscriptionDistribution()
    {
        var distribution = new Dictionary<string, int>
        {
            ["免费版"] = 523,
            ["基础版"] = 234,
            ["专业版"] = 156,
            ["企业版"] = 89
        };

        return Ok(ApiResponse<object>.Ok(distribution));
    }

    [HttpGet("revenue-chart")]
    public async Task<IActionResult> GetRevenueChart([FromQuery] string period = "month")
    {
        var labels = period switch
        {
            "week" => new[] { "周一", "周二", "周三", "周四", "周五", "周六", "周日" },
            "year" => new[] { "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" },
            _ => new[] { "1日", "5日", "10日", "15日", "20日", "25日", "30日" }
        };

        var values = period switch
        {
            "week" => new double[] { 1234.5, 2345.6, 3456.7, 4567.8, 5678.9, 6789.0, 7890.1 },
            "year" => new double[] { 12345, 23456, 34567, 45678, 56789, 67890, 78901, 89012, 90123, 101234, 112345, 123456 },
            _ => new double[] { 5000, 8000, 12000, 15000, 18000, 22000, 25000 }
        };

        var chartData = new
        {
            Labels = labels,
            Values = values
        };

        return Ok(ApiResponse<object>.Ok(chartData));
    }
}