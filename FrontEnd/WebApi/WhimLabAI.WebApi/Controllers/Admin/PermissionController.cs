using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Constants;
using WhimLabAI.Shared.Dtos.Admin.Rbac;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos;
using System.Security.Claims;
using WhimLabAI.WebApi.Extensions;

namespace WhimLabAI.WebApi.Controllers.Admin;

/// <summary>
/// 权限管理控制器
/// </summary>
[ApiController]
[Route("api/v1/admin/permissions")]
[Authorize]
[ApiExplorerSettings(GroupName = "Admin")]
[Produces("application/json")]
public class PermissionController : ControllerBase
{
    private readonly IPermissionService _permissionService;
    private readonly ILogger<PermissionController> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public PermissionController(IPermissionService permissionService, ILogger<PermissionController> logger)
    {
        _permissionService = permissionService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有权限列表（平铺）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    [HttpGet]
    [Authorize(Policy = "permission:view")]
    [ProducesResponseType(typeof(ApiResponse<List<PermissionDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<List<PermissionDto>>>> GetAllPermissions(CancellationToken cancellationToken = default)
    {
        var result = await _permissionService.GetPermissionsAsync(cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(ApiResponse<List<PermissionDto>>.Ok(result.Value));
        }
        
        return BadRequest(ApiResponse<List<PermissionDto>>.Fail(result.Error));
    }

    /// <summary>
    /// 按分类获取权限
    /// </summary>
    /// <param name="category">分类名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    [HttpGet("category/{category}")]
    [Authorize(Policy = "permission:view")]
    [ProducesResponseType(typeof(ApiResponse<List<PermissionDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<List<PermissionDto>>>> GetPermissionsByCategory(string category, CancellationToken cancellationToken = default)
    {
        var result = await _permissionService.GetPermissionsByCategoryAsync(category, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(ApiResponse<List<PermissionDto>>.Ok(result.Value));
        }
        
        return BadRequest(ApiResponse<List<PermissionDto>>.Fail(result.Error));
    }

    /// <summary>
    /// 获取权限树形结构
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限树</returns>
    [HttpGet("tree")]
    [Authorize(Policy = "permission:view")]
    [ProducesResponseType(typeof(ApiResponse<List<PermissionTreeDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<List<PermissionTreeDto>>>> GetPermissionTree(CancellationToken cancellationToken = default)
    {
        var result = await _permissionService.GetPermissionTreeAsync(cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(ApiResponse<List<PermissionTreeDto>>.Ok(result.Value));
        }
        
        return BadRequest(ApiResponse<List<PermissionTreeDto>>.Fail(result.Error));
    }

    /// <summary>
    /// 获取权限详情
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限详情</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = "permission:view")]
    [ProducesResponseType(typeof(ApiResponse<PermissionDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<PermissionDto>>> GetPermission(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _permissionService.GetPermissionAsync(id, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(ApiResponse<PermissionDto>.Ok(result.Value));
        }
        
        return NotFound(ApiResponse<PermissionDto>.Fail(result.Error));
    }

    /// <summary>
    /// 创建权限
    /// </summary>
    /// <param name="dto">创建权限DTO</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的权限</returns>
    [HttpPost]
    [Authorize(Policy = "permission:create")]
    [ProducesResponseType(typeof(ApiResponse<PermissionDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<PermissionDto>>> CreatePermission([FromBody] CreatePermissionDto dto, CancellationToken cancellationToken = default)
    {
        var result = await _permissionService.CreatePermissionAsync(dto, cancellationToken);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("创建权限成功: {PermissionCode} ({PermissionName})", result.Value.Code, result.Value.Name);
            return CreatedAtAction(nameof(GetPermission), new { id = result.Value.Id }, 
                ApiResponse<PermissionDto>.Ok(result.Value, "权限创建成功"));
        }
        
        return BadRequest(ApiResponse<PermissionDto>.Fail(result.Error));
    }

    /// <summary>
    /// 更新权限
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <param name="dto">更新权限DTO</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id}")]
    [Authorize(Policy = "permission:update")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<object>>> UpdatePermission(Guid id, [FromBody] UpdatePermissionDto dto, CancellationToken cancellationToken = default)
    {
        var operatorId = GetCurrentUserId();
        var result = await _permissionService.UpdatePermissionAsync(id, dto, operatorId, cancellationToken);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("更新权限成功: {PermissionId}", id);
            return Ok(ApiResponse<object>.Ok(null, "权限更新成功"));
        }
        
        return BadRequest(ApiResponse<object>.Fail(result.Error));
    }

    /// <summary>
    /// 启用权限
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id}/enable")]
    [Authorize(Policy = "permission:update")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<object>>> EnablePermission(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _permissionService.EnablePermissionAsync(id, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(ApiResponse<object>.Ok(null, "权限已启用"));
        }
        
        return BadRequest(ApiResponse<object>.Fail(result.Error));
    }

    /// <summary>
    /// 禁用权限
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id}/disable")]
    [Authorize(Policy = "permission:update")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<object>>> DisablePermission(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _permissionService.DisablePermissionAsync(id, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(ApiResponse<object>.Ok(null, "权限已禁用"));
        }
        
        return BadRequest(ApiResponse<object>.Fail(result.Error));
    }

    /// <summary>
    /// 删除权限
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    [Authorize(Policy = "permission:delete")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<object>>> DeletePermission(Guid id, CancellationToken cancellationToken = default)
    {
        var operatorId = GetCurrentUserId();
        var result = await _permissionService.DeletePermissionAsync(id, operatorId, cancellationToken);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("删除权限成功: {PermissionId}", id);
            return Ok(ApiResponse<object>.Ok(null, "权限删除成功"));
        }
        
        return BadRequest(ApiResponse<object>.Fail(result.Error));
    }

    /// <summary>
    /// 获取按模块分组的权限列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分组的权限列表</returns>
    [HttpGet("grouped")]
    [Authorize(Policy = "permission:view")]
    [ProducesResponseType(typeof(ApiResponse<List<RolePermissionDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<List<RolePermissionDto>>>> GetGroupedPermissions(CancellationToken cancellationToken = default)
    {
        var result = await _permissionService.GetGroupedPermissionsAsync(cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(ApiResponse<List<RolePermissionDto>>.Ok(result.Value));
        }
        
        return BadRequest(ApiResponse<List<RolePermissionDto>>.Fail(result.Error));
    }
    
    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private Guid GetCurrentUserId()
    {
        try
        {
            return User.GetUserId();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "无法获取当前用户ID");
            return Guid.Empty;
        }
    }
}