using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.WebApi.Controllers.Admin;

/// <summary>
/// 管理员支付管理控制器
/// </summary>
[ApiController]
[Route("api/v1/admin/payment")]
[Authorize(Roles = "Admin")]
public class AdminPaymentController : ControllerBase
{
    private readonly IPaymentService _paymentService;
    private readonly IOrderService _orderService;
    private readonly ILogger<AdminPaymentController> _logger;

    public AdminPaymentController(
        IPaymentService paymentService,
        IOrderService orderService,
        ILogger<AdminPaymentController> logger)
    {
        _paymentService = paymentService;
        _orderService = orderService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有支付记录（带分页和筛选）
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetPayments(
        [FromQuery] PaymentQueryDto query, 
        CancellationToken cancellationToken)
    {
        // Note: This would require a new method in IPaymentService
        // For now, return a placeholder response
        return Ok(new
        {
            message = "支付记录查询功能需要扩展IPaymentService接口",
            query
        });
    }

    /// <summary>
    /// 手动处理退款
    /// </summary>
    [HttpPost("manual-refund")]
    public async Task<IActionResult> ManualRefund([FromBody] ManualRefundRequest request, CancellationToken cancellationToken)
    {
        var refundDto = new ProcessRefundDto
        {
            OrderId = request.OrderId,
            PaymentId = request.PaymentId,
            RefundAmount = request.RefundAmount,
            Reason = request.Reason,
            OperatorId = User.Identity?.Name ?? "Admin"
        };

        var result = await _paymentService.ProcessRefundAsync(refundDto, cancellationToken);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("Manual refund processed for order {OrderId} by {OperatorId}", 
                request.OrderId, refundDto.OperatorId);
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 批量查询支付状态
    /// </summary>
    [HttpPost("batch-check-status")]
    public async Task<IActionResult> BatchCheckPaymentStatus(
        [FromBody] List<string> paymentNos, 
        CancellationToken cancellationToken)
    {
        if (!paymentNos.Any() || paymentNos.Count > 50)
        {
            return BadRequest(new { error = "支付单号数量必须在1-50之间" });
        }

        var results = new List<object>();
        foreach (var paymentNo in paymentNos)
        {
            var result = await _paymentService.CheckPaymentStatusAsync(paymentNo, cancellationToken);
            results.Add(new
            {
                paymentNo,
                success = result.IsSuccess,
                error = result.Error
            });
        }

        return Ok(new { results });
    }

    /// <summary>
    /// 批量查询退款状态
    /// </summary>
    [HttpPost("batch-check-refund-status")]
    public async Task<IActionResult> BatchCheckRefundStatus(
        [FromBody] List<string> refundNos, 
        CancellationToken cancellationToken)
    {
        if (!refundNos.Any() || refundNos.Count > 50)
        {
            return BadRequest(new { error = "退款单号数量必须在1-50之间" });
        }

        var results = new List<object>();
        foreach (var refundNo in refundNos)
        {
            var result = await _paymentService.CheckRefundStatusAsync(refundNo, cancellationToken);
            results.Add(new
            {
                refundNo,
                success = result.IsSuccess,
                error = result.Error
            });
        }

        return Ok(new { results });
    }

    /// <summary>
    /// 获取支付统计仪表板数据
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<IActionResult> GetPaymentDashboard(CancellationToken cancellationToken)
    {
        var today = DateTime.UtcNow.Date;
        var startOfMonth = new DateTime(today.Year, today.Month, 1);
        var startOfWeek = today.AddDays(-(int)today.DayOfWeek);

        // Get statistics for different periods
        var todayStats = await _paymentService.GetPaymentStatisticsAsync(today, today.AddDays(1).AddSeconds(-1), cancellationToken);
        var weekStats = await _paymentService.GetPaymentStatisticsAsync(startOfWeek, today.AddDays(1).AddSeconds(-1), cancellationToken);
        var monthStats = await _paymentService.GetPaymentStatisticsAsync(startOfMonth, today.AddDays(1).AddSeconds(-1), cancellationToken);

        return Ok(new
        {
            today = todayStats.Value,
            week = weekStats.Value,
            month = monthStats.Value
        });
    }

    /// <summary>
    /// 导出支付记录
    /// </summary>
    [HttpGet("export")]
    public async Task<IActionResult> ExportPayments(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] PaymentStatus? status,
        [FromQuery] PaymentMethod? method,
        CancellationToken cancellationToken)
    {
        // This would typically generate a CSV or Excel file
        // For now, return export metadata
        return Ok(new
        {
            message = "支付记录导出功能尚未实现",
            exportFormat = "CSV",
            fields = new[] 
            { 
                "PaymentNo", "OrderNo", "Amount", "PaymentMethod", 
                "Status", "TransactionId", "CreatedAt", "PaidAt" 
            },
            filters = new
            {
                startDate,
                endDate,
                status,
                method
            }
        });
    }

    /// <summary>
    /// 获取支付方式配置状态
    /// </summary>
    [HttpGet("gateway-status")]
    public async Task<IActionResult> GetPaymentGatewayStatus(CancellationToken cancellationToken)
    {
        var gateways = new[]
        {
            new { method = PaymentMethod.Alipay, name = "支付宝", enabled = true, configured = true },
            new { method = PaymentMethod.WeChatPay, name = "微信支付", enabled = true, configured = true }
        };

        return Ok(new { gateways });
    }

    /// <summary>
    /// 获取异常支付记录
    /// </summary>
    [HttpGet("anomalies")]
    public async Task<IActionResult> GetPaymentAnomalies(CancellationToken cancellationToken)
    {
        // This would identify payments that need attention:
        // - Stuck in pending for too long
        // - Failed refunds
        // - Mismatched amounts
        // etc.
        
        return Ok(new
        {
            message = "异常支付检测功能尚未实现",
            categories = new[]
            {
                "长时间待支付",
                "退款失败",
                "金额不匹配",
                "重复支付"
            }
        });
    }
}

/// <summary>
/// 支付查询参数
/// </summary>
public class PaymentQueryDto
{
    public PaymentStatus? Status { get; set; }
    public PaymentMethod? Method { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Keyword { get; set; }
    public decimal? MinAmount { get; set; }
    public decimal? MaxAmount { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// 手动退款请求
/// </summary>
public class ManualRefundRequest
{
    public Guid OrderId { get; set; }
    public Guid PaymentId { get; set; }
    public decimal RefundAmount { get; set; }
    public string Reason { get; set; } = string.Empty;
}