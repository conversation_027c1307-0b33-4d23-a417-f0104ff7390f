using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.WebApi.Attributes;
using WhimLabAI.WebApi.Authorization;

namespace WhimLabAI.WebApi.Controllers.Admin;

/// <summary>
/// 管理员数据库性能控制器
/// </summary>
[ApiController]
[Route("api/v1/admin/database-performance")]
[Authorize(Roles = "Admin,SuperAdmin")]
public class AdminDatabasePerformanceController : ControllerBase
{
    private readonly IDatabasePerformanceService _databasePerformanceService;
    private readonly ILogger<AdminDatabasePerformanceController> _logger;

    public AdminDatabasePerformanceController(
        IDatabasePerformanceService databasePerformanceService,
        ILogger<AdminDatabasePerformanceController> logger)
    {
        _databasePerformanceService = databasePerformanceService;
        _logger = logger;
    }

    /// <summary>
    /// 分析数据库索引
    /// </summary>
    [HttpGet("indexes/analyze")]
    [RequirePermission("database.analyze")]
    [ProducesResponseType(typeof(IndexAnalysisResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AnalyzeIndexes()
    {
        _logger.LogInformation("Starting index analysis by {AdminId}", User.Identity?.Name);
        
        var result = await _databasePerformanceService.AnalyzeIndexesAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Index analysis completed. Health score: {Score}", 
            result.Value!.HealthScore.OverallScore);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 分析慢查询
    /// </summary>
    [HttpGet("queries/slow")]
    [RequirePermission("database.analyze")]
    [ProducesResponseType(typeof(SlowQueryAnalysisResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AnalyzeSlowQueries(
        [FromQuery] int thresholdMs = 1000,
        [FromQuery] int limit = 100)
    {
        _logger.LogInformation("Analyzing slow queries with threshold {Threshold}ms by {AdminId}", 
            thresholdMs, User.Identity?.Name);
        
        var result = await _databasePerformanceService.AnalyzeSlowQueriesAsync(
            TimeSpan.FromMilliseconds(thresholdMs), 
            limit);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Found {Count} slow queries", result.Value!.TotalSlowQueries);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 分析查询执行计划
    /// </summary>
    [HttpPost("queries/plan")]
    [RequirePermission("database.analyze")]
    [ProducesResponseType(typeof(QueryPlanAnalysis), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AnalyzeQueryPlan([FromBody] QueryPlanRequest request)
    {
        _logger.LogInformation("Analyzing query plan by {AdminId}", User.Identity?.Name);
        
        var result = await _databasePerformanceService.AnalyzeQueryPlanAsync(request.Query);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 获取数据库统计信息
    /// </summary>
    [HttpGet("statistics")]
    [RequirePermission("database.view")]
    [ProducesResponseType(typeof(DatabaseStatistics), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetDatabaseStatistics()
    {
        var result = await _databasePerformanceService.GetDatabaseStatisticsAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 获取表统计信息
    /// </summary>
    [HttpGet("tables/statistics")]
    [RequirePermission("database.view")]
    [ProducesResponseType(typeof(List<TableStatistics>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetTableStatistics()
    {
        var result = await _databasePerformanceService.GetTableStatisticsAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 优化数据库
    /// </summary>
    [HttpPost("optimize")]
    [RequirePermission("database.optimize")]
    [ProducesResponseType(typeof(DatabaseOptimizationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> OptimizeDatabase([FromBody] DatabaseOptimizationOptions options)
    {
        _logger.LogInformation("Starting database optimization by {AdminId}", User.Identity?.Name);
        
        var result = await _databasePerformanceService.OptimizeDatabaseAsync(options);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Database optimization completed. Space reclaimed: {Space} bytes", 
            result.Value!.SpaceReclaimed);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 创建建议的索引
    /// </summary>
    [HttpPost("indexes/create-suggested")]
    [RequirePermission("database.optimize")]
    [ProducesResponseType(typeof(IndexCreationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateSuggestedIndexes([FromBody] List<IndexSuggestion> suggestions)
    {
        _logger.LogInformation("Creating {Count} suggested indexes by {AdminId}", 
            suggestions.Count, User.Identity?.Name);
        
        var result = await _databasePerformanceService.CreateSuggestedIndexesAsync(suggestions);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Created {Created}/{Total} indexes successfully", 
            result.Value!.CreatedIndexes, result.Value.RequestedIndexes);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 清理数据库
    /// </summary>
    [HttpPost("cleanup")]
    [RequirePermission("database.cleanup")]
    [ProducesResponseType(typeof(DatabaseCleanupResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CleanupDatabase([FromBody] DatabaseCleanupOptions options)
    {
        _logger.LogInformation("Starting database cleanup by {AdminId}", User.Identity?.Name);
        
        var result = await _databasePerformanceService.CleanupDatabaseAsync(options);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Database cleanup completed. Space reclaimed: {Space} bytes", 
            result.Value!.SpaceReclaimed);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 监控数据库性能
    /// </summary>
    [HttpPost("monitor")]
    [RequirePermission("database.monitor")]
    [ProducesResponseType(typeof(DatabasePerformanceMetrics), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> MonitorPerformance([FromBody] MonitorRequest request)
    {
        _logger.LogInformation("Starting performance monitoring for {Duration} minutes by {AdminId}", 
            request.DurationMinutes, User.Identity?.Name);
        
        var result = await _databasePerformanceService.MonitorPerformanceAsync(
            TimeSpan.FromMinutes(request.DurationMinutes));
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Performance monitoring completed. Captured {Count} snapshots", 
            result.Value!.Snapshots.Count);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 生成数据库性能报告
    /// </summary>
    [HttpPost("report/generate")]
    [RequirePermission("database.reports")]
    [ProducesResponseType(typeof(DatabasePerformanceReport), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GeneratePerformanceReport()
    {
        _logger.LogInformation("Generating database performance report by {AdminId}", User.Identity?.Name);
        
        var result = await _databasePerformanceService.GeneratePerformanceReportAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Performance report generated: {ReportId} with score {Score}", 
            result.Value!.ReportId, result.Value.Score.OverallScore);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 获取数据库优化建议
    /// </summary>
    [HttpGet("recommendations")]
    [RequirePermission("database.view")]
    [ProducesResponseType(typeof(DatabaseOptimizationRecommendations), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetOptimizationRecommendations()
    {
        // 生成完整报告并提取建议
        var reportResult = await _databasePerformanceService.GeneratePerformanceReportAsync();
        
        if (!reportResult.IsSuccess)
        {
            return BadRequest(new { error = reportResult.Error });
        }

        var report = reportResult.Value!;
        var recommendations = new DatabaseOptimizationRecommendations
        {
            GeneratedAt = DateTime.UtcNow,
            OverallScore = report.Score.OverallScore,
            Grade = report.Score.Grade,
            TotalRecommendations = report.Recommendations.Count,
            HighPriorityCount = report.Recommendations.Count(r => r.Priority == "High"),
            MediumPriorityCount = report.Recommendations.Count(r => r.Priority == "Medium"),
            LowPriorityCount = report.Recommendations.Count(r => r.Priority == "Low"),
            Recommendations = report.Recommendations,
            QuickWins = report.Recommendations
                .Where(r => r.ExpectedImprovement >= 30 && r.Priority != "Low")
                .OrderByDescending(r => r.ExpectedImprovement)
                .Take(5)
                .ToList()
        };

        return Ok(recommendations);
    }

    /// <summary>
    /// 执行快速优化
    /// </summary>
    [HttpPost("quick-optimize")]
    [RequirePermission("database.optimize")]
    [ProducesResponseType(typeof(QuickOptimizationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> PerformQuickOptimization()
    {
        _logger.LogInformation("Starting quick database optimization by {AdminId}", User.Identity?.Name);

        var result = new QuickOptimizationResult
        {
            StartTime = DateTime.UtcNow,
            Actions = new List<string>()
        };

        // 1. 分析索引
        var indexAnalysis = await _databasePerformanceService.AnalyzeIndexesAsync();
        if (indexAnalysis.IsSuccess)
        {
            result.Actions.Add($"Analyzed {indexAnalysis.Value!.ExistingIndexes.Count} indexes");

            // 删除未使用的索引（限制前5个）
            var unusedIndexes = indexAnalysis.Value.UnusedIndexes.Take(5).ToList();
            if (unusedIndexes.Any())
            {
                // 这里只是记录，实际删除需要谨慎
                result.Actions.Add($"Identified {unusedIndexes.Count} unused indexes for removal");
                result.UnusedIndexesFound = unusedIndexes.Count;
            }
        }

        // 2. 运行基本优化
        var optimizationOptions = new DatabaseOptimizationOptions
        {
            AnalyzeTables = true,
            UpdateStatistics = true,
            VacuumDatabase = false, // VACUUM较慢，不在快速优化中执行
            RebuildIndexes = false,
            OptimizeTables = false,
            CleanupLogs = false
        };

        var optimizationResult = await _databasePerformanceService.OptimizeDatabaseAsync(optimizationOptions);
        if (optimizationResult.IsSuccess)
        {
            result.Actions.Add("Updated table statistics");
            result.SpaceReclaimed = optimizationResult.Value!.SpaceReclaimed;
        }

        result.EndTime = DateTime.UtcNow;
        result.Success = true;
        result.Message = "Quick optimization completed successfully";

        _logger.LogInformation("Quick optimization completed in {Duration}ms", 
            result.Duration.TotalMilliseconds);

        return Ok(result);
    }
}

#region DTOs

/// <summary>
/// 查询计划请求
/// </summary>
public class QueryPlanRequest
{
    public string Query { get; set; } = string.Empty;
}

/// <summary>
/// 监控请求
/// </summary>
public class MonitorRequest
{
    public double DurationMinutes { get; set; } = 5;
}

/// <summary>
/// 数据库优化建议
/// </summary>
public class DatabaseOptimizationRecommendations
{
    public DateTime GeneratedAt { get; set; }
    public int OverallScore { get; set; }
    public string Grade { get; set; } = string.Empty;
    public int TotalRecommendations { get; set; }
    public int HighPriorityCount { get; set; }
    public int MediumPriorityCount { get; set; }
    public int LowPriorityCount { get; set; }
    public List<PerformanceRecommendation> Recommendations { get; set; } = new();
    public List<PerformanceRecommendation> QuickWins { get; set; } = new();
}

/// <summary>
/// 快速优化结果
/// </summary>
public class QuickOptimizationResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration => EndTime - StartTime;
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<string> Actions { get; set; } = new();
    public long SpaceReclaimed { get; set; }
    public int UnusedIndexesFound { get; set; }
}

#endregion