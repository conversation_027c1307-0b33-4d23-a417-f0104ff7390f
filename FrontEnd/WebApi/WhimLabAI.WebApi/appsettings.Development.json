{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Information",
      "Microsoft.EntityFrameworkCore.Database.Command": "Information"
    }
  },
  "DetailedErrors": true,
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=whimlabai;Username=postgres;Password=***********;Pooling=true;MinPoolSize=5;MaxPoolSize=100;ConnectionLifetime=300;CommandTimeout=30;Timeout=30",
    "Redis": "localhost:6379,password=redis123,defaultDatabase=1,abortConnect=false,connectTimeout=5000,syncTimeout=5000,asyncTimeout=5000,keepAlive=30,connectRetry=3"
  },
  "Jwt": {
    // IMPORTANT: For production, use a cryptographically secure key stored in environment variables or Azure Key Vault
    // To generate a secure key: dotnet user-secrets set "Jwt:SecretKey" "$(openssl rand -base64 64)"
    // The application will generate a temporary key if this is not properly configured in development
    "SecretKey": "ThisIsAVeryLongSecretKeyForJWT123456789012345678901234567890"
  },
  "AI": {
    "SemanticKernel": {
      "ApiKey": "sk-test-key-replace-with-actual-openai-key",
      "Endpoint": "",
      "DefaultModel": "gpt-3.5-turbo"
    }
  },
  "Storage": {
    "Provider": "minio",
    "MinIO": {
      "Endpoint": "localhost:9000",
      "AccessKey": "admin",
      "SecretKey": "admin123",
      "UseSSL": false,
      "DefaultBucket": "whimlab-ai",
      "DefaultUrlExpiryMinutes": 1440,
      "PublicEndpoint": "http://localhost:9000"
    }
  },
  "Verification": {
    "AdminCaptchaPolicy": {
      "Enabled": true,
      "AlwaysRequired": true,
      "DisableInDevelopment": false
    },
    "CustomerCaptchaPolicy": {
      "Enabled": true,
      "AlwaysRequired": false,
      "FailedAttemptsThreshold": 3,
      "HourlyAttemptsThreshold": 10,
      "DisableInDevelopment": true
    }
  },
  "Sms": {
    "Enabled": true,
    "Provider": "twilio",
    "Providers": {
      "Twilio": {
        "AccountSid": "ACtest1234567890abcdef1234567890ab",
        "AuthToken": "test1234567890abcdef1234567890ab",
        "FromPhoneNumber": "+***********",
        "MessagingServiceSid": null,
        "MaxRetries": 1,
        "RetryDelaySeconds": 2
      },
      "Aliyun": {
        "AccessKeyId": "test-aliyun-access-key-id",
        "AccessKeySecret": "test-aliyun-access-key-secret",
        "SignName": "WhimLabAI测试",
        "TemplateCode": "SMS_123456789",
        "RegionId": "cn-hangzhou",
        "Product": "Dysmsapi",
        "Domain": "dysmsapi.aliyuncs.com",
        "Version": "2017-05-25",
        "Action": "SendSms"
      },
      "Tencent": {
        "SecretId": "test-tencent-secret-id",
        "SecretKey": "test-tencent-secret-key",
        "SmsSdkAppId": "**********",
        "SignName": "WhimLabAI测试",
        "TemplateId": "123456",
        "Region": "ap-guangzhou",
        "Endpoint": "sms.tencentcloudapi.com"
      }
    },
    "RateLimiting": {
      "MaxSmsPerPhonePerDay": 100,
      "MaxSmsPerPhonePerHour": 20,
      "MaxSmsPerIpPerDay": 200
    }
  },
  "Payment": {
    "BaseUrl": "https://localhost:15801",
    "CallbackUrl": "https://localhost:15801",
    "Security": {
      "EnableIpWhitelist": false,
      "IpWhitelist": [
        "127.0.0.1",
        "::1"
      ],
      "ReplayWindowMinutes": 5,
      "EnableSignatureValidation": false
    },
    "Alipay": {
      "AppId": "2021000000000001",
      "PrivateKey": "MIIEpAIBAAKCAQEAvKyb1mAqT7J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0wIDAQABAoIBAQCrXsYXL5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0AoGBAPgL5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0AoGBAMJL5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0A=",
      "PublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvKyb1mAqT7J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0wIDAQAB",
      "AlipayPublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAliN8Kl5H1Y5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0wIDAQAB",
      "IsProduction": false,
      "Gateway": "https://openapi.alipaydev.com/gateway.do",
      "NotifyUrl": "https://localhost:15801/api/payment/alipay/notify",
      "ReturnUrl": "https://localhost:15801/payment/result"
    },
    "WeChatPay": {
      "AppId": "wx0000000000000001",
      "MchId": "1000000001",
      "ApiKey": "test-api-key-32-characters-12345",
      "CertPath": "./certs/wechat/apiclient_cert_dev.p12",
      "Gateway": "https://api.mch.weixin.qq.com/sandboxnew",
      "NotifyUrl": "https://localhost:15801/api/payment/wechatpay/notify",
      "ReturnUrl": "https://localhost:15801/payment/result"
    }
  }
}
