#!/bin/bash

# ================================================================
# WhimLabAI Configuration Validation Script
# ================================================================
# This script validates all configuration files to ensure consistency
# across the entire project including ports, passwords, and API URLs
# ================================================================

set -uo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
ERRORS=0
WARNINGS=0
PASSED=0

# Log functions
log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((ERRORS++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    ((WARNINGS++))
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED++))
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Get project root (parent of scripts directory)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WEBAPI_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"

echo "================================================================"
echo "WhimLabAI Configuration Validation"
echo "================================================================"
echo ""
log_info "Project Root: $PROJECT_ROOT"
log_info "WebAPI Root: $WEBAPI_ROOT"
echo ""

# ================================================================
# 1. Port Configuration Validation
# ================================================================
echo "1. Validating Port Configurations"
echo "================================="

# Expected ports
WEBAPI_HTTP_PORT=15800
WEBAPI_HTTPS_PORT=15801
ADMIN_HTTP_PORT=7216
ADMIN_HTTPS_PORT=7217
CUSTOMER_HTTP_PORT=7040
CUSTOMER_HTTPS_PORT=7041

# Check WebAPI launchSettings.json
WEBAPI_LAUNCH="$WEBAPI_ROOT/Properties/launchSettings.json"
if [ -f "$WEBAPI_LAUNCH" ]; then
    # Check HTTP port
    if grep -q "\"applicationUrl\".*http://localhost:$WEBAPI_HTTP_PORT" "$WEBAPI_LAUNCH"; then
        log_success "WebAPI HTTP port correctly set to $WEBAPI_HTTP_PORT"
    else
        log_error "WebAPI HTTP port is not set to $WEBAPI_HTTP_PORT in launchSettings.json"
    fi

    # Check HTTPS port
    if grep -q "https://localhost:$WEBAPI_HTTPS_PORT" "$WEBAPI_LAUNCH"; then
        log_success "WebAPI HTTPS port correctly set to $WEBAPI_HTTPS_PORT"
    else
        log_error "WebAPI HTTPS port is not set to $WEBAPI_HTTPS_PORT in launchSettings.json"
    fi
else
    log_error "WebAPI launchSettings.json not found at $WEBAPI_LAUNCH"
fi

# Check Dockerfile port exposure
DOCKERFILE="$PROJECT_ROOT/deploy/docker/Dockerfile"
if [ -f "$DOCKERFILE" ]; then
    if grep -q "EXPOSE 80" "$DOCKERFILE" && grep -q "EXPOSE 443" "$DOCKERFILE"; then
        log_success "Dockerfile exposes correct ports (80, 443)"
    else
        log_error "Dockerfile does not expose the correct ports"
    fi
else
    log_info "Dockerfile not found at $DOCKERFILE (check deploy/docker/ directory)"
fi

# Check docker-compose.yml port mappings
if [ -f "$PROJECT_ROOT/docker-compose.yml" ]; then
    if grep -q "webapi:" "$PROJECT_ROOT/docker-compose.yml"; then
        if grep -A 5 "webapi:" "$PROJECT_ROOT/docker-compose.yml" | grep -q "5000:80"; then
            log_success "docker-compose.yml maps WebAPI port correctly (5000:80)"
        else
            log_error "docker-compose.yml does not map WebAPI port correctly"
        fi
    else
        log_info "WebAPI service not defined in docker-compose.yml (services are run separately in development)"
    fi
else
    log_error "docker-compose.yml not found"
fi

echo ""

# ================================================================
# 2. Frontend API URL Configuration
# ================================================================
echo "2. Validating Frontend API URL Configurations"
echo "============================================="

API_BASE_URL="http://localhost:$WEBAPI_HTTP_PORT"

# Check Admin Frontend appsettings
ADMIN_APPSETTINGS="$PROJECT_ROOT/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/appsettings.json"
if [ -f "$ADMIN_APPSETTINGS" ]; then
    if grep -q "\"BaseUrl\".*\"$API_BASE_URL\"" "$ADMIN_APPSETTINGS"; then
        log_success "Admin Frontend API URL correctly set to $API_BASE_URL"
    else
        log_error "Admin Frontend API URL is not set to $API_BASE_URL"
    fi
else
    log_warning "Admin Frontend appsettings.json not found"
fi

# Check Customer Frontend appsettings
CUSTOMER_APPSETTINGS="$PROJECT_ROOT/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web/appsettings.json"
if [ -f "$CUSTOMER_APPSETTINGS" ]; then
    if grep -q "\"BaseUrl\".*\"$API_BASE_URL\"" "$CUSTOMER_APPSETTINGS"; then
        log_success "Customer Frontend API URL correctly set to $API_BASE_URL"
    else
        log_error "Customer Frontend API URL is not set to $API_BASE_URL"
    fi
else
    log_warning "Customer Frontend appsettings.json not found"
fi

echo ""

# ================================================================
# 3. Database Password Configuration
# ================================================================
echo "3. Validating Database Password Consistency"
echo "==========================================="

# Expected development password
DEV_DB_PASSWORD="postgres123"

# Check WebAPI appsettings.Development.json
WEBAPI_DEV_SETTINGS="$WEBAPI_ROOT/appsettings.Development.json"
if [ -f "$WEBAPI_DEV_SETTINGS" ]; then
    if grep -q "Password=$DEV_DB_PASSWORD" "$WEBAPI_DEV_SETTINGS"; then
        log_success "WebAPI development database password is correct"
    else
        log_error "WebAPI development database password is not '$DEV_DB_PASSWORD'"
    fi
else
    log_error "WebAPI appsettings.Development.json not found"
fi

# Check docker-compose.yml PostgreSQL password
if [ -f "$PROJECT_ROOT/docker-compose.yml" ]; then
    if grep -A 10 "postgres:" "$PROJECT_ROOT/docker-compose.yml" | grep -q "POSTGRES_PASSWORD.*$DEV_DB_PASSWORD"; then
        log_success "docker-compose PostgreSQL password matches development settings"
    else
        log_error "docker-compose PostgreSQL password does not match development settings"
    fi
else
    log_error "docker-compose.yml not found for password check"
fi

echo ""

# ================================================================
# 4. Docker Compose Services Configuration
# ================================================================
echo "4. Validating Docker Compose Services"
echo "====================================="

REQUIRED_SERVICES=("postgres" "redis" "rabbitmq" "minio" "elasticsearch" "kibana" "prometheus" "grafana")

if [ -f "$PROJECT_ROOT/docker-compose.yml" ]; then
    for service in "${REQUIRED_SERVICES[@]}"; do
        if grep -q "^[[:space:]]*${service}:" "$PROJECT_ROOT/docker-compose.yml"; then
            log_success "Service '$service' is defined in docker-compose.yml"
        else
            log_error "Service '$service' is missing from docker-compose.yml"
        fi
    done
else
    log_error "docker-compose.yml not found"
fi

echo ""

# ================================================================
# 5. CORS Configuration
# ================================================================
echo "5. Validating CORS Configuration"
echo "================================="

# Check if CORS is configured for frontend ports
if [ -f "$WEBAPI_DEV_SETTINGS" ]; then
    if grep -q "AllowedOrigins.*7216" "$WEBAPI_DEV_SETTINGS" && \
       grep -q "AllowedOrigins.*7040" "$WEBAPI_DEV_SETTINGS"; then
        log_success "CORS is configured for both Admin (7216) and Customer (7040) frontends"
    else
        log_warning "CORS may not be properly configured for all frontend ports"
    fi
else
    log_warning "Cannot check CORS configuration - appsettings.Development.json not found"
fi

echo ""

# ================================================================
# 6. SSL Certificate Configuration
# ================================================================
echo "6. Validating SSL Certificate Configuration"
echo "==========================================="

# Check if dev certificates are mentioned in setup scripts
SETUP_SCRIPT="$PROJECT_ROOT/scripts/setup-dev.sh"
if [ -f "$SETUP_SCRIPT" ]; then
    if grep -q "dotnet dev-certs https" "$SETUP_SCRIPT"; then
        log_success "Development SSL certificates are configured in setup script"
    else
        log_warning "Development SSL certificates may not be configured in setup script"
    fi
else
    log_warning "setup-dev.sh script not found"
fi

echo ""

# ================================================================
# 7. Environment Variables Consistency
# ================================================================
echo "7. Validating Environment Variables"
echo "==================================="

# Check .env file if exists
ENV_FILE="$PROJECT_ROOT/.env"
if [ -f "$ENV_FILE" ]; then
    log_info "Found .env file - checking for required variables"

    REQUIRED_VARS=("POSTGRES_PASSWORD" "JWT_SECRET" "REDIS_PASSWORD")
    for var in "${REQUIRED_VARS[@]}"; do
        if grep -q "^${var}=" "$ENV_FILE"; then
            log_success "Environment variable '$var' is defined"
        else
            log_warning "Environment variable '$var' is not defined in .env"
        fi
    done
else
    log_info ".env file not found (this is okay if using appsettings.json)"
fi

echo ""

# ================================================================
# 8. Nginx Configuration (Production)
# ================================================================
echo "8. Validating Nginx Configuration"
echo "=================================="

NGINX_CONF="$PROJECT_ROOT/nginx/nginx.prod.conf"
if [ -f "$NGINX_CONF" ]; then
    # Check upstream configuration
    if grep -q "upstream webapi" "$NGINX_CONF" || grep -q "proxy_pass.*webapi" "$NGINX_CONF"; then
        log_success "Nginx upstream configuration is present"
    else
        log_warning "Nginx upstream configuration may need review"
    fi

    # Check SSL configuration
    if grep -q "ssl_certificate" "$NGINX_CONF" && grep -q "ssl_certificate_key" "$NGINX_CONF"; then
        log_success "Nginx SSL configuration is present"
    else
        log_warning "Nginx SSL configuration may be incomplete"
    fi
else
    log_info "Nginx production configuration not found (only needed for production)"
fi

echo ""

# ================================================================
# Summary Report
# ================================================================
echo "================================================================"
echo "Configuration Validation Summary"
echo "================================================================"
echo -e "${GREEN}Passed:${NC} $PASSED"
echo -e "${YELLOW}Warnings:${NC} $WARNINGS"
echo -e "${RED}Errors:${NC} $ERRORS"
echo ""

if [ $ERRORS -eq 0 ]; then
    if [ $WARNINGS -eq 0 ]; then
        echo -e "${GREEN}✓ All configuration checks passed!${NC}"
        exit 0
    else
        echo -e "${YELLOW}⚠ Configuration validation completed with warnings.${NC}"
        echo "Please review the warnings above to ensure optimal configuration."
        exit 0
    fi
else
    echo -e "${RED}✗ Configuration validation failed with $ERRORS error(s).${NC}"
    echo ""
    echo "Recommended fixes:"
    echo "1. Ensure all port configurations match the expected values"
    echo "2. Update frontend API URLs to point to http://localhost:5000"
    echo "3. Verify database passwords are consistent across all configurations"
    echo "4. Check that all required Docker services are defined"
    echo "5. Run './scripts/setup-dev.sh' to set up development environment"
    echo ""
    echo "For detailed fixes, review the error messages above."
    exit 1
fi
