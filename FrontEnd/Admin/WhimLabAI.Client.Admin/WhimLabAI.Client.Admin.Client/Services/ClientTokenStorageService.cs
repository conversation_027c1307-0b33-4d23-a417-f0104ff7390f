using Microsoft.JSInterop;

namespace WhimLabAI.Client.Admin.Client.Services;

/// <summary>
/// Client-side implementation of token storage using localStorage
/// This ensures tokens persist across page refreshes and browser sessions
/// </summary>
public class ClientTokenStorageService : ITokenStorageService
{
    private readonly IJSRuntime _jsRuntime;

    public ClientTokenStorageService(IJSRuntime jsRuntime)
    {
        _jsRuntime = jsRuntime;
    }

    public async Task<string?> GetTokenAsync(string key)
    {
        try
        {
            return await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", key);
        }
        catch (Exception)
        {
            // 如果localStorage不可用（如SSR期间），返回null
            return null;
        }
    }

    public async Task SetTokenAsync(string key, string value)
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", key, value);
        }
        catch (Exception)
        {
            // 如果localStorage不可用（如SSR期间），忽略错误
        }
    }

    public async Task RemoveTokenAsync(string key)
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", key);
        }
        catch (Exception)
        {
            // 如果localStorage不可用（如SSR期间），忽略错误
        }
    }
}