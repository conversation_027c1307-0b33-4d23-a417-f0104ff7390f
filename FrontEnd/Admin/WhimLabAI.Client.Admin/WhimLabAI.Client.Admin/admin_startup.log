从 /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/Properties/launchSettings.json 使用启动设置...
正在生成...
生成启动时间为 2025/7/22 03:52:05。
     1>项目“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj”在节点 1 上(Restore 个目标)。
     1>_GetAllRestoreProjectPathItems:
         正在确定要还原的项目…
       Restore:
         X.509 证书链验证将使用 "/usr/local/share/dotnet/sdk/9.0.301/trustedroots/codesignctl.pem" 处的回退证书捆绑包。
         X.509 证书链验证将使用 "/usr/local/share/dotnet/sdk/9.0.301/trustedroots/timestampctl.pem" 处的回退证书捆绑包。
         资产文件未改变。跳过资产文件写入。路径: /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Abstractions/obj/project.assets.json
         资产文件未改变。跳过资产文件写入。路径: /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Domain/obj/project.assets.json
         资产文件未改变。跳过资产文件写入。路径: /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Shared/obj/project.assets.json
         资产文件未改变。跳过资产文件写入。路径: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/project.assets.json
         已还原 /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Abstractions/WhimLabAI.Abstractions.csproj (用时 22 毫秒)。
         已还原 /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/WhimLabAI.Client.Admin.Client.csproj (用时 24 毫秒)。
         已还原 /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Domain/WhimLabAI.Domain.csproj (用时 22 毫秒)。
         已还原 /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Shared/WhimLabAI.Shared.csproj (用时 22 毫秒)。
         资产文件未改变。跳过资产文件写入。路径: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/obj/project.assets.json
         资产文件未改变。跳过资产文件写入。路径: /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/obj/project.assets.json
         已还原 /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj (用时 32 毫秒)。
         已还原 /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj (用时 32 毫秒)。
         
         使用的 NuGet 配置文件:
             /Users/<USER>/RiderProjects/WhimLabAI/NuGet.Config
             /Users/<USER>/.nuget/NuGet/NuGet.Config
         
         使用的源:
             https://api.nuget.org/v3/index.json
             /usr/local/share/dotnet/library-packs
         所有项目均是最新的，无法还原。
     1>已完成生成项目“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj”(Restore 个目标)的操作。
   1:7>节点 3 上的项目“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj”(默认目标)。
   1:7>项目“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj”(1:7)正在节点 4 上生成“/Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Shared/WhimLabAI.Shared.csproj”(3:10) (默认目标)。
     3>GenerateTargetFrameworkMonikerAttribute:
       正在跳过目标“GenerateTargetFrameworkMonikerAttribute”，因为所有输出文件相对于输入文件而言都是最新的。
       CoreGenerateAssemblyInfo:
       正在跳过目标“CoreGenerateAssemblyInfo”，因为所有输出文件相对于输入文件而言都是最新的。
       _GenerateSourceLinkFile:
         Source Link 为空，则文件 "obj/Debug/net9.0/WhimLabAI.Shared.sourcelink.json" 不存在。
       CoreCompile:
       正在跳过目标“CoreCompile”，因为所有输出文件相对于输入文件而言都是最新的。
       GenerateBuildDependencyFile:
       正在跳过目标“GenerateBuildDependencyFile”，因为所有输出文件相对于输入文件而言都是最新的。
       CopyFilesToOutputDirectory:
         WhimLabAI.Shared -> /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Shared/bin/Debug/net9.0/WhimLabAI.Shared.dll
     3>已完成生成项目“/Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Shared/WhimLabAI.Shared.csproj”(默认目标)的操作。
   1:7>项目“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj”(1:7)正在节点 5 上生成“/Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Abstractions/WhimLabAI.Abstractions.csproj”(6:7) (默认目标)。
     6>GenerateTargetFrameworkMonikerAttribute:
       正在跳过目标“GenerateTargetFrameworkMonikerAttribute”，因为所有输出文件相对于输入文件而言都是最新的。
       CoreGenerateAssemblyInfo:
       正在跳过目标“CoreGenerateAssemblyInfo”，因为所有输出文件相对于输入文件而言都是最新的。
       _GenerateSourceLinkFile:
         Source Link 为空，则文件 "obj/Debug/net9.0/WhimLabAI.Abstractions.sourcelink.json" 不存在。
       CoreCompile:
       正在跳过目标“CoreCompile”，因为所有输出文件相对于输入文件而言都是最新的。
       GenerateBuildDependencyFile:
       正在跳过目标“GenerateBuildDependencyFile”，因为所有输出文件相对于输入文件而言都是最新的。
       CopyFilesToOutputDirectory:
         WhimLabAI.Abstractions -> /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Abstractions/bin/Debug/net9.0/WhimLabAI.Abstractions.dll
     6>已完成生成项目“/Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Abstractions/WhimLabAI.Abstractions.csproj”(默认目标)的操作。
   1:7>项目“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj”(1:7)正在节点 2 上生成“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/WhimLabAI.Client.Admin.Client.csproj”(2:6) (默认目标)。
     2>GenerateTargetFrameworkMonikerAttribute:
       正在跳过目标“GenerateTargetFrameworkMonikerAttribute”，因为所有输出文件相对于输入文件而言都是最新的。
       CoreGenerateAssemblyInfo:
       正在跳过目标“CoreGenerateAssemblyInfo”，因为所有输出文件相对于输入文件而言都是最新的。
       _DiscoverMvcApplicationParts:
       正在跳过目标“_DiscoverMvcApplicationParts”，因为所有输出文件相对于输入文件而言都是最新的。
   1:7>项目“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj”(1:7)正在节点 1 上生成“/Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Domain/WhimLabAI.Domain.csproj”(5:7) (默认目标)。
     5>GenerateTargetFrameworkMonikerAttribute:
       正在跳过目标“GenerateTargetFrameworkMonikerAttribute”，因为所有输出文件相对于输入文件而言都是最新的。
     2>_GenerateSourceLinkFile:
         Source Link 为空，则文件 "obj/Debug/net9.0/WhimLabAI.Client.Admin.Client.sourcelink.json" 不存在。
       CoreCompile:
       正在跳过目标“CoreCompile”，因为所有输出文件相对于输入文件而言都是最新的。
     5>CoreGenerateAssemblyInfo:
       正在跳过目标“CoreGenerateAssemblyInfo”，因为所有输出文件相对于输入文件而言都是最新的。
       _GenerateSourceLinkFile:
         Source Link 为空，则文件 "obj/Debug/net9.0/WhimLabAI.Domain.sourcelink.json" 不存在。
       CoreCompile:
       正在跳过目标“CoreCompile”，因为所有输出文件相对于输入文件而言都是最新的。
       GenerateBuildDependencyFile:
       正在跳过目标“GenerateBuildDependencyFile”，因为所有输出文件相对于输入文件而言都是最新的。
       CopyFilesToOutputDirectory:
         WhimLabAI.Domain -> /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Domain/bin/Debug/net9.0/WhimLabAI.Domain.dll
     5>已完成生成项目“/Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Domain/WhimLabAI.Domain.csproj”(默认目标)的操作。
     2>_ProcessScopedCssFiles:
       正在跳过目标“_ProcessScopedCssFiles”，因为它没有输出。
       _ProcessScopedCssFiles:
       正在跳过目标“_ProcessScopedCssFiles”，因为它没有输出。
   1:7>项目“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj”(1:7)正在节点 3 上生成“/Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj”(4:6) (默认目标)。
     4>GenerateTargetFrameworkMonikerAttribute:
       正在跳过目标“GenerateTargetFrameworkMonikerAttribute”，因为所有输出文件相对于输入文件而言都是最新的。
     2>ResolveBuildCompressedStaticWebAssetsConfiguration:
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tzxjg6is5z-mhumzvqd1g.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/packages/mudblazor/7.15.0/staticwebassets/MudBlazor.min.css'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/0wz98yz2xy-rnojub5shy.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/packages/mudblazor/7.15.0/staticwebassets/MudBlazor.min.js'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bvwwiycvad-x0ueugt8gp.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/wwwroot/appsettings.Development.json'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/e85wydtwhk-md9yvkcqlf.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/blazor.webassembly.js'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/2onw7zqc0q-bvu82j4ad3.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Authorization.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/mgeagab6el-ptfrz3fits.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kqlbgdmef6-a48sropenz.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Authorization.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iljmgw0vwz-73oi73dvgk.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Forms.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xxgbm0j00s-pm8mpy5cip.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Web.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6rohpx0jp4-4ni28tl690.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/phh8l6kdjc-zqe4lhsgbv.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Cryptography.Internal.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qxsmnl9be0-dxj5dhqc57.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Cryptography.KeyDerivation.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ccdweloy0o-eyher82q7e.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Metadata.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/0c6hz2tvex-itm12vk377.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w3gfnu58ro-8ewlps0g9m.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Abstractions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/20x6c7lchl-yr6bnfroy5.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Binder.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/peqh7izl3n-9nblf8ao5a.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k23jtmruy4-mjuqqf9ko8.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Json.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/82ugqqfvem-v66dtpac4v.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kfvy2x7ypi-apuz8nsfml.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ydli3xi7x2-1bmpr6w9dd.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Diagnostics.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l0pzjzuaf5-2lb9066vyq.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Diagnostics.Abstractions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zis4b96xtm-8bt7as0i9i.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/junn8xb7k3-etpym877t9.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Physical.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/o5v1nq94v3-g2w0sei4ut.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.FileSystemGlobbing.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yez9tca4uc-1gmjxv0m8c.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Http.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/8e1yrfewp1-dxvjhqivbe.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Http.Polly.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/69dn8ctlxh-nx0z2f3xfv.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Localization.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ouirgkdwmp-5map5kd04b.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Localization.Abstractions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jvz7akeq8d-ul0xzjnwdm.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Logging.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/763yfuq04o-nwxyu3e2hm.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Logging.Abstractions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/plhi4fcglh-l36scmr1xu.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Options.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6xj3air3k4-zrlhdwvckr.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Options.ConfigurationExtensions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/shgd0ocsue-358c2dzezi.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Primitives.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/g1t9toi5e2-umcehv4hi0.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.Abstractions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/1bgcmpc0jc-a8kopo0x7x.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.JsonWebTokens.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/z41ga79u82-6tq6rr4bdi.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.Logging.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x83d0ih4di-vtgtakmfrj.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.Tokens.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w2njbnapj6-nanjlpvyw1.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.JSInterop.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/9zkhy2cfoh-zjb9sj3c45.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.JSInterop.WebAssembly.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j383sqgg7e-xcgdsu1w4u.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/MudBlazor.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tpc6bb39s9-qkbufwhni2.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Newtonsoft.Json.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/472l0jvuwa-ivrqkyor03.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Polly.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/aggo97e8fk-aamf4ift6v.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Polly.Extensions.Http.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vulfe9nibb-80sv9vo4du.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IdentityModel.Tokens.Jwt.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x1tkfmixpv-9gws8s7zmg.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.CSharp.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/sueqpqhp6x-hev5t09xbg.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.VisualBasic.Core.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4vhkgnu1fx-wy3cb00pkv.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.VisualBasic.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pil2fefgg2-nt18748s0w.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Win32.Primitives.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wbt0vq8qnn-ykr6iyjchr.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Win32.Registry.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/akubdugh7y-3h1likbfvx.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.AppContext.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4jjwn1zyph-wt7n1r1ovk.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Buffers.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nce2t1qage-65adg6natn.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.Concurrent.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6n762dpqgw-dufaq3kp3z.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.Immutable.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/spbx4l1nal-rxjrzzpp9g.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.NonGeneric.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/azj660y99d-grj2h3kseq.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.Specialized.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oggwzfetx2-cip8dbnu43.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ushg81yn3b-6hr3q9fx89.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.Annotations.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/otoue458v7-k6p4pn9w0l.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.DataAnnotations.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/joe8l3b9g5-p61cj2koso.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.EventBasedAsync.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/7jchfjht1c-fea7hw9xtf.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.Primitives.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gp0wj3dn0g-etd3dkcep2.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.TypeConverter.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r0uxym1jwi-0lm42x51au.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j36lc6l7sa-ex6vy58iyk.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Configuration.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4ahezzndm6-s0qgw5psci.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Console.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gwhlabvgwk-zknkrutld3.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Core.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fgpeaobpo0-lu92ceoi50.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Data.Common.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ll30i2jjyx-2ddk0zm05l.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Data.DataSetExtensions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ex6xkqi28v-3adg3wr0gn.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Data.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tyg7g06ixs-voyqcmzm7a.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Contracts.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x3p8e3g5tu-tuw7jnpdtf.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Debug.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xos9qf4smb-orwvw7tsnw.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.DiagnosticSource.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b6wcoyk6wt-i2nxqnh8ia.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.FileVersionInfo.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u27cyy023u-yj1m2auw1z.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Process.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hxo3j02wpb-9u6hm41m9t.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.StackTrace.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l28j0atxfq-670flx7nki.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.TextWriterTraceListener.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vgcwtuln9j-2vqkac8ysr.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Tools.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/phu1yyc87w-n515vmkk2p.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.TraceSource.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dhirh5u6y6-ogliygwa1r.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Tracing.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/1trtf5dn93-zk693pwck8.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Drawing.Primitives.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5vajrisgrh-wxhr0xa5hb.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Drawing.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3sfd7ocphf-ipprcrczgj.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Dynamic.Runtime.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ai31glpnsq-okhe897m5z.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Formats.Asn1.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xzbepgdxtw-i93u5bq4fn.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Formats.Tar.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/y8iz39xywh-x0sb683rhi.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Globalization.Calendars.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gp8yve0a37-o54lsqobzb.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Globalization.Extensions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5v9b9m10yt-tde8zuw0yw.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Globalization.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hem8237y0t-vx3bcge4ol.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.Brotli.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/odc9oykyfr-yhtj6e0w69.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.FileSystem.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/mldr5c3vyn-quahjtap8r.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.ZipFile.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ob4a6ppm9p-jtaurxkbzi.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hb3mb70q0t-3yfpgyrku1.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.AccessControl.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/9wd57hf2zj-8nnv647ull.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.DriveInfo.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pi39r9o8rn-ir5j8vbyan.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.Primitives.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vam5ahm2qy-1lxrwwxsho.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.Watcher.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ksg0dx0ew5-gyxexdekj3.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jom0rd5wf5-tsgf6g1ztd.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.IsolatedStorage.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x9k3m27ryv-j4sjofqyi5.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.MemoryMappedFiles.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xyoxqz1sgs-jiaey0kmyh.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Pipelines.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u83o8uvgfq-qfh40ih8l6.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Pipes.AccessControl.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j8cs0ucm7h-al6w1uowde.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Pipes.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xi5o3mwesh-d0g45p3x9u.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.UnmanagedMemoryStream.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wlqmsea170-2zge8rv4ra.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nmvjopidz2-2lw1u6ymmp.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.Expressions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xvi6xv3v2k-1jvfownmci.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.Parallel.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/df6ta7bnig-mv4cb7fqwu.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.Queryable.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/v6faaw44t4-3djr1lshgb.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qinpfrc2zb-ub9sra6ubv.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Memory.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rtphd1bm3m-346n69ja1w.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Http.Json.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f8pi37btuo-eupgag7vx5.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Http.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/y07xkid4jb-m0tberhw26.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.HttpListener.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ssgxii2rc9-7wmkfq1voo.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Mail.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tvi9a4m6no-ee8vwc4vcc.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.NameResolution.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/28s1sbmc3k-h1hduhi84u.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.NetworkInformation.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iy0kt5q41u-y4g427qvfa.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Ping.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dkhaa7yv2g-zv1ut64ban.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Primitives.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/v39f5kpyzm-lnozeoe9re.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Quic.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/le8g1olvuv-omoxxcqo90.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Requests.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p1r3skqu4x-t3a07csu2b.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Security.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ki35vzrtgv-5v95sh5c67.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.ServicePoint.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/a391hzw274-ww3h8yu74p.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Sockets.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3028p113ne-345793p9fr.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebClient.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oqnqqo1plh-odv41wuu54.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebHeaderCollection.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pz32pqjv0l-ksx7w94zni.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebProxy.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zr297s2wqn-b37svw0y4i.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebSockets.Client.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bc3z1bqli0-9fasahbeiq.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebSockets.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/a9eau71e6a-qt5fpja9tg.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fgpe9cg5g3-i6kirq3og4.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Numerics.Vectors.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jzl52t66rj-497r8m9pev.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Numerics.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jttqbd8cll-k9az0iuxjb.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ObjectModel.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yu67w5y0cs-mnc7tnpegn.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.DataContractSerialization.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/8b69lacdso-58q5onb7r6.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.Uri.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b7omoe7s6n-35ud51k85s.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.Xml.Linq.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qfjueerx11-ygkocwikl4.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.Xml.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nc3ak4pddv-13d6e679le.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.DispatchProxy.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fk5xezacae-tnlqh325q4.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Emit.ILGeneration.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/byc7oyc0yb-6pezgz31ve.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Emit.Lightweight.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/586e2ph24s-1sfjh9emmw.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Emit.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/t3f4z12pt1-o3fapkxyot.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Extensions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k0n4wdcetg-tx83z6ho7l.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Metadata.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iaihmruvy7-z6035msxdy.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Primitives.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ns5g0gijw6-xqbpbwu9vz.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.TypeExtensions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ijic6tnk23-1kaq8volf4.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f4wyx1t3le-3d1gwadcaj.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Resources.Reader.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ym959s3sep-pdb0cwov9g.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Resources.ResourceManager.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zivznzm9ep-wfwt17t25p.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Resources.Writer.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p643yst6vl-rt5a291rko.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.CompilerServices.Unsafe.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w7vnofy4nh-gigtt0ldg1.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.CompilerServices.VisualC.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/lbapmuz12p-kaw15hufc0.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Extensions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kbop9ca5b4-7qypx0bvu1.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Handles.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xuj2eaav7h-k67jm10rbw.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.InteropServices.JavaScript.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x6l7w8vh34-uanr5ywdiz.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.InteropServices.RuntimeInformation.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/199ri7b6uw-fel5k50x7l.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.InteropServices.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ygxzkhmvcj-eoagj84dsy.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Intrinsics.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u8d1rlyxaj-7g62ykjls0.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Loader.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/uqbkx2w7r5-tp0shtj6gv.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Numerics.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l9mpdytfe7-nvsnsgm1il.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Formatters.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wj2h9uz5im-4t62p34f9u.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Json.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w46193pajr-8mh3k1xubv.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Primitives.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5vklr2rhzc-6heyz9oosd.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Xml.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xwtrtjxy5w-1oa8jl3amd.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oily32j1b5-xqvdvko8po.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yrprc7r9zg-jtnq7vre8d.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.AccessControl.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xl8jct8hsa-9fyr8onzdl.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Claims.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/cgl56q48ua-49z3p61zui.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Algorithms.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bzk3mvklf4-01efu89mjc.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Cng.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k83ghw0e32-m6kt5rkphi.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Csp.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/o1rz2re8a4-4j2304etti.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Encoding.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/lpp7c7jom0-rgf4gnhaju.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.OpenSsl.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/c4z844znau-wqi94vu5m0.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Primitives.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3owiob0otr-e4s9csihna.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.X509Certificates.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b06tcmc47q-z9o6jihhaw.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/489m8l4g7t-d93pggsupp.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Principal.Windows.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jroi4xhyv4-sa193kq3m2.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Principal.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hsxmqxw24g-z4ma9duddm.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.SecureString.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vowu26nxt0-rztf0whns2.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/81s6htribw-bnlcmxi1w6.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ServiceModel.Web.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/c79fun266a-drbdk7bquo.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ServiceProcess.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dvfzx7enik-7iff0d2lb6.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encoding.CodePages.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rfk8ukacy6-dc711vstge.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encoding.Extensions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ar000gwv14-3696nx7xrc.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encoding.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/2144j6gt7i-ksemyzm5ld.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encodings.Web.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/amy6c1v7af-x92ye0v3y1.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Json.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/63xijh3qra-dnj9z23s0g.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.RegularExpressions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tz9uciawbg-tomvzoqfcf.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Channels.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kb3qt14yb2-0g3k20op8c.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Overlapped.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/swwijaq0mj-55tewhp7kf.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.Dataflow.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/g6qs1odf3n-sce61xpslf.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.Extensions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/i50rbnd9da-548crbk151.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.Parallel.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/03bnaxp8pw-x86n4j91or.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jq91h45uco-07bttawl88.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Thread.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/khyttbh3aw-zt447d1n6v.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.ThreadPool.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hnszoi551l-r3c1h58f9w.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Timer.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vtlp2sf1wq-4z6mzh73ny.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/18szqtniot-pil3cjgvw5.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Transactions.Local.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/96pg0e161z-g6ni30uafv.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Transactions.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r52milnz3a-adv6hyw1vi.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ValueTuple.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/q5fo2ek16l-4yi0atwy17.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Web.HttpUtility.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r2ovqman6v-8uickrr2w7.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Web.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nxpuu7b9i1-idlgil0u1u.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Windows.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6j6k2vwo6l-jhjtvo31q0.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.Linq.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/crthk2z63b-pcqwh7wu97.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.ReaderWriter.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kxh9lij222-0x6beqi7zp.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.Serialization.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6vc32habv7-8luigpl137.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XDocument.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kdykqvob7j-rps120mzwr.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XPath.XDocument.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zsp3w2dy1h-nj6o6nhskf.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XPath.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rx1uk0rxdy-t7u25q5to4.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XmlDocument.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/h5x6wll9af-ig2qir1wep.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XmlSerializer.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vtfz6liee9-lnwczuoimm.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/t60nuhczep-00ls1afmp9.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ahca1etpnj-u25hol0de4.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WindowsBase.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pvgef1cedl-brg9pkj3je.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/mscorlib.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l8c1abxyiq-wuzd3f1y6v.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/netstandard.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zbebno3igu-05ksnw82w3.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.CoreLib.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/etssdwqupj-vr46os3pyt.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.js'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p35ssw82to-es3ekshrlb.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.js.map'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jhcrrc0uvv-rtblh4npr3.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.native.js'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6u3axqu108-aqhezbunpl.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.native.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dkctafev3b-d1pzlaz2ez.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.runtime.js'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xxmo02kvwx-ctf2q9h8m2.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.runtime.js.map'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jk7ur0nhtm-tjcz0u77k5.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/icudt_CJK.dat'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l90pdhl644-tptq2av103.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/icudt_EFIGS.dat'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/z4d5n0j65c-lfu7j35m59.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/icudt_no_CJK.dat'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b3h6s2rdzn-6gwt6lijwv.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Shared.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f40f7hhmpm-0eofereeop.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Shared.pdb'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/aprz43y33m-9ci5gwj2hh.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Client.Admin.Client.wasm'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ja3g98vaup-kc6q74lq96.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Client.Admin.Client.pdb'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/7ox5xzfjkx-cphfs8w2ny.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/blazor.boot.json'.
         Resolved 228 compressed assets for 228 candidate assets.
     4>CoreGenerateAssemblyInfo:
       正在跳过目标“CoreGenerateAssemblyInfo”，因为所有输出文件相对于输入文件而言都是最新的。
       AddEFCoreNpgsqlPgvector:
       正在跳过目标“AddEFCoreNpgsqlPgvector”，因为所有输出文件相对于输入文件而言都是最新的。
       _GenerateSourceLinkFile:
         Source Link 为空，则文件 "obj/Debug/net9.0/WhimLabAI.Infrastructure.sourcelink.json" 不存在。
       CoreCompile:
       正在跳过目标“CoreCompile”，因为所有输出文件相对于输入文件而言都是最新的。
       GenerateBuildDependencyFile:
       正在跳过目标“GenerateBuildDependencyFile”，因为所有输出文件相对于输入文件而言都是最新的。
       GenerateBuildRuntimeConfigurationFiles:
       正在跳过目标“GenerateBuildRuntimeConfigurationFiles”，因为所有输出文件相对于输入文件而言都是最新的。
       CopyFilesToOutputDirectory:
         WhimLabAI.Infrastructure -> /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/bin/Debug/net9.0/WhimLabAI.Infrastructure.dll
     4>已完成生成项目“/Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj”(默认目标)的操作。
     2>ResolveBuildCompressedStaticWebAssets:
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tzxjg6is5z-mhumzvqd1g.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/0wz98yz2xy-rnojub5shy.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bvwwiycvad-x0ueugt8gp.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/e85wydtwhk-md9yvkcqlf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/2onw7zqc0q-bvu82j4ad3.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/mgeagab6el-ptfrz3fits.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kqlbgdmef6-a48sropenz.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iljmgw0vwz-73oi73dvgk.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xxgbm0j00s-pm8mpy5cip.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6rohpx0jp4-4ni28tl690.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/phh8l6kdjc-zqe4lhsgbv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qxsmnl9be0-dxj5dhqc57.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ccdweloy0o-eyher82q7e.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/0c6hz2tvex-itm12vk377.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w3gfnu58ro-8ewlps0g9m.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/20x6c7lchl-yr6bnfroy5.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/peqh7izl3n-9nblf8ao5a.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k23jtmruy4-mjuqqf9ko8.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/82ugqqfvem-v66dtpac4v.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kfvy2x7ypi-apuz8nsfml.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ydli3xi7x2-1bmpr6w9dd.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l0pzjzuaf5-2lb9066vyq.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zis4b96xtm-8bt7as0i9i.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/junn8xb7k3-etpym877t9.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/o5v1nq94v3-g2w0sei4ut.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yez9tca4uc-1gmjxv0m8c.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/8e1yrfewp1-dxvjhqivbe.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/69dn8ctlxh-nx0z2f3xfv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ouirgkdwmp-5map5kd04b.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jvz7akeq8d-ul0xzjnwdm.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/763yfuq04o-nwxyu3e2hm.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/plhi4fcglh-l36scmr1xu.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6xj3air3k4-zrlhdwvckr.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/shgd0ocsue-358c2dzezi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/g1t9toi5e2-umcehv4hi0.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/1bgcmpc0jc-a8kopo0x7x.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/z41ga79u82-6tq6rr4bdi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x83d0ih4di-vtgtakmfrj.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w2njbnapj6-nanjlpvyw1.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/9zkhy2cfoh-zjb9sj3c45.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j383sqgg7e-xcgdsu1w4u.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tpc6bb39s9-qkbufwhni2.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/472l0jvuwa-ivrqkyor03.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/aggo97e8fk-aamf4ift6v.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vulfe9nibb-80sv9vo4du.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x1tkfmixpv-9gws8s7zmg.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/sueqpqhp6x-hev5t09xbg.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4vhkgnu1fx-wy3cb00pkv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pil2fefgg2-nt18748s0w.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wbt0vq8qnn-ykr6iyjchr.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/akubdugh7y-3h1likbfvx.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4jjwn1zyph-wt7n1r1ovk.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nce2t1qage-65adg6natn.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6n762dpqgw-dufaq3kp3z.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/spbx4l1nal-rxjrzzpp9g.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/azj660y99d-grj2h3kseq.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oggwzfetx2-cip8dbnu43.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ushg81yn3b-6hr3q9fx89.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/otoue458v7-k6p4pn9w0l.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/joe8l3b9g5-p61cj2koso.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/7jchfjht1c-fea7hw9xtf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gp0wj3dn0g-etd3dkcep2.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r0uxym1jwi-0lm42x51au.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j36lc6l7sa-ex6vy58iyk.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4ahezzndm6-s0qgw5psci.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gwhlabvgwk-zknkrutld3.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fgpeaobpo0-lu92ceoi50.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ll30i2jjyx-2ddk0zm05l.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ex6xkqi28v-3adg3wr0gn.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tyg7g06ixs-voyqcmzm7a.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x3p8e3g5tu-tuw7jnpdtf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xos9qf4smb-orwvw7tsnw.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b6wcoyk6wt-i2nxqnh8ia.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u27cyy023u-yj1m2auw1z.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hxo3j02wpb-9u6hm41m9t.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l28j0atxfq-670flx7nki.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vgcwtuln9j-2vqkac8ysr.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/phu1yyc87w-n515vmkk2p.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dhirh5u6y6-ogliygwa1r.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/1trtf5dn93-zk693pwck8.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5vajrisgrh-wxhr0xa5hb.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3sfd7ocphf-ipprcrczgj.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ai31glpnsq-okhe897m5z.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xzbepgdxtw-i93u5bq4fn.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/y8iz39xywh-x0sb683rhi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gp8yve0a37-o54lsqobzb.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5v9b9m10yt-tde8zuw0yw.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hem8237y0t-vx3bcge4ol.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/odc9oykyfr-yhtj6e0w69.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/mldr5c3vyn-quahjtap8r.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ob4a6ppm9p-jtaurxkbzi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hb3mb70q0t-3yfpgyrku1.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/9wd57hf2zj-8nnv647ull.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pi39r9o8rn-ir5j8vbyan.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vam5ahm2qy-1lxrwwxsho.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ksg0dx0ew5-gyxexdekj3.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jom0rd5wf5-tsgf6g1ztd.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x9k3m27ryv-j4sjofqyi5.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xyoxqz1sgs-jiaey0kmyh.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u83o8uvgfq-qfh40ih8l6.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j8cs0ucm7h-al6w1uowde.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xi5o3mwesh-d0g45p3x9u.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wlqmsea170-2zge8rv4ra.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nmvjopidz2-2lw1u6ymmp.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xvi6xv3v2k-1jvfownmci.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/df6ta7bnig-mv4cb7fqwu.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/v6faaw44t4-3djr1lshgb.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qinpfrc2zb-ub9sra6ubv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rtphd1bm3m-346n69ja1w.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f8pi37btuo-eupgag7vx5.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/y07xkid4jb-m0tberhw26.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ssgxii2rc9-7wmkfq1voo.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tvi9a4m6no-ee8vwc4vcc.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/28s1sbmc3k-h1hduhi84u.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iy0kt5q41u-y4g427qvfa.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dkhaa7yv2g-zv1ut64ban.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/v39f5kpyzm-lnozeoe9re.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/le8g1olvuv-omoxxcqo90.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p1r3skqu4x-t3a07csu2b.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ki35vzrtgv-5v95sh5c67.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/a391hzw274-ww3h8yu74p.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3028p113ne-345793p9fr.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oqnqqo1plh-odv41wuu54.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pz32pqjv0l-ksx7w94zni.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zr297s2wqn-b37svw0y4i.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bc3z1bqli0-9fasahbeiq.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/a9eau71e6a-qt5fpja9tg.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fgpe9cg5g3-i6kirq3og4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jzl52t66rj-497r8m9pev.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jttqbd8cll-k9az0iuxjb.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yu67w5y0cs-mnc7tnpegn.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/8b69lacdso-58q5onb7r6.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b7omoe7s6n-35ud51k85s.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qfjueerx11-ygkocwikl4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nc3ak4pddv-13d6e679le.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fk5xezacae-tnlqh325q4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/byc7oyc0yb-6pezgz31ve.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/586e2ph24s-1sfjh9emmw.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/t3f4z12pt1-o3fapkxyot.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k0n4wdcetg-tx83z6ho7l.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iaihmruvy7-z6035msxdy.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ns5g0gijw6-xqbpbwu9vz.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ijic6tnk23-1kaq8volf4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f4wyx1t3le-3d1gwadcaj.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ym959s3sep-pdb0cwov9g.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zivznzm9ep-wfwt17t25p.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p643yst6vl-rt5a291rko.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w7vnofy4nh-gigtt0ldg1.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/lbapmuz12p-kaw15hufc0.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kbop9ca5b4-7qypx0bvu1.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xuj2eaav7h-k67jm10rbw.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x6l7w8vh34-uanr5ywdiz.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/199ri7b6uw-fel5k50x7l.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ygxzkhmvcj-eoagj84dsy.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u8d1rlyxaj-7g62ykjls0.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/uqbkx2w7r5-tp0shtj6gv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l9mpdytfe7-nvsnsgm1il.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wj2h9uz5im-4t62p34f9u.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w46193pajr-8mh3k1xubv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5vklr2rhzc-6heyz9oosd.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xwtrtjxy5w-1oa8jl3amd.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oily32j1b5-xqvdvko8po.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yrprc7r9zg-jtnq7vre8d.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xl8jct8hsa-9fyr8onzdl.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/cgl56q48ua-49z3p61zui.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bzk3mvklf4-01efu89mjc.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k83ghw0e32-m6kt5rkphi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/o1rz2re8a4-4j2304etti.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/lpp7c7jom0-rgf4gnhaju.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/c4z844znau-wqi94vu5m0.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3owiob0otr-e4s9csihna.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b06tcmc47q-z9o6jihhaw.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/489m8l4g7t-d93pggsupp.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jroi4xhyv4-sa193kq3m2.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hsxmqxw24g-z4ma9duddm.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vowu26nxt0-rztf0whns2.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/81s6htribw-bnlcmxi1w6.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/c79fun266a-drbdk7bquo.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dvfzx7enik-7iff0d2lb6.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rfk8ukacy6-dc711vstge.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ar000gwv14-3696nx7xrc.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/2144j6gt7i-ksemyzm5ld.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/amy6c1v7af-x92ye0v3y1.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/63xijh3qra-dnj9z23s0g.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tz9uciawbg-tomvzoqfcf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kb3qt14yb2-0g3k20op8c.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/swwijaq0mj-55tewhp7kf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/g6qs1odf3n-sce61xpslf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/i50rbnd9da-548crbk151.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/03bnaxp8pw-x86n4j91or.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jq91h45uco-07bttawl88.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/khyttbh3aw-zt447d1n6v.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hnszoi551l-r3c1h58f9w.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vtlp2sf1wq-4z6mzh73ny.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/18szqtniot-pil3cjgvw5.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/96pg0e161z-g6ni30uafv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r52milnz3a-adv6hyw1vi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/q5fo2ek16l-4yi0atwy17.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r2ovqman6v-8uickrr2w7.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nxpuu7b9i1-idlgil0u1u.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6j6k2vwo6l-jhjtvo31q0.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/crthk2z63b-pcqwh7wu97.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kxh9lij222-0x6beqi7zp.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6vc32habv7-8luigpl137.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kdykqvob7j-rps120mzwr.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zsp3w2dy1h-nj6o6nhskf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rx1uk0rxdy-t7u25q5to4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/h5x6wll9af-ig2qir1wep.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vtfz6liee9-lnwczuoimm.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/t60nuhczep-00ls1afmp9.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ahca1etpnj-u25hol0de4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pvgef1cedl-brg9pkj3je.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l8c1abxyiq-wuzd3f1y6v.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zbebno3igu-05ksnw82w3.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/etssdwqupj-vr46os3pyt.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p35ssw82to-es3ekshrlb.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jhcrrc0uvv-rtblh4npr3.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6u3axqu108-aqhezbunpl.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dkctafev3b-d1pzlaz2ez.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xxmo02kvwx-ctf2q9h8m2.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jk7ur0nhtm-tjcz0u77k5.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l90pdhl644-tptq2av103.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/z4d5n0j65c-lfu7j35m59.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b3h6s2rdzn-6gwt6lijwv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f40f7hhmpm-0eofereeop.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/aprz43y33m-9ci5gwj2hh.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ja3g98vaup-kc6q74lq96.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/7ox5xzfjkx-cphfs8w2ny.gz
       _BuildCopyStaticWebAssetsPreserveNewest:
       正在部分生成目标“_BuildCopyStaticWebAssetsPreserveNewest”，因为某些输出文件相对于其输入文件而言已经过期。
         正在将文件从“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/blazor.boot.json”复制到“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/blazor.boot.json”。
         正在将文件从“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/7ox5xzfjkx-cphfs8w2ny.gz”复制到“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/blazor.boot.json.gz”。
       _CopyOutOfDateSourceItemsToOutputDirectory:
       正在部分生成目标“_CopyOutOfDateSourceItemsToOutputDirectory”，因为某些输出文件相对于其输入文件而言已经过期。
         正在将文件从“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/staticwebassets.build.endpoints.json”复制到“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/WhimLabAI.Client.Admin.Client.staticwebassets.endpoints.json”。
       CopyFilesToOutputDirectory:
         WhimLabAI.Client.Admin.Client -> /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/WhimLabAI.Client.Admin.Client.dll
       _BlazorCopyFilesToOutputDirectory:
         WhimLabAI.Client.Admin.Client (Blazor output) -> /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot
     2>已完成生成项目“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/WhimLabAI.Client.Admin.Client.csproj”(默认目标)的操作。
     1>GenerateTargetFrameworkMonikerAttribute:
       正在跳过目标“GenerateTargetFrameworkMonikerAttribute”，因为所有输出文件相对于输入文件而言都是最新的。
       CoreGenerateAssemblyInfo:
       正在跳过目标“CoreGenerateAssemblyInfo”，因为所有输出文件相对于输入文件而言都是最新的。
       _DiscoverMvcApplicationParts:
       正在跳过目标“_DiscoverMvcApplicationParts”，因为所有输出文件相对于输入文件而言都是最新的。
       AddEFCoreSqlServerNetTopologySuite:
       正在跳过目标“AddEFCoreSqlServerNetTopologySuite”，因为所有输出文件相对于输入文件而言都是最新的。
       _GenerateSourceLinkFile:
         Source Link 为空，则文件 "obj/Debug/net9.0/WhimLabAI.Client.Admin.sourcelink.json" 不存在。
       CoreCompile:
       正在跳过目标“CoreCompile”，因为所有输出文件相对于输入文件而言都是最新的。
       _CreateAppHost:
       正在跳过目标“_CreateAppHost”，因为所有输出文件相对于输入文件而言都是最新的。
       _ProcessScopedCssFiles:
       正在跳过目标“_ProcessScopedCssFiles”，因为它没有输出。
       _ProcessScopedCssFiles:
       正在跳过目标“_ProcessScopedCssFiles”，因为它没有输出。
       ResolveBuildCompressedStaticWebAssetsConfiguration:
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/03bnaxp8pw-x86n4j91or.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/0c6hz2tvex-itm12vk377.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/18szqtniot-pil3cjgvw5.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Transactions.Local.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/199ri7b6uw-fel5k50x7l.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.InteropServices.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/1bgcmpc0jc-a8kopo0x7x.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.JsonWebTokens.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/1trtf5dn93-zk693pwck8.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Drawing.Primitives.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/20x6c7lchl-yr6bnfroy5.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Binder.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/2144j6gt7i-ksemyzm5ld.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encodings.Web.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/28s1sbmc3k-h1hduhi84u.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.NetworkInformation.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/2onw7zqc0q-bvu82j4ad3.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Authorization.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3028p113ne-345793p9fr.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebClient.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3owiob0otr-e4s9csihna.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.X509Certificates.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3sfd7ocphf-ipprcrczgj.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Dynamic.Runtime.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/472l0jvuwa-ivrqkyor03.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Polly.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/489m8l4g7t-d93pggsupp.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Principal.Windows.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4ahezzndm6-s0qgw5psci.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Console.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4jjwn1zyph-wt7n1r1ovk.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Buffers.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4vhkgnu1fx-wy3cb00pkv.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.VisualBasic.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/586e2ph24s-1sfjh9emmw.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Emit.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5v9b9m10yt-tde8zuw0yw.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Globalization.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5vajrisgrh-wxhr0xa5hb.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Drawing.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5vklr2rhzc-6heyz9oosd.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Xml.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/63xijh3qra-dnj9z23s0g.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.RegularExpressions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/69dn8ctlxh-nx0z2f3xfv.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Localization.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6j6k2vwo6l-jhjtvo31q0.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.Linq.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6n762dpqgw-dufaq3kp3z.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.Immutable.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6rohpx0jp4-4ni28tl690.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6u3axqu108-aqhezbunpl.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.native.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6vc32habv7-8luigpl137.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XDocument.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6xj3air3k4-zrlhdwvckr.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Options.ConfigurationExtensions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/763yfuq04o-nwxyu3e2hm.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Logging.Abstractions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/7jchfjht1c-fea7hw9xtf.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.Primitives.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/7ox5xzfjkx-cphfs8w2ny.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/blazor.boot.json' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/81s6htribw-bnlcmxi1w6.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ServiceModel.Web.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/82ugqqfvem-v66dtpac4v.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/8b69lacdso-58q5onb7r6.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.Uri.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/8e1yrfewp1-dxvjhqivbe.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Http.Polly.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/96pg0e161z-g6ni30uafv.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Transactions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/9wd57hf2zj-8nnv647ull.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.DriveInfo.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/9zkhy2cfoh-zjb9sj3c45.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.JSInterop.WebAssembly.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/a391hzw274-ww3h8yu74p.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Sockets.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/a9eau71e6a-qt5fpja9tg.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/aggo97e8fk-aamf4ift6v.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Polly.Extensions.Http.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ahca1etpnj-u25hol0de4.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WindowsBase.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ai31glpnsq-okhe897m5z.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Formats.Asn1.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/akubdugh7y-3h1likbfvx.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.AppContext.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/amy6c1v7af-x92ye0v3y1.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Json.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/aprz43y33m-9ci5gwj2hh.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Client.Admin.Client.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ar000gwv14-3696nx7xrc.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encoding.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/azj660y99d-grj2h3kseq.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.Specialized.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b06tcmc47q-z9o6jihhaw.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b3h6s2rdzn-6gwt6lijwv.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Shared.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b6wcoyk6wt-i2nxqnh8ia.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.FileVersionInfo.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b7omoe7s6n-35ud51k85s.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.Xml.Linq.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bc3z1bqli0-9fasahbeiq.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebSockets.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bvwwiycvad-x0ueugt8gp.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/wwwroot/appsettings.Development.json' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/byc7oyc0yb-6pezgz31ve.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Emit.Lightweight.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bzk3mvklf4-01efu89mjc.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Cng.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/c4z844znau-wqi94vu5m0.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Primitives.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/c79fun266a-drbdk7bquo.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ServiceProcess.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ccdweloy0o-eyher82q7e.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Metadata.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/cgl56q48ua-49z3p61zui.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Algorithms.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/crthk2z63b-pcqwh7wu97.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.ReaderWriter.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/df6ta7bnig-mv4cb7fqwu.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.Queryable.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dhirh5u6y6-ogliygwa1r.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Tracing.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dkctafev3b-d1pzlaz2ez.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.runtime.js' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dkhaa7yv2g-zv1ut64ban.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Primitives.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dvfzx7enik-7iff0d2lb6.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encoding.CodePages.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/e85wydtwhk-md9yvkcqlf.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/blazor.webassembly.js' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/etssdwqupj-vr46os3pyt.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.js' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ex6xkqi28v-3adg3wr0gn.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Data.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f40f7hhmpm-0eofereeop.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Shared.pdb' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f4wyx1t3le-3d1gwadcaj.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Resources.Reader.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f8pi37btuo-eupgag7vx5.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Http.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fgpe9cg5g3-i6kirq3og4.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Numerics.Vectors.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fgpeaobpo0-lu92ceoi50.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Data.Common.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fk5xezacae-tnlqh325q4.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Emit.ILGeneration.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/g1t9toi5e2-umcehv4hi0.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.Abstractions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/g6qs1odf3n-sce61xpslf.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.Extensions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gp0wj3dn0g-etd3dkcep2.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.TypeConverter.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gp8yve0a37-o54lsqobzb.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Globalization.Extensions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gwhlabvgwk-zknkrutld3.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Core.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/h5x6wll9af-ig2qir1wep.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XmlSerializer.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hb3mb70q0t-3yfpgyrku1.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.AccessControl.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hem8237y0t-vx3bcge4ol.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.Brotli.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hnszoi551l-r3c1h58f9w.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Timer.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hsxmqxw24g-z4ma9duddm.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.SecureString.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hxo3j02wpb-9u6hm41m9t.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.StackTrace.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/i50rbnd9da-548crbk151.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.Parallel.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iaihmruvy7-z6035msxdy.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Primitives.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ijic6tnk23-1kaq8volf4.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iljmgw0vwz-73oi73dvgk.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Forms.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iy0kt5q41u-y4g427qvfa.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Ping.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j36lc6l7sa-ex6vy58iyk.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Configuration.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j383sqgg7e-xcgdsu1w4u.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/MudBlazor.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j8cs0ucm7h-al6w1uowde.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Pipes.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ja3g98vaup-kc6q74lq96.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Client.Admin.Client.pdb' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jhcrrc0uvv-rtblh4npr3.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.native.js' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jk7ur0nhtm-tjcz0u77k5.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/icudt_CJK.dat' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/joe8l3b9g5-p61cj2koso.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.EventBasedAsync.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jom0rd5wf5-tsgf6g1ztd.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.IsolatedStorage.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jq91h45uco-07bttawl88.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Thread.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jroi4xhyv4-sa193kq3m2.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Principal.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jttqbd8cll-k9az0iuxjb.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ObjectModel.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/junn8xb7k3-etpym877t9.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Physical.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jvz7akeq8d-ul0xzjnwdm.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Logging.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jzl52t66rj-497r8m9pev.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Numerics.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k0n4wdcetg-tx83z6ho7l.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Metadata.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k23jtmruy4-mjuqqf9ko8.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Json.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k83ghw0e32-m6kt5rkphi.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Csp.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kb3qt14yb2-0g3k20op8c.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Overlapped.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kbop9ca5b4-7qypx0bvu1.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Handles.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kdykqvob7j-rps120mzwr.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XPath.XDocument.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kfvy2x7ypi-apuz8nsfml.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/khyttbh3aw-zt447d1n6v.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.ThreadPool.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ki35vzrtgv-5v95sh5c67.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.ServicePoint.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kqlbgdmef6-a48sropenz.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Authorization.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ksg0dx0ew5-gyxexdekj3.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kxh9lij222-0x6beqi7zp.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.Serialization.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l0pzjzuaf5-2lb9066vyq.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Diagnostics.Abstractions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l28j0atxfq-670flx7nki.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.TextWriterTraceListener.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l8c1abxyiq-wuzd3f1y6v.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/netstandard.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l90pdhl644-tptq2av103.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/icudt_EFIGS.dat' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l9mpdytfe7-nvsnsgm1il.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Formatters.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/lbapmuz12p-kaw15hufc0.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Extensions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/le8g1olvuv-omoxxcqo90.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Requests.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ll30i2jjyx-2ddk0zm05l.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Data.DataSetExtensions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/lpp7c7jom0-rgf4gnhaju.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.OpenSsl.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/mgeagab6el-ptfrz3fits.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/mldr5c3vyn-quahjtap8r.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.ZipFile.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nc3ak4pddv-13d6e679le.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.DispatchProxy.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nce2t1qage-65adg6natn.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.Concurrent.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nmvjopidz2-2lw1u6ymmp.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.Expressions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ns5g0gijw6-xqbpbwu9vz.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.TypeExtensions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nxpuu7b9i1-idlgil0u1u.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Windows.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/o1rz2re8a4-4j2304etti.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Encoding.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/o5v1nq94v3-g2w0sei4ut.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.FileSystemGlobbing.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ob4a6ppm9p-jtaurxkbzi.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/odc9oykyfr-yhtj6e0w69.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.FileSystem.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oggwzfetx2-cip8dbnu43.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oily32j1b5-xqvdvko8po.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oqnqqo1plh-odv41wuu54.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebHeaderCollection.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/otoue458v7-k6p4pn9w0l.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.DataAnnotations.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ouirgkdwmp-5map5kd04b.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Localization.Abstractions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p1r3skqu4x-t3a07csu2b.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Security.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p35ssw82to-es3ekshrlb.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.js.map' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p643yst6vl-rt5a291rko.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.CompilerServices.Unsafe.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/peqh7izl3n-9nblf8ao5a.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/phh8l6kdjc-zqe4lhsgbv.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Cryptography.Internal.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/phu1yyc87w-n515vmkk2p.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.TraceSource.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pi39r9o8rn-ir5j8vbyan.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.Primitives.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pil2fefgg2-nt18748s0w.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Win32.Primitives.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/plhi4fcglh-l36scmr1xu.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Options.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pvgef1cedl-brg9pkj3je.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/mscorlib.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pz32pqjv0l-ksx7w94zni.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebProxy.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/q5fo2ek16l-4yi0atwy17.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Web.HttpUtility.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qfjueerx11-ygkocwikl4.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.Xml.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qinpfrc2zb-ub9sra6ubv.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Memory.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qxsmnl9be0-dxj5dhqc57.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Cryptography.KeyDerivation.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r0uxym1jwi-0lm42x51au.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r2ovqman6v-8uickrr2w7.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Web.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r52milnz3a-adv6hyw1vi.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ValueTuple.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rfk8ukacy6-dc711vstge.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encoding.Extensions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rtphd1bm3m-346n69ja1w.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Http.Json.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rx1uk0rxdy-t7u25q5to4.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XmlDocument.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/shgd0ocsue-358c2dzezi.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Primitives.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/spbx4l1nal-rxjrzzpp9g.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.NonGeneric.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ssgxii2rc9-7wmkfq1voo.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Mail.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/sueqpqhp6x-hev5t09xbg.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.VisualBasic.Core.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/swwijaq0mj-55tewhp7kf.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.Dataflow.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/t3f4z12pt1-o3fapkxyot.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Extensions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/t60nuhczep-00ls1afmp9.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tpc6bb39s9-qkbufwhni2.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Newtonsoft.Json.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tvi9a4m6no-ee8vwc4vcc.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.NameResolution.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tyg7g06ixs-voyqcmzm7a.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Contracts.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tz9uciawbg-tomvzoqfcf.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Channels.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u27cyy023u-yj1m2auw1z.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Process.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u83o8uvgfq-qfh40ih8l6.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Pipes.AccessControl.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u8d1rlyxaj-7g62ykjls0.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Loader.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/uqbkx2w7r5-tp0shtj6gv.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Numerics.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ushg81yn3b-6hr3q9fx89.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.Annotations.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/v39f5kpyzm-lnozeoe9re.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Quic.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/v6faaw44t4-3djr1lshgb.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vam5ahm2qy-1lxrwwxsho.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.Watcher.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vgcwtuln9j-2vqkac8ysr.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Tools.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vowu26nxt0-rztf0whns2.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vtfz6liee9-lnwczuoimm.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vtlp2sf1wq-4z6mzh73ny.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vulfe9nibb-80sv9vo4du.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IdentityModel.Tokens.Jwt.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w2njbnapj6-nanjlpvyw1.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.JSInterop.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w3gfnu58ro-8ewlps0g9m.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Abstractions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w46193pajr-8mh3k1xubv.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Primitives.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w7vnofy4nh-gigtt0ldg1.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.CompilerServices.VisualC.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wbt0vq8qnn-ykr6iyjchr.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Win32.Registry.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wj2h9uz5im-4t62p34f9u.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Json.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wlqmsea170-2zge8rv4ra.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x1tkfmixpv-9gws8s7zmg.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.CSharp.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x3p8e3g5tu-tuw7jnpdtf.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Debug.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x6l7w8vh34-uanr5ywdiz.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.InteropServices.RuntimeInformation.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x83d0ih4di-vtgtakmfrj.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.Tokens.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x9k3m27ryv-j4sjofqyi5.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.MemoryMappedFiles.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xi5o3mwesh-d0g45p3x9u.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.UnmanagedMemoryStream.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xl8jct8hsa-9fyr8onzdl.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Claims.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xos9qf4smb-orwvw7tsnw.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.DiagnosticSource.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xuj2eaav7h-k67jm10rbw.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.InteropServices.JavaScript.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xvi6xv3v2k-1jvfownmci.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.Parallel.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xwtrtjxy5w-1oa8jl3amd.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xxgbm0j00s-pm8mpy5cip.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Web.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xxmo02kvwx-ctf2q9h8m2.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.runtime.js.map' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xyoxqz1sgs-jiaey0kmyh.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Pipelines.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xzbepgdxtw-i93u5bq4fn.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Formats.Tar.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/y07xkid4jb-m0tberhw26.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.HttpListener.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/y8iz39xywh-x0sb683rhi.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Globalization.Calendars.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ydli3xi7x2-1bmpr6w9dd.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Diagnostics.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yez9tca4uc-1gmjxv0m8c.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Http.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ygxzkhmvcj-eoagj84dsy.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Intrinsics.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ym959s3sep-pdb0cwov9g.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Resources.ResourceManager.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yrprc7r9zg-jtnq7vre8d.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.AccessControl.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yu67w5y0cs-mnc7tnpegn.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.DataContractSerialization.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/z41ga79u82-6tq6rr4bdi.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.Logging.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/z4d5n0j65c-lfu7j35m59.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/icudt_no_CJK.dat' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zbebno3igu-05ksnw82w3.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.CoreLib.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zis4b96xtm-8bt7as0i9i.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zivznzm9ep-wfwt17t25p.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Resources.Writer.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zr297s2wqn-b37svw0y4i.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebSockets.Client.wasm' was detected as already compressed with format 'gzip'.
         The asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zsp3w2dy1h-nj6o6nhskf.gz' with related asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XPath.wasm' was detected as already compressed with format 'gzip'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/obj/Debug/net9.0/compressed/tzxjg6is5z-mhumzvqd1g.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/packages/mudblazor/7.15.0/staticwebassets/MudBlazor.min.css'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/obj/Debug/net9.0/compressed/0wz98yz2xy-rnojub5shy.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/packages/mudblazor/7.15.0/staticwebassets/MudBlazor.min.js'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/obj/Debug/net9.0/compressed/zdix5nvhx8-otns5idd7x.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/wwwroot/appsettings.json'.
         Accepted compressed asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/obj/Debug/net9.0/compressed/1znlgmn87b-2jeq8efc6q.gz' for '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/wwwroot/favicon.ico'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/blazor.boot.json' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/blazor.webassembly.js' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.js' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.js.map' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.native.js' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.native.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.runtime.js' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/dotnet.runtime.js.map' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/icudt_CJK.dat' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/icudt_EFIGS.dat' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/icudt_no_CJK.dat' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Authorization.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Authorization.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Forms.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.Web.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Cryptography.Internal.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Cryptography.KeyDerivation.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.AspNetCore.Metadata.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.CSharp.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Abstractions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Binder.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.Json.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Configuration.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.DependencyInjection.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Diagnostics.Abstractions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Diagnostics.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.FileProviders.Physical.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.FileSystemGlobbing.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Http.Polly.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Http.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Localization.Abstractions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Localization.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Logging.Abstractions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Logging.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Options.ConfigurationExtensions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Options.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Extensions.Primitives.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.Abstractions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.JsonWebTokens.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.Logging.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.IdentityModel.Tokens.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.JSInterop.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.JSInterop.WebAssembly.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.VisualBasic.Core.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.VisualBasic.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Win32.Primitives.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Microsoft.Win32.Registry.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/mscorlib.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/MudBlazor.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/netstandard.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Newtonsoft.Json.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Polly.Extensions.Http.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/Polly.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.AppContext.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Buffers.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.Concurrent.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.Immutable.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.NonGeneric.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.Specialized.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Collections.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.Annotations.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.DataAnnotations.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.EventBasedAsync.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.Primitives.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.TypeConverter.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ComponentModel.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Configuration.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Console.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Core.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Data.Common.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Data.DataSetExtensions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Data.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Contracts.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Debug.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.DiagnosticSource.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.FileVersionInfo.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Process.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.StackTrace.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.TextWriterTraceListener.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Tools.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.TraceSource.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Diagnostics.Tracing.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Drawing.Primitives.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Drawing.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Dynamic.Runtime.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Formats.Asn1.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Formats.Tar.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Globalization.Calendars.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Globalization.Extensions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Globalization.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IdentityModel.Tokens.Jwt.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.Brotli.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.FileSystem.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Compression.ZipFile.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.AccessControl.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.DriveInfo.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.Primitives.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.FileSystem.Watcher.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.IsolatedStorage.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.MemoryMappedFiles.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Pipelines.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Pipes.AccessControl.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.Pipes.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.UnmanagedMemoryStream.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.IO.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.Expressions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.Parallel.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.Queryable.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Linq.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Memory.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Http.Json.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Http.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.HttpListener.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Mail.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.NameResolution.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.NetworkInformation.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Ping.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Primitives.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Quic.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Requests.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Security.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.ServicePoint.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.Sockets.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebClient.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebHeaderCollection.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebProxy.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebSockets.Client.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Net.WebSockets.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Numerics.Vectors.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Numerics.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ObjectModel.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.CoreLib.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.DataContractSerialization.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.Uri.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.Xml.Linq.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Private.Xml.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.DispatchProxy.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Emit.ILGeneration.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Emit.Lightweight.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Emit.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Extensions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Metadata.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.Primitives.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.TypeExtensions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Reflection.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Resources.Reader.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Resources.ResourceManager.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Resources.Writer.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.CompilerServices.Unsafe.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.CompilerServices.VisualC.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Extensions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Handles.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.InteropServices.JavaScript.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.InteropServices.RuntimeInformation.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.InteropServices.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Intrinsics.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Loader.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Numerics.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Formatters.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Json.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Primitives.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.Serialization.Xml.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Runtime.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.AccessControl.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Claims.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Algorithms.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Cng.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Csp.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Encoding.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.OpenSsl.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.Primitives.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Cryptography.X509Certificates.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Principal.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.Principal.Windows.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.SecureString.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Security.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ServiceModel.Web.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ServiceProcess.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encoding.CodePages.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encoding.Extensions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encoding.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Encodings.Web.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.Json.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Text.RegularExpressions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Channels.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Overlapped.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.Dataflow.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.Extensions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.Parallel.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Tasks.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Thread.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.ThreadPool.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.Timer.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Threading.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Transactions.Local.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Transactions.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.ValueTuple.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Web.HttpUtility.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Web.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Windows.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.Linq.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.ReaderWriter.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.Serialization.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XDocument.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XmlDocument.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XmlSerializer.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XPath.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/System.Xml.XPath.XDocument.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Client.Admin.Client.pdb' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Client.Admin.Client.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Shared.pdb' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WhimLabAI.Shared.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/bin/Debug/net9.0/wwwroot/_framework/WindowsBase.wasm' because it was already resolved with format 'gzip'.
         Ignoring asset '/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/wwwroot/appsettings.Development.json' because it was already resolved with format 'gzip'.
         Resolved 4 compressed assets for 230 candidate assets.
       ResolveBuildCompressedStaticWebAssets:
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/03bnaxp8pw-x86n4j91or.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/0c6hz2tvex-itm12vk377.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/18szqtniot-pil3cjgvw5.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/199ri7b6uw-fel5k50x7l.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/1bgcmpc0jc-a8kopo0x7x.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/1trtf5dn93-zk693pwck8.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/20x6c7lchl-yr6bnfroy5.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/2144j6gt7i-ksemyzm5ld.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/28s1sbmc3k-h1hduhi84u.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/2onw7zqc0q-bvu82j4ad3.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3028p113ne-345793p9fr.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3owiob0otr-e4s9csihna.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/3sfd7ocphf-ipprcrczgj.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/472l0jvuwa-ivrqkyor03.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/489m8l4g7t-d93pggsupp.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4ahezzndm6-s0qgw5psci.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4jjwn1zyph-wt7n1r1ovk.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/4vhkgnu1fx-wy3cb00pkv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/586e2ph24s-1sfjh9emmw.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5v9b9m10yt-tde8zuw0yw.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5vajrisgrh-wxhr0xa5hb.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/5vklr2rhzc-6heyz9oosd.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/63xijh3qra-dnj9z23s0g.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/69dn8ctlxh-nx0z2f3xfv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6j6k2vwo6l-jhjtvo31q0.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6n762dpqgw-dufaq3kp3z.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6rohpx0jp4-4ni28tl690.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6u3axqu108-aqhezbunpl.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6vc32habv7-8luigpl137.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/6xj3air3k4-zrlhdwvckr.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/763yfuq04o-nwxyu3e2hm.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/7jchfjht1c-fea7hw9xtf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/7ox5xzfjkx-cphfs8w2ny.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/81s6htribw-bnlcmxi1w6.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/82ugqqfvem-v66dtpac4v.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/8b69lacdso-58q5onb7r6.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/8e1yrfewp1-dxvjhqivbe.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/96pg0e161z-g6ni30uafv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/9wd57hf2zj-8nnv647ull.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/9zkhy2cfoh-zjb9sj3c45.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/a391hzw274-ww3h8yu74p.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/a9eau71e6a-qt5fpja9tg.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/aggo97e8fk-aamf4ift6v.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ahca1etpnj-u25hol0de4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ai31glpnsq-okhe897m5z.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/akubdugh7y-3h1likbfvx.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/amy6c1v7af-x92ye0v3y1.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/aprz43y33m-9ci5gwj2hh.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ar000gwv14-3696nx7xrc.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/azj660y99d-grj2h3kseq.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b06tcmc47q-z9o6jihhaw.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b3h6s2rdzn-6gwt6lijwv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b6wcoyk6wt-i2nxqnh8ia.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/b7omoe7s6n-35ud51k85s.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bc3z1bqli0-9fasahbeiq.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bvwwiycvad-x0ueugt8gp.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/byc7oyc0yb-6pezgz31ve.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/bzk3mvklf4-01efu89mjc.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/c4z844znau-wqi94vu5m0.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/c79fun266a-drbdk7bquo.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ccdweloy0o-eyher82q7e.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/cgl56q48ua-49z3p61zui.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/crthk2z63b-pcqwh7wu97.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/df6ta7bnig-mv4cb7fqwu.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dhirh5u6y6-ogliygwa1r.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dkctafev3b-d1pzlaz2ez.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dkhaa7yv2g-zv1ut64ban.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/dvfzx7enik-7iff0d2lb6.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/e85wydtwhk-md9yvkcqlf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/etssdwqupj-vr46os3pyt.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ex6xkqi28v-3adg3wr0gn.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f40f7hhmpm-0eofereeop.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f4wyx1t3le-3d1gwadcaj.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/f8pi37btuo-eupgag7vx5.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fgpe9cg5g3-i6kirq3og4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fgpeaobpo0-lu92ceoi50.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/fk5xezacae-tnlqh325q4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/g1t9toi5e2-umcehv4hi0.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/g6qs1odf3n-sce61xpslf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gp0wj3dn0g-etd3dkcep2.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gp8yve0a37-o54lsqobzb.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/gwhlabvgwk-zknkrutld3.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/h5x6wll9af-ig2qir1wep.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hb3mb70q0t-3yfpgyrku1.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hem8237y0t-vx3bcge4ol.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hnszoi551l-r3c1h58f9w.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hsxmqxw24g-z4ma9duddm.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/hxo3j02wpb-9u6hm41m9t.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/i50rbnd9da-548crbk151.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iaihmruvy7-z6035msxdy.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ijic6tnk23-1kaq8volf4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iljmgw0vwz-73oi73dvgk.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/iy0kt5q41u-y4g427qvfa.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j36lc6l7sa-ex6vy58iyk.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j383sqgg7e-xcgdsu1w4u.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/j8cs0ucm7h-al6w1uowde.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ja3g98vaup-kc6q74lq96.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jhcrrc0uvv-rtblh4npr3.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jk7ur0nhtm-tjcz0u77k5.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/joe8l3b9g5-p61cj2koso.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jom0rd5wf5-tsgf6g1ztd.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jq91h45uco-07bttawl88.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jroi4xhyv4-sa193kq3m2.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jttqbd8cll-k9az0iuxjb.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/junn8xb7k3-etpym877t9.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jvz7akeq8d-ul0xzjnwdm.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/jzl52t66rj-497r8m9pev.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k0n4wdcetg-tx83z6ho7l.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k23jtmruy4-mjuqqf9ko8.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/k83ghw0e32-m6kt5rkphi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kb3qt14yb2-0g3k20op8c.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kbop9ca5b4-7qypx0bvu1.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kdykqvob7j-rps120mzwr.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kfvy2x7ypi-apuz8nsfml.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/khyttbh3aw-zt447d1n6v.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ki35vzrtgv-5v95sh5c67.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kqlbgdmef6-a48sropenz.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ksg0dx0ew5-gyxexdekj3.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/kxh9lij222-0x6beqi7zp.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l0pzjzuaf5-2lb9066vyq.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l28j0atxfq-670flx7nki.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l8c1abxyiq-wuzd3f1y6v.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l90pdhl644-tptq2av103.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/l9mpdytfe7-nvsnsgm1il.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/lbapmuz12p-kaw15hufc0.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/le8g1olvuv-omoxxcqo90.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ll30i2jjyx-2ddk0zm05l.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/lpp7c7jom0-rgf4gnhaju.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/mgeagab6el-ptfrz3fits.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/mldr5c3vyn-quahjtap8r.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nc3ak4pddv-13d6e679le.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nce2t1qage-65adg6natn.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nmvjopidz2-2lw1u6ymmp.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ns5g0gijw6-xqbpbwu9vz.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/nxpuu7b9i1-idlgil0u1u.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/o1rz2re8a4-4j2304etti.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/o5v1nq94v3-g2w0sei4ut.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ob4a6ppm9p-jtaurxkbzi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/odc9oykyfr-yhtj6e0w69.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oggwzfetx2-cip8dbnu43.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oily32j1b5-xqvdvko8po.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/oqnqqo1plh-odv41wuu54.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/otoue458v7-k6p4pn9w0l.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ouirgkdwmp-5map5kd04b.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p1r3skqu4x-t3a07csu2b.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p35ssw82to-es3ekshrlb.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/p643yst6vl-rt5a291rko.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/peqh7izl3n-9nblf8ao5a.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/phh8l6kdjc-zqe4lhsgbv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/phu1yyc87w-n515vmkk2p.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pi39r9o8rn-ir5j8vbyan.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pil2fefgg2-nt18748s0w.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/plhi4fcglh-l36scmr1xu.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pvgef1cedl-brg9pkj3je.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/pz32pqjv0l-ksx7w94zni.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/q5fo2ek16l-4yi0atwy17.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qfjueerx11-ygkocwikl4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qinpfrc2zb-ub9sra6ubv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/qxsmnl9be0-dxj5dhqc57.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r0uxym1jwi-0lm42x51au.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r2ovqman6v-8uickrr2w7.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/r52milnz3a-adv6hyw1vi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rfk8ukacy6-dc711vstge.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rtphd1bm3m-346n69ja1w.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/rx1uk0rxdy-t7u25q5to4.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/shgd0ocsue-358c2dzezi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/spbx4l1nal-rxjrzzpp9g.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ssgxii2rc9-7wmkfq1voo.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/sueqpqhp6x-hev5t09xbg.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/swwijaq0mj-55tewhp7kf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/t3f4z12pt1-o3fapkxyot.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/t60nuhczep-00ls1afmp9.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tpc6bb39s9-qkbufwhni2.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tvi9a4m6no-ee8vwc4vcc.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tyg7g06ixs-voyqcmzm7a.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/tz9uciawbg-tomvzoqfcf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u27cyy023u-yj1m2auw1z.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u83o8uvgfq-qfh40ih8l6.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/u8d1rlyxaj-7g62ykjls0.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/uqbkx2w7r5-tp0shtj6gv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ushg81yn3b-6hr3q9fx89.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/v39f5kpyzm-lnozeoe9re.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/v6faaw44t4-3djr1lshgb.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vam5ahm2qy-1lxrwwxsho.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vgcwtuln9j-2vqkac8ysr.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vowu26nxt0-rztf0whns2.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vtfz6liee9-lnwczuoimm.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vtlp2sf1wq-4z6mzh73ny.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/vulfe9nibb-80sv9vo4du.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w2njbnapj6-nanjlpvyw1.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w3gfnu58ro-8ewlps0g9m.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w46193pajr-8mh3k1xubv.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/w7vnofy4nh-gigtt0ldg1.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wbt0vq8qnn-ykr6iyjchr.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wj2h9uz5im-4t62p34f9u.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/wlqmsea170-2zge8rv4ra.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x1tkfmixpv-9gws8s7zmg.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x3p8e3g5tu-tuw7jnpdtf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x6l7w8vh34-uanr5ywdiz.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x83d0ih4di-vtgtakmfrj.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/x9k3m27ryv-j4sjofqyi5.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xi5o3mwesh-d0g45p3x9u.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xl8jct8hsa-9fyr8onzdl.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xos9qf4smb-orwvw7tsnw.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xuj2eaav7h-k67jm10rbw.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xvi6xv3v2k-1jvfownmci.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xwtrtjxy5w-1oa8jl3amd.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xxgbm0j00s-pm8mpy5cip.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xxmo02kvwx-ctf2q9h8m2.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xyoxqz1sgs-jiaey0kmyh.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/xzbepgdxtw-i93u5bq4fn.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/y07xkid4jb-m0tberhw26.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/y8iz39xywh-x0sb683rhi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ydli3xi7x2-1bmpr6w9dd.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yez9tca4uc-1gmjxv0m8c.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ygxzkhmvcj-eoagj84dsy.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/ym959s3sep-pdb0cwov9g.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yrprc7r9zg-jtnq7vre8d.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/yu67w5y0cs-mnc7tnpegn.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/z41ga79u82-6tq6rr4bdi.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/z4d5n0j65c-lfu7j35m59.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zbebno3igu-05ksnw82w3.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zis4b96xtm-8bt7as0i9i.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zivznzm9ep-wfwt17t25p.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zr297s2wqn-b37svw0y4i.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/compressed/zsp3w2dy1h-nj6o6nhskf.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/obj/Debug/net9.0/compressed/zdix5nvhx8-otns5idd7x.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/obj/Debug/net9.0/compressed/0wz98yz2xy-rnojub5shy.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/obj/Debug/net9.0/compressed/1znlgmn87b-2jeq8efc6q.gz
         Processing compressed asset: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/obj/Debug/net9.0/compressed/tzxjg6is5z-mhumzvqd1g.gz
       _BuildCopyStaticWebAssetsPreserveNewest:
       正在跳过目标“_BuildCopyStaticWebAssetsPreserveNewest”，因为它没有输出。
       _CopyOutOfDateSourceItemsToOutputDirectory:
       正在部分生成目标“_CopyOutOfDateSourceItemsToOutputDirectory”，因为某些输出文件相对于其输入文件而言已经过期。
         正在将文件从“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.Client/obj/Debug/net9.0/staticwebassets.build.endpoints.json”复制到“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/bin/Debug/net9.0/WhimLabAI.Client.Admin.Client.staticwebassets.endpoints.json”。
         正在将文件从“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/obj/Debug/net9.0/staticwebassets.build.endpoints.json”复制到“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/bin/Debug/net9.0/WhimLabAI.Client.Admin.staticwebassets.endpoints.json”。
       GenerateBuildDependencyFile:
       正在跳过目标“GenerateBuildDependencyFile”，因为所有输出文件相对于输入文件而言都是最新的。
       GenerateBuildRuntimeConfigurationFiles:
       正在跳过目标“GenerateBuildRuntimeConfigurationFiles”，因为所有输出文件相对于输入文件而言都是最新的。
       CopyFilesToOutputDirectory:
         WhimLabAI.Client.Admin -> /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/bin/Debug/net9.0/WhimLabAI.Client.Admin.dll
     1>已完成生成项目“/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin.csproj”(默认目标)的操作。

已成功生成。
    0 个警告
    0 个错误

已用时间 00:00:03.51
