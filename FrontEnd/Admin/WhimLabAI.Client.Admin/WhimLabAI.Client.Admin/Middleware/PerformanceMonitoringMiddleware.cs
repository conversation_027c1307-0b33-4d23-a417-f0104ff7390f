using System.Diagnostics;
using WhimLabAI.Client.Admin.Services;

namespace WhimLabAI.Client.Admin.Middleware;

/// <summary>
/// 性能监控中间件
/// 监控HTTP请求的响应时间和性能指标
/// </summary>
public class PerformanceMonitoringMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PerformanceMonitoringMiddleware> _logger;

    public PerformanceMonitoringMiddleware(
        RequestDelegate next,
        ILogger<PerformanceMonitoringMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, IAdminPerformanceMonitor performanceMonitor)
    {
        var stopwatch = Stopwatch.StartNew();
        var requestPath = context.Request.Path.Value ?? "unknown";
        var method = context.Request.Method;
        var operationName = $"{method} {requestPath}";

        try
        {
            // 记录请求开始
            _logger.LogDebug("Request started: {Method} {Path}", method, requestPath);

            // 执行下一个中间件
            await _next(context);

            stopwatch.Stop();

            // 记录性能数据
            performanceMonitor.RecordOperationTime(operationName, stopwatch.Elapsed);

            // 记录慢请求
            if (stopwatch.ElapsedMilliseconds > 1000)
            {
                _logger.LogWarning("Slow request detected: {Method} {Path} took {ElapsedMs}ms, Status: {StatusCode}",
                    method, requestPath, stopwatch.ElapsedMilliseconds, context.Response.StatusCode);
            }
            else
            {
                _logger.LogDebug("Request completed: {Method} {Path} in {ElapsedMs}ms, Status: {StatusCode}",
                    method, requestPath, stopwatch.ElapsedMilliseconds, context.Response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            // 记录错误请求的性能数据
            performanceMonitor.RecordOperationTime($"{operationName}_ERROR", stopwatch.Elapsed);
            
            _logger.LogError(ex, "Request failed: {Method} {Path} after {ElapsedMs}ms",
                method, requestPath, stopwatch.ElapsedMilliseconds);
            
            throw;
        }
    }
}

/// <summary>
/// 性能监控中间件扩展
/// </summary>
public static class PerformanceMonitoringMiddlewareExtensions
{
    /// <summary>
    /// 添加性能监控中间件
    /// </summary>
    public static IApplicationBuilder UsePerformanceMonitoring(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<PerformanceMonitoringMiddleware>();
    }
}

/// <summary>
/// 请求日志中间件
/// 提供详细的请求日志记录
/// </summary>
public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    public RequestLoggingMiddleware(
        RequestDelegate next,
        ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var requestId = Guid.NewGuid().ToString("N")[..8];
        var request = context.Request;
        
        // 记录请求信息
        _logger.LogInformation("Request {RequestId}: {Method} {Path}{QueryString} from {RemoteIp}",
            requestId,
            request.Method,
            request.Path,
            request.QueryString,
            context.Connection.RemoteIpAddress?.ToString() ?? "unknown");

        // 记录请求头（仅在Debug级别）
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            foreach (var header in request.Headers.Where(h => !IsSecuritySensitiveHeader(h.Key)))
            {
                _logger.LogDebug("Request {RequestId} Header: {HeaderName} = {HeaderValue}",
                    requestId, header.Key, string.Join(", ", header.Value.ToArray()));
            }
        }

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await _next(context);
            
            stopwatch.Stop();
            
            // 记录响应信息
            _logger.LogInformation("Response {RequestId}: {StatusCode} in {ElapsedMs}ms, Size: {ResponseSize} bytes",
                requestId,
                context.Response.StatusCode,
                stopwatch.ElapsedMilliseconds,
                context.Response.ContentLength ?? 0);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            _logger.LogError(ex, "Request {RequestId} failed after {ElapsedMs}ms: {ErrorMessage}",
                requestId, stopwatch.ElapsedMilliseconds, ex.Message);
            
            throw;
        }
    }

    /// <summary>
    /// 检查是否为敏感的安全头
    /// </summary>
    private static bool IsSecuritySensitiveHeader(string headerName)
    {
        var sensitiveHeaders = new[]
        {
            "Authorization",
            "Cookie",
            "X-API-Key",
            "X-Auth-Token"
        };

        return sensitiveHeaders.Contains(headerName, StringComparer.OrdinalIgnoreCase);
    }
}

/// <summary>
/// 请求日志中间件扩展
/// </summary>
public static class RequestLoggingMiddlewareExtensions
{
    /// <summary>
    /// 添加请求日志中间件
    /// </summary>
    public static IApplicationBuilder UseRequestLogging(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<RequestLoggingMiddleware>();
    }
}

/// <summary>
/// 健康检查中间件
/// 提供简单的健康检查端点
/// </summary>
public class HealthCheckMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<HealthCheckMiddleware> _logger;

    public HealthCheckMiddleware(
        RequestDelegate next,
        ILogger<HealthCheckMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, IAdminPerformanceMonitor performanceMonitor)
    {
        if (context.Request.Path.StartsWithSegments("/health"))
        {
            var stats = performanceMonitor.GetPerformanceStats();
            
            var healthInfo = new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Uptime = stats.Uptime.ToString(@"dd\.hh\:mm\:ss"),
                TotalOperations = stats.TotalOperations,
                CacheHitRate = $"{stats.OverallCacheHitRate:P2}",
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                MachineName = Environment.MachineName,
                ProcessId = Environment.ProcessId,
                WorkingSet = $"{Environment.WorkingSet / 1024 / 1024} MB"
            };

            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(healthInfo, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            }));
            
            return;
        }

        await _next(context);
    }
}

/// <summary>
/// 健康检查中间件扩展
/// </summary>
public static class HealthCheckMiddlewareExtensions
{
    /// <summary>
    /// 添加健康检查中间件
    /// </summary>
    public static IApplicationBuilder UseHealthCheck(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<HealthCheckMiddleware>();
    }
}
