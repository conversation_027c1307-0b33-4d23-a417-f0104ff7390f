using MudBlazor.Services;
using WhimLabAI.Client.Admin.Client.Pages;
using WhimLabAI.Client.Admin.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using WhimLabAI.Client.Admin.Services;
using WhimLabAI.Client.Admin.Client.Services;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Infrastructure.Data.Repositories;

var builder = WebApplication.CreateBuilder(args);

// 配置文件加载顺序
builder.Configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddJsonFile("appsettings.QuickDev.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();

// Add MudBlazor services
builder.Services.AddMudServices();

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents();

// Add Authentication
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    var jwtSettings = builder.Configuration.GetSection("Jwt");
    var secretKey = jwtSettings["SecretKey"] ?? "your-256-bit-secret-key-for-development-only-must-be-at-least-32-characters";

    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = jwtSettings["Issuer"] ?? "WhimLabAI",
        ValidAudience = jwtSettings["Audience"] ?? "WhimLabAI",
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
        ClockSkew = TimeSpan.Zero,
        RequireSignedTokens = true
    };

    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
            {
                context.Response.Headers.Append("Token-Expired", "true");
            }
            return Task.CompletedTask;
        }
    };
});

// Add Authorization
builder.Services.AddAuthorizationCore();
builder.Services.AddCascadingAuthenticationState();

// Add HttpContextAccessor
builder.Services.AddHttpContextAccessor();

// Add Memory Cache for session caching
builder.Services.AddMemoryCache();

// Add Database Context
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddDbContext<WhimLabAIDbContext>(options =>
    options.UseNpgsql(connectionString));

// Add Repositories
builder.Services.AddScoped<IAdminSessionRepository, AdminSessionRepository>();

// Add Token Storage Service (server-side implementation)
builder.Services.AddScoped<WhimLabAI.Client.Admin.Client.Services.ITokenStorageService, ServerTokenStorageService>();

// Add Admin Token Service for unified JWT token management
builder.Services.AddScoped<IAdminTokenService, AdminTokenService>();

// Add both AuthStateProviders - DatabaseAuthStateProvider as primary, ServerAuthStateProvider for TokenRefreshService
builder.Services.AddScoped<ServerAuthStateProvider>();
builder.Services.AddScoped<DatabaseAuthStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider =>
    provider.GetRequiredService<DatabaseAuthStateProvider>());

// Configure API base URL
var apiBaseUrl = builder.Configuration["ApiSettings:BaseUrl"] ?? "http://localhost:15800";

// Add HttpClient for API calls
builder.Services.AddHttpClient("API", client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<ServerSideAuthorizationHandler>();

// Add Token Refresh Service
builder.Services.AddScoped<TokenRefreshService>();
builder.Services.AddTransient<AdminTokenRefreshHandler>();
builder.Services.AddTransient<ServerSideAuthorizationHandler>();

// Remove ServerAuthStateProvider since we're using DatabaseAuthStateProvider now
// builder.Services.AddScoped<ServerAuthStateProvider>();

// Add Client Services for WebAssembly components with HttpClient
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.ClientAuthService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.DashboardService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.CustomerUserService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.AdminUserService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.RoleService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.AgentService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.ConversationService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.SubscriptionService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.OrderService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.CouponService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.AnalyticsService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.SystemSettingsService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.LogService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.MonitoringService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.ProfileService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<AdminTokenRefreshHandler>();

// Add ClientAuthStateProvider
builder.Services.AddScoped<WhimLabAI.Client.Admin.Client.Services.ClientAuthStateProvider>();

// Add ClientAuthService (server-side version)
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Services.ClientAuthService>("ServerAuthService", client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<ServerSideAuthorizationHandler>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseAuthentication();
app.UseAuthorization();

app.UseAntiforgery();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies(typeof(WhimLabAI.Client.Admin.Client._Imports).Assembly);

app.Run();
