using MudBlazor.Services;
using WhimLabAI.Client.Admin.Client.Pages;
using WhimLabAI.Client.Admin.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using WhimLabAI.Client.Admin.Services;
using WhimLabAI.Client.Admin.Client.Services;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Infrastructure.Data.Repositories;
using Microsoft.Extensions.Http;
using WhimLabAI.Client.Admin.Middleware;

var builder = WebApplication.CreateBuilder(args);

// 配置文件加载顺序
builder.Configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddJsonFile("appsettings.QuickDev.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();

// Add MudBlazor services
builder.Services.AddMudServices();

// Add caching services for performance optimization
builder.Services.AddMemoryCache(options =>
{
    options.SizeLimit = 100_000; // 限制缓存项数量
    options.CompactionPercentage = 0.25; // 当达到限制时清理25%的项目
    options.ExpirationScanFrequency = TimeSpan.FromMinutes(5); // 每5分钟扫描过期项
});

// Add distributed cache (Redis) if available
var redisConnectionString = builder.Configuration.GetConnectionString("Redis");
if (!string.IsNullOrEmpty(redisConnectionString))
{
    builder.Services.AddStackExchangeRedisCache(options =>
    {
        options.Configuration = redisConnectionString;
        options.InstanceName = "WhimLabAI-Admin";
    });
}
else
{
    // Fallback to in-memory distributed cache for development
    builder.Services.AddDistributedMemoryCache();
}

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents();

// Add Authentication
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    var jwtSettings = builder.Configuration.GetSection("Jwt");
    var secretKey = jwtSettings["SecretKey"] ?? "your-256-bit-secret-key-for-development-only-must-be-at-least-32-characters";

    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = jwtSettings["Issuer"] ?? "WhimLabAI",
        ValidAudience = jwtSettings["Audience"] ?? "WhimLabAI",
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
        ClockSkew = TimeSpan.Zero,
        RequireSignedTokens = true
    };

    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
            {
                context.Response.Headers.Append("Token-Expired", "true");
            }
            return Task.CompletedTask;
        }
    };
});

// Add Authorization
builder.Services.AddAuthorizationCore();
builder.Services.AddCascadingAuthenticationState();

// Add HttpContextAccessor
builder.Services.AddHttpContextAccessor();

// Add Memory Cache for session caching
builder.Services.AddMemoryCache();

// Add Database Context
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddDbContext<WhimLabAIDbContext>(options =>
    options.UseNpgsql(connectionString));

// Add Repositories
builder.Services.AddScoped<IAdminSessionRepository, AdminSessionRepository>();

// Add Token Storage Service (server-side implementation)
builder.Services.AddScoped<WhimLabAI.Client.Admin.Client.Services.ITokenStorageService, ServerTokenStorageService>();

// Add Admin Token Service for unified JWT token management
builder.Services.AddScoped<IAdminTokenService, AdminTokenService>();

// Add Admin Cache Service for performance optimization
builder.Services.AddScoped<IAdminCacheService, AdminCacheService>();

// Add Admin Performance Monitor
builder.Services.AddSingleton<IAdminPerformanceMonitor, AdminPerformanceMonitor>();

// Add both AuthStateProviders - DatabaseAuthStateProvider as primary, ServerAuthStateProvider for TokenRefreshService
builder.Services.AddScoped<ServerAuthStateProvider>();
builder.Services.AddScoped<DatabaseAuthStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider =>
    provider.GetRequiredService<DatabaseAuthStateProvider>());

// Configure API base URL
var apiBaseUrl = builder.Configuration["ApiSettings:BaseUrl"] ?? "http://localhost:15800";

// Configure HttpClient connection pool settings for optimal performance
builder.Services.Configure<HttpClientFactoryOptions>("API", options =>
{
    options.HandlerLifetime = TimeSpan.FromMinutes(5); // 连接池生命周期
});

builder.Services.Configure<HttpClientFactoryOptions>("AdminAPI", options =>
{
    options.HandlerLifetime = TimeSpan.FromMinutes(5); // 连接池生命周期
});

// Add HttpClient for API calls with connection pool optimization
builder.Services.AddHttpClient("API", client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
    client.Timeout = TimeSpan.FromSeconds(30); // 请求超时
    client.DefaultRequestHeaders.Add("User-Agent", "WhimLabAI-Admin/1.0");
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
{
    MaxConnectionsPerServer = 10, // 每个服务器最大连接数
    UseCookies = false // 禁用自动Cookie处理，我们手动管理
})
.AddHttpMessageHandler<ServerSideAuthorizationHandler>();

// Add Token Refresh Service
builder.Services.AddScoped<TokenRefreshService>();
builder.Services.AddTransient<AdminTokenRefreshHandler>();
builder.Services.AddTransient<ServerSideAuthorizationHandler>();

// Remove ServerAuthStateProvider since we're using DatabaseAuthStateProvider now
// builder.Services.AddScoped<ServerAuthStateProvider>();

// Add Client Services for WebAssembly components with optimized HttpClient
// 使用统一的配置来减少重复代码并优化连接池
Action<HttpClient> configureAdminClient = client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
    client.Timeout = TimeSpan.FromSeconds(30);
    client.DefaultRequestHeaders.Add("User-Agent", "WhimLabAI-Admin/1.0");
};

Func<HttpMessageHandler> createOptimizedHandler = () => new HttpClientHandler()
{
    MaxConnectionsPerServer = 10,
    UseCookies = false,
    UseProxy = false // 禁用代理以提升性能
};

// 配置所有Admin API服务的HttpClient
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.ClientAuthService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.DashboardService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.CustomerUserService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.AdminUserService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.RoleService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.AgentService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.ConversationService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.SubscriptionService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.OrderService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.CouponService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.AnalyticsService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.SystemSettingsService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.LogService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.MonitoringService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Client.Services.ProfileService>(configureAdminClient)
    .ConfigurePrimaryHttpMessageHandler(createOptimizedHandler)
    .AddHttpMessageHandler<AdminTokenRefreshHandler>();

// Add ClientAuthStateProvider
builder.Services.AddScoped<WhimLabAI.Client.Admin.Client.Services.ClientAuthStateProvider>();

// Add ClientAuthService (server-side version) with optimized connection pool
builder.Services.AddHttpClient<WhimLabAI.Client.Admin.Services.ClientAuthService>("ServerAuthService", client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
    client.Timeout = TimeSpan.FromSeconds(30);
    client.DefaultRequestHeaders.Add("User-Agent", "WhimLabAI-Admin-Server/1.0");
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
{
    MaxConnectionsPerServer = 10,
    UseCookies = false,
    UseProxy = false
})
.AddHttpMessageHandler<ServerSideAuthorizationHandler>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

// Add performance monitoring and logging middleware
app.UseHealthCheck();
app.UseRequestLogging();
app.UsePerformanceMonitoring();

app.UseAuthentication();
app.UseAuthorization();

app.UseAntiforgery();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies(typeof(WhimLabAI.Client.Admin.Client._Imports).Assembly);

app.Run();
