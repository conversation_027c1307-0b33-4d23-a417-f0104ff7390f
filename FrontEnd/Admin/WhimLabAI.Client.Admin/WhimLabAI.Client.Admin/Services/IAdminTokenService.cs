namespace WhimLabAI.Client.Admin.Services;

/// <summary>
/// 管理员JWT Token服务接口
/// </summary>
public interface IAdminTokenService
{
    /// <summary>
    /// 获取访问令牌
    /// </summary>
    string? GetAccessToken();
    
    /// <summary>
    /// 获取刷新令牌
    /// </summary>
    string? GetRefreshToken();
    
    /// <summary>
    /// 设置令牌
    /// </summary>
    void SetTokens(string accessToken, string? refreshToken = null);
    
    /// <summary>
    /// 清除令牌
    /// </summary>
    void ClearTokens();
    
    /// <summary>
    /// 检查令牌是否存在
    /// </summary>
    bool HasToken();
}
