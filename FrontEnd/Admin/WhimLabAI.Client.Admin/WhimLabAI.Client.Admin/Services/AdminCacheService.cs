using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace WhimLabAI.Client.Admin.Services;

/// <summary>
/// Admin项目专用缓存服务，提供内存缓存和分布式缓存的统一接口
/// 优化前端性能，减少API调用
/// </summary>
public interface IAdminCacheService
{
    /// <summary>
    /// 获取缓存数据
    /// </summary>
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 设置缓存数据
    /// </summary>
    Task SetAsync<T>(string key, T value, TimeSpan? expiry = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 移除缓存数据
    /// </summary>
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取或设置缓存数据（Cache-Aside模式）
    /// </summary>
    Task<T?> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量移除缓存（按前缀）
    /// </summary>
    Task RemoveByPrefixAsync(string prefix, CancellationToken cancellationToken = default);
}

/// <summary>
/// Admin缓存服务实现
/// 优先使用内存缓存，回退到分布式缓存，集成性能监控
/// </summary>
public class AdminCacheService : IAdminCacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;
    private readonly ILogger<AdminCacheService> _logger;
    private readonly IAdminPerformanceMonitor _performanceMonitor;
    private readonly JsonSerializerOptions _jsonOptions;
    
    // 缓存键前缀
    private const string CachePrefix = "admin:";
    
    // 默认过期时间配置
    private static readonly TimeSpan DefaultExpiry = TimeSpan.FromMinutes(30);
    private static readonly TimeSpan ShortExpiry = TimeSpan.FromMinutes(5);
    private static readonly TimeSpan LongExpiry = TimeSpan.FromHours(2);

    public AdminCacheService(
        IMemoryCache memoryCache,
        IDistributedCache distributedCache,
        ILogger<AdminCacheService> logger,
        IAdminPerformanceMonitor performanceMonitor)
    {
        _memoryCache = memoryCache;
        _distributedCache = distributedCache;
        _logger = logger;
        _performanceMonitor = performanceMonitor;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            WriteIndented = false
        };
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        var cacheKey = GetCacheKey(key);
        
        try
        {
            // 首先尝试从内存缓存获取
            if (_memoryCache.TryGetValue(cacheKey, out T? memoryValue))
            {
                _performanceMonitor.RecordCacheHit($"memory:{cacheKey}");
                _logger.LogDebug("Cache hit from memory for key: {Key}", cacheKey);
                return memoryValue;
            }

            // 然后尝试从分布式缓存获取
            var distributedValue = await _distributedCache.GetStringAsync(cacheKey, cancellationToken);
            if (!string.IsNullOrEmpty(distributedValue))
            {
                var deserializedValue = JsonSerializer.Deserialize<T>(distributedValue, _jsonOptions);

                // 将数据同步到内存缓存
                var memoryOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = ShortExpiry,
                    Size = 1
                };
                _memoryCache.Set(cacheKey, deserializedValue, memoryOptions);

                _performanceMonitor.RecordCacheHit($"distributed:{cacheKey}");
                _logger.LogDebug("Cache hit from distributed cache for key: {Key}", cacheKey);
                return deserializedValue;
            }

            _performanceMonitor.RecordCacheMiss(cacheKey);
            _logger.LogDebug("Cache miss for key: {Key}", cacheKey);
            return default;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache value for key: {Key}", cacheKey);
            return default;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        var cacheKey = GetCacheKey(key);
        var expiryTime = expiry ?? DefaultExpiry;
        
        try
        {
            // 设置内存缓存
            var memoryOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiryTime,
                Size = 1,
                Priority = CacheItemPriority.Normal
            };
            _memoryCache.Set(cacheKey, value, memoryOptions);

            // 设置分布式缓存
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            var distributedOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiryTime
            };
            await _distributedCache.SetStringAsync(cacheKey, serializedValue, distributedOptions, cancellationToken);
            
            _logger.LogDebug("Cache set for key: {Key}, expiry: {Expiry}", cacheKey, expiryTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache value for key: {Key}", cacheKey);
        }
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        var cacheKey = GetCacheKey(key);
        
        try
        {
            // 从内存缓存移除
            _memoryCache.Remove(cacheKey);
            
            // 从分布式缓存移除
            await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
            
            _logger.LogDebug("Cache removed for key: {Key}", cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache value for key: {Key}", cacheKey);
        }
    }

    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        // 首先尝试获取缓存
        var cachedValue = await GetAsync<T>(key, cancellationToken);
        if (cachedValue != null)
        {
            return cachedValue;
        }

        try
        {
            // 执行工厂方法获取数据
            var value = await factory();
            if (value != null)
            {
                // 设置缓存
                await SetAsync(key, value, expiry, cancellationToken);
            }
            
            return value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetOrSetAsync for key: {Key}", key);
            return default;
        }
    }

    public Task RemoveByPrefixAsync(string prefix, CancellationToken cancellationToken = default)
    {
        var cachePrefix = GetCacheKey(prefix);

        try
        {
            // 注意：这是一个简化实现
            // 在生产环境中，可能需要更复杂的实现来处理分布式缓存的前缀删除
            _logger.LogWarning("RemoveByPrefixAsync is not fully implemented for distributed cache. Prefix: {Prefix}", cachePrefix);

            // 对于内存缓存，我们无法直接按前缀删除，这里只是记录日志
            _logger.LogDebug("Cache prefix removal requested for: {Prefix}", cachePrefix);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache by prefix: {Prefix}", cachePrefix);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 生成缓存键
    /// </summary>
    private static string GetCacheKey(string key)
    {
        return $"{CachePrefix}{key}";
    }
}

/// <summary>
/// Admin缓存键常量
/// </summary>
public static class AdminCacheKeys
{
    // 用户相关
    public static string UserProfile(string userId) => $"user:profile:{userId}";
    public static string UserPermissions(string userId) => $"user:permissions:{userId}";
    public static string UserRoles(string userId) => $"user:roles:{userId}";
    
    // 系统数据
    public static string SystemStats() => "system:stats";
    public static string DashboardData(string userId) => $"dashboard:data:{userId}";
    
    // 列表数据
    public static string UserList(int page, int pageSize) => $"users:list:p{page}:s{pageSize}";
    public static string AgentList(int page, int pageSize) => $"agents:list:p{page}:s{pageSize}";
    public static string OrderList(int page, int pageSize) => $"orders:list:p{page}:s{pageSize}";
    
    // 配置数据
    public static string SystemSettings() => "system:settings";
    public static string FeatureFlags() => "system:features";
    
    // 统计数据
    public static string Analytics(string period) => $"analytics:{period}";
    public static string Reports(string type, string period) => $"reports:{type}:{period}";
}
