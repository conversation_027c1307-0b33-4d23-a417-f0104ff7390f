using System.Net.Http.Headers;

namespace WhimLabAI.Client.Admin.Services;

/// <summary>
/// Server-side authorization handler that reads tokens from cookies during SSR
/// 使用统一的AdminTokenService获取token，支持缓存机制
/// </summary>
public class ServerSideAuthorizationHandler : DelegatingHandler
{
    private readonly IAdminTokenService _tokenService;
    private readonly ILogger<ServerSideAuthorizationHandler> _logger;

    public ServerSideAuthorizationHandler(
        IAdminTokenService tokenService,
        ILogger<ServerSideAuthorizationHandler> logger)
    {
        _tokenService = tokenService;
        _logger = logger;
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        try
        {
            // 使用统一的AdminTokenService获取token（支持缓存）
            var token = _tokenService.GetAccessToken();
            if (!string.IsNullOrEmpty(token))
            {
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                _logger.LogDebug("Added authorization header from AdminTokenService for request to {Uri}", request.RequestUri);
            }
            else
            {
                _logger.LogDebug("No auth token found for request to {Uri}", request.RequestUri);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding authorization header");
        }

        return await base.SendAsync(request, cancellationToken);
    }
}