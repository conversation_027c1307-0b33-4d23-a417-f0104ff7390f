using System.Diagnostics;
using Microsoft.Extensions.Caching.Memory;

namespace WhimLabAI.Client.Admin.Services;

/// <summary>
/// Admin项目性能监控服务
/// 监控关键性能指标，包括响应时间、内存使用、缓存命中率等
/// </summary>
public interface IAdminPerformanceMonitor
{
    /// <summary>
    /// 开始性能监控
    /// </summary>
    IDisposable StartOperation(string operationName);
    
    /// <summary>
    /// 记录操作耗时
    /// </summary>
    void RecordOperationTime(string operationName, TimeSpan duration);
    
    /// <summary>
    /// 记录缓存命中
    /// </summary>
    void RecordCacheHit(string cacheKey);
    
    /// <summary>
    /// 记录缓存未命中
    /// </summary>
    void RecordCacheMiss(string cacheKey);
    
    /// <summary>
    /// 获取性能统计
    /// </summary>
    AdminPerformanceStats GetPerformanceStats();
    
    /// <summary>
    /// 重置统计数据
    /// </summary>
    void ResetStats();
}

/// <summary>
/// Admin性能监控实现
/// </summary>
public class AdminPerformanceMonitor : IAdminPerformanceMonitor
{
    private readonly ILogger<AdminPerformanceMonitor> _logger;
    private readonly Dictionary<string, List<TimeSpan>> _operationTimes = new();
    private readonly Dictionary<string, int> _cacheHits = new();
    private readonly Dictionary<string, int> _cacheMisses = new();
    private readonly object _lock = new();
    private readonly DateTime _startTime = DateTime.UtcNow;

    public AdminPerformanceMonitor(ILogger<AdminPerformanceMonitor> logger)
    {
        _logger = logger;
    }

    public IDisposable StartOperation(string operationName)
    {
        return new OperationTimer(this, operationName);
    }

    public void RecordOperationTime(string operationName, TimeSpan duration)
    {
        lock (_lock)
        {
            if (!_operationTimes.ContainsKey(operationName))
            {
                _operationTimes[operationName] = new List<TimeSpan>();
            }
            
            _operationTimes[operationName].Add(duration);
            
            // 记录慢操作
            if (duration.TotalMilliseconds > 1000)
            {
                _logger.LogWarning("Slow operation detected: {OperationName} took {Duration}ms", 
                    operationName, duration.TotalMilliseconds);
            }
        }
    }

    public void RecordCacheHit(string cacheKey)
    {
        lock (_lock)
        {
            _cacheHits[cacheKey] = _cacheHits.GetValueOrDefault(cacheKey, 0) + 1;
        }
    }

    public void RecordCacheMiss(string cacheKey)
    {
        lock (_lock)
        {
            _cacheMisses[cacheKey] = _cacheMisses.GetValueOrDefault(cacheKey, 0) + 1;
        }
    }

    public AdminPerformanceStats GetPerformanceStats()
    {
        lock (_lock)
        {
            var stats = new AdminPerformanceStats
            {
                StartTime = _startTime,
                Uptime = DateTime.UtcNow - _startTime,
                OperationStats = new Dictionary<string, OperationStats>(),
                CacheStats = new Dictionary<string, CacheStats>(),
                TotalOperations = _operationTimes.Values.Sum(list => list.Count),
                TotalCacheHits = _cacheHits.Values.Sum(),
                TotalCacheMisses = _cacheMisses.Values.Sum()
            };

            // 计算操作统计
            foreach (var kvp in _operationTimes)
            {
                var times = kvp.Value;
                if (times.Count > 0)
                {
                    stats.OperationStats[kvp.Key] = new OperationStats
                    {
                        Count = times.Count,
                        AverageTime = TimeSpan.FromMilliseconds(times.Average(t => t.TotalMilliseconds)),
                        MinTime = times.Min(),
                        MaxTime = times.Max(),
                        TotalTime = TimeSpan.FromMilliseconds(times.Sum(t => t.TotalMilliseconds))
                    };
                }
            }

            // 计算缓存统计
            var allCacheKeys = _cacheHits.Keys.Union(_cacheMisses.Keys);
            foreach (var key in allCacheKeys)
            {
                var hits = _cacheHits.GetValueOrDefault(key, 0);
                var misses = _cacheMisses.GetValueOrDefault(key, 0);
                var total = hits + misses;
                
                stats.CacheStats[key] = new CacheStats
                {
                    Hits = hits,
                    Misses = misses,
                    Total = total,
                    HitRate = total > 0 ? (double)hits / total : 0
                };
            }

            // 计算总体缓存命中率
            var totalCacheOperations = stats.TotalCacheHits + stats.TotalCacheMisses;
            stats.OverallCacheHitRate = totalCacheOperations > 0 
                ? (double)stats.TotalCacheHits / totalCacheOperations 
                : 0;

            return stats;
        }
    }

    public void ResetStats()
    {
        lock (_lock)
        {
            _operationTimes.Clear();
            _cacheHits.Clear();
            _cacheMisses.Clear();
        }
        
        _logger.LogInformation("Performance statistics reset");
    }

    /// <summary>
    /// 操作计时器
    /// </summary>
    private class OperationTimer : IDisposable
    {
        private readonly AdminPerformanceMonitor _monitor;
        private readonly string _operationName;
        private readonly Stopwatch _stopwatch;

        public OperationTimer(AdminPerformanceMonitor monitor, string operationName)
        {
            _monitor = monitor;
            _operationName = operationName;
            _stopwatch = Stopwatch.StartNew();
        }

        public void Dispose()
        {
            _stopwatch.Stop();
            _monitor.RecordOperationTime(_operationName, _stopwatch.Elapsed);
        }
    }
}

/// <summary>
/// Admin性能统计数据
/// </summary>
public class AdminPerformanceStats
{
    public DateTime StartTime { get; set; }
    public TimeSpan Uptime { get; set; }
    public Dictionary<string, OperationStats> OperationStats { get; set; } = new();
    public Dictionary<string, CacheStats> CacheStats { get; set; } = new();
    public int TotalOperations { get; set; }
    public int TotalCacheHits { get; set; }
    public int TotalCacheMisses { get; set; }
    public double OverallCacheHitRate { get; set; }
}

/// <summary>
/// 操作统计
/// </summary>
public class OperationStats
{
    public int Count { get; set; }
    public TimeSpan AverageTime { get; set; }
    public TimeSpan MinTime { get; set; }
    public TimeSpan MaxTime { get; set; }
    public TimeSpan TotalTime { get; set; }
}

/// <summary>
/// 缓存统计
/// </summary>
public class CacheStats
{
    public int Hits { get; set; }
    public int Misses { get; set; }
    public int Total { get; set; }
    public double HitRate { get; set; }
}

/// <summary>
/// 性能监控扩展方法
/// </summary>
public static class PerformanceMonitorExtensions
{
    /// <summary>
    /// 监控异步操作性能
    /// </summary>
    public static async Task<T> MonitorAsync<T>(this IAdminPerformanceMonitor monitor, 
        string operationName, Func<Task<T>> operation)
    {
        using (monitor.StartOperation(operationName))
        {
            return await operation();
        }
    }

    /// <summary>
    /// 监控同步操作性能
    /// </summary>
    public static T Monitor<T>(this IAdminPerformanceMonitor monitor, 
        string operationName, Func<T> operation)
    {
        using (monitor.StartOperation(operationName))
        {
            return operation();
        }
    }
}
