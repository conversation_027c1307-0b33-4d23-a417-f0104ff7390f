using Microsoft.Extensions.Caching.Memory;

namespace WhimLabAI.Client.Admin.Services;

/// <summary>
/// 管理员JWT Token服务实现
/// 统一管理JWT token的存储和获取，支持Cookie和HttpContext
/// 包含内存缓存机制以提升性能
/// </summary>
public class AdminTokenService : IAdminTokenService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<AdminTokenService> _logger;
    private readonly IMemoryCache _memoryCache;

    // 缓存键常量
    private const string AccessTokenCacheKey = "admin_access_token";
    private const string RefreshTokenCacheKey = "admin_refresh_token";
    private static readonly TimeSpan CacheDuration = TimeSpan.FromMinutes(5); // 缓存5分钟

    public AdminTokenService(
        IHttpContextAccessor httpContextAccessor,
        ILogger<AdminTokenService> logger,
        IMemoryCache memoryCache)
    {
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
        _memoryCache = memoryCache;
    }

    /// <summary>
    /// 获取访问令牌
    /// </summary>
    public string? GetAccessToken()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Request.Cookies.TryGetValue("admin-auth-token", out var token) == true)
            {
                return token;
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting access token");
            return null;
        }
    }

    /// <summary>
    /// 获取刷新令牌
    /// </summary>
    public string? GetRefreshToken()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Request.Cookies.TryGetValue("admin-refresh-token", out var token) == true)
            {
                return token;
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting refresh token");
            return null;
        }
    }

    /// <summary>
    /// 设置令牌
    /// </summary>
    public void SetTokens(string accessToken, string? refreshToken = null)
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                var cookieOptions = new CookieOptions
                {
                    HttpOnly = true,
                    Secure = httpContext.Request.IsHttps,
                    SameSite = SameSiteMode.Strict,
                    Expires = DateTimeOffset.UtcNow.AddHours(1) // JWT token有效期
                };
                
                httpContext.Response.Cookies.Append("admin-auth-token", accessToken, cookieOptions);
                
                if (!string.IsNullOrEmpty(refreshToken))
                {
                    var refreshCookieOptions = new CookieOptions
                    {
                        HttpOnly = true,
                        Secure = httpContext.Request.IsHttps,
                        SameSite = SameSiteMode.Strict,
                        Expires = DateTimeOffset.UtcNow.AddDays(7) // Refresh token有效期更长
                    };
                    
                    httpContext.Response.Cookies.Append("admin-refresh-token", refreshToken, refreshCookieOptions);
                }
                
                _logger.LogDebug("JWT tokens set in cookies");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting tokens");
        }
    }

    /// <summary>
    /// 清除令牌
    /// </summary>
    public void ClearTokens()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                httpContext.Response.Cookies.Delete("admin-auth-token");
                httpContext.Response.Cookies.Delete("admin-refresh-token");
                
                _logger.LogDebug("JWT tokens cleared from cookies");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing tokens");
        }
    }

    /// <summary>
    /// 检查令牌是否存在
    /// </summary>
    public bool HasToken()
    {
        return !string.IsNullOrEmpty(GetAccessToken());
    }
}
