using System.Net.Http.Headers;
using System.Text.Json;
using Microsoft.AspNetCore.Components.Authorization;

namespace WhimLabAI.Client.Admin.Services;

/// <summary>
/// Token自动刷新服务（管理员端）
/// </summary>
public class TokenRefreshService
{
    private readonly HttpClient _httpClient;
    private readonly ServerAuthStateProvider _authStateProvider;
    private readonly IAdminTokenService _tokenService;
    private readonly ILogger<TokenRefreshService> _logger;
    private readonly SemaphoreSlim _refreshSemaphore = new(1, 1);
    private DateTime _lastRefreshTime = DateTime.MinValue;
    private const int RefreshBeforeExpiryMinutes = 5;

    public TokenRefreshService(
        IHttpClientFactory httpClientFactory,
        ServerAuthStateProvider authStateProvider,
        IAdminTokenService tokenService,
        ILogger<TokenRefreshService> logger)
    {
        _httpClient = httpClientFactory.CreateClient("API");
        _authStateProvider = authStateProvider;
        _tokenService = tokenService;
        _logger = logger;
    }

    /// <summary>
    /// 检查并刷新Token
    /// </summary>
    public async Task<bool> CheckAndRefreshTokenAsync()
    {
        try
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            if (!authState.User.Identity?.IsAuthenticated ?? true)
                return false;

            var expiryClaim = authState.User.FindFirst("exp");
            if (expiryClaim == null)
                return false;

            var expiry = DateTimeOffset.FromUnixTimeSeconds(long.Parse(expiryClaim.Value)).UtcDateTime;
            
            // 如果Token将在5分钟内过期，则刷新
            if (expiry.AddMinutes(-RefreshBeforeExpiryMinutes) <= DateTime.UtcNow)
            {
                return await RefreshTokenAsync();
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking token expiry");
            return false;
        }
    }

    /// <summary>
    /// 刷新Token
    /// </summary>
    public async Task<bool> RefreshTokenAsync()
    {
        // 防止并发刷新
        if (!await _refreshSemaphore.WaitAsync(0))
        {
            // 如果其他线程正在刷新，等待完成
            await _refreshSemaphore.WaitAsync();
            _refreshSemaphore.Release();
            return true;
        }

        try
        {
            // 检查是否刚刚刷新过（防止重复刷新）
            if ((DateTime.UtcNow - _lastRefreshTime).TotalSeconds < 10)
            {
                return true;
            }

            var refreshToken = _tokenService.GetRefreshToken();
            if (string.IsNullOrEmpty(refreshToken))
            {
                _logger.LogWarning("No refresh token available");
                return false;
            }

            var request = new HttpRequestMessage(HttpMethod.Post, "api/admin-auth/refresh");
            request.Content = new StringContent(
                JsonSerializer.Serialize(new { refreshToken }),
                System.Text.Encoding.UTF8,
                "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (tokenResponse != null && !string.IsNullOrEmpty(tokenResponse.AccessToken))
                {
                    // 更新认证状态和token存储
                    await _authStateProvider.UpdateTokenAsync(tokenResponse.AccessToken, tokenResponse.RefreshToken);
                    _tokenService.SetTokens(tokenResponse.AccessToken, tokenResponse.RefreshToken);

                    _lastRefreshTime = DateTime.UtcNow;
                    _logger.LogInformation("Admin token refreshed successfully");
                    return true;
                }
            }
            else
            {
                _logger.LogWarning("Admin token refresh failed with status: {StatusCode}", response.StatusCode);
                
                // 如果刷新失败，清除认证状态
                if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    await _authStateProvider.LogoutAsync();
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing admin token");
            return false;
        }
        finally
        {
            _refreshSemaphore.Release();
        }
    }

    private class TokenResponse
    {
        public string AccessToken { get; set; } = string.Empty;
        public string? RefreshToken { get; set; }
        public int ExpiresIn { get; set; }
    }
}

/// <summary>
/// HTTP消息处理器，自动刷新Token（管理员端）
/// 包含性能优化：token有效期预检查、缓存机制
/// </summary>
public class AdminTokenRefreshHandler : DelegatingHandler
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AdminTokenRefreshHandler> _logger;
    private static readonly Dictionary<string, DateTime> _tokenExpiryCache = new();
    private static readonly object _cacheLock = new();

    public AdminTokenRefreshHandler(
        IServiceProvider serviceProvider,
        ILogger<AdminTokenRefreshHandler> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        // 使用作用域服务
        using var scope = _serviceProvider.CreateScope();
        var tokenRefreshService = scope.ServiceProvider.GetRequiredService<TokenRefreshService>();
        var tokenService = scope.ServiceProvider.GetRequiredService<IAdminTokenService>();

        // 获取当前Token
        var token = tokenService.GetAccessToken();

        // 性能优化：只有在token即将过期时才检查刷新
        if (!string.IsNullOrEmpty(token) && IsTokenNearExpiry(token))
        {
            await tokenRefreshService.CheckAndRefreshTokenAsync();
            // 重新获取可能已刷新的token
            token = tokenService.GetAccessToken();
        }

        // 添加Token到请求头
        if (!string.IsNullOrEmpty(token))
        {
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }

        var response = await base.SendAsync(request, cancellationToken);

        // 如果收到401，尝试刷新Token并重试
        if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized &&
            !request.RequestUri?.ToString().Contains("admin-auth/refresh") == true)
        {
            _logger.LogInformation("Received 401, attempting to refresh admin token");

            if (await tokenRefreshService.RefreshTokenAsync())
            {
                // 克隆原始请求
                var newRequest = CloneHttpRequestMessage(request);

                // 使用新Token
                var newToken = tokenService.GetAccessToken();
                if (!string.IsNullOrEmpty(newToken))
                {
                    newRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", newToken);
                }

                // 重试请求
                response = await base.SendAsync(newRequest, cancellationToken);
            }
        }

        return response;
    }

    /// <summary>
    /// 检查token是否即将过期（性能优化）
    /// </summary>
    private bool IsTokenNearExpiry(string token)
    {
        try
        {
            lock (_cacheLock)
            {
                // 检查缓存中是否有该token的过期时间
                if (_tokenExpiryCache.TryGetValue(token, out var cachedExpiry))
                {
                    // 如果距离过期时间少于5分钟，认为需要刷新
                    return DateTime.UtcNow.AddMinutes(5) >= cachedExpiry;
                }
            }

            // 解析JWT token获取过期时间
            var parts = token.Split('.');
            if (parts.Length != 3) return true; // 无效token，需要刷新

            var payload = parts[1];
            // 添加padding if needed
            switch (payload.Length % 4)
            {
                case 2: payload += "=="; break;
                case 3: payload += "="; break;
            }

            var jsonBytes = Convert.FromBase64String(payload);
            var json = System.Text.Encoding.UTF8.GetString(jsonBytes);
            var tokenData = JsonSerializer.Deserialize<JsonElement>(json);

            if (tokenData.TryGetProperty("exp", out var expElement))
            {
                var exp = expElement.GetInt64();
                var expiry = DateTimeOffset.FromUnixTimeSeconds(exp).UtcDateTime;

                // 缓存过期时间
                lock (_cacheLock)
                {
                    _tokenExpiryCache[token] = expiry;

                    // 清理过期的缓存项（简单的清理策略）
                    if (_tokenExpiryCache.Count > 10)
                    {
                        var expiredKeys = _tokenExpiryCache
                            .Where(kvp => kvp.Value < DateTime.UtcNow)
                            .Select(kvp => kvp.Key)
                            .ToList();

                        foreach (var key in expiredKeys)
                        {
                            _tokenExpiryCache.Remove(key);
                        }
                    }
                }

                // 如果距离过期时间少于5分钟，认为需要刷新
                return DateTime.UtcNow.AddMinutes(5) >= expiry;
            }

            return true; // 无法解析过期时间，保守起见认为需要刷新
        }
        catch
        {
            return true; // 解析失败，保守起见认为需要刷新
        }
    }

    private static HttpRequestMessage CloneHttpRequestMessage(HttpRequestMessage req)
    {
        var clone = new HttpRequestMessage(req.Method, req.RequestUri)
        {
            Version = req.Version
        };

        // 复制请求内容
        if (req.Content != null)
        {
            var ms = new MemoryStream();
            req.Content.CopyTo(ms, null, CancellationToken.None);
            ms.Position = 0;
            clone.Content = new StreamContent(ms);

            // 复制内容头
            foreach (var header in req.Content.Headers)
            {
                clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }
        }

        // 复制请求头（除了Authorization）
        foreach (var header in req.Headers.Where(h => h.Key != "Authorization"))
        {
            clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }

        // 复制属性
        foreach (var prop in req.Options)
        {
            clone.Options.Set(new HttpRequestOptionsKey<object?>(prop.Key), prop.Value);
        }

        return clone;
    }
}