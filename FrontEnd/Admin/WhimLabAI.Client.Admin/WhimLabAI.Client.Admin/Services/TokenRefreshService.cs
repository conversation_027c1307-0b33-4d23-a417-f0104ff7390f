using System.Net.Http.Headers;
using System.Text.Json;
using Microsoft.AspNetCore.Components.Authorization;

namespace WhimLabAI.Client.Admin.Services;

/// <summary>
/// Token自动刷新服务（管理员端）
/// </summary>
public class TokenRefreshService
{
    private readonly HttpClient _httpClient;
    private readonly ServerAuthStateProvider _authStateProvider;
    private readonly IAdminTokenService _tokenService;
    private readonly ILogger<TokenRefreshService> _logger;
    private readonly SemaphoreSlim _refreshSemaphore = new(1, 1);
    private DateTime _lastRefreshTime = DateTime.MinValue;
    private const int RefreshBeforeExpiryMinutes = 5;

    public TokenRefreshService(
        IHttpClientFactory httpClientFactory,
        ServerAuthStateProvider authStateProvider,
        IAdminTokenService tokenService,
        ILogger<TokenRefreshService> logger)
    {
        _httpClient = httpClientFactory.CreateClient("API");
        _authStateProvider = authStateProvider;
        _tokenService = tokenService;
        _logger = logger;
    }

    /// <summary>
    /// 检查并刷新Token
    /// </summary>
    public async Task<bool> CheckAndRefreshTokenAsync()
    {
        try
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            if (!authState.User.Identity?.IsAuthenticated ?? true)
                return false;

            var expiryClaim = authState.User.FindFirst("exp");
            if (expiryClaim == null)
                return false;

            var expiry = DateTimeOffset.FromUnixTimeSeconds(long.Parse(expiryClaim.Value)).UtcDateTime;
            
            // 如果Token将在5分钟内过期，则刷新
            if (expiry.AddMinutes(-RefreshBeforeExpiryMinutes) <= DateTime.UtcNow)
            {
                return await RefreshTokenAsync();
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking token expiry");
            return false;
        }
    }

    /// <summary>
    /// 刷新Token
    /// </summary>
    public async Task<bool> RefreshTokenAsync()
    {
        // 防止并发刷新
        if (!await _refreshSemaphore.WaitAsync(0))
        {
            // 如果其他线程正在刷新，等待完成
            await _refreshSemaphore.WaitAsync();
            _refreshSemaphore.Release();
            return true;
        }

        try
        {
            // 检查是否刚刚刷新过（防止重复刷新）
            if ((DateTime.UtcNow - _lastRefreshTime).TotalSeconds < 10)
            {
                return true;
            }

            var refreshToken = _tokenService.GetRefreshToken();
            if (string.IsNullOrEmpty(refreshToken))
            {
                _logger.LogWarning("No refresh token available");
                return false;
            }

            var request = new HttpRequestMessage(HttpMethod.Post, "api/admin-auth/refresh");
            request.Content = new StringContent(
                JsonSerializer.Serialize(new { refreshToken }),
                System.Text.Encoding.UTF8,
                "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (tokenResponse != null && !string.IsNullOrEmpty(tokenResponse.AccessToken))
                {
                    // 更新认证状态和token存储
                    await _authStateProvider.UpdateTokenAsync(tokenResponse.AccessToken, tokenResponse.RefreshToken);
                    _tokenService.SetTokens(tokenResponse.AccessToken, tokenResponse.RefreshToken);

                    _lastRefreshTime = DateTime.UtcNow;
                    _logger.LogInformation("Admin token refreshed successfully");
                    return true;
                }
            }
            else
            {
                _logger.LogWarning("Admin token refresh failed with status: {StatusCode}", response.StatusCode);
                
                // 如果刷新失败，清除认证状态
                if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    await _authStateProvider.LogoutAsync();
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing admin token");
            return false;
        }
        finally
        {
            _refreshSemaphore.Release();
        }
    }

    private class TokenResponse
    {
        public string AccessToken { get; set; } = string.Empty;
        public string? RefreshToken { get; set; }
        public int ExpiresIn { get; set; }
    }
}

/// <summary>
/// HTTP消息处理器，自动刷新Token（管理员端）
/// </summary>
public class AdminTokenRefreshHandler : DelegatingHandler
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AdminTokenRefreshHandler> _logger;

    public AdminTokenRefreshHandler(
        IServiceProvider serviceProvider,
        ILogger<AdminTokenRefreshHandler> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        // 使用作用域服务
        using var scope = _serviceProvider.CreateScope();
        var tokenRefreshService = scope.ServiceProvider.GetRequiredService<TokenRefreshService>();
        var tokenService = scope.ServiceProvider.GetRequiredService<IAdminTokenService>();

        // 在发送请求前检查Token
        await tokenRefreshService.CheckAndRefreshTokenAsync();

        // 添加当前Token到请求头
        var token = tokenService.GetAccessToken();
        if (!string.IsNullOrEmpty(token))
        {
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }

        var response = await base.SendAsync(request, cancellationToken);

        // 如果收到401，尝试刷新Token并重试
        if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized &&
            !request.RequestUri?.ToString().Contains("admin-auth/refresh") == true)
        {
            _logger.LogInformation("Received 401, attempting to refresh admin token");

            if (await tokenRefreshService.RefreshTokenAsync())
            {
                // 克隆原始请求
                var newRequest = CloneHttpRequestMessage(request);

                // 使用新Token
                var newToken = tokenService.GetAccessToken();
                if (!string.IsNullOrEmpty(newToken))
                {
                    newRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", newToken);
                }

                // 重试请求
                response = await base.SendAsync(newRequest, cancellationToken);
            }
        }

        return response;
    }

    private static HttpRequestMessage CloneHttpRequestMessage(HttpRequestMessage req)
    {
        var clone = new HttpRequestMessage(req.Method, req.RequestUri)
        {
            Version = req.Version
        };

        // 复制请求内容
        if (req.Content != null)
        {
            var ms = new MemoryStream();
            req.Content.CopyTo(ms, null, CancellationToken.None);
            ms.Position = 0;
            clone.Content = new StreamContent(ms);

            // 复制内容头
            foreach (var header in req.Content.Headers)
            {
                clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }
        }

        // 复制请求头（除了Authorization）
        foreach (var header in req.Headers.Where(h => h.Key != "Authorization"))
        {
            clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }

        // 复制属性
        foreach (var prop in req.Options)
        {
            clone.Options.Set(new HttpRequestOptionsKey<object?>(prop.Key), prop.Value);
        }

        return clone;
    }
}