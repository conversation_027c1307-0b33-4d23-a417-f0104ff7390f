using WhimLabAI.Client.Admin.Client.Services;
using WhimLabAI.Client.Admin.DTOs;

namespace WhimLabAI.Client.Admin.Services;

/// <summary>
/// Dashboard服务的缓存装饰器
/// 使用装饰器模式为Dashboard数据添加缓存层，提升性能
/// </summary>
public class CachedDashboardService : IDashboardService
{
    private readonly IDashboardService _dashboardService;
    private readonly IAdminCacheService _cacheService;
    private readonly ILogger<CachedDashboardService> _logger;
    
    // 缓存过期时间配置
    private static readonly TimeSpan StatsExpiry = TimeSpan.FromMinutes(5); // 统计数据5分钟过期
    private static readonly TimeSpan ChartDataExpiry = TimeSpan.FromMinutes(10); // 图表数据10分钟过期
    private static readonly TimeSpan RecentActivityExpiry = TimeSpan.FromMinutes(2); // 最近活动2分钟过期

    public CachedDashboardService(
        IDashboardService dashboardService,
        IAdminCacheService cacheService,
        ILogger<CachedDashboardService> logger)
    {
        _dashboardService = dashboardService;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<DashboardStatsDto> GetDashboardStatsAsync()
    {
        const string cacheKey = "dashboard:stats";
        
        return await _cacheService.GetOrSetAsync(
            cacheKey,
            async () =>
            {
                _logger.LogDebug("Fetching dashboard stats from API");
                return await _dashboardService.GetDashboardStatsAsync();
            },
            StatsExpiry
        ) ?? new DashboardStatsDto();
    }

    public async Task<List<ChartDataDto>> GetUserRegistrationChartAsync(int days = 30)
    {
        var cacheKey = $"dashboard:chart:user_registration:{days}";
        
        return await _cacheService.GetOrSetAsync(
            cacheKey,
            async () =>
            {
                _logger.LogDebug("Fetching user registration chart data from API for {Days} days", days);
                return await _dashboardService.GetUserRegistrationChartAsync(days);
            },
            ChartDataExpiry
        ) ?? new List<ChartDataDto>();
    }

    public async Task<List<ChartDataDto>> GetRevenueChartAsync(int days = 30)
    {
        var cacheKey = $"dashboard:chart:revenue:{days}";
        
        return await _cacheService.GetOrSetAsync(
            cacheKey,
            async () =>
            {
                _logger.LogDebug("Fetching revenue chart data from API for {Days} days", days);
                return await _dashboardService.GetRevenueChartAsync(days);
            },
            ChartDataExpiry
        ) ?? new List<ChartDataDto>();
    }

    public async Task<List<ChartDataDto>> GetTokenUsageChartAsync(int days = 30)
    {
        var cacheKey = $"dashboard:chart:token_usage:{days}";
        
        return await _cacheService.GetOrSetAsync(
            cacheKey,
            async () =>
            {
                _logger.LogDebug("Fetching token usage chart data from API for {Days} days", days);
                return await _dashboardService.GetTokenUsageChartAsync(days);
            },
            ChartDataExpiry
        ) ?? new List<ChartDataDto>();
    }

    public async Task<List<RecentActivityDto>> GetRecentActivitiesAsync(int count = 10)
    {
        var cacheKey = $"dashboard:recent_activities:{count}";
        
        return await _cacheService.GetOrSetAsync(
            cacheKey,
            async () =>
            {
                _logger.LogDebug("Fetching recent activities from API, count: {Count}", count);
                return await _dashboardService.GetRecentActivitiesAsync(count);
            },
            RecentActivityExpiry
        ) ?? new List<RecentActivityDto>();
    }

    public async Task<List<TopAgentDto>> GetTopAgentsAsync(int count = 10)
    {
        var cacheKey = $"dashboard:top_agents:{count}";
        
        return await _cacheService.GetOrSetAsync(
            cacheKey,
            async () =>
            {
                _logger.LogDebug("Fetching top agents from API, count: {Count}", count);
                return await _dashboardService.GetTopAgentsAsync(count);
            },
            ChartDataExpiry
        ) ?? new List<TopAgentDto>();
    }

    public async Task<SystemHealthDto> GetSystemHealthAsync()
    {
        const string cacheKey = "dashboard:system_health";
        
        return await _cacheService.GetOrSetAsync(
            cacheKey,
            async () =>
            {
                _logger.LogDebug("Fetching system health from API");
                return await _dashboardService.GetSystemHealthAsync();
            },
            TimeSpan.FromMinutes(1) // 系统健康状态1分钟过期
        ) ?? new SystemHealthDto();
    }

    /// <summary>
    /// 刷新Dashboard缓存
    /// </summary>
    public async Task RefreshCacheAsync()
    {
        _logger.LogInformation("Refreshing dashboard cache");
        
        try
        {
            // 移除所有Dashboard相关的缓存
            await _cacheService.RemoveByPrefixAsync("dashboard:");
            
            _logger.LogInformation("Dashboard cache refreshed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing dashboard cache");
        }
    }
}

/// <summary>
/// Dashboard数据传输对象
/// </summary>
public class DashboardStatsDto
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int TotalAgents { get; set; }
    public int TotalConversations { get; set; }
    public decimal TotalRevenue { get; set; }
    public long TotalTokensUsed { get; set; }
    public int NewUsersToday { get; set; }
    public int ConversationsToday { get; set; }
    public decimal RevenueToday { get; set; }
    public long TokensUsedToday { get; set; }
}

public class ChartDataDto
{
    public string Label { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public DateTime Date { get; set; }
}

public class RecentActivityDto
{
    public string Id { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
}

public class TopAgentDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int UsageCount { get; set; }
    public decimal Rating { get; set; }
    public string Category { get; set; } = string.Empty;
}

public class SystemHealthDto
{
    public string Status { get; set; } = "Unknown";
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public int ActiveConnections { get; set; }
    public TimeSpan Uptime { get; set; }
    public List<ServiceStatusDto> Services { get; set; } = new();
}

public class ServiceStatusDto
{
    public string Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public DateTime LastCheck { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Dashboard服务接口
/// </summary>
public interface IDashboardService
{
    Task<DashboardStatsDto> GetDashboardStatsAsync();
    Task<List<ChartDataDto>> GetUserRegistrationChartAsync(int days = 30);
    Task<List<ChartDataDto>> GetRevenueChartAsync(int days = 30);
    Task<List<ChartDataDto>> GetTokenUsageChartAsync(int days = 30);
    Task<List<RecentActivityDto>> GetRecentActivitiesAsync(int count = 10);
    Task<List<TopAgentDto>> GetTopAgentsAsync(int count = 10);
    Task<SystemHealthDto> GetSystemHealthAsync();
}
