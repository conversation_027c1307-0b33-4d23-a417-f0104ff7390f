using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Client.Admin.Services;

namespace WhimLabAI.Client.Admin.Controllers;

/// <summary>
/// 性能监控API控制器
/// 提供性能统计数据和监控信息的API端点
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin")]
public class PerformanceController : ControllerBase
{
    private readonly IAdminPerformanceMonitor _performanceMonitor;
    private readonly ILogger<PerformanceController> _logger;

    public PerformanceController(
        IAdminPerformanceMonitor performanceMonitor,
        ILogger<PerformanceController> logger)
    {
        _performanceMonitor = performanceMonitor;
        _logger = logger;
    }

    /// <summary>
    /// 获取性能统计数据
    /// </summary>
    /// <returns>性能统计信息</returns>
    [HttpGet("stats")]
    public ActionResult<AdminPerformanceStats> GetPerformanceStats()
    {
        try
        {
            var stats = _performanceMonitor.GetPerformanceStats();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving performance statistics");
            return StatusCode(500, "Internal server error while retrieving performance statistics");
        }
    }

    /// <summary>
    /// 获取性能摘要信息
    /// </summary>
    /// <returns>性能摘要</returns>
    [HttpGet("summary")]
    public ActionResult<object> GetPerformanceSummary()
    {
        try
        {
            var stats = _performanceMonitor.GetPerformanceStats();
            
            var summary = new
            {
                Uptime = stats.Uptime.ToString(@"dd\.hh\:mm\:ss"),
                TotalOperations = stats.TotalOperations,
                CacheHitRate = $"{stats.OverallCacheHitRate:P2}",
                TotalCacheHits = stats.TotalCacheHits,
                TotalCacheMisses = stats.TotalCacheMisses,
                TopSlowOperations = stats.OperationStats
                    .Where(kvp => kvp.Value.Count > 0)
                    .OrderByDescending(kvp => kvp.Value.AverageTime.TotalMilliseconds)
                    .Take(5)
                    .Select(kvp => new
                    {
                        Operation = kvp.Key,
                        AverageTime = $"{kvp.Value.AverageTime.TotalMilliseconds:F2}ms",
                        Count = kvp.Value.Count,
                        MaxTime = $"{kvp.Value.MaxTime.TotalMilliseconds:F2}ms"
                    })
                    .ToList(),
                CachePerformance = stats.CacheStats
                    .OrderByDescending(kvp => kvp.Value.Total)
                    .Take(10)
                    .Select(kvp => new
                    {
                        CacheKey = kvp.Key,
                        HitRate = $"{kvp.Value.HitRate:P2}",
                        Hits = kvp.Value.Hits,
                        Misses = kvp.Value.Misses,
                        Total = kvp.Value.Total
                    })
                    .ToList()
            };

            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving performance summary");
            return StatusCode(500, "Internal server error while retrieving performance summary");
        }
    }

    /// <summary>
    /// 重置性能统计数据
    /// </summary>
    /// <returns>操作结果</returns>
    [HttpPost("reset")]
    public ActionResult ResetPerformanceStats()
    {
        try
        {
            _performanceMonitor.ResetStats();
            _logger.LogInformation("Performance statistics reset by admin user");
            return Ok(new { Message = "Performance statistics have been reset successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting performance statistics");
            return StatusCode(500, "Internal server error while resetting performance statistics");
        }
    }

    /// <summary>
    /// 获取系统健康状态
    /// </summary>
    /// <returns>系统健康信息</returns>
    [HttpGet("health")]
    public ActionResult<object> GetSystemHealth()
    {
        try
        {
            var stats = _performanceMonitor.GetPerformanceStats();
            
            // 计算健康指标
            var avgResponseTime = stats.OperationStats.Values
                .Where(s => s.Count > 0)
                .Select(s => s.AverageTime.TotalMilliseconds)
                .DefaultIfEmpty(0)
                .Average();

            var slowOperationsCount = stats.OperationStats.Values
                .Count(s => s.AverageTime.TotalMilliseconds > 1000);

            var healthStatus = "Healthy";
            if (avgResponseTime > 2000 || slowOperationsCount > 5)
            {
                healthStatus = "Degraded";
            }
            else if (avgResponseTime > 5000 || slowOperationsCount > 10)
            {
                healthStatus = "Unhealthy";
            }

            var health = new
            {
                Status = healthStatus,
                Timestamp = DateTime.UtcNow,
                Uptime = stats.Uptime.ToString(@"dd\.hh\:mm\:ss"),
                Metrics = new
                {
                    AverageResponseTime = $"{avgResponseTime:F2}ms",
                    SlowOperationsCount = slowOperationsCount,
                    CacheHitRate = $"{stats.OverallCacheHitRate:P2}",
                    TotalOperations = stats.TotalOperations
                },
                SystemInfo = new
                {
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                    MachineName = Environment.MachineName,
                    ProcessId = Environment.ProcessId,
                    WorkingSet = $"{Environment.WorkingSet / 1024 / 1024} MB",
                    GCMemory = $"{GC.GetTotalMemory(false) / 1024 / 1024} MB"
                }
            };

            return Ok(health);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving system health information");
            return StatusCode(500, "Internal server error while retrieving system health");
        }
    }

    /// <summary>
    /// 获取操作性能详情
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <returns>操作性能详情</returns>
    [HttpGet("operations/{operationName}")]
    public ActionResult<object> GetOperationDetails(string operationName)
    {
        try
        {
            var stats = _performanceMonitor.GetPerformanceStats();
            
            if (!stats.OperationStats.TryGetValue(operationName, out var operationStats))
            {
                return NotFound($"Operation '{operationName}' not found in performance statistics");
            }

            var details = new
            {
                OperationName = operationName,
                Statistics = new
                {
                    Count = operationStats.Count,
                    AverageTime = $"{operationStats.AverageTime.TotalMilliseconds:F2}ms",
                    MinTime = $"{operationStats.MinTime.TotalMilliseconds:F2}ms",
                    MaxTime = $"{operationStats.MaxTime.TotalMilliseconds:F2}ms",
                    TotalTime = $"{operationStats.TotalTime.TotalMilliseconds:F2}ms"
                },
                Performance = new
                {
                    IsHealthy = operationStats.AverageTime.TotalMilliseconds < 1000,
                    IsSlow = operationStats.AverageTime.TotalMilliseconds > 2000,
                    PerformanceRating = operationStats.AverageTime.TotalMilliseconds switch
                    {
                        < 100 => "Excellent",
                        < 500 => "Good",
                        < 1000 => "Fair",
                        < 2000 => "Poor",
                        _ => "Critical"
                    }
                }
            };

            return Ok(details);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving operation details for {OperationName}", operationName);
            return StatusCode(500, "Internal server error while retrieving operation details");
        }
    }

    /// <summary>
    /// 获取缓存性能详情
    /// </summary>
    /// <returns>缓存性能详情</returns>
    [HttpGet("cache")]
    public ActionResult<object> GetCachePerformance()
    {
        try
        {
            var stats = _performanceMonitor.GetPerformanceStats();
            
            var cachePerformance = new
            {
                Overall = new
                {
                    TotalHits = stats.TotalCacheHits,
                    TotalMisses = stats.TotalCacheMisses,
                    HitRate = $"{stats.OverallCacheHitRate:P2}",
                    TotalOperations = stats.TotalCacheHits + stats.TotalCacheMisses
                },
                Details = stats.CacheStats
                    .OrderByDescending(kvp => kvp.Value.Total)
                    .Select(kvp => new
                    {
                        CacheKey = kvp.Key,
                        Hits = kvp.Value.Hits,
                        Misses = kvp.Value.Misses,
                        Total = kvp.Value.Total,
                        HitRate = $"{kvp.Value.HitRate:P2}",
                        Efficiency = kvp.Value.HitRate switch
                        {
                            >= 0.9 => "Excellent",
                            >= 0.7 => "Good",
                            >= 0.5 => "Fair",
                            >= 0.3 => "Poor",
                            _ => "Critical"
                        }
                    })
                    .ToList()
            };

            return Ok(cachePerformance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cache performance information");
            return StatusCode(500, "Internal server error while retrieving cache performance");
        }
    }
}
