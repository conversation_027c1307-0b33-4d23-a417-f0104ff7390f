using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using WhimLabAI.Client.Admin.Services;
using Xunit;

namespace WhimLabAI.Client.Admin.Tests.Services;

/// <summary>
/// AdminTokenService单元测试
/// 测试JWT token的存储、获取和清除功能
/// </summary>
public class AdminTokenServiceTests
{
    private readonly Mock<IHttpContextAccessor> _mockHttpContextAccessor;
    private readonly Mock<ILogger<AdminTokenService>> _mockLogger;
    private readonly Mock<HttpContext> _mockHttpContext;
    private readonly Mock<HttpRequest> _mockRequest;
    private readonly Mock<HttpResponse> _mockResponse;
    private readonly Mock<IRequestCookieCollection> _mockRequestCookies;
    private readonly Mock<IResponseCookies> _mockResponseCookies;
    private readonly AdminTokenService _adminTokenService;

    public AdminTokenServiceTests()
    {
        _mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
        _mockLogger = new Mock<ILogger<AdminTokenService>>();
        _mockHttpContext = new Mock<HttpContext>();
        _mockRequest = new Mock<HttpRequest>();
        _mockResponse = new Mock<HttpResponse>();
        _mockRequestCookies = new Mock<IRequestCookieCollection>();
        _mockResponseCookies = new Mock<IResponseCookies>();

        // 设置HttpContext模拟
        _mockHttpContext.Setup(x => x.Request).Returns(_mockRequest.Object);
        _mockHttpContext.Setup(x => x.Response).Returns(_mockResponse.Object);
        _mockRequest.Setup(x => x.Cookies).Returns(_mockRequestCookies.Object);
        _mockRequest.Setup(x => x.IsHttps).Returns(true);
        _mockResponse.Setup(x => x.Cookies).Returns(_mockResponseCookies.Object);
        _mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(_mockHttpContext.Object);

        _adminTokenService = new AdminTokenService(_mockHttpContextAccessor.Object, _mockLogger.Object);
    }

    [Fact]
    public void GetAccessToken_WhenTokenExists_ReturnsToken()
    {
        // Arrange
        const string expectedToken = "test-access-token";
        _mockRequestCookies.Setup(x => x.TryGetValue("admin-auth-token", out It.Ref<string>.IsAny))
            .Returns((string key, out string value) =>
            {
                value = expectedToken;
                return true;
            });

        // Act
        var result = _adminTokenService.GetAccessToken();

        // Assert
        Assert.Equal(expectedToken, result);
    }

    [Fact]
    public void GetAccessToken_WhenTokenDoesNotExist_ReturnsNull()
    {
        // Arrange
        _mockRequestCookies.Setup(x => x.TryGetValue("admin-auth-token", out It.Ref<string>.IsAny))
            .Returns((string key, out string value) =>
            {
                value = null!;
                return false;
            });

        // Act
        var result = _adminTokenService.GetAccessToken();

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void GetRefreshToken_WhenTokenExists_ReturnsToken()
    {
        // Arrange
        const string expectedToken = "test-refresh-token";
        _mockRequestCookies.Setup(x => x.TryGetValue("admin-refresh-token", out It.Ref<string>.IsAny))
            .Returns((string key, out string value) =>
            {
                value = expectedToken;
                return true;
            });

        // Act
        var result = _adminTokenService.GetRefreshToken();

        // Assert
        Assert.Equal(expectedToken, result);
    }

    [Fact]
    public void GetRefreshToken_WhenTokenDoesNotExist_ReturnsNull()
    {
        // Arrange
        _mockRequestCookies.Setup(x => x.TryGetValue("admin-refresh-token", out It.Ref<string>.IsAny))
            .Returns((string key, out string value) =>
            {
                value = null!;
                return false;
            });

        // Act
        var result = _adminTokenService.GetRefreshToken();

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void SetTokens_WithAccessTokenOnly_SetsAccessTokenCookie()
    {
        // Arrange
        const string accessToken = "test-access-token";

        // Act
        _adminTokenService.SetTokens(accessToken);

        // Assert
        _mockResponseCookies.Verify(x => x.Append(
            "admin-auth-token", 
            accessToken, 
            It.Is<CookieOptions>(opt => 
                opt.HttpOnly && 
                opt.Secure && 
                opt.SameSite == SameSiteMode.Strict &&
                opt.Expires.HasValue)), Times.Once);
    }

    [Fact]
    public void SetTokens_WithBothTokens_SetsBothCookies()
    {
        // Arrange
        const string accessToken = "test-access-token";
        const string refreshToken = "test-refresh-token";

        // Act
        _adminTokenService.SetTokens(accessToken, refreshToken);

        // Assert
        _mockResponseCookies.Verify(x => x.Append(
            "admin-auth-token", 
            accessToken, 
            It.IsAny<CookieOptions>()), Times.Once);
            
        _mockResponseCookies.Verify(x => x.Append(
            "admin-refresh-token", 
            refreshToken, 
            It.IsAny<CookieOptions>()), Times.Once);
    }

    [Fact]
    public void ClearTokens_RemovesBothCookies()
    {
        // Act
        _adminTokenService.ClearTokens();

        // Assert
        _mockResponseCookies.Verify(x => x.Delete("admin-auth-token"), Times.Once);
        _mockResponseCookies.Verify(x => x.Delete("admin-refresh-token"), Times.Once);
    }

    [Fact]
    public void HasToken_WhenTokenExists_ReturnsTrue()
    {
        // Arrange
        _mockRequestCookies.Setup(x => x.TryGetValue("admin-auth-token", out It.Ref<string>.IsAny))
            .Returns((string key, out string value) =>
            {
                value = "test-token";
                return true;
            });

        // Act
        var result = _adminTokenService.HasToken();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void HasToken_WhenTokenDoesNotExist_ReturnsFalse()
    {
        // Arrange
        _mockRequestCookies.Setup(x => x.TryGetValue("admin-auth-token", out It.Ref<string>.IsAny))
            .Returns((string key, out string value) =>
            {
                value = null!;
                return false;
            });

        // Act
        var result = _adminTokenService.HasToken();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void HasToken_WhenTokenIsEmpty_ReturnsFalse()
    {
        // Arrange
        _mockRequestCookies.Setup(x => x.TryGetValue("admin-auth-token", out It.Ref<string>.IsAny))
            .Returns((string key, out string value) =>
            {
                value = "";
                return true;
            });

        // Act
        var result = _adminTokenService.HasToken();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void GetAccessToken_WhenHttpContextIsNull_ReturnsNull()
    {
        // Arrange
        _mockHttpContextAccessor.Setup(x => x.HttpContext).Returns((HttpContext?)null);

        // Act
        var result = _adminTokenService.GetAccessToken();

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void SetTokens_WhenHttpContextIsNull_DoesNotThrow()
    {
        // Arrange
        _mockHttpContextAccessor.Setup(x => x.HttpContext).Returns((HttpContext?)null);

        // Act & Assert
        var exception = Record.Exception(() => _adminTokenService.SetTokens("test-token"));
        Assert.Null(exception);
    }

    [Fact]
    public void ClearTokens_WhenHttpContextIsNull_DoesNotThrow()
    {
        // Arrange
        _mockHttpContextAccessor.Setup(x => x.HttpContext).Returns((HttpContext?)null);

        // Act & Assert
        var exception = Record.Exception(() => _adminTokenService.ClearTokens());
        Assert.Null(exception);
    }
}
