using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using WhimLabAI.Client.Admin.Services;
using Xunit;

namespace WhimLabAI.Client.Admin.Tests.Services;

// 测试用的简化DTO类
public class AdminLoginRequestDto
{
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string CaptchaId { get; set; } = string.Empty;
    public string CaptchaCode { get; set; } = string.Empty;
}

public class AdminLoginResponseDto
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public string? AccessToken { get; set; }
    public string? RefreshToken { get; set; }
    public AdminUserDto? User { get; set; }
}

public class AdminUserDto
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
}

public class CaptchaResponseDto
{
    public string Id { get; set; } = string.Empty;
    public string ImageBase64 { get; set; } = string.Empty;
}

/// <summary>
/// ClientAuthService单元测试
/// 测试管理员认证服务的登录、注销功能
/// </summary>
public class ClientAuthServiceTests : IDisposable
{
    private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
    private readonly Mock<AuthenticationStateProvider> _mockAuthStateProvider;
    private readonly Mock<IAdminTokenService> _mockTokenService;
    private readonly Mock<ILogger<ClientAuthService>> _mockLogger;
    private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
    private readonly HttpClient _httpClient;
    private readonly ClientAuthService _clientAuthService;

    public ClientAuthServiceTests()
    {
        _mockHttpClientFactory = new Mock<IHttpClientFactory>();
        _mockAuthStateProvider = new Mock<AuthenticationStateProvider>();
        _mockTokenService = new Mock<IAdminTokenService>();
        _mockLogger = new Mock<ILogger<ClientAuthService>>();
        _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

        _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
        {
            BaseAddress = new Uri("http://localhost:15800/")
        };

        _mockHttpClientFactory.Setup(x => x.CreateClient("API")).Returns(_httpClient);

        _clientAuthService = new ClientAuthService(
            _mockHttpClientFactory.Object,
            _mockAuthStateProvider.Object,
            _mockTokenService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task LoginAsync_WithValidCredentials_ReturnsSuccessResult()
    {
        // Arrange
        var loginRequest = new AdminLoginRequestDto
        {
            Username = "admin",
            Password = "password123",
            CaptchaId = "captcha-id",
            CaptchaCode = "1234"
        };

        var loginResponse = new AdminLoginResponseDto
        {
            Success = true,
            AccessToken = "access-token",
            RefreshToken = "refresh-token",
            User = new AdminUserDto
            {
                Id = Guid.NewGuid(),
                Username = "admin",
                Email = "<EMAIL>"
            }
        };

        var responseContent = JsonSerializer.Serialize(loginResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
        };

        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Mock DatabaseAuthStateProvider
        var mockDatabaseProvider = new Mock<DatabaseAuthStateProvider>();
        _mockAuthStateProvider.As<DatabaseAuthStateProvider>()
            .Setup(x => x.MarkUserAsAuthenticated(It.IsAny<string>(), It.IsAny<Guid>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _clientAuthService.LoginAsync(loginRequest);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Success);
        Assert.Equal("access-token", result.AccessToken);
        Assert.Equal("refresh-token", result.RefreshToken);
        Assert.NotNull(result.User);
        Assert.Equal("admin", result.User.Username);

        // 验证token被设置
        _mockTokenService.Verify(x => x.SetTokens("access-token", "refresh-token"), Times.Once);
    }

    [Fact]
    public async Task LoginAsync_WithInvalidCredentials_ReturnsFailureResult()
    {
        // Arrange
        var loginRequest = new AdminLoginRequestDto
        {
            Username = "admin",
            Password = "wrong-password",
            CaptchaId = "captcha-id",
            CaptchaCode = "1234"
        };

        var loginResponse = new AdminLoginResponseDto
        {
            Success = false,
            Message = "Invalid credentials"
        };

        var responseContent = JsonSerializer.Serialize(loginResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
        };

        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _clientAuthService.LoginAsync(loginRequest);

        // Assert
        Assert.NotNull(result);
        Assert.False(result.Success);
        Assert.Equal("Invalid credentials", result.Message);

        // 验证token没有被设置
        _mockTokenService.Verify(x => x.SetTokens(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task LoginAsync_WhenHttpExceptionOccurs_ReturnsFailureResult()
    {
        // Arrange
        var loginRequest = new AdminLoginRequestDto
        {
            Username = "admin",
            Password = "password123",
            CaptchaId = "captcha-id",
            CaptchaCode = "1234"
        };

        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ThrowsAsync(new HttpRequestException("Network error"));

        // Act
        var result = await _clientAuthService.LoginAsync(loginRequest);

        // Assert
        Assert.NotNull(result);
        Assert.False(result.Success);
        Assert.Contains("Network error", result.Message);
    }

    [Fact]
    public async Task LoginAsync_SendsCorrectRequest()
    {
        // Arrange
        var loginRequest = new AdminLoginRequestDto
        {
            Username = "admin",
            Password = "password123",
            CaptchaId = "captcha-id",
            CaptchaCode = "1234"
        };

        HttpRequestMessage? capturedRequest = null;
        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Callback<HttpRequestMessage, CancellationToken>((request, _) => capturedRequest = request)
            .ReturnsAsync(new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent("{\"success\":false}", Encoding.UTF8, "application/json")
            });

        // Act
        await _clientAuthService.LoginAsync(loginRequest);

        // Assert
        Assert.NotNull(capturedRequest);
        Assert.Equal(HttpMethod.Post, capturedRequest.Method);
        Assert.Contains("api/admin-auth/login", capturedRequest.RequestUri?.ToString());
        
        var requestContent = await capturedRequest.Content!.ReadAsStringAsync();
        var sentRequest = JsonSerializer.Deserialize<AdminLoginRequestDto>(requestContent);
        Assert.Equal(loginRequest.Username, sentRequest?.Username);
        Assert.Equal(loginRequest.Password, sentRequest?.Password);
        Assert.Equal(loginRequest.CaptchaId, sentRequest?.CaptchaId);
        Assert.Equal(loginRequest.CaptchaCode, sentRequest?.CaptchaCode);
    }

    [Fact]
    public async Task LogoutAsync_CallsLogoutEndpointAndClearsTokens()
    {
        // Arrange
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK);

        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Mock DatabaseAuthStateProvider
        var mockDatabaseProvider = new Mock<DatabaseAuthStateProvider>();
        _mockAuthStateProvider.As<DatabaseAuthStateProvider>()
            .Setup(x => x.MarkUserAsLoggedOut())
            .Returns(Task.CompletedTask);

        // Act
        await _clientAuthService.LogoutAsync();

        // Assert
        _mockTokenService.Verify(x => x.ClearTokens(), Times.Once);
    }

    [Fact]
    public async Task LogoutAsync_WhenHttpExceptionOccurs_StillClearsTokens()
    {
        // Arrange
        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ThrowsAsync(new HttpRequestException("Network error"));

        // Mock DatabaseAuthStateProvider
        var mockDatabaseProvider = new Mock<DatabaseAuthStateProvider>();
        _mockAuthStateProvider.As<DatabaseAuthStateProvider>()
            .Setup(x => x.MarkUserAsLoggedOut())
            .Returns(Task.CompletedTask);

        // Act & Assert - 不应该抛出异常
        var exception = await Record.ExceptionAsync(() => _clientAuthService.LogoutAsync());
        Assert.Null(exception);

        // 验证仍然清除了tokens
        _mockTokenService.Verify(x => x.ClearTokens(), Times.Once);
    }

    [Fact]
    public async Task GetCaptchaAsync_ReturnsValidCaptcha()
    {
        // Arrange
        var captchaResponse = new CaptchaResponseDto
        {
            Id = "captcha-id",
            ImageBase64 = "base64-image-data"
        };

        var responseContent = JsonSerializer.Serialize(captchaResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
        };

        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _clientAuthService.GetCaptchaAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal("captcha-id", result.Id);
        Assert.Equal("base64-image-data", result.ImageBase64);
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
        GC.SuppressFinalize(this);
    }
}
