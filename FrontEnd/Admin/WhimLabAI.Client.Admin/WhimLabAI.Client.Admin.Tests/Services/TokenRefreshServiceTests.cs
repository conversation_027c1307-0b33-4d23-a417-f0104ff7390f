using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using WhimLabAI.Client.Admin.Services;
using Xunit;

namespace WhimLabAI.Client.Admin.Tests.Services;

/// <summary>
/// TokenRefreshService单元测试
/// 测试JWT token刷新功能
/// </summary>
public class TokenRefreshServiceTests : IDisposable
{
    private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
    private readonly Mock<ServerAuthStateProvider> _mockAuthStateProvider;
    private readonly Mock<IAdminTokenService> _mockTokenService;
    private readonly Mock<ILogger<TokenRefreshService>> _mockLogger;
    private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
    private readonly HttpClient _httpClient;
    private readonly TokenRefreshService _tokenRefreshService;

    public TokenRefreshServiceTests()
    {
        _mockHttpClientFactory = new Mock<IHttpClientFactory>();
        _mockAuthStateProvider = new Mock<ServerAuthStateProvider>();
        _mockTokenService = new Mock<IAdminTokenService>();
        _mockLogger = new Mock<ILogger<TokenRefreshService>>();
        _mockHttpMessageHandler = new Mock<HttpMessageHandler>();

        _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
        {
            BaseAddress = new Uri("http://localhost:15800/")
        };

        _mockHttpClientFactory.Setup(x => x.CreateClient("API")).Returns(_httpClient);

        _tokenRefreshService = new TokenRefreshService(
            _mockHttpClientFactory.Object,
            _mockAuthStateProvider.Object,
            _mockTokenService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task RefreshTokenAsync_WhenRefreshTokenExists_ReturnsTrue()
    {
        // Arrange
        const string refreshToken = "test-refresh-token";
        const string newAccessToken = "new-access-token";
        const string newRefreshToken = "new-refresh-token";

        _mockTokenService.Setup(x => x.GetRefreshToken()).Returns(refreshToken);

        var tokenResponse = new
        {
            AccessToken = newAccessToken,
            RefreshToken = newRefreshToken,
            ExpiresIn = 3600
        };

        var responseContent = JsonSerializer.Serialize(tokenResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
        };

        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _tokenRefreshService.RefreshTokenAsync();

        // Assert
        Assert.True(result);
        _mockAuthStateProvider.Verify(x => x.UpdateTokenAsync(newAccessToken, newRefreshToken), Times.Once);
        _mockTokenService.Verify(x => x.SetTokens(newAccessToken, newRefreshToken), Times.Once);
    }

    [Fact]
    public async Task RefreshTokenAsync_WhenRefreshTokenDoesNotExist_ReturnsFalse()
    {
        // Arrange
        _mockTokenService.Setup(x => x.GetRefreshToken()).Returns((string?)null);

        // Act
        var result = await _tokenRefreshService.RefreshTokenAsync();

        // Assert
        Assert.False(result);
        _mockAuthStateProvider.Verify(x => x.UpdateTokenAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        _mockTokenService.Verify(x => x.SetTokens(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task RefreshTokenAsync_WhenApiReturnsError_ReturnsFalse()
    {
        // Arrange
        const string refreshToken = "test-refresh-token";
        _mockTokenService.Setup(x => x.GetRefreshToken()).Returns(refreshToken);

        var httpResponse = new HttpResponseMessage(HttpStatusCode.Unauthorized);

        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _tokenRefreshService.RefreshTokenAsync();

        // Assert
        Assert.False(result);
        _mockAuthStateProvider.Verify(x => x.UpdateTokenAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        _mockTokenService.Verify(x => x.SetTokens(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task RefreshTokenAsync_WhenApiReturnsInvalidJson_ReturnsFalse()
    {
        // Arrange
        const string refreshToken = "test-refresh-token";
        _mockTokenService.Setup(x => x.GetRefreshToken()).Returns(refreshToken);

        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent("invalid json", Encoding.UTF8, "application/json")
        };

        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _tokenRefreshService.RefreshTokenAsync();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task RefreshTokenAsync_WhenHttpExceptionOccurs_ReturnsFalse()
    {
        // Arrange
        const string refreshToken = "test-refresh-token";
        _mockTokenService.Setup(x => x.GetRefreshToken()).Returns(refreshToken);

        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ThrowsAsync(new HttpRequestException("Network error"));

        // Act
        var result = await _tokenRefreshService.RefreshTokenAsync();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task CheckAndRefreshTokenAsync_WhenTokenNeedsRefresh_CallsRefreshToken()
    {
        // Arrange
        const string refreshToken = "test-refresh-token";
        const string newAccessToken = "new-access-token";

        _mockTokenService.Setup(x => x.GetRefreshToken()).Returns(refreshToken);

        var tokenResponse = new
        {
            AccessToken = newAccessToken,
            RefreshToken = refreshToken,
            ExpiresIn = 3600
        };

        var responseContent = JsonSerializer.Serialize(tokenResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
        };

        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        await _tokenRefreshService.CheckAndRefreshTokenAsync();

        // Assert
        _mockAuthStateProvider.Verify(x => x.UpdateTokenAsync(newAccessToken, refreshToken), Times.Once);
        _mockTokenService.Verify(x => x.SetTokens(newAccessToken, refreshToken), Times.Once);
    }

    [Fact]
    public async Task RefreshTokenAsync_SendsCorrectRequest()
    {
        // Arrange
        const string refreshToken = "test-refresh-token";
        _mockTokenService.Setup(x => x.GetRefreshToken()).Returns(refreshToken);

        HttpRequestMessage? capturedRequest = null;
        _mockHttpMessageHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Callback<HttpRequestMessage, CancellationToken>((request, _) => capturedRequest = request)
            .ReturnsAsync(new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent("{\"accessToken\":\"new-token\",\"refreshToken\":\"new-refresh\",\"expiresIn\":3600}", 
                    Encoding.UTF8, "application/json")
            });

        // Act
        await _tokenRefreshService.RefreshTokenAsync();

        // Assert
        Assert.NotNull(capturedRequest);
        Assert.Equal(HttpMethod.Post, capturedRequest.Method);
        Assert.Contains("admin-auth/refresh", capturedRequest.RequestUri?.ToString());
        
        var requestContent = await capturedRequest.Content!.ReadAsStringAsync();
        var refreshRequest = JsonSerializer.Deserialize<dynamic>(requestContent);
        // 由于使用了匿名对象，我们检查JSON字符串内容
        Assert.Contains($"\"refreshToken\":\"{refreshToken}\"", requestContent);
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
        GC.SuppressFinalize(this);
    }
}
