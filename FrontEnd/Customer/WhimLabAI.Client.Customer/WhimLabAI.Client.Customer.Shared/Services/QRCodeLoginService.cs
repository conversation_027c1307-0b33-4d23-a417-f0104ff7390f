using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR.Client;
using WhimLabAI.Shared.Dtos.CustomerAuth;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Client.Customer.Shared.Services
{
    /// <summary>
    /// 扫码登录服务
    /// </summary>
    public class QRCodeLoginService : IAsyncDisposable
    {
        private readonly HttpClient _httpClient;
        private HubConnection? _hubConnection;
        private string? _currentQrKey;

        // 事件
        public event Action<string>? OnStatusChanged;
        public event Action<string, UserInfo>? OnLoginSuccess;

        public QRCodeLoginService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        /// <summary>
        /// 生成登录二维码
        /// </summary>
        public async Task<QRCodeGenerateResponseDto?> GenerateQRCodeAsync()
        {
            try
            {
                var response = await _httpClient.PostAsync("api/customer/auth/qrcode/generate", null);
                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<QRCodeApiResponse<QRCodeGenerateResponseDto>>(
                        jsonContent,
                        new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    if (result?.Success == true && result.Data != null)
                    {
                        return result.Data;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to generate QR code: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 开始监听扫码状态
        /// </summary>
        public async Task StartListeningAsync(string qrKey)
        {
            try
            {
                _currentQrKey = qrKey;

                // 构建SignalR连接
                var hubUrl = GetHubUrl();
                _hubConnection = new HubConnectionBuilder()
                    .WithUrl(hubUrl)
                    .WithAutomaticReconnect()
                    .Build();

                // 注册事件处理器
                _hubConnection.On<string>("OnQRCodeScanned", (key) =>
                {
                    if (key == _currentQrKey)
                    {
                        OnStatusChanged?.Invoke("scanned");
                    }
                });

                _hubConnection.On<string, string, string>("OnQRCodeAuthenticated", (key, accessToken, userInfoJson) =>
                {
                    if (key == _currentQrKey)
                    {
                        try
                        {
                            OnStatusChanged?.Invoke("authenticated");

                            // 解析用户信息
                            var userInfo = System.Text.Json.JsonSerializer.Deserialize<UserInfo>(
                                userInfoJson,
                                new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                            if (userInfo != null)
                            {
                                OnLoginSuccess?.Invoke(accessToken, userInfo);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Failed to parse authentication data: {ex.Message}");
                            OnStatusChanged?.Invoke("failed");
                        }
                    }
                });

                _hubConnection.On<string>("OnQRCodeFailed", (key) =>
                {
                    if (key == _currentQrKey)
                    {
                        OnStatusChanged?.Invoke("failed");
                    }
                });

                // 启动连接
                await _hubConnection.StartAsync();

                // 加入监听组
                await _hubConnection.InvokeAsync("JoinQRCodeGroup", qrKey);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to start QR code listening: {ex.Message}");
                OnStatusChanged?.Invoke("failed");
            }
        }

        /// <summary>
        /// 停止监听
        /// </summary>
        public async Task StopListeningAsync()
        {
            try
            {
                if (_hubConnection != null && _currentQrKey != null)
                {
                    if (_hubConnection.State == HubConnectionState.Connected)
                    {
                        await _hubConnection.InvokeAsync("LeaveQRCodeGroup", _currentQrKey);
                        await _hubConnection.StopAsync();
                    }

                    await _hubConnection.DisposeAsync();
                    _hubConnection = null;
                }

                _currentQrKey = null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error stopping QR code listening: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取SignalR Hub URL
        /// </summary>
        private string GetHubUrl()
        {
            var baseUrl = _httpClient.BaseAddress?.ToString() ?? "https://localhost:15801";
            // 移除末尾的斜杠
            baseUrl = baseUrl.TrimEnd('/');
            // 移除 api 前缀（如果有）
            if (baseUrl.EndsWith("/api", StringComparison.OrdinalIgnoreCase))
            {
                baseUrl = baseUrl.Substring(0, baseUrl.Length - 4);
            }
            return $"{baseUrl}/hubs/qrcode";
        }

        public async ValueTask DisposeAsync()
        {
            await StopListeningAsync();
        }
    }

    /// <summary>
    /// API响应包装（本地定义，避免与后端ApiResult冲突）
    /// </summary>
    internal class QRCodeApiResponse<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string? Message { get; set; }
    }
}
