using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Auth;

namespace WhimLabAI.Application.Validators.Auth;

public class AdminLoginDtoValidator : AbstractValidator<AdminLoginDto>
{
    public AdminLoginDtoValidator()
    {
        RuleFor(x => x.Account)
            .NotEmpty().WithMessage("Account is required")
            .MinimumLength(3).WithMessage("Account must be at least 3 characters")
            .MaximumLength(50).WithMessage("Account must not exceed 50 characters")
            .SafeInput();

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(6).WithMessage("Password must be at least 6 characters")
            .MaximumLength(100).WithMessage("Password must not exceed 100 characters")
            .When(x => x.LoginMethod == AdminLoginMethod.Password);

        RuleFor(x => x.TwoFactorCode)
            .Matches(@"^\d{6}$").WithMessage("Two-factor code must be 6 digits")
            .When(x => !string.IsNullOrEmpty(x.TwoFactorCode));
            
        RuleFor(x => x.CaptchaCode)
            .NotEmpty().WithMessage("Captcha code is required");
            
        RuleFor(x => x.CaptchaId)
            .NotEmpty().WithMessage("Captcha ID is required");

        RuleFor(x => x.IpAddress)
            .MaximumLength(45).WithMessage("IP address must not exceed 45 characters")
            .When(x => !string.IsNullOrEmpty(x.IpAddress));
    }
}