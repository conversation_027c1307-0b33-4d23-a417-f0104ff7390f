using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Auth.Customer;

namespace WhimLabAI.Application.Validators.Auth;

public class CustomerChangePasswordDtoValidator : AbstractValidator<CustomerChangePasswordDto>
{
    public CustomerChangePasswordDtoValidator()
    {
        RuleFor(x => x.OldPassword)
            .NotEmpty().WithMessage("原密码不能为空")
            .MinimumLength(6).WithMessage("原密码长度至少6个字符");

        RuleFor(x => x.NewPassword)
            .StrongPassword()
            .NotEqual(x => x.OldPassword).WithMessage("新密码不能与原密码相同");
    }
}