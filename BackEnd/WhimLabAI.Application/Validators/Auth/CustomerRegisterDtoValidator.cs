using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Auth;

namespace WhimLabAI.Application.Validators.Auth;

public class CustomerRegisterDtoValidator : AbstractValidator<CustomerRegisterDto>
{
    public CustomerRegisterDtoValidator()
    {
        RuleFor(x => x.Username)
            .ValidUsername();

        RuleFor(x => x.Password)
            .StrongPassword();

        RuleFor(x => x.Email)
            .ValidEmail()
            .When(x => !string.IsNullOrEmpty(x.Email));

        RuleFor(x => x.PhoneNumber)
            .ValidPhone()
            .When(x => !string.IsNullOrEmpty(x.PhoneNumber));

        RuleFor(x => x.LoginMethod)
            .IsInEnum().WithMessage("Invalid login method");

        RuleFor(x => x.InviteCode)
            .MaximumLength(50).WithMessage("Invite code must not exceed 50 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.InviteCode));

        RuleFor(x => x.DeviceId)
            .MaximumLength(100).WithMessage("Device ID must not exceed 100 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.DeviceId));

        RuleFor(x => x.DeviceName)
            .MaximumLength(100).WithMessage("Device name must not exceed 100 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.DeviceName));

        RuleFor(x => x.DeviceType)
            .MaximumLength(50).WithMessage("Device type must not exceed 50 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.DeviceType));

        // At least one contact method is required
        RuleFor(x => x)
            .Must(x => !string.IsNullOrEmpty(x.Email) || !string.IsNullOrEmpty(x.PhoneNumber))
            .WithMessage("At least one contact method (email or phone) is required");
    }
}