using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Auth;

namespace WhimLabAI.Application.Validators.Auth;

public class SendVerificationCodeDtoValidator : AbstractValidator<SendVerificationCodeDto>
{
    public SendVerificationCodeDtoValidator()
    {
        RuleFor(x => x.Target)
            .NotEmpty().WithMessage("目标不能为空")
            .Must(BeValidTarget).WithMessage("目标必须是有效的邮箱或手机号")
            .SafeInput();

        RuleFor(x => x.Type)
            .NotEmpty().WithMessage("类型不能为空")
            .Must(x => new[] { "Login", "Register", "ResetPassword" }.Contains(x))
            .WithMessage("无效的验证码类型");
    }

    private bool BeValidTarget(string target)
    {
        if (string.IsNullOrWhiteSpace(target))
            return false;

        // Check if it's an email
        if (target.Contains('@'))
            return System.Text.RegularExpressions.Regex.IsMatch(target, @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");

        // Check if it's a phone number
        return System.Text.RegularExpressions.Regex.IsMatch(target, @"^(\+?[1-9]\d{0,14}|\d{10,15})$");
    }
}