using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Auth;

namespace WhimLabAI.Application.Validators.Auth;

public class CustomerLoginDtoValidator : AbstractValidator<CustomerLoginDto>
{
    public CustomerLoginDtoValidator()
    {
        RuleFor(x => x.Account)
            .NotEmpty().WithMessage("Account is required")
            .MaximumLength(255).WithMessage("Account must not exceed 255 characters")
            .SafeInput()
            .Must(BeValidAccountFormat).WithMessage("Account must be a valid email, phone number, or username");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(6).WithMessage("Password must be at least 6 characters")
            .MaximumLength(100).WithMessage("Password must not exceed 100 characters");

        RuleFor(x => x.DeviceId)
            .MaximumLength(100).WithMessage("Device ID must not exceed 100 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.DeviceId));

        RuleFor(x => x.DeviceName)
            .MaximumLength(200).WithMessage("Device name must not exceed 200 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.DeviceName));

        RuleFor(x => x.DeviceType)
            .MaximumLength(50).WithMessage("Device type must not exceed 50 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.DeviceType));

        RuleFor(x => x.IpAddress)
            .MaximumLength(45).WithMessage("IP address must not exceed 45 characters")
            .Matches(@"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$")
            .WithMessage("Invalid IP address format")
            .When(x => !string.IsNullOrEmpty(x.IpAddress));
    }

    private bool BeValidAccountFormat(string account)
    {
        if (string.IsNullOrWhiteSpace(account))
            return false;

        // Check if it's an email
        if (account.Contains('@'))
            return System.Text.RegularExpressions.Regex.IsMatch(account, @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");

        // Check if it's a phone number (digits only, 10-15 characters)
        if (System.Text.RegularExpressions.Regex.IsMatch(account, @"^\d+$"))
            return account.Length >= 10 && account.Length <= 15;

        // Otherwise, treat as username (3-30 characters, alphanumeric with underscore and dash)
        return System.Text.RegularExpressions.Regex.IsMatch(account, @"^[a-zA-Z0-9_-]{3,30}$");
    }
}