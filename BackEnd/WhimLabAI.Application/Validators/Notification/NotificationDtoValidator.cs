using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Notification;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Application.Validators.Notification;

public class CreateNotificationDtoValidator : AbstractValidator<CreateNotificationDto>
{
    public CreateNotificationDtoValidator()
    {
        RuleFor(x => x.UserId)
            .ValidGuid();

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("通知标题不能为空")
            .MaximumLength(200).WithMessage("通知标题不能超过200个字符")
            .SafeInput();

        RuleFor(x => x.Content)
            .NotEmpty().WithMessage("通知内容不能为空")
            .MaximumLength(2000).WithMessage("通知内容不能超过2000个字符")
            .SafeInput();

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage("无效的通知类型");

        RuleFor(x => x.Priority)
            .InclusiveBetween(1, 5).WithMessage("优先级必须在1到5之间");

        RuleFor(x => x.ActionUrl)
            .ValidUrl()
            .When(x => !string.IsNullOrEmpty(x.ActionUrl));

        RuleFor(x => x.ActionText)
            .MaximumLength(50).WithMessage("操作文本不能超过50个字符")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.ActionText));

        RuleFor(x => x.ExpiresAt)
            .GreaterThan(DateTime.UtcNow).WithMessage("过期时间必须大于当前时间")
            .When(x => x.ExpiresAt.HasValue);

        RuleFor(x => x.Metadata)
            .Must(metadata => metadata == null || metadata.Count <= 20).WithMessage("元数据最多20个")
            .When(x => x.Metadata != null);

        RuleFor(x => x.Tags)
            .Must(tags => tags == null || tags.Count <= 5).WithMessage("标签最多5个")
            .ForEach(tag => tag.MaximumLength(30).SafeInput())
            .When(x => x.Tags != null);
    }
}

public class SendBatchNotificationDtoValidator : AbstractValidator<SendBatchNotificationDto>
{
    public SendBatchNotificationDtoValidator()
    {
        RuleFor(x => x.UserIds)
            .NotEmptyCollection()
            .Must(userIds => userIds.Count() <= 1000).WithMessage("批量发送最多支持1000个用户")
            .ForEach(userId => userId.NotEmpty().Must(id => Guid.TryParse(id, out _)).WithMessage("无效的用户ID格式"));

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("通知标题不能为空")
            .MaximumLength(200).WithMessage("通知标题不能超过200个字符")
            .SafeInput();

        RuleFor(x => x.Content)
            .NotEmpty().WithMessage("通知内容不能为空")
            .MaximumLength(2000).WithMessage("通知内容不能超过2000个字符")
            .SafeInput();

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage("无效的通知类型");

        RuleFor(x => x.Priority)
            .InclusiveBetween(1, 5).WithMessage("优先级必须在1到5之间");

        RuleFor(x => x.ScheduledAt)
            .GreaterThan(DateTime.UtcNow).WithMessage("计划发送时间必须大于当前时间")
            .When(x => x.ScheduledAt.HasValue);
    }
}

public class MarkNotificationReadDtoValidator : AbstractValidator<MarkNotificationReadDto>
{
    public MarkNotificationReadDtoValidator()
    {
        RuleFor(x => x.NotificationIds)
            .NotEmptyCollection()
            .Must(ids => ids.Count() <= 100).WithMessage("批量标记最多支持100个通知")
            .ForEach(id => id.NotEmpty().Must(nid => Guid.TryParse(nid, out _)).WithMessage("无效的通知ID格式"));
    }
}