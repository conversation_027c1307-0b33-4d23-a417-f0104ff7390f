using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Conversation;

namespace WhimLabAI.Application.Validators.Conversation;

public class SendMessageDtoValidator : AbstractValidator<SendMessageDto>
{
    public SendMessageDtoValidator()
    {
        RuleFor(x => x.ConversationId)
            .ValidGuid();

        RuleFor(x => x.Content)
            .NotEmpty().WithMessage("Message content is required")
            .MaximumLength(5000).WithMessage("Message content must not exceed 5000 characters")
            .SafeInput();

        RuleFor(x => x.Attachments)
            .Must(attachments => attachments == null || attachments.Count <= 10).WithMessage("Maximum 10 attachments allowed")
            .ForEach(attachment => attachment.SetValidator(new MessageAttachmentValidator()))
            .When(x => x.Attachments != null);

        RuleFor(x => x.ReplyToMessageId)
            .ValidGuid()
            .When(x => !string.IsNullOrEmpty(x.ReplyToMessageId));

        RuleFor(x => x.Metadata)
            .Must(metadata => metadata == null || metadata.Count <= 20).WithMessage("Metadata can contain at most 20 items")
            .When(x => x.Metadata != null);
    }
}

public class MessageAttachmentValidator : AbstractValidator<MessageAttachmentDto>
{
    public MessageAttachmentValidator()
    {
        RuleFor(x => x.FileName)
            .NotEmpty().WithMessage("File name is required")
            .MaximumLength(255).WithMessage("File name must not exceed 255 characters")
            .SafeInput();

        RuleFor(x => x.FileSize)
            .MaxFileSize(50); // 50MB max

        RuleFor(x => x.ContentType)
            .NotEmpty().WithMessage("Content type is required")
            .MaximumLength(100).WithMessage("Content type must not exceed 100 characters")
            .Must(BeValidContentType).WithMessage("Invalid content type");

        RuleFor(x => x.Url)
            .ValidUrl()
            .When(x => !string.IsNullOrEmpty(x.Url));
    }

    private bool BeValidContentType(string contentType)
    {
        var allowedTypes = new[]
        {
            "image/jpeg", "image/png", "image/gif", "image/webp", "image/svg+xml",
            "application/pdf", "text/plain", "text/markdown",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/json", "application/xml"
        };

        return allowedTypes.Contains(contentType.ToLower());
    }
}