using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Conversation;

namespace WhimLabAI.Application.Validators.Conversation;

public class CreateConversationDtoValidator : AbstractValidator<CreateConversationDto>
{
    public CreateConversationDtoValidator()
    {
        RuleFor(x => x.AgentId)
            .ValidGuid();

        RuleFor(x => x.Title)
            .MaximumLength(200).WithMessage("Title must not exceed 200 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.Title));

        RuleFor(x => x.InitialMessage)
            .NotEmpty().WithMessage("Initial message is required")
            .MaximumLength(5000).WithMessage("Initial message must not exceed 5000 characters")
            .SafeInput();

        RuleFor(x => x.Context)
            .Must(context => context == null || context.Count <= 50).WithMessage("Context can contain at most 50 items")
            .When(x => x.Context != null);

        RuleFor(x => x.SessionId)
            .MaximumLength(100).WithMessage("Session ID must not exceed 100 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.SessionId));

        RuleFor(x => x.ClientPlatform)
            .MaximumLength(50).WithMessage("Client platform must not exceed 50 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.ClientPlatform));
    }
}