using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Payment;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Application.Validators.Payment;

public class CreatePaymentDtoValidator : AbstractValidator<CreatePaymentDto>
{
    public CreatePaymentDtoValidator()
    {
        RuleFor(x => x.Amount)
            .ValidAmount();

        RuleFor(x => x.Currency)
            .NotEmpty().WithMessage("Currency is required")
            .Must(x => x == "CNY").WithMessage("Only CNY currency is supported");

        RuleFor(x => x.PaymentMethod)
            .IsInEnum().WithMessage("Invalid payment method");

        RuleFor(x => x.OrderId)
            .ValidGuid();

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("Description is required")
            .MaximumLength(500).WithMessage("Description must not exceed 500 characters")
            .SafeInput();

        RuleFor(x => x.ReturnUrl)
            .ValidUrl()
            .When(x => !string.IsNullOrEmpty(x.ReturnUrl));

        RuleFor(x => x.NotifyUrl)
            .ValidUrl()
            .When(x => !string.IsNullOrEmpty(x.NotifyUrl));

        RuleFor(x => x.ClientIp)
            .NotEmpty().WithMessage("Client IP is required")
            .MaximumLength(45).WithMessage("Client IP must not exceed 45 characters");

        RuleFor(x => x.Metadata)
            .Must(x => x == null || x.Count <= 20).WithMessage("Metadata can contain at most 20 items")
            .When(x => x.Metadata != null);
    }
}