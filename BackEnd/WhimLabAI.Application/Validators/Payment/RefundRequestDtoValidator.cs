using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Payment;

namespace WhimLabAI.Application.Validators.Payment;

public class RefundRequestDtoValidator : AbstractValidator<RefundRequestDto>
{
    public RefundRequestDtoValidator()
    {
        RuleFor(x => x.PaymentId)
            .ValidGuid();

        RuleFor(x => x.Amount)
            .ValidAmount();

        RuleFor(x => x.Reason)
            .NotEmpty().WithMessage("Refund reason is required")
            .MinimumLength(10).WithMessage("Refund reason must be at least 10 characters")
            .MaximumLength(1000).WithMessage("Refund reason must not exceed 1000 characters")
            .SafeInput();

        RuleFor(x => x.RefundType)
            .NotEmpty().WithMessage("Refund type is required")
            .Must(x => x == "FULL" || x == "PARTIAL").WithMessage("Refund type must be FULL or PARTIAL");

        RuleFor(x => x.CustomerNotes)
            .MaximumLength(500).WithMessage("Customer notes must not exceed 500 characters")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.CustomerNotes));
    }
}