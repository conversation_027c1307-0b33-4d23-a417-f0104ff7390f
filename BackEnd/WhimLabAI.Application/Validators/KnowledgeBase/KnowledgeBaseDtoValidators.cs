using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.Application.Validators.KnowledgeBase;

public class CreateKnowledgeBaseDtoValidator : AbstractValidator<CreateKnowledgeBaseDto>
{
    public CreateKnowledgeBaseDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("知识库名称不能为空")
            .MinimumLength(2).WithMessage("知识库名称至少2个字符")
            .MaximumLength(100).WithMessage("知识库名称不能超过100个字符")
            .SafeInput();

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("描述不能超过500个字符")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.EmbeddingModel)
            .NotEmpty().WithMessage("嵌入模型不能为空")
            .MaximumLength(50).WithMessage("嵌入模型名称不能超过50个字符")
            .SafeInput();

        RuleFor(x => x.ChunkSize)
            .InclusiveBetween(100, 4000).WithMessage("分块大小必须在100到4000之间");

        RuleFor(x => x.ChunkOverlap)
            .InclusiveBetween(0, 500).WithMessage("分块重叠必须在0到500之间")
            .LessThan(x => x.ChunkSize).WithMessage("分块重叠必须小于分块大小");

        RuleFor(x => x.Tags)
            .Must(tags => tags == null || tags.Count <= 10).WithMessage("标签最多10个")
            .ForEach(tag => tag.MaximumLength(30).SafeInput())
            .When(x => x.Tags != null);

        RuleFor(x => x.IsPublic)
            .NotNull().WithMessage("必须指定是否公开");

        RuleFor(x => x.MaxFileSize)
            .InclusiveBetween(1, 100).WithMessage("最大文件大小必须在1MB到100MB之间");
    }
}

public class UpdateKnowledgeBaseDtoValidator : AbstractValidator<UpdateKnowledgeBaseDto>
{
    public UpdateKnowledgeBaseDtoValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("ID不能为空");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("知识库名称不能为空")
            .MinimumLength(2).WithMessage("知识库名称至少2个字符")
            .MaximumLength(100).WithMessage("知识库名称不能超过100个字符")
            .SafeInput();

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("描述不能超过500个字符")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.Tags)
            .Must(tags => tags == null || tags.Count <= 10).WithMessage("标签最多10个")
            .ForEach(tag => tag.MaximumLength(30).SafeInput())
            .When(x => x.Tags != null);

        RuleFor(x => x.IsPublic)
            .NotNull().WithMessage("必须指定是否公开");
    }
}

public class UploadDocumentDtoValidator : AbstractValidator<UploadDocumentDto>
{
    private readonly string[] _allowedExtensions = { ".txt", ".pdf", ".md", ".docx", ".csv", ".json", ".xml" };
    
    public UploadDocumentDtoValidator()
    {
        RuleFor(x => x.FileName)
            .NotEmpty().WithMessage("文件名不能为空")
            .MaximumLength(255).WithMessage("文件名不能超过255个字符")
            .SafeInput()
            .Must(BeValidFileName).WithMessage("文件名包含无效字符")
            .Must(HaveAllowedExtension).WithMessage($"不支持的文件类型，允许的类型：{string.Join(", ", _allowedExtensions)}");

        RuleFor(x => x.ContentType)
            .NotEmpty().WithMessage("内容类型不能为空")
            .Must(BeAllowedContentType).WithMessage("不支持的内容类型");

        RuleFor(x => x.FileSize)
            .GreaterThan(0).WithMessage("文件大小必须大于0")
            .MaxFileSize(50); // 50MB

        RuleFor(x => x.Metadata)
            .Must(metadata => metadata == null || metadata.Count <= 20).WithMessage("元数据最多20个")
            .When(x => x.Metadata != null);
    }

    private bool BeValidFileName(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            return false;

        // 检查路径遍历攻击
        if (fileName.Contains("..") || fileName.Contains("/") || fileName.Contains("\\"))
            return false;

        var invalidChars = Path.GetInvalidFileNameChars();
        return !fileName.Any(ch => invalidChars.Contains(ch));
    }

    private bool HaveAllowedExtension(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            return false;

        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return _allowedExtensions.Contains(extension);
    }

    private bool BeAllowedContentType(string contentType)
    {
        var allowedTypes = new[]
        {
            "text/plain", "application/pdf", "text/markdown", 
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/csv", "application/json", "application/xml", "text/xml"
        };

        return allowedTypes.Contains(contentType.ToLower());
    }
}

public class VectorSearchRequestValidator : AbstractValidator<VectorSearchRequest>
{
    public VectorSearchRequestValidator()
    {
        RuleFor(x => x.KnowledgeBaseId)
            .NotEmpty().WithMessage("知识库ID不能为空");

        RuleFor(x => x.Query)
            .NotEmpty().WithMessage("查询内容不能为空")
            .MaximumLength(1000).WithMessage("查询内容不能超过1000个字符")
            .SafeInput();

        RuleFor(x => x.TopK)
            .InclusiveBetween(1, 100).WithMessage("TopK必须在1到100之间");

        RuleFor(x => x.ScoreThreshold)
            .InclusiveBetween(0, 1).WithMessage("相似度阈值必须在0到1之间");

        RuleFor(x => x.Filters)
            .Must(filters => filters == null || filters.Count <= 10).WithMessage("过滤条件最多10个")
            .When(x => x.Filters != null);
    }
}