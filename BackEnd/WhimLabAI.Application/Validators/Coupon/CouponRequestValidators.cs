using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Coupon;

namespace WhimLabAI.Application.Validators.Coupon;

public class CreateCouponRequestValidator : AbstractValidator<CreateCouponRequest>
{
    public CreateCouponRequestValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("优惠券代码不能为空")
            .MaximumLength(50).WithMessage("优惠券代码长度不能超过50个字符")
            .Matches(@"^[A-Z0-9_-]+$").WithMessage("优惠券代码只能包含大写字母、数字、下划线和破折号")
            .SafeInput();

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("优惠券名称不能为空")
            .MaximumLength(100).WithMessage("优惠券名称长度不能超过100个字符")
            .SafeInput();

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("描述长度不能超过500个字符")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.Type)
            .NotEmpty().WithMessage("优惠券类型不能为空")
            .Must(x => x == "FixedAmount" || x == "Percentage")
            .WithMessage("优惠券类型必须是 FixedAmount 或 Percentage");

        // Conditional validation based on type
        When(x => x.Type == "FixedAmount", () =>
        {
            RuleFor(x => x.DiscountAmount)
                .NotNull().WithMessage("固定金额优惠券必须设置折扣金额")
                .GreaterThan(0).WithMessage("折扣金额必须大于0")
                .PrecisionScale(10, 2, true).WithMessage("折扣金额最多有2位小数")
                .When(x => x.DiscountAmount.HasValue);

            RuleFor(x => x.DiscountCurrency)
                .NotEmpty().WithMessage("固定金额优惠券必须设置货币")
                .Length(3).WithMessage("货币代码长度必须是3个字符")
                .Must(x => x == "CNY").WithMessage("目前只支持CNY货币");
        });

        When(x => x.Type == "Percentage", () =>
        {
            RuleFor(x => x.DiscountPercentage)
                .NotNull().WithMessage("百分比优惠券必须设置折扣百分比")
                .InclusiveBetween(0.01m, 100).WithMessage("折扣百分比必须在0.01到100之间");
        });

        RuleFor(x => x.MinimumAmount)
            .GreaterThanOrEqualTo(0).WithMessage("最低消费金额不能为负数")
            .When(x => x.MinimumAmount.HasValue);

        RuleFor(x => x.MinimumCurrency)
            .Length(3).WithMessage("货币代码长度必须是3个字符")
            .Must(x => x == "CNY").WithMessage("目前只支持CNY货币")
            .When(x => !string.IsNullOrEmpty(x.MinimumCurrency));

        RuleFor(x => x.MaximumDiscount)
            .GreaterThanOrEqualTo(0).WithMessage("最大折扣金额不能为负数")
            .When(x => x.MaximumDiscount.HasValue);

        RuleFor(x => x.MaximumDiscountCurrency)
            .Length(3).WithMessage("货币代码长度必须是3个字符")
            .Must(x => x == "CNY").WithMessage("目前只支持CNY货币")
            .When(x => !string.IsNullOrEmpty(x.MaximumDiscountCurrency));

        RuleFor(x => x.ValidFrom)
            .NotEmpty().WithMessage("有效期开始时间不能为空")
            .GreaterThanOrEqualTo(DateTime.UtcNow.AddMinutes(-5))
            .WithMessage("有效期开始时间不能早于当前时间");

        RuleFor(x => x.ValidTo)
            .NotEmpty().WithMessage("有效期结束时间不能为空")
            .GreaterThan(x => x.ValidFrom).WithMessage("有效期结束时间必须晚于开始时间")
            .LessThanOrEqualTo(DateTime.UtcNow.AddYears(2))
            .WithMessage("有效期结束时间不能超过2年后");

        RuleFor(x => x.TotalQuota)
            .GreaterThanOrEqualTo(-1).WithMessage("总配额必须大于等于-1（-1表示无限制）");

        RuleFor(x => x.UsagePerUser)
            .GreaterThan(0).WithMessage("每用户使用次数必须大于0")
            .When(x => x.UsagePerUser.HasValue);

        RuleFor(x => x.Scope)
            .Must(x => new[] { "All", "Subscription", "TokenPackage", "Specific" }.Contains(x))
            .WithMessage("适用范围必须是 All, Subscription, TokenPackage 或 Specific");

        RuleFor(x => x.ApplicableProducts)
            .NotEmptyCollection()
            .ForEach(product => product.ValidGuid())
            .When(x => x.Scope == "Specific");

        RuleFor(x => x.Rules)
            .Must(x => x == null || x.Count <= 10).WithMessage("规则最多只能有10个")
            .When(x => x.Rules != null);

        RuleFor(x => x.Metadata)
            .Must(x => x == null || x.Count <= 20).WithMessage("元数据最多只能有20个")
            .When(x => x.Metadata != null);
    }
}

public class UpdateCouponRequestValidator : AbstractValidator<UpdateCouponRequest>
{
    public UpdateCouponRequestValidator()
    {
        RuleFor(x => x.Name)
            .MaximumLength(100).WithMessage("优惠券名称长度不能超过100个字符")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.Name));

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("描述长度不能超过500个字符")
            .SafeInput()
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.MinimumAmount)
            .GreaterThanOrEqualTo(0).WithMessage("最低消费金额不能为负数")
            .When(x => x.MinimumAmount.HasValue);

        RuleFor(x => x.MinimumCurrency)
            .Length(3).WithMessage("货币代码长度必须是3个字符")
            .Must(x => x == "CNY").WithMessage("目前只支持CNY货币")
            .When(x => !string.IsNullOrEmpty(x.MinimumCurrency));

        RuleFor(x => x.MaximumDiscount)
            .GreaterThanOrEqualTo(0).WithMessage("最大折扣金额不能为负数")
            .When(x => x.MaximumDiscount.HasValue);

        RuleFor(x => x.MaximumDiscountCurrency)
            .Length(3).WithMessage("货币代码长度必须是3个字符")
            .Must(x => x == "CNY").WithMessage("目前只支持CNY货币")
            .When(x => !string.IsNullOrEmpty(x.MaximumDiscountCurrency));

        RuleFor(x => x.TotalQuota)
            .GreaterThanOrEqualTo(-1).WithMessage("总配额必须大于等于-1（-1表示无限制）")
            .When(x => x.TotalQuota.HasValue);

        RuleFor(x => x.UsagePerUser)
            .GreaterThan(0).WithMessage("每用户使用次数必须大于0")
            .When(x => x.UsagePerUser.HasValue);

        RuleFor(x => x.Scope)
            .Must(x => new[] { "All", "Subscription", "TokenPackage", "Specific" }.Contains(x))
            .WithMessage("适用范围必须是 All, Subscription, TokenPackage 或 Specific")
            .When(x => !string.IsNullOrEmpty(x.Scope));

        RuleFor(x => x.ApplicableProducts)
            .ForEach(product => product.ValidGuid())
            .When(x => x.ApplicableProducts != null && x.ApplicableProducts.Any());

        RuleFor(x => x.Rules)
            .Must(x => x == null || x.Count <= 10).WithMessage("规则最多只能有10个")
            .When(x => x.Rules != null);

        RuleFor(x => x.Metadata)
            .Must(x => x == null || x.Count <= 20).WithMessage("元数据最多只能有20个")
            .When(x => x.Metadata != null);
    }
}

public class ExtendValidityRequestValidator : AbstractValidator<ExtendValidityRequest>
{
    public ExtendValidityRequestValidator()
    {
        RuleFor(x => x.NewValidTo)
            .NotEmpty().WithMessage("新的有效期结束时间不能为空")
            .GreaterThan(DateTime.UtcNow).WithMessage("新的有效期结束时间必须晚于当前时间")
            .LessThanOrEqualTo(DateTime.UtcNow.AddYears(2))
            .WithMessage("有效期结束时间不能超过2年后");
    }
}

public class CalculateDiscountRequestValidator : AbstractValidator<CalculateDiscountRequest>
{
    public CalculateDiscountRequestValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("优惠券代码不能为空")
            .MaximumLength(50).WithMessage("优惠券代码长度不能超过50个字符")
            .SafeInput();

        RuleFor(x => x.OrderAmount)
            .ValidAmount();

        RuleFor(x => x.Currency)
            .NotEmpty().WithMessage("货币不能为空")
            .Length(3).WithMessage("货币代码长度必须是3个字符")
            .Must(x => x == "CNY").WithMessage("目前只支持CNY货币");

        RuleFor(x => x.ProductId)
            .ValidGuid()
            .When(x => !string.IsNullOrEmpty(x.ProductId));
    }
}

public class ApplyCouponRequestValidator : AbstractValidator<ApplyCouponRequest>
{
    public ApplyCouponRequestValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("优惠券代码不能为空")
            .MaximumLength(50).WithMessage("优惠券代码长度不能超过50个字符")
            .SafeInput();

        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("订单ID不能为空");

        RuleFor(x => x.DiscountAmount)
            .ValidAmount();

        RuleFor(x => x.Currency)
            .NotEmpty().WithMessage("货币不能为空")
            .Length(3).WithMessage("货币代码长度必须是3个字符")
            .Must(x => x == "CNY").WithMessage("目前只支持CNY货币");
    }
}

public class ValidateCouponRequestValidator : AbstractValidator<ValidateCouponRequest>
{
    public ValidateCouponRequestValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("优惠券代码不能为空")
            .MaximumLength(50).WithMessage("优惠券代码长度不能超过50个字符")
            .SafeInput();
    }
}