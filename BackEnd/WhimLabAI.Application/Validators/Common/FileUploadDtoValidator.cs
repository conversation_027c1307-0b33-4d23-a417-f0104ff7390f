using FluentValidation;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Application.Validators.Common;

namespace WhimLabAI.Application.Validators.Common;

public class FileUploadDtoValidator : AbstractValidator<FileUploadDto>
{
    private const int MaxFileSizeMB = 50;
    
    public FileUploadDtoValidator()
    {
        RuleFor(x => x.FileName)
            .NotEmpty().WithMessage("File name is required")
            .MaximumLength(255).WithMessage("File name must not exceed 255 characters")
            .SafeInput()
            .Must(BeValidFileName).WithMessage("File name contains invalid characters");

        RuleFor(x => x.ContentType)
            .NotEmpty().WithMessage("Content type is required")
            .Must(BeAllowedContentType).WithMessage("File type not allowed");

        RuleFor(x => x.FileSize)
            .GreaterThan(0).WithMessage("File size must be greater than 0")
            .MaxFileSize(MaxFileSizeMB);

        // Either FileStream or FileContent must be provided
        RuleFor(x => x)
            .Must(x => x.FileStream != null || x.FileContent != null)
            .WithMessage("Either FileStream or FileContent must be provided")
            .WithName("FileData");

        RuleFor(x => x.FileKey)
            .NotEmpty().WithMessage("File key is required")
            .MaximumLength(100).WithMessage("File key must not exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.FileKey));
    }

    private bool BeValidFileName(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            return false;

        // Check for path traversal attempts
        if (fileName.Contains("..") || fileName.Contains("/") || fileName.Contains("\\"))
            return false;

        // Check for invalid characters
        var invalidChars = Path.GetInvalidFileNameChars();
        return !fileName.Any(ch => invalidChars.Contains(ch));
    }

    private bool BeAllowedContentType(string contentType)
    {
        var allowedTypes = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // Images
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/svg+xml",
            // Documents
            "application/pdf", "text/plain", "text/markdown", "text/csv",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/msword", "application/vnd.ms-excel",
            // Data
            "application/json", "application/xml", "text/xml"
        };

        return allowedTypes.Contains(contentType);
    }

}