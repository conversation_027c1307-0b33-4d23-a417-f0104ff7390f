using FluentValidation;
using WhimLabAI.Shared.DTOs.Common;

namespace WhimLabAI.Application.Validators.Common;

public class PagedRequestValidator : AbstractValidator<PagedRequest>
{
    public PagedRequestValidator()
    {
        RuleFor(x => x.PageNumber)
            .ValidPageNumber();

        RuleFor(x => x.PageSize)
            .ValidPageSize(100);

        RuleFor(x => x.OrderBy)
            .MaximumLength(50).WithMessage("Order by field must not exceed 50 characters")
            .Matches(@"^[a-zA-Z0-9_]+$").WithMessage("Order by field can only contain letters, numbers, and underscores")
            .When(x => !string.IsNullOrEmpty(x.OrderBy));

        // Note: the DTO uses "Descending" property, not "IsDescending"
    }
}