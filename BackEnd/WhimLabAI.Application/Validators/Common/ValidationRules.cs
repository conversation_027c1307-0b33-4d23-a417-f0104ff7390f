using FluentValidation;
using System.Text.RegularExpressions;

namespace WhimLabAI.Application.Validators.Common;

/// <summary>
/// Common validation rules for security and data integrity
/// </summary>
public static class ValidationRules
{
    // SQL Injection patterns
    private static readonly Regex SqlInjectionPattern = new(
        @"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT( +INTO)?|MERGE|SELECT|UPDATE|UNION( +ALL)?)\b)|" +
        @"(--|;|/\*|\*/|@@|@|char|nchar|varchar|nvarchar|alter|begin|cast|create|cursor|declare|delete|drop|" +
        @"end|exec|execute|fetch|insert|kill|select|sys|sysobjects|syscolumns|table|update|xp_)",
        RegexOptions.IgnoreCase | RegexOptions.Compiled);

    // XSS patterns
    private static readonly Regex XssPattern = new(
        @"<script[\s\S]*?>[\s\S]*?</script>|" +
        @"<iframe[\s\S]*?>[\s\S]*?</iframe>|" +
        @"javascript:|" +
        @"on\w+\s*=|" +
        @"<img[^>]+src[\s]*=[\s]*[""'](javascript|data:text/html)[^>]*>|" +
        @"<object[\s\S]*?>[\s\S]*?</object>|" +
        @"<embed[\s\S]*?>[\s\S]*?</embed>|" +
        @"<applet[\s\S]*?>[\s\S]*?</applet>",
        RegexOptions.IgnoreCase | RegexOptions.Compiled);

    // Email pattern
    private static readonly Regex EmailPattern = new(
        @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        RegexOptions.Compiled);

    // Phone pattern (supports international formats)
    private static readonly Regex PhonePattern = new(
        @"^(\+?[1-9]\d{0,14}|\d{10,15})$",
        RegexOptions.Compiled);

    // URL pattern
    private static readonly Regex UrlPattern = new(
        @"^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$",
        RegexOptions.Compiled);

    // Username pattern (alphanumeric, underscore, dash, 3-30 chars)
    private static readonly Regex UsernamePattern = new(
        @"^[a-zA-Z0-9_-]{3,30}$",
        RegexOptions.Compiled);

    // File extension whitelist
    private static readonly HashSet<string> AllowedImageExtensions = new(StringComparer.OrdinalIgnoreCase)
    {
        ".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"
    };

    private static readonly HashSet<string> AllowedDocumentExtensions = new(StringComparer.OrdinalIgnoreCase)
    {
        ".pdf", ".doc", ".docx", ".txt", ".md", ".csv", ".xlsx", ".xls"
    };

    /// <summary>
    /// Validates that a string does not contain SQL injection patterns
    /// </summary>
    public static IRuleBuilderOptions<T, string> NoSqlInjection<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .Must(value => string.IsNullOrWhiteSpace(value) || !SqlInjectionPattern.IsMatch(value))
            .WithMessage("Input contains potentially dangerous SQL patterns");
    }

    /// <summary>
    /// Validates that a string does not contain XSS patterns
    /// </summary>
    public static IRuleBuilderOptions<T, string> NoXss<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .Must(value => string.IsNullOrWhiteSpace(value) || !XssPattern.IsMatch(value))
            .WithMessage("Input contains potentially dangerous HTML/JavaScript patterns");
    }

    /// <summary>
    /// Validates that a string is safe for general input (no SQL injection or XSS)
    /// </summary>
    public static IRuleBuilderOptions<T, string> SafeInput<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .NoSqlInjection()
            .NoXss();
    }

    /// <summary>
    /// Validates email format
    /// </summary>
    public static IRuleBuilderOptions<T, string> ValidEmail<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .NotEmpty().WithMessage("Email is required")
            .Matches(EmailPattern).WithMessage("Invalid email format")
            .MaximumLength(255).WithMessage("Email must not exceed 255 characters")
            .SafeInput();
    }

    /// <summary>
    /// Validates phone number format
    /// </summary>
    public static IRuleBuilderOptions<T, string> ValidPhone<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .NotEmpty().WithMessage("Phone number is required")
            .Matches(PhonePattern).WithMessage("Invalid phone number format")
            .SafeInput();
    }

    /// <summary>
    /// Validates URL format
    /// </summary>
    public static IRuleBuilderOptions<T, string> ValidUrl<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .NotEmpty().WithMessage("URL is required")
            .Matches(UrlPattern).WithMessage("Invalid URL format")
            .MaximumLength(2048).WithMessage("URL must not exceed 2048 characters");
    }

    /// <summary>
    /// Validates username format
    /// </summary>
    public static IRuleBuilderOptions<T, string> ValidUsername<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .NotEmpty().WithMessage("Username is required")
            .Matches(UsernamePattern).WithMessage("Username must be 3-30 characters and contain only letters, numbers, underscores, or dashes")
            .SafeInput();
    }

    /// <summary>
    /// Validates password strength
    /// </summary>
    public static IRuleBuilderOptions<T, string> StrongPassword<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(8).WithMessage("Password must be at least 8 characters long")
            .Matches(@"[A-Z]").WithMessage("Password must contain at least one uppercase letter")
            .Matches(@"[a-z]").WithMessage("Password must contain at least one lowercase letter")
            .Matches(@"[0-9]").WithMessage("Password must contain at least one number")
            .Matches(@"[^a-zA-Z0-9]").WithMessage("Password must contain at least one special character");
    }

    /// <summary>
    /// Validates file extension for images
    /// </summary>
    public static IRuleBuilderOptions<T, string> ValidImageFile<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .Must(fileName =>
            {
                if (string.IsNullOrWhiteSpace(fileName)) return false;
                var extension = Path.GetExtension(fileName);
                return AllowedImageExtensions.Contains(extension);
            })
            .WithMessage($"Invalid image file type. Allowed types: {string.Join(", ", AllowedImageExtensions)}");
    }

    /// <summary>
    /// Validates file extension for documents
    /// </summary>
    public static IRuleBuilderOptions<T, string> ValidDocumentFile<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .Must(fileName =>
            {
                if (string.IsNullOrWhiteSpace(fileName)) return false;
                var extension = Path.GetExtension(fileName);
                return AllowedDocumentExtensions.Contains(extension);
            })
            .WithMessage($"Invalid document file type. Allowed types: {string.Join(", ", AllowedDocumentExtensions)}");
    }

    /// <summary>
    /// Validates file size
    /// </summary>
    public static IRuleBuilderOptions<T, long> MaxFileSize<T>(this IRuleBuilder<T, long> ruleBuilder, int maxSizeMB)
    {
        var maxSizeBytes = maxSizeMB * 1024 * 1024;
        return ruleBuilder
            .LessThanOrEqualTo(maxSizeBytes)
            .WithMessage($"File size must not exceed {maxSizeMB}MB");
    }

    /// <summary>
    /// Validates GUID format
    /// </summary>
    public static IRuleBuilderOptions<T, string> ValidGuid<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .NotEmpty().WithMessage("ID is required")
            .Must(value => Guid.TryParse(value, out _))
            .WithMessage("Invalid ID format");
    }

    /// <summary>
    /// Validates decimal amount (for payments)
    /// </summary>
    public static IRuleBuilderOptions<T, decimal> ValidAmount<T>(this IRuleBuilder<T, decimal> ruleBuilder)
    {
        return ruleBuilder
            .GreaterThan(0).WithMessage("Amount must be greater than 0")
            .PrecisionScale(10, 2, true).WithMessage("Amount must have at most 2 decimal places");
    }

    /// <summary>
    /// Validates date range
    /// </summary>
    public static IRuleBuilderOptions<T, DateTime> ValidDateRange<T>(this IRuleBuilder<T, DateTime> ruleBuilder, DateTime minDate, DateTime maxDate)
    {
        return ruleBuilder
            .GreaterThanOrEqualTo(minDate).WithMessage($"Date must be after {minDate:yyyy-MM-dd}")
            .LessThanOrEqualTo(maxDate).WithMessage($"Date must be before {maxDate:yyyy-MM-dd}");
    }

    /// <summary>
    /// Validates that a collection is not empty
    /// </summary>
    public static IRuleBuilderOptions<T, IEnumerable<TElement>> NotEmptyCollection<T, TElement>(this IRuleBuilder<T, IEnumerable<TElement>> ruleBuilder)
    {
        return ruleBuilder
            .NotNull().WithMessage("Collection is required")
            .Must(collection => collection?.Any() == true).WithMessage("Collection must contain at least one item");
    }

    /// <summary>
    /// Validates pagination parameters
    /// </summary>
    public static IRuleBuilderOptions<T, int> ValidPageNumber<T>(this IRuleBuilder<T, int> ruleBuilder)
    {
        return ruleBuilder
            .GreaterThanOrEqualTo(1).WithMessage("Page number must be at least 1");
    }

    public static IRuleBuilderOptions<T, int> ValidPageSize<T>(this IRuleBuilder<T, int> ruleBuilder, int maxPageSize = 100)
    {
        return ruleBuilder
            .InclusiveBetween(1, maxPageSize).WithMessage($"Page size must be between 1 and {maxPageSize}");
    }
}