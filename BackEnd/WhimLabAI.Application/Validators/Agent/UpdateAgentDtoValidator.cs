using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Agent;

namespace WhimLabAI.Application.Validators.Agent;

public class UpdateAgentDtoValidator : AbstractValidator<UpdateAgentDto>
{
    public UpdateAgentDtoValidator()
    {
        RuleFor(x => x.Id)
            .ValidGuid();

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Agent name is required")
            .MinimumLength(3).WithMessage("Agent name must be at least 3 characters")
            .MaximumLength(100).WithMessage("Agent name must not exceed 100 characters")
            .SafeInput();

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("Description is required")
            .MinimumLength(10).WithMessage("Description must be at least 10 characters")
            .MaximumLength(1000).WithMessage("Description must not exceed 1000 characters")
            .SafeInput();

        RuleFor(x => x.SystemPrompt)
            .NotEmpty().WithMessage("System prompt is required")
            .MinimumLength(20).WithMessage("System prompt must be at least 20 characters")
            .MaximumLength(5000).WithMessage("System prompt must not exceed 5000 characters")
            .SafeInput();

        RuleFor(x => x.CategoryId)
            .ValidGuid()
            .When(x => !string.IsNullOrEmpty(x.CategoryId));

        RuleFor(x => x.AvatarUrl)
            .ValidUrl()
            .When(x => !string.IsNullOrEmpty(x.AvatarUrl));

        RuleFor(x => x.Temperature)
            .InclusiveBetween(0, 2).WithMessage("Temperature must be between 0 and 2");

        RuleFor(x => x.MaxTokens)
            .InclusiveBetween(1, 100000).WithMessage("Max tokens must be between 1 and 100000");

        RuleFor(x => x.Model)
            .NotEmpty().WithMessage("Model is required")
            .MaximumLength(50).WithMessage("Model name must not exceed 50 characters")
            .SafeInput();

        RuleFor(x => x.Tags)
            .Must(tags => tags == null || tags.Count <= 10).WithMessage("Maximum 10 tags allowed")
            .ForEach(tag => tag.MaximumLength(30).SafeInput())
            .When(x => x.Tags != null);

        RuleFor(x => x.Capabilities)
            .Must(caps => caps == null || caps.Count <= 20).WithMessage("Maximum 20 capabilities allowed")
            .ForEach(cap => cap.MaximumLength(100).SafeInput())
            .When(x => x.Capabilities != null);

        RuleFor(x => x.Price)
            .GreaterThanOrEqualTo(0).WithMessage("Price must be non-negative")
            .PrecisionScale(10, 2, true).WithMessage("Price must have at most 2 decimal places");
    }
}