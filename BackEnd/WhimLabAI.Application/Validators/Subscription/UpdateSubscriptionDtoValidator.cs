using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Subscription;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Application.Validators.Subscription;

public class UpdateSubscriptionDtoValidator : AbstractValidator<UpdateSubscriptionDto>
{
    public UpdateSubscriptionDtoValidator()
    {
        RuleFor(x => x.SubscriptionId)
            .ValidGuid();

        RuleFor(x => x.NewTier)
            .IsInEnum().WithMessage("Invalid subscription tier")
            .When(x => x.NewTier.HasValue);

        RuleFor(x => x.NewBillingCycle)
            .IsInEnum().WithMessage("Invalid billing cycle")
            .When(x => x.NewBillingCycle.HasValue);

        RuleFor(x => x.AutoRenew)
            .NotNull().WithMessage("Auto renew preference is required")
            .When(x => x.AutoRenew.HasValue);

        RuleFor(x => x.CancelAtPeriodEnd)
            .NotNull().WithMessage("Cancel at period end preference is required")
            .When(x => x.CancelAtPeriodEnd.HasValue);

        // Business rule: Can't downgrade to Free tier
        RuleFor(x => x.NewTier)
            .NotEqual(SubscriptionTier.Free)
            .WithMessage("Cannot downgrade to the Free tier")
            .When(x => x.NewTier.HasValue);

        // At least one update field must be provided
        RuleFor(x => x)
            .Must(x => x.NewTier.HasValue || x.NewBillingCycle.HasValue || 
                      x.AutoRenew.HasValue || x.CancelAtPeriodEnd.HasValue)
            .WithMessage("At least one field must be updated");
    }
}