using FluentValidation;
using WhimLabAI.Shared.Dtos.Subscription;

namespace WhimLabAI.Application.Validators.Subscription;

/// <summary>
/// 创建订阅计划验证器
/// </summary>
public class CreateSubscriptionPlanDtoValidator : AbstractValidator<CreateSubscriptionPlanDto>
{
    public CreateSubscriptionPlanDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("计划名称不能为空")
            .Length(2, 50).WithMessage("计划名称长度必须在2-50个字符之间");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("计划描述不能为空")
            .Length(10, 500).WithMessage("计划描述长度必须在10-500个字符之间");

        RuleFor(x => x.Tier)
            .IsInEnum().WithMessage("订阅层级无效");

        RuleFor(x => x.Price)
            .GreaterThanOrEqualTo(0).WithMessage("价格不能为负数");

        RuleFor(x => x.Currency)
            .NotEmpty().WithMessage("货币不能为空")
            .Matches("^[A-Z]{3}$").WithMessage("货币必须是3位大写字母");

        RuleFor(x => x.MonthlyTokens)
            .GreaterThan(0).WithMessage("月度令牌数必须大于0");

        RuleFor(x => x.Features)
            .NotNull().WithMessage("功能列表不能为空")
            .Must(x => x.Count > 0).WithMessage("至少需要一个功能");
    }
}

/// <summary>
/// 更新订阅计划验证器
/// </summary>
public class UpdateSubscriptionPlanDtoValidator : AbstractValidator<UpdateSubscriptionPlanDto>
{
    public UpdateSubscriptionPlanDtoValidator()
    {
        RuleFor(x => x.Name)
            .Length(2, 50).WithMessage("计划名称长度必须在2-50个字符之间")
            .When(x => !string.IsNullOrEmpty(x.Name));

        RuleFor(x => x.Description)
            .Length(10, 500).WithMessage("计划描述长度必须在10-500个字符之间")
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.Price)
            .GreaterThanOrEqualTo(0).WithMessage("价格不能为负数");

        RuleFor(x => x.TokenQuota)
            .GreaterThan(0).WithMessage("令牌数必须大于0");

        RuleFor(x => x.Features)
            .Must(x => x.Count > 0).WithMessage("至少需要一个功能")
            .When(x => x.Features != null);
    }
}