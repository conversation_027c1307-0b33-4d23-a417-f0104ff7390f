using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Subscription;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Application.Validators.Subscription;

public class CreateSubscriptionDtoValidator : AbstractValidator<CreateSubscriptionDto>
{
    public CreateSubscriptionDtoValidator()
    {
        RuleFor(x => x.Tier)
            .IsInEnum().WithMessage("Invalid subscription tier");

        RuleFor(x => x.BillingCycle)
            .IsInEnum().WithMessage("Invalid billing cycle");

        RuleFor(x => x.PaymentMethod)
            .IsInEnum().WithMessage("Invalid payment method");

        RuleFor(x => x.AutoRenew)
            .NotNull().WithMessage("Auto renew preference is required");

        RuleFor(x => x.PromoCode)
            .MaximumLength(50).WithMessage("Promo code must not exceed 50 characters")
            .Matches(@"^[A-Z0-9_-]+$").WithMessage("Promo code can only contain uppercase letters, numbers, underscores, and dashes")
            .When(x => !string.IsNullOrEmpty(x.PromoCode));

        RuleFor(x => x.PaymentToken)
            .MaximumLength(500).WithMessage("Payment token must not exceed 500 characters")
            .When(x => !string.IsNullOrEmpty(x.PaymentToken));

        RuleFor(x => x.ReturnUrl)
            .ValidUrl()
            .When(x => !string.IsNullOrEmpty(x.ReturnUrl));

        // Business rule: Can't subscribe to Free tier
        RuleFor(x => x.Tier)
            .NotEqual(SubscriptionTier.Free)
            .WithMessage("Cannot create a subscription for the Free tier");
    }
}