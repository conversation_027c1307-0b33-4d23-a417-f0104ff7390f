using FluentValidation;
using WhimLabAI.Application.Validators.Common;
using WhimLabAI.Shared.Dtos.Admin.User;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Application.Validators.Admin;

public class CreateAdminDtoValidator : AbstractValidator<CreateAdminDto>
{
    public CreateAdminDtoValidator()
    {
        RuleFor(x => x.Username)
            .NotEmpty().WithMessage("用户名不能为空")
            .MinimumLength(3).WithMessage("用户名至少3个字符")
            .MaximumLength(50).WithMessage("用户名不能超过50个字符")
            .Matches(@"^[a-zA-Z0-9_-]+$").WithMessage("用户名只能包含字母、数字、下划线和破折号")
            .SafeInput();

        RuleFor(x => x.Password)
            .StrongPassword();

        RuleFor(x => x.Email)
            .ValidEmail();

        RuleFor(x => x.Phone)
            .ValidPhone();

        RuleFor(x => x.Nickname)
            .NotEmpty().WithMessage("昵称不能为空")
            .MaximumLength(50).WithMessage("昵称不能超过50个字符")
            .SafeInput();

        RuleFor(x => x.RoleIds)
            .ForEach(roleId => roleId.NotEmpty())
            .When(x => x.RoleIds != null && x.RoleIds.Any());
            
        RuleFor(x => x.SendNotification)
            .NotNull().WithMessage("必须指定是否发送通知");
    }
}