﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\WhimLabAI.Domain\WhimLabAI.Domain.csproj" />
      <ProjectReference Include="..\WhimLabAI.Abstractions\WhimLabAI.Abstractions.csproj" />
      <ProjectReference Include="..\WhimLabAI.Shared\WhimLabAI.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="AutoMapper" Version="$(AutoMapperVersion)" />
      <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="$(FluentValidationVersion)" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="9.0.7" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Analyzers" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.7" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.7" />
      <PackageReference Include="Otp.NET" Version="$(OtpDotNetVersion)" />
      <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
      <PackageReference Include="MediatR" Version="$(MediatRVersion)" />
      <PackageReference Include="MathNet.Numerics" Version="5.0.0" />
      <PackageReference Include="QuestPDF" Version="2024.12.3" />
    </ItemGroup>

</Project>
