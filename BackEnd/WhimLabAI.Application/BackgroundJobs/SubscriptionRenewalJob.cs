using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.Application.BackgroundJobs;

/// <summary>
/// 订阅自动续费后台作业
/// </summary>
public class SubscriptionRenewalJob
{
    private readonly ILogger<SubscriptionRenewalJob> _logger;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IOrderService _orderService;
    private readonly IPaymentService _paymentService;
    private readonly INotificationService _notificationService;

    public SubscriptionRenewalJob(
        ILogger<SubscriptionRenewalJob> logger,
        IUnitOfWork unitOfWork,
        IOrderService orderService,
        IPaymentService paymentService,
        INotificationService notificationService)
    {
        _logger = logger;
        _unitOfWork = unitOfWork;
        _orderService = orderService;
        _paymentService = paymentService;
        _notificationService = notificationService;
    }

    public async Task ProcessAutoRenewalsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting subscription auto-renewal job");
        
        try
        {
            // 获取需要续费的订阅（到期前3天内且开启自动续费）
            var expiringSubscriptions = await _unitOfWork.Subscriptions
                .GetExpiringSubscriptionsAsync(3, cancellationToken);
            
            var successCount = 0;
            var failureCount = 0;
            
            foreach (var subscription in expiringSubscriptions)
            {
                try
                {
                    if (!subscription.AutoRenew)
                    {
                        continue;
                    }
                    
                    // 检查是否已经有待支付的续费订单
                    var existingOrder = await _unitOfWork.Orders
                        .HasPendingRenewalOrderAsync(subscription.CustomerUserId, subscription.PlanId, cancellationToken);
                    
                    if (existingOrder)
                    {
                        _logger.LogInformation("Subscription {SubscriptionId} already has pending renewal order", 
                            subscription.Id);
                        continue;
                    }
                    
                    // 创建续费订单
                    var defaultPaymentMethod = await GetUserDefaultPaymentMethodAsync(subscription.CustomerUserId, cancellationToken);
                    var orderDto = new CreateOrderDto
                    {
                        Items = new List<OrderItemDto>
                        {
                            new OrderItemDto
                            {
                                ProductId = subscription.PlanId,
                                ProductType = ProductType.SubscriptionPlan,
                                ProductName = "订阅续费",
                                Quantity = 1,
                                UnitPrice = 0 // 价格将从套餐中获取
                            }
                        },
                        PaymentMethod = defaultPaymentMethod ?? PaymentMethod.Alipay, // 默认支付宝
                        Remark = "自动续费"
                    };
                    
                    var orderResult = await _orderService.CreateOrderAsync(orderDto, subscription.CustomerUserId, cancellationToken);
                    
                    if (orderResult.IsSuccess)
                    {
                        _logger.LogInformation("Created renewal order {OrderId} for subscription {SubscriptionId}", 
                            orderResult.Value!.Id, subscription.Id);
                        
                        // 如果用户有绑定的支付方式，尝试自动扣款
                        var paymentMethod = await GetUserDefaultPaymentMethodAsync(subscription.CustomerUserId, cancellationToken);
                        if (paymentMethod != null)
                        {
                            var paymentDto = new ProcessPaymentDto
                            {
                                OrderId = orderResult.Value.Id,
                                PaymentMethod = paymentMethod.Value
                            };
                            
                            var paymentResult = await _paymentService.ProcessPaymentAsync(paymentDto, cancellationToken);
                            
                            if (paymentResult.IsSuccess)
                            {
                                _logger.LogInformation("Auto-payment initiated for order {OrderId}", orderResult.Value.Id);
                            }
                            else
                            {
                                _logger.LogWarning("Failed to initiate auto-payment for order {OrderId}: {Error}", 
                                    orderResult.Value.Id, paymentResult.Error);
                            }
                        }
                        
                        // 发送续费通知
                        await _notificationService.SendNotificationAsync(new SendNotificationDto
                        {
                            UserId = subscription.CustomerUserId,
                            Title = "订阅续费提醒",
                            Content = $"您的订阅即将到期，我们已为您创建续费订单。请及时完成支付以避免服务中断。",
                            Type = "subscription",
                            Level = "warning",
                            Metadata = new Dictionary<string, object>
                            {
                                ["orderId"] = orderResult.Value.Id,
                                ["subscriptionId"] = subscription.Id
                            }
                        }, cancellationToken);
                        
                        successCount++;
                    }
                    else
                    {
                        _logger.LogError("Failed to create renewal order for subscription {SubscriptionId}: {Error}", 
                            subscription.Id, orderResult.Error);
                        failureCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing renewal for subscription {SubscriptionId}", subscription.Id);
                    failureCount++;
                }
            }
            
            _logger.LogInformation("Subscription auto-renewal job completed. Success: {Success}, Failures: {Failures}", 
                successCount, failureCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in subscription auto-renewal job");
            throw;
        }
    }
    
    private async Task<PaymentMethod?> GetUserDefaultPaymentMethodAsync(Guid userId, CancellationToken cancellationToken)
    {
        // Get user's active subscription to retrieve payment method
        var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
        var activeSubscription = await subscriptionRepository.FirstOrDefaultAsync(
            s => s.CustomerUserId == userId && 
                 s.Status == SubscriptionStatus.Active &&
                 !string.IsNullOrEmpty(s.PaymentMethod),
            cancellationToken);
        
        if (activeSubscription?.PaymentMethod != null)
        {
            // Try to parse the payment method
            if (Enum.TryParse<PaymentMethod>(activeSubscription.PaymentMethod, out var paymentMethod))
            {
                _logger.LogInformation("Found default payment method {PaymentMethod} for user {UserId}", 
                    paymentMethod, userId);
                return paymentMethod;
            }
            else
            {
                _logger.LogWarning("Invalid payment method value {PaymentMethod} for user {UserId}", 
                    activeSubscription.PaymentMethod, userId);
            }
        }
        
        _logger.LogInformation("No default payment method found for user {UserId}", userId);
        return null;
    }
}