using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Application.BackgroundJobs;

/// <summary>
/// 数据清理后台作业
/// </summary>
public class DataCleanupJob
{
    private readonly ILogger<DataCleanupJob> _logger;
    private readonly IUnitOfWork _unitOfWork;

    public DataCleanupJob(
        ILogger<DataCleanupJob> logger,
        IUnitOfWork unitOfWork)
    {
        _logger = logger;
        _unitOfWork = unitOfWork;
    }

    public async Task CleanupExpiredDataAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting data cleanup job");
        
        try
        {
            var totalDeleted = 0;
            
            // 1. 清理过期的会话数据（超过7天）
            // 清理客户用户会话
            var expiredCustomerSessions = await _unitOfWork.CustomerSessions
                .GetExpiredSessionsAsync(7, cancellationToken);
            
            foreach (var session in expiredCustomerSessions)
            {
                _unitOfWork.CustomerSessions.Remove(session);
                totalDeleted++;
            }
            
            _logger.LogInformation("Deleted {Count} expired customer sessions", expiredCustomerSessions.Count());
            
            // 清理管理员用户会话
            var expiredAdminSessions = await _unitOfWork.AdminSessions
                .GetExpiredSessionsAsync(7, cancellationToken);
            
            foreach (var session in expiredAdminSessions)
            {
                _unitOfWork.AdminSessions.Remove(session);
                totalDeleted++;
            }
            
            _logger.LogInformation("Deleted {Count} expired admin sessions", expiredAdminSessions.Count());
            
            // 2. 清理过期的通知（已读且超过30天）
            var oldNotifications = await _unitOfWork.Notifications
                .GetOldReadNotificationsAsync(30, cancellationToken);
            
            foreach (var notification in oldNotifications)
            {
                _unitOfWork.Notifications.Remove(notification);
                totalDeleted++;
            }
            
            _logger.LogInformation("Deleted {Count} old read notifications", oldNotifications.Count());
            
            // 3. 清理过期的审计日志（超过90天的非敏感日志）
            var oldAuditLogs = await _unitOfWork.AuditLogs
                .GetOldNonSensitiveLogsAsync(90, cancellationToken);
            
            foreach (var log in oldAuditLogs)
            {
                _unitOfWork.AuditLogs.Remove(log);
                totalDeleted++;
            }
            
            _logger.LogInformation("Deleted {Count} old audit logs", oldAuditLogs.Count());
            
            // 4. 清理过期的临时文件记录（超过24小时）
            var expiredFiles = await _unitOfWork.Files
                .GetExpiredTemporaryFilesAsync(24, cancellationToken);
            
            foreach (var file in expiredFiles)
            {
                // 标记为已删除，实际文件删除由存储服务处理
                file.MarkAsDeleted();
                _unitOfWork.Files.Update(file);
                totalDeleted++;
            }
            
            _logger.LogInformation("Marked {Count} temporary files for deletion", expiredFiles.Count());
            
            // 5. 清理过期的验证码（超过10分钟）
            var expiredCodes = await _unitOfWork.VerificationCodes
                .GetExpiredCodesAsync(cancellationToken);
            
            foreach (var code in expiredCodes)
            {
                _unitOfWork.VerificationCodes.Remove(code);
                totalDeleted++;
            }
            
            _logger.LogInformation("Deleted {Count} expired verification codes", expiredCodes.Count());
            
            // 6. 清理已取消的订单（超过30天）
            var oldCancelledOrders = await _unitOfWork.Orders
                .GetOldCancelledOrdersAsync(30, cancellationToken);
            
            foreach (var order in oldCancelledOrders)
            {
                // 软删除订单
                order.Delete();
                _unitOfWork.Orders.Update(order);
                totalDeleted++;
            }
            
            _logger.LogInformation("Soft deleted {Count} old cancelled orders", oldCancelledOrders.Count());
            
            // 7. 压缩对话历史（超过90天的对话）
            var oldConversations = await _unitOfWork.Conversations
                .GetOldConversationsAsync(90, cancellationToken);
            
            foreach (var conversation in oldConversations)
            {
                // 将消息归档到压缩存储
                conversation.ArchiveMessages();
                _unitOfWork.Conversations.Update(conversation);
            }
            
            _logger.LogInformation("Archived {Count} old conversations", oldConversations.Count());
            
            // 保存所有更改
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 记录清理统计
            var cleanupEvent = new Domain.Entities.System.SystemEvent(
                "DataCleanup",
                "数据清理完成",
                new Dictionary<string, object>
                {
                    ["TotalDeleted"] = totalDeleted,
                    ["ExecutionTime"] = DateTime.UtcNow
                }
            );
            await _unitOfWork.SystemEvents.AddAsync(cleanupEvent, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Data cleanup job completed. Total items processed: {TotalDeleted}", totalDeleted);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in data cleanup job");
            throw;
        }
    }
}