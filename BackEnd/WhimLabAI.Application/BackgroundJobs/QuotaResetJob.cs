using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Application.BackgroundJobs;

/// <summary>
/// 配额月度重置后台作业
/// </summary>
public class QuotaResetJob
{
    private readonly ILogger<QuotaResetJob> _logger;
    private readonly IUnitOfWork _unitOfWork;
    private readonly INotificationService _notificationService;

    public QuotaResetJob(
        ILogger<QuotaResetJob> logger,
        IUnitOfWork unitOfWork,
        INotificationService notificationService)
    {
        _logger = logger;
        _unitOfWork = unitOfWork;
        _notificationService = notificationService;
    }

    public async Task ResetMonthlyQuotasAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting monthly quota reset job");
        
        try
        {
            // 获取所有需要重置的活跃订阅
            var activeSubscriptions = await _unitOfWork.Subscriptions
                .GetActiveSubscriptionsAsync(cancellationToken);
            
            var resetCount = 0;
            var errorCount = 0;
            
            foreach (var subscriptionObj in activeSubscriptions)
            {
                try
                {
                    // 确保是 Subscription 实体
                    if (subscriptionObj is not Subscription subscription)
                    {
                        _logger.LogWarning("Invalid subscription object type");
                        continue;
                    }
                    
                    // 检查是否需要重置
                    if (subscription.NextResetDate > DateTime.UtcNow)
                    {
                        continue; // 还未到重置时间
                    }
                    
                    // 记录重置前的使用量（用于统计）
                    var previousUsage = subscription.TokenQuota.Used;
                    var totalTokens = subscription.TokenQuota.Total;
                    
                    // 重置月度配额
                    subscription.ResetMonthlyTokens();
                    _unitOfWork.Subscriptions.Update(subscription);
                    
                    // 创建使用记录作为历史
                    var resetRecord = new UsageRecord(
                        subscriptionId: subscription.Id,
                        userId: subscription.CustomerUserId,
                        conversationId: Guid.Empty,
                        agentId: Guid.Empty,
                        modelName: "System",
                        modelProvider: "System",
                        tokensUsed: 0,
                        costAmount: 0
                    );
                    resetRecord.AddMetadata("Type", "MonthlyReset");
                    resetRecord.AddMetadata("PreviousUsage", previousUsage);
                    resetRecord.AddMetadata("TotalTokens", totalTokens);
                    resetRecord.AddMetadata("Reason", "月度配额重置");
                    
                    await _unitOfWork.UsageRecords.AddAsync(resetRecord, cancellationToken);
                    
                    // 发送通知
                    await _notificationService.SendNotificationAsync(new SendNotificationDto
                    {
                        UserId = subscription.CustomerUserId,
                        Title = "月度配额已重置",
                        Content = $"您的月度配额已重置。本月可用配额：{totalTokens:N0} Tokens",
                        Type = NotificationTypes.System,
                        Level = NotificationLevels.Info
                    }, cancellationToken);
                    
                    resetCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error resetting quota for subscription {SubscriptionId}", subscriptionObj.Id);
                    errorCount++;
                }
            }
            
            // 保存所有更改
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Monthly quota reset job completed. Reset: {ResetCount}, Errors: {ErrorCount}", 
                resetCount, errorCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in monthly quota reset job");
            throw;
        }
    }
}