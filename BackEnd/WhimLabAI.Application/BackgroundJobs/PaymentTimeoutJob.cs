using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Application.BackgroundJobs;

/// <summary>
/// 支付超时检查后台作业
/// </summary>
public class PaymentTimeoutJob
{
    private readonly ILogger<PaymentTimeoutJob> _logger;
    private readonly IUnitOfWork _unitOfWork;

    public PaymentTimeoutJob(
        ILogger<PaymentTimeoutJob> logger,
        IUnitOfWork unitOfWork)
    {
        _logger = logger;
        _unitOfWork = unitOfWork;
    }

    public async Task CheckPaymentTimeoutsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting payment timeout check job");
        
        try
        {
            // 获取所有超时的待支付订单
            var expiredOrders = await _unitOfWork.Orders
                .GetExpiredPendingOrdersAsync(cancellationToken);
            
            var cancelledCount = 0;
            var errorCount = 0;
            
            foreach (var order in expiredOrders)
            {
                try
                {
                    // 标记订单为已取消
                    order.Cancel("支付超时自动取消");
                    _unitOfWork.Orders.Update(order);
                    
                    // 获取相关的支付记录
                    var payments = await _unitOfWork.Payments
                        .GetOrderPaymentsAsync(order.Id, cancellationToken);
                    
                    // 取消所有待处理的支付
                    foreach (var payment in payments)
                    {
                        if (payment.Status == TransactionStatus.Pending)
                        {
                            payment.MarkAsCancelled();
                            _unitOfWork.Payments.Update(payment);
                        }
                    }
                    
                    _logger.LogInformation("Cancelled expired order {OrderNo} due to payment timeout", order.OrderNo);
                    cancelledCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error cancelling expired order {OrderId}", order.Id);
                    errorCount++;
                }
            }
            
            // 检查超时的支付交易
            var expiredPayments = await _unitOfWork.Payments
                .GetExpiredPendingPaymentsAsync(cancellationToken);
            
            foreach (var payment in expiredPayments)
            {
                try
                {
                    payment.MarkAsFailed("支付超时");
                    _unitOfWork.Payments.Update(payment);
                    
                    _logger.LogInformation("Marked payment {PaymentNo} as failed due to timeout", payment.PaymentNo);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error marking payment {PaymentId} as failed", payment.Id);
                    errorCount++;
                }
            }
            
            // 保存所有更改
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Payment timeout check job completed. Cancelled orders: {CancelledCount}, Errors: {ErrorCount}", 
                cancelledCount, errorCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in payment timeout check job");
            throw;
        }
    }
}