using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;
using WhimLabAI.Abstractions.Application.Services;

namespace WhimLabAI.Application.Services.Order;

public class OrderService : IOrderService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly ICouponService _couponService;
    private readonly ILogger<OrderService> _logger;

    public OrderService(
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        ICouponService couponService,
        ILogger<OrderService> logger)
    {
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _couponService = couponService;
        _logger = logger;
    }

    public async Task<Result<OrderDto>> CreateOrderAsync(CreateOrderDto request, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Currently only support single item orders
            if (request.Items.Count != 1)
            {
                return Result<OrderDto>.Failure("SINGLE_ITEM_ONLY", "目前只支持单个商品订单");
            }

            // Begin transaction
            await _unitOfWork.BeginTransactionAsync(cancellationToken: cancellationToken);

            try
            {
                var firstItem = request.Items.First();
                
                // Validate product and get details
                string productName = firstItem.ProductName;
                decimal unitPrice = firstItem.UnitPrice;
                
                if (firstItem.ProductType == ProductType.SubscriptionPlan)
                {
                    var plan = await _unitOfWork.Repository<Domain.Entities.Subscription.SubscriptionPlan>()
                        .GetByIdAsync(firstItem.ProductId, cancellationToken);
                    if (plan == null || !plan.IsActive)
                    {
                        await _unitOfWork.RollbackAsync(cancellationToken);
                        return Result<OrderDto>.Failure("INVALID_PRODUCT", $"无效的产品: {firstItem.ProductId}");
                    }
                    productName = plan.Name;
                    unitPrice = plan.Price.Amount;
                }
                
                var totalAmount = unitPrice * firstItem.Quantity;
                
                // Determine order type based on product type
                var orderType = firstItem.ProductType switch
                {
                    ProductType.SubscriptionPlan => OrderType.Subscription,
                    ProductType.TokenPackage => OrderType.TokenPackage,
                    ProductType.AddonService => OrderType.OneTimeService,
                    ProductType.AIAgent => OrderType.OneTimeService,
                    _ => OrderType.Other
                };
                
                // Create order
                var order = new Domain.Entities.Payment.Order(
                    GenerateOrderNo(),
                    userId,
                    orderType,
                    Money.Create(totalAmount, "CNY"),
                    request.PaymentMethod,
                    firstItem.ProductId,
                    productName,
                    request.Remark,
                    30);


                // Apply coupon if provided
                if (!string.IsNullOrEmpty(request.CouponCode))
                {
                    var discountResult = await _couponService.CalculateDiscountAsync(
                        request.CouponCode,
                        totalAmount,
                        "CNY",
                        firstItem.ProductId.ToString(),
                        cancellationToken);

                    if (discountResult.IsSuccess && discountResult.Value.IsValid && discountResult.Value.CanUse)
                    {
                        // Apply the coupon discount to the order
                        // Note: We need to get the coupon ID from the discount result or fetch it separately
                        var couponResult = await _couponService.GetByCodeAsync(request.CouponCode, cancellationToken);
                        if (couponResult.IsSuccess)
                        {
                            order.ApplyCoupon(
                                couponResult.Value.Id,
                                request.CouponCode,
                                Money.Create(discountResult.Value.DiscountAmount, discountResult.Value.Currency));
                        }
                        
                        _logger.LogInformation("Coupon applied successfully: {CouponCode}, Discount: {DiscountAmount} {Currency}", 
                            request.CouponCode, 
                            discountResult.Value.DiscountAmount, 
                            discountResult.Value.Currency);
                    }
                    else
                    {
                        var errorMessage = discountResult.Value?.Message ?? "优惠券无效或不可用";
                        _logger.LogWarning("Failed to apply coupon {CouponCode}: {Message}", request.CouponCode, errorMessage);
                        
                        // Optionally, you can decide whether to fail the order creation or continue without the coupon
                        // For now, we'll continue without the coupon and log the warning
                    }
                }

                await _unitOfWork.Repository<Domain.Entities.Payment.Order>().AddAsync(order, cancellationToken);
                await _unitOfWork.CommitAsync(cancellationToken);

                _logger.LogInformation("Order created: {OrderNo} for user {UserId}", order.OrderNo, userId);

                return Result<OrderDto>.Success(MapToDto(order));
            }
            catch
            {
                await _unitOfWork.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating order for user {UserId}", userId);
            return Result<OrderDto>.Failure("CREATE_ORDER_ERROR", "创建订单失败");
        }
    }

    public async Task<Result<OrderDto>> GetOrderAsync(Guid orderId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var order = await _unitOfWork.Repository<Domain.Entities.Payment.Order>().GetByIdAsync(orderId, cancellationToken);
            if (order == null)
            {
                return Result<OrderDto>.Failure("ORDER_NOT_FOUND", "订单不存在");
            }

            if (order.CustomerUserId != userId)
            {
                return Result<OrderDto>.Failure("UNAUTHORIZED", "无权访问此订单");
            }

            return Result<OrderDto>.Success(MapToDto(order));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order {OrderId}", orderId);
            return Result<OrderDto>.Failure("GET_ORDER_ERROR", "获取订单失败");
        }
    }

    public async Task<Result<OrderDto>> GetOrderByNoAsync(string orderNo, CancellationToken cancellationToken = default)
    {
        try
        {
            var orderRepository = _unitOfWork.Repository<Domain.Entities.Payment.Order>();
            var order = await orderRepository.FirstOrDefaultAsync(o => o.OrderNo == orderNo, cancellationToken);
            if (order == null)
            {
                return Result<OrderDto>.Failure("ORDER_NOT_FOUND", "订单不存在");
            }

            return Result<OrderDto>.Success(MapToDto(order));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order by no {OrderNo}", orderNo);
            return Result<OrderDto>.Failure("GET_ORDER_ERROR", "获取订单失败");
        }
    }

    public async Task<Result<PagedResult<OrderDto>>> GetUserOrdersAsync(Guid userId, OrderQueryDto query, CancellationToken cancellationToken = default)
    {
        try
        {
            var orderRepository = _unitOfWork.Repository<Domain.Entities.Payment.Order>();
            var orders = await orderRepository.GetAsync(o => o.CustomerUserId == userId, cancellationToken);
            var orderList = orders.ToList();

            // Apply additional filters
            if (query.Status.HasValue)
            {
                orderList = orderList.Where(o => o.Status == query.Status.Value).ToList();
            }

            if (query.StartDate.HasValue)
            {
                orderList = orderList.Where(o => o.CreatedAt >= query.StartDate.Value).ToList();
            }

            if (query.EndDate.HasValue)
            {
                orderList = orderList.Where(o => o.CreatedAt <= query.EndDate.Value).ToList();
            }

            if (!string.IsNullOrWhiteSpace(query.Keyword))
            {
                orderList = orderList.Where(o => 
                    o.OrderNo.Contains(query.Keyword, StringComparison.OrdinalIgnoreCase) ||
                    (o.ProductName != null && o.ProductName.Contains(query.Keyword, StringComparison.OrdinalIgnoreCase)))
                    .ToList();
            }

            // Sort
            orderList = query.OrderBy.ToLower() switch
            {
                "amount" => query.Descending ? orderList.OrderByDescending(o => o.FinalAmount.Amount).ToList() : orderList.OrderBy(o => o.FinalAmount.Amount).ToList(),
                "status" => query.Descending ? orderList.OrderByDescending(o => o.Status).ToList() : orderList.OrderBy(o => o.Status).ToList(),
                _ => query.Descending ? orderList.OrderByDescending(o => o.CreatedAt).ToList() : orderList.OrderBy(o => o.CreatedAt).ToList()
            };

            var totalCount = orderList.Count;
            var pagedOrders = orderList
                .Skip((query.PageNumber - 1) * query.PageSize)
                .Take(query.PageSize)
                .Select(MapToDto)
                .ToList();

            var result = new PagedResult<OrderDto>(pagedOrders, totalCount, query.PageNumber, query.PageSize);
            return Result<PagedResult<OrderDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user orders for {UserId}", userId);
            return Result<PagedResult<OrderDto>>.Failure("GET_ORDERS_ERROR", "获取订单列表失败");
        }
    }

    public async Task<Result> CancelOrderAsync(Guid orderId, Guid userId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            var order = await _unitOfWork.Repository<Domain.Entities.Payment.Order>().GetByIdAsync(orderId, cancellationToken);
            if (order == null)
            {
                return Result.Failure("ORDER_NOT_FOUND", "订单不存在");
            }

            if (order.CustomerUserId != userId)
            {
                return Result.Failure("UNAUTHORIZED", "无权取消此订单");
            }

            if (order.Status != OrderStatus.Pending)
            {
                return Result.Failure("INVALID_STATUS", "只能取消待支付的订单");
            }

            order.Cancel(reason);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Order {OrderNo} cancelled by user {UserId}", order.OrderNo, userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling order {OrderId}", orderId);
            return Result.Failure("CANCEL_ERROR", "取消订单失败");
        }
    }

    public async Task<Result<PaymentRequestDto>> InitiatePaymentAsync(Guid orderId, PaymentMethod paymentMethod, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var order = await _unitOfWork.Repository<Domain.Entities.Payment.Order>().GetByIdAsync(orderId, cancellationToken);
            if (order == null)
            {
                return Result<PaymentRequestDto>.Failure("ORDER_NOT_FOUND", "订单不存在");
            }

            if (order.CustomerUserId != userId)
            {
                return Result<PaymentRequestDto>.Failure("UNAUTHORIZED", "无权支付此订单");
            }

            if (order.Status != OrderStatus.Pending)
            {
                return Result<PaymentRequestDto>.Failure("INVALID_STATUS", "订单状态不允许支付");
            }

            if (DateTime.UtcNow > order.ExpireAt)
            {
                return Result<PaymentRequestDto>.Failure("ORDER_EXPIRED", "订单已过期");
            }

            // Create payment transaction
            var payment = order.CreatePayment(GeneratePaymentNo());

            // Payment is already added to order through CreatePayment method
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Build payment request
            var paymentRequest = new PaymentRequestDto
            {
                OrderId = order.Id,
                OrderNo = order.OrderNo,
                PaymentNo = payment.TransactionId ?? GeneratePaymentNo(),
                Amount = order.PayableAmount.Amount,
                Subject = order.ProductName ?? "商品订单",
                ExpireAt = order.ExpireAt
            };

            _logger.LogInformation("Payment initiated for order {OrderNo}, payment method: {PaymentMethod}", 
                order.OrderNo, paymentMethod);

            return Result<PaymentRequestDto>.Success(paymentRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initiating payment for order {OrderId}", orderId);
            return Result<PaymentRequestDto>.Failure("PAYMENT_INIT_ERROR", "发起支付失败");
        }
    }

    public async Task<Result> UpdateOrderStatusAsync(Guid orderId, OrderStatus newStatus, CancellationToken cancellationToken = default)
    {
        try
        {
            var order = await _unitOfWork.Repository<Domain.Entities.Payment.Order>().GetByIdAsync(orderId, cancellationToken);
            if (order == null)
            {
                return Result.Failure("ORDER_NOT_FOUND", "订单不存在");
            }
            
            // Update status based on new status
            switch (newStatus)
            {
                case OrderStatus.Paid:
                    order.MarkAsPaid(GeneratePaymentNo());
                    break;
                case OrderStatus.Cancelled:
                    order.Cancel("系统取消");
                    break;
                case OrderStatus.Failed:
                    order.MarkAsFailed("支付失败");
                    break;
                case OrderStatus.Expired:
                    order.Expire();
                    break;
                default:
                    return Result.Failure("INVALID_STATUS", "无效的订单状态");
            }
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Order {OrderId} status updated to {Status}", orderId, newStatus);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating order status {OrderId}", orderId);
            return Result.Failure("UPDATE_ERROR", "更新订单状态失败");
        }
    }

    public async Task<Result<OrderStatisticsDto>> GetOrderStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var orderRepository = _unitOfWork.Repository<Domain.Entities.Payment.Order>();
            var orders = await orderRepository.GetAsync(o => o.CreatedAt >= startDate && o.CreatedAt <= endDate, cancellationToken);
            var orderList = orders.ToList();

            var dto = new OrderStatisticsDto
            {
                TotalOrders = orderList.Count,
                CompletedOrders = orderList.Count(o => o.Status == OrderStatus.Paid),
                CancelledOrders = orderList.Count(o => o.Status == OrderStatus.Cancelled),
                RefundedOrders = orderList.Count(o => o.Status == OrderStatus.Refunded),
                TotalRevenue = orderList.Where(o => o.Status == OrderStatus.Paid).Sum(o => o.FinalAmount.Amount),
                AverageOrderValue = orderList.Where(o => o.Status == OrderStatus.Paid).Select(o => o.FinalAmount.Amount).DefaultIfEmpty(0).Average(),
                OrdersByType = orderList.GroupBy(o => o.Type).ToDictionary(g => g.Key.ToString(), g => g.Count()),
                DailyRevenue = orderList.Where(o => o.Status == OrderStatus.Paid)
                    .GroupBy(o => o.PaidAt?.Date ?? o.CreatedAt.Date)
                    .ToDictionary(g => g.Key, g => g.Sum(o => o.FinalAmount.Amount))
            };

            return Result<OrderStatisticsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order statistics");
            return Result<OrderStatisticsDto>.Failure("STATS_ERROR", "获取订单统计失败");
        }
    }

    private string GenerateOrderNo()
    {
        return $"ORD{DateTime.UtcNow:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";
    }

    private string GeneratePaymentNo()
    {
        return $"PAY{DateTime.UtcNow:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";
    }


    private OrderDto MapToDto(Domain.Entities.Payment.Order order)
    {
        return new OrderDto
        {
            Id = order.Id,
            OrderNo = order.OrderNo,
            UserId = order.CustomerUserId,
            Status = order.Status,
            TotalAmount = order.Amount.Amount,
            DiscountAmount = order.DiscountAmount?.Amount ?? 0,
            PayableAmount = order.PayableAmount.Amount,
            Currency = order.Amount.Currency,
            Items = new List<OrderItemDto>
            {
                new OrderItemDto
                {
                    ProductType = order.Type switch
                    {
                        OrderType.Subscription => ProductType.SubscriptionPlan,
                        OrderType.TokenPackage => ProductType.TokenPackage,
                        OrderType.OneTimeService => ProductType.AddonService,
                        _ => ProductType.Other
                    },
                    ProductId = order.ProductId ?? Guid.Empty,
                    ProductName = order.ProductName ?? "商品",
                    Quantity = 1,
                    UnitPrice = order.Amount.Amount
                }
            },
            CouponCode = order.CouponCode,
            Remark = order.Remark,
            CreatedAt = order.CreatedAt,
            PaidAt = order.PaidAt,
            RefundedAt = order.RefundedAt,
            ExpireAt = order.ExpireAt
        };
    }
}