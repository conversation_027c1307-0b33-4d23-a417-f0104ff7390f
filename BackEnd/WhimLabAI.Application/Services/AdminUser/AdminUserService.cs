using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Admin;
using WhimLabAI.Shared.Dtos.Admin.User;
using WhimLabAI.Shared.Dtos.Admin.Rbac;
using WhimLabAI.Shared.Dtos.Audit;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.AdminUser;

public class AdminUserService : IAdminUserService
{
    private readonly IAdminUserRepository _adminUserRepository;
    private readonly IRoleRepository _roleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AdminUserService> _logger;
    private readonly IAuditLogger _auditLogger;
    private readonly IAuditLogApplicationService _auditLogService;

    public AdminUserService(
        IAdminUserRepository adminUserRepository,
        IRoleRepository roleRepository,
        IUnitOfWork unitOfWork,
        ILogger<AdminUserService> logger,
        IAuditLogger auditLogger,
        IAuditLogApplicationService auditLogService)
    {
        _adminUserRepository = adminUserRepository;
        _roleRepository = roleRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _auditLogger = auditLogger;
        _auditLogService = auditLogService;
    }

    public async Task<Result<PagedResult<AdminListDto>>> GetAdminListAsync(AdminQueryDto query, CancellationToken cancellationToken = default)
    {
        try
        {
            var queryable = _adminUserRepository.GetQueryable();

            // 关键词搜索
            if (!string.IsNullOrWhiteSpace(query.Keyword))
            {
                queryable = queryable.Where(a => 
                    a.Username.Contains(query.Keyword) ||
                    (a.Nickname != null && a.Nickname.Contains(query.Keyword)) ||
                    (a.Email.Value.Contains(query.Keyword)) ||
                    (a.Phone != null && a.Phone.Value.Contains(query.Keyword)));
            }

            // 角色筛选
            if (!string.IsNullOrWhiteSpace(query.Role))
            {
                queryable = queryable.Where(a => a.UserRoles.Any(ur => ur.Role != null && ur.Role.Name == query.Role));
            }

            // 状态筛选
            if (query.IsActive.HasValue)
            {
                queryable = queryable.Where(a => a.IsActive == query.IsActive.Value);
            }

            // 总数
            var totalCount = await _adminUserRepository.CountAsync(a => queryable.Contains(a), cancellationToken);

            // 分页和排序
            var admins = await _adminUserRepository.GetPagedAsync(
                query.PageNumber,
                query.PageSize,
                a => queryable.Contains(a),
                q => q.OrderByDescending(a => a.CreatedAt),
                cancellationToken);

            var items = admins.Select(a => new AdminListDto
            {
                Id = a.Id,
                Username = a.Username,
                Nickname = a.Nickname,
                Email = a.Email?.Value,
                Phone = a.Phone?.Value,
                IsActive = a.IsActive,
                IsSuperAdmin = a.IsSuperAdmin,
                Roles = a.UserRoles.Where(ur => ur.Role != null).Select(ur => ur.Role.Name).ToList(),
                LastLoginAt = a.LastLoginAt,
                CreatedAt = a.CreatedAt
            }).ToList();

            var result = new PagedResult<AdminListDto>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = query.PageNumber,
                PageSize = query.PageSize
            };

            return Result<PagedResult<AdminListDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取管理员列表失败");
            return Result<PagedResult<AdminListDto>>.Failure("获取管理员列表失败");
        }
    }

    public async Task<Result<AdminDetailDto>> GetAdminDetailAsync(Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result<AdminDetailDto>.Failure("管理员不存在");
            }

            // 获取最近操作记录
            var recentLogsResult = await _auditLogService.QueryAsync(new AuditLogQueryDto
            {
                UserId = admin.Id.ToString(),
                PageNumber = 1,
                PageSize = 10
            }, cancellationToken);

            var recentOperations = new List<AdminOperationLogDto>();
            if (recentLogsResult != null && recentLogsResult.Items != null)
            {
                recentOperations = recentLogsResult.Items.Select(log => new AdminOperationLogDto
                {
                    Operation = $"{log.Module ?? ""} - {log.Action}",
                    Description = log.Description,
                    IpAddress = log.IpAddress,
                    OperatedAt = log.CreatedAt
                }).ToList();
            }

            var adminDetail = new AdminDetailDto
            {
                Id = admin.Id,
                Username = admin.Username,
                Nickname = admin.Nickname,
                Email = admin.Email?.Value,
                Phone = admin.Phone?.Value,
                IsActive = admin.IsActive,
                IsSuperAdmin = admin.IsSuperAdmin,
                Roles = admin.UserRoles.Where(ur => ur.Role != null).Select(ur => ur.Role.Name).ToList(),
                LastLoginAt = admin.LastLoginAt,
                CreatedAt = admin.CreatedAt,
                Permissions = admin.GetAllPermissions(),
                LastLoginIp = admin.LastLoginIp,
                PasswordChangedAt = admin.UpdatedAt, // 使用UpdatedAt作为密码更改时间的近似值
                RecentOperations = recentOperations
            };

            return Result<AdminDetailDto>.Success(adminDetail);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取管理员详情失败，AdminId: {AdminId}", adminId);
            return Result<AdminDetailDto>.Failure("获取管理员详情失败");
        }
    }

    public async Task<Result<Guid>> CreateAdminAsync(CreateAdminDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证用户名是否已存在
            if (await _adminUserRepository.IsUsernameExistsAsync(request.Username, cancellationToken))
            {
                return Result<Guid>.Failure("用户名已存在");
            }

            // 验证邮箱是否已存在
            if (!string.IsNullOrWhiteSpace(request.Email))
            {
                if (await _adminUserRepository.IsEmailExistsAsync(request.Email, cancellationToken))
                {
                    return Result<Guid>.Failure("邮箱已被使用");
                }
            }

            // 验证手机号是否已存在
            if (!string.IsNullOrWhiteSpace(request.Phone))
            {
                var existingAdmin = await _adminUserRepository.GetByPhoneAsync(request.Phone, cancellationToken);
                if (existingAdmin != null)
                {
                    return Result<Guid>.Failure("手机号已被使用");
                }
            }

            // 创建管理员
            var admin = new WhimLabAI.Domain.Entities.User.AdminUser(
                request.Username,
                request.Email ?? $"{request.Username}@whimlabai.com", // 如果没有提供邮箱，使用默认邮箱
                request.Password,
                request.Phone,
                false // 新创建的管理员不是超级管理员
            );

            // 设置昵称
            if (!string.IsNullOrWhiteSpace(request.Nickname))
            {
                admin.UpdateProfile(nickname: request.Nickname);
            }

            // 分配角色
            if (request.RoleIds.Any())
            {
                foreach (var roleId in request.RoleIds)
                {
                    var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
                    if (role != null && role.IsEnabled)
                    {
                        admin.AssignRole(roleId);
                    }
                    else
                    {
                        _logger.LogWarning("角色不存在或已禁用: {RoleId}", roleId);
                    }
                }
            }

            await _adminUserRepository.AddAsync(admin, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "CreateAdmin",
                "AdminManagement",
                $"创建管理员账号: {admin.Username}",
                new
                {
                    AdminId = admin.Id,
                    Username = admin.Username,
                    Email = admin.Email,
                    Nickname = admin.Nickname,
                    IsSuperAdmin = admin.IsSuperAdmin,
                    Roles = request.RoleIds
                });

            _logger.LogInformation("管理员创建成功: {Username}, ID: {AdminId}", admin.Username, admin.Id);
            return Result<Guid>.Success(admin.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建管理员失败");
            return Result<Guid>.Failure("创建管理员失败");
        }
    }

    public async Task<Result> UpdateAdminAsync(Guid adminId, UpdateAdminDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("管理员不存在");
            }

            // 记录原始信息用于审计
            var originalData = new
            {
                Nickname = admin.Nickname,
                Email = admin.Email?.Value,
                Phone = admin.Phone?.Value,
                IsActive = admin.IsActive
            };

            // 验证邮箱唯一性
            if (!string.IsNullOrWhiteSpace(request.Email) && request.Email != admin.Email?.Value)
            {
                var existingAdmin = await _adminUserRepository.GetByEmailAsync(request.Email, cancellationToken);
                if (existingAdmin != null && existingAdmin.Id != adminId)
                {
                    return Result.Failure("邮箱已被其他账号使用");
                }
            }

            // 验证手机号唯一性
            if (!string.IsNullOrWhiteSpace(request.Phone) && request.Phone != admin.Phone?.Value)
            {
                var existingAdmin = await _adminUserRepository.GetByPhoneAsync(request.Phone, cancellationToken);
                if (existingAdmin != null && existingAdmin.Id != adminId)
                {
                    return Result.Failure("手机号已被其他账号使用");
                }
            }

            // 更新基本信息
            admin.UpdateProfile(
                nickname: request.Nickname,
                email: request.Email,
                phone: request.Phone
            );

            // 更新状态
            if (request.IsActive.HasValue && request.IsActive.Value != admin.IsActive)
            {
                if (request.IsActive.Value)
                {
                    admin.Activate();
                }
                else
                {
                    admin.Deactivate();
                }
            }

            _adminUserRepository.Update(admin);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            var changedFields = new List<string>();
            if (!string.IsNullOrEmpty(request.Nickname)) changedFields.Add("昵称");
            if (!string.IsNullOrEmpty(request.Email)) changedFields.Add("邮箱");
            if (!string.IsNullOrEmpty(request.Phone)) changedFields.Add("手机号");
            if (request.IsActive.HasValue) changedFields.Add("状态");
            
            await _auditLogger.LogActionAsync(
                "UpdateAdmin",
                "AdminManagement",
                $"更新管理员信息: {admin.Username}, 修改字段: {string.Join(", ", changedFields)}",
                new
                {
                    AdminId = adminId,
                    Username = admin.Username,
                    UpdatedFields = changedFields,
                    OriginalData = originalData,
                    UpdatedData = new
                    {
                        Nickname = request.Nickname,
                        Email = request.Email,
                        Phone = request.Phone,
                        IsActive = request.IsActive
                    }
                });

            _logger.LogInformation("管理员信息更新成功: {Username}, ID: {AdminId}", admin.Username, adminId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新管理员信息失败，AdminId: {AdminId}", adminId);
            return Result.Failure("更新管理员信息失败");
        }
    }

    public async Task<Result> DeleteAdminAsync(Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("管理员不存在");
            }

            if (admin.IsSuperAdmin)
            {
                return Result.Failure("不能删除超级管理员");
            }

            // 检查是否是最后一个活跃的管理员
            var activeAdminCount = await _adminUserRepository.CountAsync(a => a.IsActive && a.Id != adminId, cancellationToken);
            if (activeAdminCount == 0)
            {
                return Result.Failure("不能删除最后一个活跃的管理员");
            }

            _adminUserRepository.Remove(admin);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "DeleteAdmin",
                "AdminManagement",
                $"删除管理员账号: {admin.Username}",
                new
                {
                    AdminId = adminId,
                    Username = admin.Username,
                    Email = admin.Email,
                    Nickname = admin.Nickname,
                    IsSuperAdmin = admin.IsSuperAdmin
                });

            _logger.LogInformation("管理员删除成功: {Username}, ID: {AdminId}", admin.Username, adminId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除管理员失败，AdminId: {AdminId}", adminId);
            return Result.Failure("删除管理员失败");
        }
    }

    public async Task<Result> AssignRolesAsync(Guid adminId, List<Guid> roleIds, Guid? assignedBy = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetWithRolesAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("管理员不存在");
            }

            // 验证所有角色是否存在
            var roles = await _roleRepository.GetByIdsWithPermissionsAsync(roleIds, cancellationToken);
            var validRoleIds = roles.Where(r => r.IsEnabled).Select(r => r.Id).ToList();
            
            // 获取原有角色用于审计
            var oldRoleIds = admin.UserRoles.Select(ur => ur.RoleId).ToList();
            
            // 更新角色
            admin.UpdateRoles(validRoleIds, assignedBy);

            _adminUserRepository.Update(admin);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "AssignRoles",
                "AdminManagement",
                $"为管理员 {admin.Username} 分配角色",
                new
                {
                    AdminId = adminId,
                    Username = admin.Username,
                    OldRoles = oldRoleIds,
                    NewRoles = validRoleIds,
                    AssignedBy = assignedBy,
                    RolesAdded = validRoleIds.Except(oldRoleIds).ToList(),
                    RolesRemoved = oldRoleIds.Except(validRoleIds).ToList()
                });

            _logger.LogInformation("角色分配成功: AdminId={AdminId}, Roles={Roles}", adminId, string.Join(",", validRoleIds));
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分配角色失败，AdminId: {AdminId}", adminId);
            return Result.Failure("分配角色失败");
        }
    }

    public async Task<Result> RemoveRoleAsync(Guid adminId, Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetWithRolesAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("管理员不存在");
            }

            // 获取角色信息用于审计
            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            var roleName = role?.Name ?? roleId.ToString();

            admin.RemoveRole(roleId);

            _adminUserRepository.Update(admin);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "RemoveRole",
                "AdminManagement",
                $"从管理员 {admin.Username} 移除角色 {roleName}",
                new
                {
                    AdminId = adminId,
                    Username = admin.Username,
                    RoleId = roleId,
                    RoleName = roleName,
                    RemainingRoles = admin.UserRoles.Select(ur => ur.RoleId).ToList()
                });

            _logger.LogInformation("移除角色成功: AdminId={AdminId}, RoleId={RoleId}", adminId, roleId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除角色失败，AdminId: {AdminId}, RoleId: {RoleId}", adminId, roleId);
            return Result.Failure("移除角色失败");
        }
    }

    public async Task<Result<AdminUserPermissionsDto>> GetUserPermissionsAsync(Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetWithRolesAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result<AdminUserPermissionsDto>.Failure("管理员不存在");
            }

            var roleIds = admin.UserRoles.Select(ur => ur.RoleId).ToList();
            var roles = await _roleRepository.GetByIdsWithPermissionsAsync(roleIds, cancellationToken);

            var dto = new AdminUserPermissionsDto
            {
                UserId = admin.Id,
                Username = admin.Username,
                IsSuperAdmin = admin.IsSuperAdmin,
                Roles = roles.Select(r => new RoleAssignmentDto
                {
                    RoleId = r.Id,
                    RoleName = r.Name,
                    RoleCode = r.Code,
                    // 使用CreatedAt作为分配时间，因为AssignedAt在数据库中不存在
                    AssignedAt = admin.UserRoles.FirstOrDefault(ur => ur.RoleId == r.Id)?.CreatedAt ?? DateTime.UtcNow
                }).ToList(),
                PermissionCodes = admin.GetPermissionCodes().ToList()
            };

            return Result<AdminUserPermissionsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取管理员权限失败，AdminId: {AdminId}", adminId);
            return Result<AdminUserPermissionsDto>.Failure("获取管理员权限失败");
        }
    }

    public async Task<Result<PermissionCheckResultDto>> CheckPermissionAsync(Guid adminId, string permissionCode, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetWithRolesAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result<PermissionCheckResultDto>.Success(new PermissionCheckResultDto
                {
                    HasPermission = false,
                    Reason = "管理员不存在"
                });
            }

            if (!admin.IsActive)
            {
                return Result<PermissionCheckResultDto>.Success(new PermissionCheckResultDto
                {
                    HasPermission = false,
                    Reason = "管理员账号已停用"
                });
            }

            var hasPermission = await _adminUserRepository.HasPermissionAsync(adminId, permissionCode, cancellationToken);

            return Result<PermissionCheckResultDto>.Success(new PermissionCheckResultDto
            {
                HasPermission = hasPermission,
                Reason = hasPermission ? null : "没有此权限"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查权限失败，AdminId: {AdminId}, Permission: {Permission}", adminId, permissionCode);
            return Result<PermissionCheckResultDto>.Failure("检查权限失败");
        }
    }

    public async Task<Result<List<string>>> GetUserPermissionCodesAsync(Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetWithRolesAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result<List<string>>.Failure("管理员不存在");
            }

            var permissionCodes = admin.GetPermissionCodes().ToList();
            return Result<List<string>>.Success(permissionCodes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取管理员权限代码失败，AdminId: {AdminId}", adminId);
            return Result<List<string>>.Failure("获取管理员权限代码失败");
        }
    }

    public async Task<Result<IpWhitelistResponseDto>> GetIpWhitelistAsync(Guid adminId, string? currentIp = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result<IpWhitelistResponseDto>.Failure("管理员不存在");
            }

            var response = new IpWhitelistResponseDto
            {
                EnableIpWhitelist = admin.EnableIpWhitelist,
                IpWhitelist = admin.IpWhitelist.ToList(),
                CurrentIp = currentIp,
                IsCurrentIpAllowed = string.IsNullOrEmpty(currentIp) || admin.IsIpAllowed(currentIp)
            };

            return Result<IpWhitelistResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取IP白名单失败，AdminId: {AdminId}", adminId);
            return Result<IpWhitelistResponseDto>.Failure("获取IP白名单失败");
        }
    }

    public async Task<Result> UpdateIpWhitelistAsync(Guid adminId, UpdateIpWhitelistDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("管理员不存在");
            }

            // 验证IP地址格式
            var invalidIps = request.IpWhitelist.Where(ip => !IsValidIpAddress(ip)).ToList();
            if (invalidIps.Any())
            {
                return Result.Failure($"以下IP地址格式无效: {string.Join(", ", invalidIps)}");
            }

            // 获取原有IP白名单用于审计
            var oldIpWhitelist = admin.IpWhitelist.ToList();
            var oldEnabled = admin.EnableIpWhitelist;

            // 更新IP白名单
            admin.SetIpWhitelist(request.IpWhitelist.ToArray());
            
            // 更新启用状态
            if (request.EnableIpWhitelist)
            {
                admin.EnableIpWhitelistFeature();
            }
            else
            {
                admin.DisableIpWhitelistFeature();
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "UpdateIpWhitelist",
                "AdminManagement",
                $"更新管理员 {admin.Username} 的IP白名单",
                new
                {
                    AdminId = adminId,
                    Username = admin.Username,
                    OldIpWhitelist = oldIpWhitelist,
                    NewIpWhitelist = request.IpWhitelist,
                    OldEnabled = oldEnabled,
                    NewEnabled = request.EnableIpWhitelist,
                    IpsAdded = request.IpWhitelist.Except(oldIpWhitelist).ToList(),
                    IpsRemoved = oldIpWhitelist.Except(request.IpWhitelist).ToList()
                });

            _logger.LogInformation("管理员 {AdminId} 更新了IP白名单，启用状态: {EnableStatus}, IP数量: {IpCount}", 
                adminId, request.EnableIpWhitelist, request.IpWhitelist.Count);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新IP白名单失败，AdminId: {AdminId}", adminId);
            return Result.Failure("更新IP白名单失败");
        }
    }

    public async Task<Result> AddIpToWhitelistAsync(Guid adminId, string ipAddress, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!IsValidIpAddress(ipAddress))
            {
                return Result.Failure("IP地址格式无效");
            }

            var admin = await _adminUserRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("管理员不存在");
            }

            admin.AddIpToWhitelist(ipAddress);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "AddIpToWhitelist",
                "AdminManagement",
                $"管理员 {admin.Username} 添加IP {ipAddress} 到白名单",
                new
                {
                    AdminId = adminId,
                    Username = admin.Username,
                    IpAddress = ipAddress,
                    CurrentWhitelist = admin.IpWhitelist.ToList()
                });

            _logger.LogInformation("管理员 {AdminId} 添加IP {IpAddress} 到白名单", adminId, ipAddress);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加IP到白名单失败，AdminId: {AdminId}, IP: {IpAddress}", adminId, ipAddress);
            return Result.Failure("添加IP到白名单失败");
        }
    }

    public async Task<Result> RemoveIpFromWhitelistAsync(Guid adminId, string ipAddress, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("管理员不存在");
            }

            admin.RemoveIpFromWhitelist(ipAddress);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "RemoveIpFromWhitelist",
                "AdminManagement",
                $"管理员 {admin.Username} 从白名单移除IP {ipAddress}",
                new
                {
                    AdminId = adminId,
                    Username = admin.Username,
                    IpAddress = ipAddress,
                    CurrentWhitelist = admin.IpWhitelist.ToList()
                });

            _logger.LogInformation("管理员 {AdminId} 从白名单移除IP {IpAddress}", adminId, ipAddress);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从白名单移除IP失败，AdminId: {AdminId}, IP: {IpAddress}", adminId, ipAddress);
            return Result.Failure("从白名单移除IP失败");
        }
    }

    public async Task<Result> ToggleIpWhitelistAsync(Guid adminId, bool enable, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminUserRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("管理员不存在");
            }

            if (enable)
            {
                // 启用前检查是否已设置IP白名单
                if (!admin.IpWhitelist.Any())
                {
                    return Result.Failure("请先添加至少一个IP地址到白名单");
                }
                admin.EnableIpWhitelistFeature();
            }
            else
            {
                admin.DisableIpWhitelistFeature();
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                enable ? "EnableIpWhitelist" : "DisableIpWhitelist",
                "AdminManagement",
                $"管理员 {admin.Username} {(enable ? "启用" : "禁用")}IP白名单功能",
                new
                {
                    AdminId = adminId,
                    Username = admin.Username,
                    Enabled = enable,
                    IpWhitelist = admin.IpWhitelist.ToList()
                });

            _logger.LogInformation("管理员 {AdminId} {Action} IP白名单功能", 
                adminId, enable ? "启用" : "禁用");

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换IP白名单状态失败，AdminId: {AdminId}, Enable: {Enable}", adminId, enable);
            return Result.Failure("切换IP白名单状态失败");
        }
    }

    private bool IsValidIpAddress(string ipAddress)
    {
        if (string.IsNullOrWhiteSpace(ipAddress))
            return false;

        // 简单的IPv4验证
        var parts = ipAddress.Split('.');
        if (parts.Length != 4)
            return false;

        return parts.All(part => byte.TryParse(part, out var b));
    }
}