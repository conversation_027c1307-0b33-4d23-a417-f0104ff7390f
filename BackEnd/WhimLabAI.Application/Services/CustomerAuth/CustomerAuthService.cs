using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Dtos.Auth;
using WhimLabAI.Shared.Dtos.Customer.Auth;
using WhimLabAI.Shared.Dtos.Customer.Security;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Constants;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Utilities;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;

namespace WhimLabAI.Application.Services.CustomerAuth;

/// <summary>
/// QR码状态数据类
/// </summary>
internal class QRCodeStatusData
{
    public QRCodeSessionStatus Status { get; set; }
    public DateTime? ScannedAt { get; set; }
    public DateTime? AuthenticatedAt { get; set; }
    public AuthResponseDto? AuthResponse { get; set; }
}

/// <summary>
/// 刷新令牌数据类
/// </summary>
internal class RefreshTokenData
{
    public Guid UserId { get; set; }
    public string Username { get; set; } = string.Empty;
    public UserType UserType { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
}

public class CustomerAuthService : ICustomerAuthService
{
    private readonly ICustomerUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IJwtTokenService _jwtTokenService;
    private readonly ICacheService _cacheService;
    private readonly IVerificationService _verificationService;
    private readonly IQRCodeGeneratorService _qrCodeGeneratorService;
    private readonly IQRCodeSessionRepository _qrCodeSessionRepository;
    private readonly IRealtimeNotificationService _realtimeNotificationService;
    private readonly ILogger<CustomerAuthService> _logger;
    private readonly VerificationOptions _verificationOptions;
    private readonly IConfiguration _configuration;
    private readonly IServiceProvider _serviceProvider;
    private readonly IRecoveryCodeRepository _recoveryCodeRepository;

    public CustomerAuthService(
        ICustomerUserRepository userRepository,
        IUnitOfWork unitOfWork,
        IJwtTokenService jwtTokenService,
        ICacheService cacheService,
        IVerificationService verificationService,
        IQRCodeGeneratorService qrCodeGeneratorService,
        IQRCodeSessionRepository qrCodeSessionRepository,
        IRealtimeNotificationService realtimeNotificationService,
        ILogger<CustomerAuthService> logger,
        IOptions<VerificationOptions> verificationOptions,
        IConfiguration configuration,
        IServiceProvider serviceProvider,
        IRecoveryCodeRepository recoveryCodeRepository)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _jwtTokenService = jwtTokenService;
        _cacheService = cacheService;
        _verificationService = verificationService;
        _qrCodeGeneratorService = qrCodeGeneratorService;
        _qrCodeSessionRepository = qrCodeSessionRepository;
        _realtimeNotificationService = realtimeNotificationService;
        _logger = logger;
        _verificationOptions = verificationOptions.Value;
        _configuration = configuration;
        _serviceProvider = serviceProvider;
        _recoveryCodeRepository = recoveryCodeRepository;
    }

    public async Task<Result<AuthResponseDto>> RegisterAsync(CustomerRegisterDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证用户名是否已存在
            if (await _userRepository.IsUsernameExistsAsync(request.Username, cancellationToken))
            {
                return Result<AuthResponseDto>.Failure("用户名已存在");
            }

            // 验证邮箱
            if (!string.IsNullOrEmpty(request.Email))
            {
                if (await _userRepository.IsEmailExistsAsync(request.Email, cancellationToken))
                {
                    return Result<AuthResponseDto>.Failure("邮箱已被注册");
                }
            }

            // 验证手机号
            if (!string.IsNullOrEmpty(request.PhoneNumber))
            {
                if (await _userRepository.IsPhoneNumberExistsAsync(request.PhoneNumber, cancellationToken))
                {
                    return Result<AuthResponseDto>.Failure("手机号已被注册");
                }
            }

            // 创建用户
            var user = new WhimLabAI.Domain.Entities.User.CustomerUser(
                request.Username,
                request.Password,
                request.Email,
                request.PhoneNumber,
                request.IpAddress);

            await _userRepository.AddAsync(user, cancellationToken);

            // 创建默认的用户资料
            var profile = new CustomerProfile(user.Id);
            await _unitOfWork.Repository<CustomerProfile>().AddAsync(profile, cancellationToken);

            // 激活免费订阅
            await ActivateFreeSubscriptionAsync(user, cancellationToken);

            // 如果提供了设备信息，创建设备授权
            if (!string.IsNullOrEmpty(request.DeviceId))
            {
                var deviceInfo = request.DeviceType ?? "Unknown";
                user.AuthorizeDevice(
                    request.DeviceId,
                    request.DeviceName ?? "Unknown Device",
                    deviceInfo);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户注册成功: {Username}", user.Username);

            // 生成令牌
            var response = await GenerateAuthResponseAsync(user, profile);
            return Result<AuthResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户注册失败: {Username}, 错误详情: {ErrorMessage}, 堆栈: {StackTrace}", 
                request.Username, ex.Message, ex.StackTrace);
            
            // 在开发环境返回更详细的错误信息
            var environment = _configuration["ASPNETCORE_ENVIRONMENT"] ?? "Production";
            if (environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
            {
                return Result<AuthResponseDto>.Failure($"注册失败: {ex.Message}");
            }
            
            return Result<AuthResponseDto>.Failure("注册失败，请稍后重试");
        }
    }

    public async Task<Result<AuthResponseDto>> LoginAsync(CustomerLoginDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 定义失败尝试键，在整个方法中重用
            var failedAttemptKey = $"{SystemConstants.CacheKeys.LoginFailedCount}{request.Account}";
            
            // 验证图形验证码（根据配置决定是否需要）
            if (await ShouldVerifyCaptchaAsync(request.Account))
            {
                if (string.IsNullOrEmpty(request.CaptchaId) || string.IsNullOrEmpty(request.CaptchaCode))
                {
                    return Result<AuthResponseDto>.Failure("请输入验证码");
                }
                
                var captchaResult = await _verificationService.VerifyCaptchaAsync(request.CaptchaId, request.CaptchaCode, cancellationToken);
                if (!captchaResult.IsSuccess || !captchaResult.Value)
                {
                    return Result<AuthResponseDto>.Failure("验证码错误或已过期");
                }
            }

            // 验证输入参数
            if (request.LoginMethod == LoginMethod.EmailCode || request.LoginMethod == LoginMethod.PhoneCode)
            {
                if (string.IsNullOrEmpty(request.VerificationCode))
                {
                    return Result<AuthResponseDto>.Failure("验证码登录时验证码不能为空");
                }
            }
            else if (string.IsNullOrEmpty(request.Password))
            {
                return Result<AuthResponseDto>.Failure("密码登录时密码不能为空");
            }

            WhimLabAI.Domain.Entities.User.CustomerUser? user = null;

            // 根据登录方式查找用户
            if (request.LoginMethod == LoginMethod.EmailCode)
            {
                user = await _userRepository.GetByEmailAsync(request.Account, cancellationToken);
            }
            else if (request.LoginMethod == LoginMethod.PhoneCode)
            {
                user = await _userRepository.GetByPhoneNumberAsync(request.Account, cancellationToken);
            }
            else
            {
                // 密码登录：尝试多种方式查找用户
                user = await _userRepository.GetByUsernameAsync(request.Account, cancellationToken) ??
                       await _userRepository.GetByEmailAsync(request.Account, cancellationToken) ??
                       await _userRepository.GetByPhoneNumberAsync(request.Account, cancellationToken);
            }

            if (user == null)
            {
                return Result<AuthResponseDto>.Failure("账号或密码错误");
            }

            // 检查账号是否被锁定
            if (user.IsLocked())
            {
                var remainingTime = user.GetLockRemainingMinutes();
                return Result<AuthResponseDto>.Failure($"账号已被锁定，请{remainingTime}分钟后重试");
            }

            // 根据登录方式进行验证
            if (request.LoginMethod == LoginMethod.EmailCode || request.LoginMethod == LoginMethod.PhoneCode)
            {
                // 验证码登录：验证验证码而不是密码
                if (string.IsNullOrEmpty(request.VerificationCode))
                {
                    return Result<AuthResponseDto>.Failure("验证码不能为空");
                }

                var target = request.LoginMethod == LoginMethod.EmailCode ? user.Email?.Value : user.Phone?.Value;
                if (string.IsNullOrEmpty(target))
                {
                    return Result<AuthResponseDto>.Failure("账号未绑定对应的邮箱或手机号");
                }

                var verificationType = request.LoginMethod == LoginMethod.EmailCode ? VerificationType.Login : VerificationType.Login;
                var verifyResult = await _verificationService.VerifyCodeAsync(target, request.VerificationCode, verificationType, cancellationToken);
                
                if (!verifyResult.IsSuccess)
                {
                    user.RecordFailedLogin();
                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                    
                    // 增加缓存中的失败次数
                    var failedCount = await _cacheService.GetAsync<int?>(failedAttemptKey) ?? 0;
                    await _cacheService.SetAsync(failedAttemptKey, failedCount + 1, TimeSpan.FromMinutes(30), cancellationToken);
                    
                    return Result<AuthResponseDto>.Failure("验证码错误或已过期");
                }

                // 清除验证状态
                await _verificationService.ClearVerificationAsync(target, verificationType, cancellationToken);
            }
            else
            {
                // 密码登录：验证密码
                if (!PasswordHelper.VerifyPassword(request.Password, user.PasswordHash.Hash))
                {
                    user.RecordFailedLogin();
                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                    
                    // 增加缓存中的失败次数
                    var failedCount = await _cacheService.GetAsync<int?>(failedAttemptKey) ?? 0;
                    await _cacheService.SetAsync(failedAttemptKey, failedCount + 1, TimeSpan.FromMinutes(30), cancellationToken);

                    if (user.IsLocked())
                    {
                        return Result<AuthResponseDto>.Failure($"密码错误次数过多，账号已被锁定{BusinessConstants.Account.AccountLockMinutes}分钟");
                    }

                    return Result<AuthResponseDto>.Failure($"账号或密码错误，剩余尝试次数: {BusinessConstants.Account.MaxLoginAttempts - user.LoginFailedCount}");
                }
            }

            // 检查是否需要双因素认证
            if (user.TwoFactorEnabled)
            {
                if (string.IsNullOrEmpty(request.TwoFactorCode))
                {
                    // 生成临时会话
                    var sessionId = Guid.NewGuid().ToString();
                    await _cacheService.SetAsync(
                        $"{SystemConstants.CacheKeys.TwoFactorSession}{sessionId}",
                        user.Id,
                        TimeSpan.FromMinutes(5),
                        cancellationToken);

                    return Result<AuthResponseDto>.Success(new AuthResponseDto
                    {
                        RequiresTwoFactor = true,
                        TwoFactorSessionId = sessionId
                    });
                }

                // 验证双因素认证码
                var totpService = _serviceProvider.GetRequiredService<ITotpService>();
                var secret = user.GetTwoFactorSecret();
                if (string.IsNullOrEmpty(secret) || !totpService.ValidateCode(secret, request.TwoFactorCode))
                {
                    return Result<AuthResponseDto>.Failure("INVALID_2FA_CODE", "双因素认证码无效");
                }
            }

            // 更新登录信息
            user.RecordLogin(request.IpAddress ?? "Unknown");
            
            // 清除登录失败次数缓存
            await _cacheService.RemoveAsync(failedAttemptKey, cancellationToken);

            // 记录登录历史
            var loginHistory = new LoginHistory(
                user.Id,
                request.IpAddress ?? "Unknown",
                request.LoginMethod, // 使用实际的登录方式
                true,
                request.DeviceType,
                null, // browser
                request.UserAgent);

            await _unitOfWork.Repository<LoginHistory>().AddAsync(loginHistory, cancellationToken);

            // 更新设备授权
            if (!string.IsNullOrEmpty(request.DeviceId))
            {
                user.AuthorizeDevice(
                    request.DeviceId,
                    request.DeviceName ?? "Unknown Device",
                    request.DeviceType ?? "Unknown");
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户登录成功: {Username}", user.Username);

            // 生成令牌
            var response = await GenerateAuthResponseAsync(user, user.Profile);
            return Result<AuthResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户登录失败: {Account}", request.Account);
            return Result<AuthResponseDto>.Failure("登录失败，请稍后重试");
        }
    }

    public async Task<Result<AuthResponseDto>> LoginByVerificationCodeAsync(CustomerLoginByVerificationCodeDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证账号格式
            bool isEmail = ValidationHelper.IsValidEmail(request.Account);
            bool isPhone = ValidationHelper.IsValidPhoneNumber(request.Account);
            
            if (!isEmail && !isPhone)
            {
                return Result<AuthResponseDto>.Failure("账号格式不正确，请输入正确的邮箱或手机号");
            }

            // 检查验证码是否有效
            var verificationType = VerificationType.Login;
            var verifyResult = await _verificationService.VerifyCodeAsync(request.Account, request.VerificationCode, verificationType, cancellationToken);
            if (!verifyResult.IsSuccess)
            {
                return Result<AuthResponseDto>.Failure(verifyResult.Error);
            }

            // 查找用户
            WhimLabAI.Domain.Entities.User.CustomerUser? user = null;
            if (isEmail)
            {
                user = await _userRepository.GetByEmailAsync(request.Account, cancellationToken);
            }
            else if (isPhone)
            {
                user = await _userRepository.GetByPhoneNumberAsync(request.Account, cancellationToken);
            }

            if (user == null)
            {
                return Result<AuthResponseDto>.Failure("账号不存在");
            }

            // 检查用户状态
            if (!user.IsActive)
            {
                return Result<AuthResponseDto>.Failure("账号已被禁用");
            }

            if (user.IsBanned)
            {
                return Result<AuthResponseDto>.Failure("账号已被封禁，请联系客服");
            }

            if (user.IsLocked())
            {
                var remainingMinutes = user.GetLockRemainingMinutes();
                return Result<AuthResponseDto>.Failure($"账号已被锁定，请{remainingMinutes}分钟后再试");
            }

            // 更新登录信息
            user.RecordLogin(request.IpAddress ?? "Unknown");
            
            // 记录登录历史
            var loginMethod = isEmail ? LoginMethod.EmailCode : LoginMethod.PhoneCode;
            var loginHistory = new LoginHistory(
                user.Id,
                request.IpAddress ?? "Unknown",
                loginMethod,
                true,
                request.DeviceName,
                null, // browser
                request.UserAgent);

            await _unitOfWork.Repository<LoginHistory>().AddAsync(loginHistory, cancellationToken);

            // 更新设备授权
            if (!string.IsNullOrEmpty(request.DeviceId))
            {
                user.AuthorizeDevice(
                    request.DeviceId,
                    request.DeviceName ?? "Unknown Device",
                    request.UserAgent ?? "Unknown");
            }

            // 清除验证码
            await _verificationService.ClearVerificationAsync(request.Account, verificationType, cancellationToken);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户通过验证码登录成功: {Account}", request.Account);

            // 生成令牌
            var response = await GenerateAuthResponseAsync(user, user.Profile);
            return Result<AuthResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证码登录失败: {Account}", request.Account);
            return Result<AuthResponseDto>.Failure("登录失败，请稍后重试");
        }
    }

    public async Task<Result> LogoutAsync(Guid userId, string? deviceId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 清除刷新令牌
            user.ClearRefreshToken();
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清除缓存
            await _cacheService.RemoveAsync($"{SystemConstants.CacheKeys.UserProfile}{userId}", cancellationToken);

            // 如果指定了设备ID，撤销该设备的授权
            if (!string.IsNullOrEmpty(deviceId))
            {
                var userObj = await _unitOfWork.CustomerUsers.GetByIdAsync(userId, cancellationToken);
                if (userObj is WhimLabAI.Domain.Entities.User.CustomerUser customerUser)
                {
                    var deviceAuth = customerUser.DeviceAuthorizations.FirstOrDefault(d => d.DeviceId == deviceId);
                    if (deviceAuth != null)
                    {
                        deviceAuth.Deactivate();
                        await _unitOfWork.SaveChangesAsync(cancellationToken);
                    }
                }
            }

            _logger.LogInformation("用户登出成功: UserId={UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户登出失败: UserId={UserId}", userId);
            return Result.Failure("登出失败，请稍后重试");
        }
    }

    public async Task<Result<AuthResponseDto>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        try
        {
            // Use the new repository method to find user by refresh token
            var user = await _userRepository.GetByRefreshTokenAsync(refreshToken, cancellationToken);
            if (user == null)
            {
                _logger.LogWarning("未找到匹配的刷新令牌: {RefreshToken}", refreshToken?.Substring(0, Math.Min(10, refreshToken?.Length ?? 0)) + "...");
                return Result<AuthResponseDto>.Failure("无效的刷新令牌");
            }

            if (!user.IsRefreshTokenValid(refreshToken))
            {
                _logger.LogWarning("刷新令牌已过期: UserId={UserId}", user.Id);
                return Result<AuthResponseDto>.Failure("刷新令牌已过期");
            }

            // 生成新的令牌
            var response = await GenerateAuthResponseAsync(user, user.Profile);
            
            _logger.LogInformation("刷新令牌成功: UserId={UserId}", user.Id);
            return Result<AuthResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新令牌失败");
            return Result<AuthResponseDto>.Failure("刷新令牌失败，请重新登录");
        }
    }

    public async Task<Result> ChangePasswordAsync(Guid userId, string oldPassword, string newPassword, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 验证旧密码
            if (!PasswordHelper.VerifyPassword(oldPassword, user.PasswordHash.Hash))
            {
                return Result.Failure("原密码错误");
            }

            // 更新密码
            user.ChangePassword(oldPassword, newPassword);

            // 清除刷新令牌，强制重新登录
            user.ClearRefreshToken();

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户修改密码成功: UserId={UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修改密码失败: UserId={UserId}", userId);
            return Result.Failure("修改密码失败，请稍后重试");
        }
    }

    public async Task<Result> ResetPasswordAsync(string account, string newPassword, string verificationCode, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证验证码
            var verifyResult = await _verificationService.VerifyCodeAsync(account, verificationCode, VerificationType.ResetPassword, cancellationToken);
            
            if (!verifyResult.IsSuccess)
            {
                return Result.Failure(verifyResult.Error);
            }

            // 查找用户
            var user = await _userRepository.GetByUsernameAsync(account, cancellationToken) ??
                      await _userRepository.GetByEmailAsync(account, cancellationToken) ??
                      await _userRepository.GetByPhoneNumberAsync(account, cancellationToken);

            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 重置密码
            user.ResetPassword(newPassword);

            // 清除刷新令牌
            user.ClearRefreshToken();

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清除验证状态
            await _verificationService.ClearVerificationAsync(account, VerificationType.ResetPassword, cancellationToken);

            _logger.LogInformation("用户重置密码成功: UserId={UserId}", user.Id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置密码失败: Account={Account}", account);
            return Result.Failure("重置密码失败，请稍后重试");
        }
    }

    private async Task<AuthResponseDto> GenerateAuthResponseAsync(WhimLabAI.Domain.Entities.User.CustomerUser user, CustomerProfile? profile)
    {
        // 生成访问令牌
        var accessToken = _jwtTokenService.GenerateAccessToken(user.Id, user.Username, UserType.Customer);
        
        // 生成刷新令牌
        var refreshToken = _jwtTokenService.GenerateRefreshToken();
        var refreshTokenExpiration = _jwtTokenService.GetRefreshTokenExpiration();
        
        // 更新用户的刷新令牌
        var expiryDays = (int)(refreshTokenExpiration - DateTime.UtcNow).TotalDays;
        user.SetRefreshToken(refreshToken, expiryDays);
        await _unitOfWork.SaveChangesAsync();

        // 构建响应
        return new AuthResponseDto
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            ExpiresIn = 3600, // 1小时
            User = new UserInfoDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email?.Value,
                PhoneNumber = user.Phone?.Value,
                IsEmailVerified = user.IsEmailVerified,
                IsPhoneNumberVerified = user.IsPhoneVerified,
                UserType = UserType.Customer,
                Profile = new UserProfileDto
                {
                    Nickname = user.Nickname,
                    Avatar = user.Avatar,
                    Gender = user.Gender,
                    Language = "zh-CN",
                    Timezone = "Asia/Shanghai"
                }
            }
        };
    }



    public async Task<Result<object>> SendVerificationCodeAsync(WhimLabAI.Shared.Dtos.Customer.Auth.SendVerificationCodeDto sendCodeDto, CancellationToken cancellationToken = default)
    {
        try
        {
            // 将Purpose枚举转换为VerificationType
            var verificationType = sendCodeDto.Purpose switch
            {
                VerificationCodePurpose.Register => VerificationType.Register,
                VerificationCodePurpose.Login => VerificationType.Login,
                VerificationCodePurpose.ResetPassword => VerificationType.ResetPassword,
                VerificationCodePurpose.ChangeEmail => VerificationType.ChangeEmail,
                VerificationCodePurpose.ChangePhone => VerificationType.ChangePhone,
                VerificationCodePurpose.BindEmail => VerificationType.ChangeEmail,
                VerificationCodePurpose.BindPhone => VerificationType.ChangePhone,
                _ => VerificationType.Register
            };
            
            // 根据验证码类型发送邮件或短信
            Result<bool> result;
            if (sendCodeDto.Type == VerificationCodeType.Email)
            {
                result = await _verificationService.SendEmailCodeAsync(sendCodeDto.Account, verificationType, cancellationToken);
            }
            else
            {
                // 对于短信，如果提供了国家代码，则拼接
                var phoneNumber = string.IsNullOrEmpty(sendCodeDto.CountryCode) 
                    ? sendCodeDto.Account 
                    : $"{sendCodeDto.CountryCode}{sendCodeDto.Account}";
                result = await _verificationService.SendSmsCodeAsync(phoneNumber, verificationType, cancellationToken);
            }
            
            if (result.IsSuccess)
            {
                return Result<object>.Success("验证码已发送");
            }
            else
            {
                return Result<object>.Failure(result.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送验证码失败: {Account}", sendCodeDto.Account);
            return Result<object>.Failure("发送验证码失败，请稍后重试");
        }
    }

    public async Task<Result<object>> VerifyCodeAsync(VerifyCodeDto verifyDto, CancellationToken cancellationToken = default)
    {
        try
        {
            // 将VerifyCodeDto转换为VerificationType
            var verificationType = verifyDto.Type.ToLower() switch
            {
                "register" => VerificationType.Register,
                "login" => VerificationType.Login,
                "resetpassword" => VerificationType.ResetPassword,
                _ => VerificationType.Register
            };
            
            var result = await _verificationService.VerifyCodeAsync(verifyDto.Target, verifyDto.Code, verificationType, cancellationToken);
            
            if (result.IsSuccess)
            {
                return Result<object>.Success("验证成功");
            }
            else
            {
                return Result<object>.Failure(result.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证码验证失败");
            return Result<object>.Failure("验证失败");
        }
    }

    
    // Interface implementation methods
    public async Task<Result<AuthResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshDto, CancellationToken cancellationToken = default)
    {
        return await RefreshTokenAsync(refreshDto.RefreshToken, cancellationToken);
    }
    
    public async Task<Result<object>> LogoutAsync(string userId, string token, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!Guid.TryParse(userId, out var userGuid))
            {
                return Result<object>.Failure("无效的用户ID");
            }
            
            // 将令牌加入黑名单
            await _cacheService.SetAsync($"token:blacklist:{token}", true, TimeSpan.FromHours(24), cancellationToken);
            
            // 清除用户缓存
            await _cacheService.RemoveAsync($"user:session:{userId}", cancellationToken);
            
            // 调用现有的登出方法
            var result = await LogoutAsync(userGuid, null, cancellationToken);
            
            return Result<object>.Success("登出成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "登出失败: UserId={UserId}", userId);
            return Result<object>.Failure("登出失败");
        }
    }
    
    public async Task<Result<QRCodeResponseDto>> GenerateQRCodeAsync(GenerateQRCodeDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 生成唯一的会话ID
            var sessionId = Guid.NewGuid().ToString();
            
            // 构建设备信息
            var deviceInfo = new Dictionary<string, object>
            {
                ["sessionId"] = sessionId,
                ["deviceId"] = request.DeviceId ?? "unknown",
                ["deviceName"] = request.DeviceName ?? "Unknown Device",
                ["deviceType"] = request.DeviceType ?? "Unknown",
                ["ipAddress"] = request.IpAddress ?? "unknown",
                ["userAgent"] = request.UserAgent ?? "unknown",
                ["timestamp"] = DateTime.UtcNow.Ticks
            };
            
            // 生成Customer QR码令牌
            var qrToken = _jwtTokenService.GenerateCustomerQRCodeToken(deviceInfo, request.ExpirationMinutes);
            
            // 构建二维码数据
            var qrCodeData = JsonSerializer.Serialize(new
            {
                SessionId = sessionId,
                Token = qrToken,
                Type = "customer_qr_login",
                Domain = "whimlab.ai",
                Timestamp = DateTime.UtcNow.Ticks
            });
            
            // 构建二维码URL
            var qrCodeUrl = $"whimlab://qr-login?session={sessionId}&token={Uri.EscapeDataString(qrToken)}";
            
            // 生成QR码图像
            var qrCodeImage = _qrCodeGeneratorService.GenerateQRCodeBase64(qrCodeUrl);
            
            // 创建QRCodeSession实体
            var qrCodeSession = new QRCodeSession(
                sessionId,
                qrCodeData,
                request.ExpirationMinutes,
                request.IpAddress,
                request.UserAgent,
                request.DeviceId,
                request.DeviceName,
                request.DeviceType
            );
            
            // 保存到数据库
            await _qrCodeSessionRepository.AddAsync(qrCodeSession, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 同时缓存状态以支持实时查询
            var cacheKey = $"qr_status:{sessionId}";
            var statusData = new QRCodeStatusData
            {
                Status = QRCodeSessionStatus.Pending,
                ScannedAt = null,
                AuthenticatedAt = null,
                AuthResponse = null
            };
            await _cacheService.SetAsync(cacheKey, JsonSerializer.Serialize(statusData), 
                TimeSpan.FromMinutes(request.ExpirationMinutes), cancellationToken);
            
            _logger.LogInformation("生成Customer QR码会话: SessionId={SessionId}, Token={Token}", 
                sessionId, qrToken.Substring(0, 20) + "...");
            
            return Result<QRCodeResponseDto>.Success(new QRCodeResponseDto
            {
                SessionId = sessionId,
                QRCodeToken = qrToken,
                QRCodeData = qrCodeData,
                QRCodeUrl = qrCodeUrl,
                QRCodeImage = qrCodeImage,
                ExpiresAt = qrCodeSession.ExpiresAt,
                ExpirationMinutes = request.ExpirationMinutes
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成二维码失败");
            return Result<QRCodeResponseDto>.Failure("生成二维码失败，请稍后重试");
        }
    }
    
    public async Task<Result<QRCodeStatusDto>> GetQRCodeStatusAsync(string qrToken, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证QR码令牌
            var principal = _jwtTokenService.ValidateToken(qrToken);
            var tokenType = _jwtTokenService.GetTokenTypeFromToken(principal);
            
            if (tokenType != "CustomerQRCode")
            {
                return Result<QRCodeStatusDto>.Failure("无效的QR码令牌类型");
            }
            
            // 从令牌中获取sessionId
            var deviceInfoClaim = principal.FindFirst("DeviceInfo")?.Value;
            if (string.IsNullOrEmpty(deviceInfoClaim))
            {
                return Result<QRCodeStatusDto>.Failure("无效的QR码令牌数据");
            }
            
            var deviceInfo = JsonSerializer.Deserialize<Dictionary<string, object>>(deviceInfoClaim);
            var sessionId = deviceInfo?.GetValueOrDefault("sessionId")?.ToString();
            
            if (string.IsNullOrEmpty(sessionId))
            {
                return Result<QRCodeStatusDto>.Failure("无法获取会话ID");
            }
            
            // 优先从数据库获取状态
            var qrCodeSession = await _qrCodeSessionRepository.GetBySessionIdAsync(sessionId, cancellationToken);
            
            QRCodeSessionStatus status;
            DateTime? scannedAt = null;
            DateTime? authenticatedAt = null;
            AuthResponseDto? authResponse = null;
            DateTime expiresAt;
            
            if (qrCodeSession != null)
            {
                // 使用数据库中的状态
                status = qrCodeSession.Status;
                scannedAt = qrCodeSession.ScannedAt;
                authenticatedAt = qrCodeSession.AuthenticatedAt;
                expiresAt = qrCodeSession.ExpiresAt;
                
                // 检查是否已过期
                if (qrCodeSession.IsExpired && status != QRCodeSessionStatus.Authenticated)
                {
                    status = QRCodeSessionStatus.Expired;
                    qrCodeSession.MarkAsExpired();
                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                }
                
                // 如果已认证，从缓存获取认证响应
                if (status == QRCodeSessionStatus.Authenticated)
                {
                    var cacheKey = $"qr_status:{sessionId}";
                    var cachedStatus = await _cacheService.GetStringAsync(cacheKey, cancellationToken);
                    if (!string.IsNullOrEmpty(cachedStatus))
                    {
                        var statusData = JsonSerializer.Deserialize<QRCodeStatusData>(cachedStatus);
                        authResponse = statusData?.AuthResponse;
                    }
                }
            }
            else
            {
                // 数据库中没有记录，返回错误
                return Result<QRCodeStatusDto>.Failure("二维码会话不存在");
            }
            
            var statusDto = new QRCodeStatusDto
            {
                SessionId = sessionId,
                QRCodeToken = qrToken,
                Status = status,
                ExpiresAt = expiresAt,
                ScannedAt = scannedAt,
                AuthenticatedAt = authenticatedAt,
                IsExpired = DateTime.UtcNow > expiresAt,
                AuthResponse = authResponse
            };
            
            return Result<QRCodeStatusDto>.Success(statusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取二维码状态失败: {QRToken}", qrToken?.Substring(0, 20) + "...");
            return Result<QRCodeStatusDto>.Failure("获取二维码状态失败");
        }
    }
    
    public async Task<Result<QRCodeScanResponseDto>> ScanQRCodeAsync(ScanQRCodeDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证QR码令牌
            var principal = _jwtTokenService.ValidateToken(request.QRCodeToken);
            var tokenType = _jwtTokenService.GetTokenTypeFromToken(principal);
            
            if (tokenType != "CustomerQRCode")
            {
                return Result<QRCodeScanResponseDto>.Failure("无效的QR码令牌类型");
            }
            
            // 获取设备信息和sessionId
            var deviceInfoClaim = principal.FindFirst("DeviceInfo")?.Value;
            if (string.IsNullOrEmpty(deviceInfoClaim))
            {
                return Result<QRCodeScanResponseDto>.Failure("无效的QR码令牌数据");
            }
            
            var deviceInfo = JsonSerializer.Deserialize<Dictionary<string, object>>(deviceInfoClaim);
            var sessionId = deviceInfo?.GetValueOrDefault("sessionId")?.ToString();
            
            if (string.IsNullOrEmpty(sessionId))
            {
                return Result<QRCodeScanResponseDto>.Failure("无法获取会话ID");
            }
            
            // 从数据库获取QR码会话
            var qrCodeSession = await _qrCodeSessionRepository.GetBySessionIdAsync(sessionId, cancellationToken);
            if (qrCodeSession == null)
            {
                return Result<QRCodeScanResponseDto>.Failure("二维码会话不存在");
            }
            
            // 检查会话状态
            if (qrCodeSession.Status != QRCodeSessionStatus.Pending)
            {
                return Result<QRCodeScanResponseDto>.Failure("二维码已被扫描或已过期");
            }
            
            if (qrCodeSession.IsExpired)
            {
                qrCodeSession.MarkAsExpired();
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                return Result<QRCodeScanResponseDto>.Failure("二维码已过期");
            }
            
            // 更新数据库状态为已扫描
            qrCodeSession.MarkAsScanned(
                request.ScannerDeviceId ?? "unknown",
                request.ScannerIp ?? "unknown"
            );
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 同时更新缓存状态
            var cacheKey = $"qr_status:{sessionId}";
            var statusData = new QRCodeStatusData
            {
                Status = QRCodeSessionStatus.Scanned,
                ScannedAt = qrCodeSession.ScannedAt
            };
            await _cacheService.SetAsync(cacheKey, JsonSerializer.Serialize(statusData), 
                TimeSpan.FromMinutes(10), cancellationToken);
            
            // 发送SignalR通知给PC端
            await _realtimeNotificationService.NotifyQRCodeScannedAsync(
                sessionId, 
                request.ScannerDeviceId ?? "unknown", 
                request.ScannerIp ?? "unknown");
            
            var response = new QRCodeScanResponseDto
            {
                SessionId = sessionId,
                QRCodeToken = request.QRCodeToken,
                Message = "二维码扫描成功，请确认登录",
                RequiresConfirmation = true,
                TargetDevice = new DeviceInfoDto
                {
                    DeviceId = qrCodeSession.DeviceId,
                    DeviceName = qrCodeSession.DeviceName,
                    DeviceType = qrCodeSession.DeviceType,
                    IpAddress = qrCodeSession.ClientIp
                }
            };
            
            _logger.LogInformation("二维码扫描成功: SessionId={SessionId}, QRToken={QRToken}", 
                sessionId, request.QRCodeToken.Substring(0, 20) + "...");
            return Result<QRCodeScanResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "扫描二维码失败: {QRToken}", request.QRCodeToken?.Substring(0, 20) + "...");
            return Result<QRCodeScanResponseDto>.Failure("扫描二维码失败");
        }
    }
    
    public async Task<Result<AuthResponseDto>> ConfirmQRCodeLoginAsync(ConfirmQRCodeLoginDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证QR码令牌
            var principal = _jwtTokenService.ValidateToken(request.QRCodeToken);
            var tokenType = _jwtTokenService.GetTokenTypeFromToken(principal);
            
            if (tokenType != "CustomerQRCode")
            {
                return Result<AuthResponseDto>.Failure("无效的QR码令牌类型");
            }
            
            // 获取sessionId
            var deviceInfoClaim = principal.FindFirst("DeviceInfo")?.Value;
            if (string.IsNullOrEmpty(deviceInfoClaim))
            {
                return Result<AuthResponseDto>.Failure("无效的QR码令牌数据");
            }
            
            var deviceInfo = JsonSerializer.Deserialize<Dictionary<string, object>>(deviceInfoClaim);
            var sessionId = deviceInfo?.GetValueOrDefault("sessionId")?.ToString();
            
            if (string.IsNullOrEmpty(sessionId))
            {
                return Result<AuthResponseDto>.Failure("无法获取会话ID");
            }
            
            // 从数据库获取QR码会话
            var qrCodeSession = await _qrCodeSessionRepository.GetBySessionIdAsync(sessionId, cancellationToken);
            if (qrCodeSession == null)
            {
                return Result<AuthResponseDto>.Failure("二维码会话不存在");
            }
            
            // 检查状态
            if (qrCodeSession.Status != QRCodeSessionStatus.Scanned)
            {
                return Result<AuthResponseDto>.Failure("二维码状态异常");
            }
            
            if (qrCodeSession.IsExpired)
            {
                qrCodeSession.MarkAsExpired();
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                return Result<AuthResponseDto>.Failure("二维码已过期");
            }
            
            if (!request.Confirm)
            {
                // 用户拒绝登录
                qrCodeSession.Reject();
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                
                // 更新缓存
                var cacheKey = $"qr_status:{sessionId}";
                var statusData = new QRCodeStatusData
                {
                    Status = QRCodeSessionStatus.Rejected,
                    ScannedAt = qrCodeSession.ScannedAt
                };
                await _cacheService.SetAsync(cacheKey, JsonSerializer.Serialize(statusData), 
                    TimeSpan.FromMinutes(10), cancellationToken);
                
                // 发送SignalR通知给PC端
                await _realtimeNotificationService.NotifyQRCodeRejectedAsync(sessionId);
                
                return Result<AuthResponseDto>.Failure("用户拒绝登录");
            }
            
            // 获取用户信息
            var user = await _userRepository.GetByIdAsync(request.UserId, cancellationToken);
            if (user == null)
            {
                return Result<AuthResponseDto>.Failure("用户不存在");
            }
            
            // 检查用户状态
            if (user.IsLocked())
            {
                return Result<AuthResponseDto>.Failure("账号已被锁定");
            }
            
            // 记录登录历史
            var loginHistory = new LoginHistory(
                user.Id,
                qrCodeSession.ClientIp ?? "Unknown",
                LoginMethod.QRCode,
                true,
                qrCodeSession.DeviceType,
                null,
                qrCodeSession.UserAgent);
            
            await _unitOfWork.Repository<LoginHistory>().AddAsync(loginHistory, cancellationToken);
            
            // 更新用户登录信息
            user.RecordLogin(qrCodeSession.ClientIp ?? "Unknown");
            
            // 更新设备授权
            if (!string.IsNullOrEmpty(qrCodeSession.DeviceId))
            {
                user.AuthorizeDevice(
                    qrCodeSession.DeviceId,
                    qrCodeSession.DeviceName ?? "Unknown Device",
                    qrCodeSession.DeviceType ?? "Unknown");
            }
            
            // 更新QR码会话状态
            qrCodeSession.MarkAsAuthenticated(user.Id);
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 生成认证响应
            var authResponse = await GenerateAuthResponseAsync(user, user.Profile);
            
            // 更新缓存状态
            var cacheKey2 = $"qr_status:{sessionId}";
            var statusData2 = new QRCodeStatusData
            {
                Status = QRCodeSessionStatus.Authenticated,
                ScannedAt = qrCodeSession.ScannedAt,
                AuthenticatedAt = qrCodeSession.AuthenticatedAt,
                AuthResponse = authResponse
            };
            
            await _cacheService.SetAsync(cacheKey2, JsonSerializer.Serialize(statusData2), 
                TimeSpan.FromMinutes(10), cancellationToken);
            
            // 发送SignalR通知给PC端，传递认证信息
            await _realtimeNotificationService.NotifyQRCodeAuthenticatedAsync(sessionId, authResponse);
            
            _logger.LogInformation("二维码登录成功: SessionId={SessionId}, UserId={UserId}", sessionId, user.Id);
            
            return Result<AuthResponseDto>.Success(authResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认二维码登录失败: {QRToken}", request.QRCodeToken?.Substring(0, 20) + "...");
            return Result<AuthResponseDto>.Failure("确认登录失败");
        }
    }
    
    public async Task<Result> CancelQRCodeAsync(string qrToken, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证QR码令牌
            var principal = _jwtTokenService.ValidateToken(qrToken);
            var tokenType = _jwtTokenService.GetTokenTypeFromToken(principal);
            
            if (tokenType != "CustomerQRCode")
            {
                return Result.Failure("无效的QR码令牌类型");
            }
            
            // 获取sessionId
            var deviceInfoClaim = principal.FindFirst("DeviceInfo")?.Value;
            if (string.IsNullOrEmpty(deviceInfoClaim))
            {
                return Result.Failure("无效的QR码令牌数据");
            }
            
            var deviceInfo = JsonSerializer.Deserialize<Dictionary<string, object>>(deviceInfoClaim);
            var sessionId = deviceInfo?.GetValueOrDefault("sessionId")?.ToString();
            
            if (string.IsNullOrEmpty(sessionId))
            {
                return Result.Failure("无法获取会话ID");
            }
            
            // 从数据库获取QR码会话
            var qrCodeSession = await _qrCodeSessionRepository.GetBySessionIdAsync(sessionId, cancellationToken);
            if (qrCodeSession == null)
            {
                return Result.Failure("二维码会话不存在");
            }
            
            // 检查状态
            if (qrCodeSession.Status == QRCodeSessionStatus.Authenticated)
            {
                return Result.Failure("已认证的二维码不能被取消");
            }
            
            // 更新数据库状态为已取消
            qrCodeSession.MarkAsCancelled();
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 更新缓存状态
            var cacheKey = $"qr_status:{sessionId}";
            var statusData = new QRCodeStatusData
            {
                Status = QRCodeSessionStatus.Cancelled,
                ScannedAt = qrCodeSession.ScannedAt
            };
            
            await _cacheService.SetAsync(cacheKey, JsonSerializer.Serialize(statusData), 
                TimeSpan.FromMinutes(10), cancellationToken);
            
            // 发送SignalR通知给PC端
            await _realtimeNotificationService.NotifyQRCodeCancelledAsync(sessionId);
            
            _logger.LogInformation("二维码会话已取消: SessionId={SessionId}", sessionId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消二维码会话失败: {QRToken}", qrToken?.Substring(0, 20) + "...");
            return Result.Failure("取消二维码会话失败");
        }
    }
    
    private async Task<bool> ShouldVerifyCaptchaAsync(string account)
    {
        var policy = _verificationOptions.CustomerCaptchaPolicy;
        
        // 如果验证码功能被禁用，直接返回false
        if (!policy.Enabled)
        {
            _logger.LogDebug("Customer验证码功能已禁用");
            return false;
        }
        
        // 在开发环境中，如果配置了禁用验证码，直接返回false
        var environment = _configuration["ASPNETCORE_ENVIRONMENT"] ?? "Production";
        if (environment.Equals("Development", StringComparison.OrdinalIgnoreCase) && policy.DisableInDevelopment)
        {
            _logger.LogDebug("开发环境中Customer验证码已禁用");
            return false;
        }
        
        // 检查QuickDev配置
        var disableCaptcha = _configuration.GetValue<bool>("Development:DisableCaptcha");
        if (disableCaptcha)
        {
            _logger.LogDebug("QuickDev模式中验证码已禁用");
            return false;
        }
        
        // 如果配置为总是需要验证码，直接返回true
        if (policy.AlwaysRequired)
        {
            _logger.LogDebug("Customer验证码配置为总是需要");
            return true;
        }
        
        // 检查登录失败次数
        var failedAttemptKey = $"{SystemConstants.CacheKeys.LoginFailedCount}{account}";
        var failedCount = await _cacheService.GetAsync<int?>(failedAttemptKey) ?? 0;
        
        // 如果失败次数超过配置的阈值，需要验证码
        if (failedCount >= policy.FailedAttemptsThreshold)
        {
            _logger.LogInformation("账号 {Account} 登录失败次数({Count})超过阈值({Threshold})，需要验证码", 
                account, failedCount, policy.FailedAttemptsThreshold);
            return true;
        }
        
        // 检查最近一小时内同一账号的登录尝试频率
        var recentAttemptsKey = $"{SystemConstants.CacheKeys.RecentLoginAttempts}{account}";
        var recentAttempts = await _cacheService.GetAsync<int?>(recentAttemptsKey) ?? 0;
        if (recentAttempts >= policy.HourlyAttemptsThreshold)
        {
            _logger.LogWarning("账号 {Account} 在一小时内登录尝试次数({Count})超过阈值({Threshold})，需要验证码", 
                account, recentAttempts, policy.HourlyAttemptsThreshold);
            return true;
        }
        
        // 检查是否是新设备或异常登录地点
        var userDevicesKey = $"{SystemConstants.CacheKeys.UserDevices}{account}";
        var knownDevices = await _cacheService.GetAsync<List<string>>(userDevicesKey);
        if (knownDevices != null && knownDevices.Count > 5)
        {
            // 如果用户有超过5个不同设备登录，可能存在风险
            _logger.LogWarning("账号 {Account} 关联设备数量异常: {Count}", account, knownDevices.Count);
            return true;
        }
        
        // 检查是否在短时间内有多个不同IP的登录尝试
        var ipAttemptsKey = $"{SystemConstants.CacheKeys.LoginIPAttempts}{account}";
        var ipList = await _cacheService.GetAsync<List<string>>(ipAttemptsKey);
        if (ipList != null && ipList.Count > 3)
        {
            // 如果在短时间内有超过3个不同IP尝试登录，可能是暴力破解
            _logger.LogWarning("账号 {Account} 在短时间内有多个IP尝试登录: {Count}", account, ipList.Count);
            return true;
        }
        
        return false;
    }
    
    public async Task<Result<AuthResponseDto>> RegisterByUsernameAsync(CustomerRegisterByUsernameDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证用户名是否已存在
            if (await _userRepository.IsUsernameExistsAsync(request.Username, cancellationToken))
            {
                return Result<AuthResponseDto>.Failure("用户名已被使用");
            }

            // 创建用户
            var user = new WhimLabAI.Domain.Entities.User.CustomerUser(
                request.Username,
                request.Password,
                $"{request.Username}@whimlabai.local", // 生成一个临时邮箱
                null, // phone
                null  // registerIp
            );

            // 设置用户资料
            var profile = new CustomerProfile(user.Id);
            await _unitOfWork.Repository<CustomerProfile>().AddAsync(profile, cancellationToken);
            
            // 激活免费订阅
            await ActivateFreeSubscriptionAsync(user, cancellationToken);

            await _userRepository.AddAsync(user, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户通过用户名注册成功: {Username}", user.Username);

            // 生成令牌
            var response = await GenerateAuthResponseAsync(user, profile);
            return Result<AuthResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户通过用户名注册失败: {Username}", request.Username);
            return Result<AuthResponseDto>.Failure("注册失败，请稍后重试");
        }
    }

    public async Task<Result<AuthResponseDto>> RegisterByEmailAsync(CustomerRegisterByEmailDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证邮箱验证码
            var verifyResult = await _verificationService.VerifyCodeAsync(
                request.Email, 
                request.VerificationCode, 
                VerificationType.Register,
                cancellationToken);
                
            if (!verifyResult.IsSuccess || !verifyResult.Value)
            {
                return Result<AuthResponseDto>.Failure("验证码错误或已过期");
            }

            // 验证邮箱是否已存在
            var existingUser = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
            if (existingUser != null)
            {
                return Result<AuthResponseDto>.Failure("该邮箱已被注册");
            }

            // 创建用户
            var username = request.Email.Split('@')[0] + "_" + Guid.NewGuid().ToString("N").Substring(0, 6);
            var user = new WhimLabAI.Domain.Entities.User.CustomerUser(
                username,
                request.Password,
                request.Email,
                null, // phone
                null  // registerIp
            );

            // 设置用户资料
            var profile = new CustomerProfile(user.Id);
            await _unitOfWork.Repository<CustomerProfile>().AddAsync(profile, cancellationToken);
            
            // 激活免费订阅
            await ActivateFreeSubscriptionAsync(user, cancellationToken);

            await _userRepository.AddAsync(user, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户通过邮箱注册成功: {Email}", user.Email);

            // 生成令牌
            var response = await GenerateAuthResponseAsync(user, profile);
            return Result<AuthResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户通过邮箱注册失败: {Email}", request.Email);
            return Result<AuthResponseDto>.Failure("注册失败，请稍后重试");
        }
    }

    public async Task<Result<AuthResponseDto>> RegisterByPhoneAsync(CustomerRegisterByPhoneDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证短信验证码
            var phone = $"{request.CountryCode}{request.PhoneNumber}";
            var verifyResult = await _verificationService.VerifyCodeAsync(
                phone, 
                request.VerificationCode, 
                VerificationType.Register,
                cancellationToken);
                
            if (!verifyResult.IsSuccess || !verifyResult.Value)
            {
                return Result<AuthResponseDto>.Failure("验证码错误或已过期");
            }

            // 验证手机号是否已存在
            var existingUser = await _userRepository.GetByPhoneNumberAsync(phone, cancellationToken);
            if (existingUser != null)
            {
                return Result<AuthResponseDto>.Failure("该手机号已被注册");
            }

            // 创建用户
            var username = "user_" + request.PhoneNumber.Substring(Math.Max(0, request.PhoneNumber.Length - 4)) + "_" + Guid.NewGuid().ToString("N").Substring(0, 6);
            var tempEmail = $"{username}@whimlabai.local";
            var user = new WhimLabAI.Domain.Entities.User.CustomerUser(
                username,
                request.Password,
                tempEmail,
                null, // phone will be set later
                null  // registerIp
            );
            
            // 绑定手机号
            user.UpdatePhone(phone);

            // 设置用户资料
            var profile = new CustomerProfile(user.Id);
            await _unitOfWork.Repository<CustomerProfile>().AddAsync(profile, cancellationToken);
            
            // 激活免费订阅
            await ActivateFreeSubscriptionAsync(user, cancellationToken);

            await _userRepository.AddAsync(user, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户通过手机号注册成功: {Phone}", phone);

            // 生成令牌
            var response = await GenerateAuthResponseAsync(user, profile);
            return Result<AuthResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户通过手机号注册失败: {Phone}", request.PhoneNumber);
            return Result<AuthResponseDto>.Failure("注册失败，请稍后重试");
        }
    }

    public async Task<Result<bool>> CheckAccountExistsAsync(CheckAccountExistsDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            bool exists = false;
            
            switch (request.Type)
            {
                case AccountType.Username:
                    exists = await _userRepository.IsUsernameExistsAsync(request.Account, cancellationToken);
                    break;
                    
                case AccountType.Email:
                    var userByEmail = await _userRepository.GetByEmailAsync(request.Account, cancellationToken);
                    exists = userByEmail != null;
                    break;
                    
                case AccountType.Phone:
                    var phone = string.IsNullOrEmpty(request.CountryCode) ? request.Account : $"{request.CountryCode}{request.Account}";
                    var userByPhone = await _userRepository.GetByPhoneNumberAsync(phone, cancellationToken);
                    exists = userByPhone != null;
                    break;
            }
            
            return Result<bool>.Success(exists);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查账号是否存在失败: {Account}", request.Account);
            return Result<bool>.Failure("检查失败，请稍后重试");
        }
    }
    
    private async Task ActivateFreeSubscriptionAsync(WhimLabAI.Domain.Entities.User.CustomerUser user, CancellationToken cancellationToken)
    {
        try
        {
            // 1. 获取免费订阅计划
            var freePlan = await _unitOfWork.Repository<Domain.Entities.Subscription.SubscriptionPlan>()
                .GetQueryable()
                .FirstOrDefaultAsync(p => p.Tier == SubscriptionTier.Free, cancellationToken);
                
            if (freePlan == null)
            {
                _logger.LogWarning("未找到免费订阅计划，跳过订阅创建");
                return;
            }
            
            // 2. 创建订阅
            var subscription = new Domain.Entities.Subscription.Subscription(
                user.Id,
                freePlan.Id,
                DateTime.UtcNow,
                DateTime.UtcNow.AddYears(100), // 免费订阅设置100年后过期
                null, // 无支付方式
                null, // 无订单ID
                true  // 自动续费
            );
            
            // 3. 设置计划信息并激活
            subscription.SetPlanInfo(freePlan.TokenQuota, freePlan.Price);
            subscription.Activate();
            
            // 4. 保存订阅
            await _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>().AddAsync(subscription, cancellationToken);
            
            _logger.LogInformation("为用户 {UserId} 成功激活免费订阅，配额: {TokenQuota} tokens", user.Id, freePlan.TokenQuota);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "激活免费订阅失败: UserId={UserId}", user.Id);
            // 不要因为订阅创建失败而阻止用户注册
        }
    }
    
    #region Two-Factor Authentication Methods
    
    public async Task<Result<TwoFactorSetupDto>> EnableTwoFactorAsync(Guid userId, EnableTwoFactorDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取用户
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<TwoFactorSetupDto>.Failure("USER_NOT_FOUND", "用户不存在");
            }
            
            // 验证密码
            if (!user.PasswordHash.Verify(request.Password))
            {
                return Result<TwoFactorSetupDto>.Failure("INVALID_PASSWORD", "密码错误");
            }
            
            // 检查是否已启用
            if (user.TwoFactorEnabled)
            {
                return Result<TwoFactorSetupDto>.Failure("ALREADY_ENABLED", "双因素认证已启用");
            }
            
            // 生成新的密钥
            var totpService = _serviceProvider.GetRequiredService<ITotpService>();
            var secret = totpService.GenerateSecret();
            var qrCodeUri = totpService.GenerateTotpUri("WhimLabAI", user.Username, secret);
            var manualEntryKey = totpService.FormatSecretForManualEntry(secret);
            
            // 临时存储密钥，等待用户确认
            var cacheKey = $"{SystemConstants.CacheKeys.TwoFactorSetup}{userId}";
            await _cacheService.SetAsync(cacheKey, secret, TimeSpan.FromMinutes(10), cancellationToken);
            
            var response = new TwoFactorSetupDto
            {
                Secret = secret,
                QrCodeUri = qrCodeUri,
                ManualEntryKey = manualEntryKey
            };
            
            _logger.LogInformation("客户用户 {UserId} 请求启用双因素认证", userId);
            
            return Result<TwoFactorSetupDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启用双因素认证失败: UserId={UserId}", userId);
            return Result<TwoFactorSetupDto>.Failure("ENABLE_2FA_ERROR", "启用双因素认证失败");
        }
    }
    
    public async Task<Result> ConfirmTwoFactorAsync(Guid userId, ConfirmTwoFactorDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取用户
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("USER_NOT_FOUND", "用户不存在");
            }
            
            // 检查是否已启用
            if (user.TwoFactorEnabled)
            {
                return Result.Failure("ALREADY_ENABLED", "双因素认证已启用");
            }
            
            // 从缓存获取临时密钥
            var cacheKey = $"{SystemConstants.CacheKeys.TwoFactorSetup}{userId}";
            var secret = await _cacheService.GetStringAsync(cacheKey, cancellationToken);
            
            if (string.IsNullOrEmpty(secret))
            {
                return Result.Failure("SETUP_EXPIRED", "设置会话已过期，请重新开始");
            }
            
            // 验证代码
            var totpService = _serviceProvider.GetRequiredService<ITotpService>();
            if (!totpService.ValidateCode(secret, request.Code))
            {
                return Result.Failure("INVALID_CODE", "验证码无效");
            }
            
            // 启用双因素认证
            user.EnableTwoFactor(secret);
            
            // 生成恢复码
            var recoveryCodes = await GenerateRecoveryCodesInternalAsync(user.Id, cancellationToken);
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 清除缓存
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);
            
            _logger.LogInformation("客户用户 {UserId} 成功启用双因素认证", userId);
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认双因素认证失败: UserId={UserId}", userId);
            return Result.Failure("CONFIRM_2FA_ERROR", "确认双因素认证失败");
        }
    }
    
    public async Task<Result> DisableTwoFactorAsync(Guid userId, DisableTwoFactorDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取用户
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("USER_NOT_FOUND", "用户不存在");
            }
            
            // 验证密码
            if (!user.PasswordHash.Verify(request.Password))
            {
                return Result.Failure("INVALID_PASSWORD", "密码错误");
            }
            
            // 检查是否已启用
            if (!user.TwoFactorEnabled)
            {
                return Result.Failure("NOT_ENABLED", "双因素认证未启用");
            }
            
            // 验证当前的双因素代码
            var totpService = _serviceProvider.GetRequiredService<ITotpService>();
            var secret = user.GetTwoFactorSecret();
            if (string.IsNullOrEmpty(secret) || !totpService.ValidateCode(secret, request.Code))
            {
                return Result.Failure("INVALID_CODE", "验证码无效");
            }
            
            // 禁用双因素认证
            user.DisableTwoFactor();
            
            // 删除所有恢复码
            await _recoveryCodeRepository.DeleteUserCodesAsync(userId, "Customer", cancellationToken);
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("客户用户 {UserId} 已禁用双因素认证", userId);
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "禁用双因素认证失败: UserId={UserId}", userId);
            return Result.Failure("DISABLE_2FA_ERROR", "禁用双因素认证失败");
        }
    }
    
    #endregion
    
    #region Recovery Code Methods
    
    public async Task<Result<GenerateRecoveryCodesResponseDto>> GenerateRecoveryCodesAsync(
        Guid userId, 
        GenerateRecoveryCodesDto request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取用户
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<GenerateRecoveryCodesResponseDto>.Failure("USER_NOT_FOUND", "用户不存在");
            }
            
            // 验证密码
            if (!user.PasswordHash.Verify(request.Password))
            {
                return Result<GenerateRecoveryCodesResponseDto>.Failure("INVALID_PASSWORD", "密码错误");
            }
            
            // 检查是否启用了2FA
            if (!user.TwoFactorEnabled)
            {
                return Result<GenerateRecoveryCodesResponseDto>.Failure("2FA_NOT_ENABLED", "请先启用双因素认证");
            }
            
            // 删除旧的恢复码
            await _recoveryCodeRepository.DeleteUserCodesAsync(userId, "Customer", cancellationToken);
            
            // 生成新的恢复码
            var recoveryCodes = await GenerateRecoveryCodesInternalAsync(userId, cancellationToken);
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            var response = new GenerateRecoveryCodesResponseDto
            {
                RecoveryCodes = recoveryCodes,
                GeneratedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddYears(1)
            };
            
            _logger.LogInformation("为客户用户 {UserId} 生成了新的恢复码", userId);
            
            return Result<GenerateRecoveryCodesResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成恢复码失败: UserId={UserId}", userId);
            return Result<GenerateRecoveryCodesResponseDto>.Failure("GENERATE_RECOVERY_CODES_ERROR", "生成恢复码失败");
        }
    }
    
    public async Task<Result<AuthResponseDto>> LoginWithRecoveryCodeAsync(
        LoginWithRecoveryCodeDto request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 查找用户
            WhimLabAI.Domain.Entities.User.CustomerUser? user = null;
            
            // 尝试多种方式查找用户
            user = await _userRepository.GetByUsernameAsync(request.Username, cancellationToken) ??
                   await _userRepository.GetByEmailAsync(request.Username, cancellationToken) ??
                   await _userRepository.GetByPhoneNumberAsync(request.Username, cancellationToken);
                   
            if (user == null)
            {
                return Result<AuthResponseDto>.Failure("INVALID_CREDENTIALS", "用户名或恢复码无效");
            }
            
            // 检查用户状态
            if (!user.IsActive)
            {
                return Result<AuthResponseDto>.Failure("ACCOUNT_INACTIVE", "账号未激活");
            }
            
            if (user.IsBanned)
            {
                return Result<AuthResponseDto>.Failure("ACCOUNT_BANNED", "账号已被封禁");
            }
            
            // 检查账号是否被锁定
            if (user.IsLocked())
            {
                var remainingMinutes = user.GetLockRemainingMinutes();
                return Result<AuthResponseDto>.Failure("ACCOUNT_LOCKED", $"账号已被锁定，请{remainingMinutes}分钟后再试");
            }
            
            // 检查是否启用了2FA
            if (!user.TwoFactorEnabled)
            {
                user.RecordFailedLogin();
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                return Result<AuthResponseDto>.Failure("INVALID_CREDENTIALS", "用户名或恢复码无效");
            }
            
            // 验证恢复码
            var recoveryCode = await _recoveryCodeRepository.GetByCodeAsync(request.RecoveryCode, cancellationToken);
            if (recoveryCode == null || 
                recoveryCode.UserId != user.Id || 
                recoveryCode.UserType != "Customer" ||
                !recoveryCode.IsValid())
            {
                user.RecordFailedLogin();
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                return Result<AuthResponseDto>.Failure("INVALID_RECOVERY_CODE", "恢复码无效");
            }
            
            // 使用恢复码
            recoveryCode.Use(request.IpAddress);
            
            // 更新登录信息
            user.RecordLogin(request.IpAddress ?? "Unknown");
            
            // 处理设备授权
            if (!string.IsNullOrEmpty(request.DeviceId) && !string.IsNullOrEmpty(request.DeviceName))
            {
                user.AuthorizeDevice(request.DeviceId, request.DeviceName, request.UserAgent);
            }
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 生成认证响应
            var response = await GenerateAuthResponseAsync(user, user.Profile);
            
            _logger.LogInformation("客户用户 {UserId} 使用恢复码成功登录", user.Id);
            
            return Result<AuthResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用恢复码登录失败: Username={Username}", request.Username);
            return Result<AuthResponseDto>.Failure("LOGIN_ERROR", "登录失败，请稍后重试");
        }
    }
    
    public async Task<Result<List<RecoveryCodeDto>>> GetRecoveryCodesAsync(
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var codes = await _recoveryCodeRepository.GetAllUserCodesAsync(userId, "Customer", cancellationToken);
            
            var dtos = codes.Select(c => new RecoveryCodeDto
            {
                Code = "****-****", // 不显示实际的恢复码
                IsUsed = c.IsUsed,
                UsedAt = c.UsedAt,
                ExpiresAt = c.ExpiresAt
            }).ToList();
            
            return Result<List<RecoveryCodeDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取恢复码列表失败: UserId={UserId}", userId);
            return Result<List<RecoveryCodeDto>>.Failure("GET_RECOVERY_CODES_ERROR", "获取恢复码列表失败");
        }
    }
    
    private async Task<List<string>> GenerateRecoveryCodesInternalAsync(Guid userId, CancellationToken cancellationToken)
    {
        const int codeCount = 8;
        const int codeLength = 8;
        var codes = new List<string>();
        
        for (int i = 0; i < codeCount; i++)
        {
            // 生成8位字母数字码
            var code = GenerateSecureRandomCode(codeLength);
            var formattedCode = $"{code.Substring(0, 4)}-{code.Substring(4)}";
            codes.Add(formattedCode);
            
            // 创建恢复码实体
            var recoveryCode = new RecoveryCode(userId, formattedCode, "Customer");
            await _recoveryCodeRepository.AddAsync(recoveryCode, cancellationToken);
        }
        
        return codes;
    }
    
    private string GenerateSecureRandomCode(int length)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var code = new char[length];
        
        using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
        {
            var bytes = new byte[length];
            rng.GetBytes(bytes);
            
            for (int i = 0; i < length; i++)
            {
                code[i] = chars[bytes[i] % chars.Length];
            }
        }
        
        return new string(code);
    }
    
    #endregion
}