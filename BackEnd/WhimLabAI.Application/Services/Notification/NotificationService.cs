using Microsoft.Extensions.Logging;
using System.Text.Json;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Entities.Notification;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Notification;

/// <summary>
/// 通知服务实现
/// </summary>
public class NotificationService : INotificationService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IRealtimeNotificationService? _realtimeService;
    private readonly ILogger<NotificationService> _logger;

    public NotificationService(
        IUnitOfWork unitOfWork,
        IRealtimeNotificationService? realtimeService,
        ILogger<NotificationService> logger)
    {
        _unitOfWork = unitOfWork;
        _realtimeService = realtimeService;
        _logger = logger;
    }

    public async Task<Result> SendNotificationAsync(SendNotificationDto notification, CancellationToken cancellationToken = default)
    {
        try
        {
            // 创建通知实体
            var metadataJson = notification.Metadata != null 
                ? JsonSerializer.Serialize(notification.Metadata) 
                : null;
                
            var entity = new Domain.Entities.Notification.Notification(
                notification.UserId,
                notification.Title,
                notification.Content,
                notification.Type,
                notification.Level,
                metadataJson,
                notification.ExpiresAt);
            
            // 保存到数据库
            await _unitOfWork.Repository<Domain.Entities.Notification.Notification>()
                .AddAsync(entity, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 实时推送
            if (notification.RealTimePush && _realtimeService != null)
            {
                await _realtimeService.PushNotificationToUserAsync(
                    notification.UserId,
                    new NotificationDto
                    {
                        Id = entity.Id,
                        UserId = entity.UserId,
                        Title = entity.Title,
                        Content = entity.Content,
                        Type = entity.Type,
                        Level = entity.Level,
                        IsRead = entity.IsRead,
                        Metadata = notification.Metadata,
                        CreatedAt = entity.CreatedAt,
                        ExpiresAt = entity.ExpiresAt
                    });
            }
            
            _logger.LogInformation("Notification sent successfully to user");
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification");
            return Result.Failure("SEND_ERROR", "发送通知失败");
        }
    }

    public async Task<Result> SendBatchNotificationsAsync(SendBatchNotificationDto batchNotification, CancellationToken cancellationToken = default)
    {
        try
        {
            var metadataJson = batchNotification.Metadata != null 
                ? JsonSerializer.Serialize(batchNotification.Metadata) 
                : null;
            
            var notifications = new List<Domain.Entities.Notification.Notification>();
            
            foreach (var userId in batchNotification.UserIds)
            {
                var entity = new Domain.Entities.Notification.Notification(
                    userId,
                    batchNotification.Title,
                    batchNotification.Content,
                    batchNotification.Type,
                    batchNotification.Level,
                    metadataJson,
                    batchNotification.ExpiresAt);
                    
                notifications.Add(entity);
            }
            
            // 批量保存
            await _unitOfWork.Repository<Domain.Entities.Notification.Notification>()
                .AddRangeAsync(notifications, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 批量实时推送
            if (batchNotification.RealTimePush && _realtimeService != null)
            {
                var notificationDto = new NotificationDto
                {
                    Title = batchNotification.Title,
                    Content = batchNotification.Content,
                    Type = batchNotification.Type,
                    Level = batchNotification.Level,
                    Metadata = batchNotification.Metadata,
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = batchNotification.ExpiresAt
                };
                
                await _realtimeService.PushNotificationToUsersAsync(
                    batchNotification.UserIds,
                    notificationDto);
            }
            
            _logger.LogInformation("Batch notifications sent successfully to {Count} users", batchNotification.UserIds.Count);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending batch notifications");
            return Result.Failure("BATCH_SEND_ERROR", "批量发送通知失败");
        }
    }

    public async Task<Result> SendSystemBroadcastAsync(SystemBroadcastDto broadcast, CancellationToken cancellationToken = default)
    {
        try
        {
            // 如果需要持久化
            if (broadcast.Persistent)
            {
                // 获取所有目标用户
                var userRepository = _unitOfWork.Repository<Domain.Entities.User.CustomerUser>();
                var allUsers = await userRepository.GetAllAsync(cancellationToken);
                var targetUsers = allUsers.ToList();
                
                // 客户用户没有角色概念，角色通常用于管理员用户
                // 如果指定了TargetRoles，则忽略客户用户广播
                if (broadcast.TargetRoles != null && broadcast.TargetRoles.Any())
                {
                    _logger.LogWarning("System broadcast specified target roles, but customer users do not have roles. Broadcast will be sent to all users.");
                }
                
                var metadataJson = JsonSerializer.Serialize(new { Broadcast = true });
                var notifications = new List<Domain.Entities.Notification.Notification>();
                
                foreach (var user in targetUsers)
                {
                    var entity = new Domain.Entities.Notification.Notification(
                        user.Id,
                        broadcast.Title,
                        broadcast.Message,
                        NotificationTypes.System,
                        broadcast.Level,
                        metadataJson,
                        broadcast.ExpiresAt);
                        
                    notifications.Add(entity);
                }
                
                await _unitOfWork.Repository<Domain.Entities.Notification.Notification>()
                    .AddRangeAsync(notifications, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }
            
            // 实时广播
            if (_realtimeService != null)
            {
                await _realtimeService.PushSystemNotificationAsync(
                    new
                    {
                        Title = broadcast.Title,
                        Message = broadcast.Message,
                        Level = broadcast.Level,
                        Timestamp = DateTime.UtcNow,
                        ExpiresAt = broadcast.ExpiresAt
                    });
            }
            
            _logger.LogInformation("System broadcast sent successfully");
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending system broadcast");
            return Result.Failure("BROADCAST_ERROR", "发送系统广播失败");
        }
    }

    public async Task<Result<PagedResult<NotificationDto>>> GetUserNotificationsAsync(
        Guid userId, 
        NotificationQueryDto query, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var repository = _unitOfWork.Repository<Domain.Entities.Notification.Notification>() as INotificationRepository;
            if (repository == null)
            {
                return Result<PagedResult<NotificationDto>>.Failure("REPO_ERROR", "通知仓储未正确配置");
            }
            
            var (items, totalCount) = await repository.GetUserNotificationsAsync(
                userId,
                query.Type,
                query.Level,
                query.IsRead,
                query.StartDate,
                query.EndDate,
                query.Keyword,
                query.PageNumber - 1,  // Repository uses 0-based index
                query.PageSize,
                cancellationToken);
            
            var dtos = items.Select(n => new NotificationDto
            {
                Id = n.Id,
                UserId = n.UserId,
                Title = n.Title,
                Content = n.Content,
                Type = n.Type,
                Level = n.Level,
                IsRead = n.IsRead,
                ReadAt = n.ReadAt,
                Metadata = string.IsNullOrEmpty(n.MetadataJson) 
                    ? null 
                    : JsonSerializer.Deserialize<Dictionary<string, object>>(n.MetadataJson),
                CreatedAt = n.CreatedAt,
                ExpiresAt = n.ExpiresAt
            }).ToList();
            
            var result = new PagedResult<NotificationDto>(dtos, totalCount, query.PageNumber - 1, query.PageSize);
            return Result<PagedResult<NotificationDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user notifications");
            return Result<PagedResult<NotificationDto>>.Failure("GET_ERROR", "获取通知列表失败");
        }
    }

    public async Task<Result> MarkAsReadAsync(Guid userId, List<Guid> notificationIds, CancellationToken cancellationToken = default)
    {
        try
        {
            var repository = _unitOfWork.Repository<Domain.Entities.Notification.Notification>() as INotificationRepository;
            if (repository == null)
            {
                return Result.Failure("REPO_ERROR", "通知仓储未正确配置");
            }
            
            var count = await repository.MarkAsReadAsync(userId, notificationIds, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Marked {Count} notifications as read for user", count);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking notifications as read");
            return Result.Failure("MARK_READ_ERROR", "标记已读失败");
        }
    }

    public async Task<Result> MarkAllAsReadAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var repository = _unitOfWork.Repository<Domain.Entities.Notification.Notification>() as INotificationRepository;
            if (repository == null)
            {
                return Result.Failure("REPO_ERROR", "通知仓储未正确配置");
            }
            
            var count = await repository.MarkAllAsReadAsync(userId, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Marked all ({Count}) notifications as read for user", count);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking all notifications as read");
            return Result.Failure("MARK_ALL_READ_ERROR", "标记全部已读失败");
        }
    }

    public async Task<Result> DeleteNotificationsAsync(Guid userId, List<Guid> notificationIds, CancellationToken cancellationToken = default)
    {
        try
        {
            var repository = _unitOfWork.Repository<Domain.Entities.Notification.Notification>() as INotificationRepository;
            if (repository == null)
            {
                return Result.Failure("REPO_ERROR", "通知仓储未正确配置");
            }
            
            var count = await repository.SoftDeleteAsync(userId, notificationIds, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Deleted {Count} notifications for user", count);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting notifications");
            return Result.Failure("DELETE_ERROR", "删除通知失败");
        }
    }

    public async Task<Result<int>> GetUnreadCountAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var repository = _unitOfWork.Repository<Domain.Entities.Notification.Notification>() as INotificationRepository;
            if (repository == null)
            {
                return Result<int>.Failure("REPO_ERROR", "通知仓储未正确配置");
            }
            
            var count = await repository.GetUnreadCountAsync(userId, cancellationToken);
            return Result<int>.Success(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unread count");
            return Result<int>.Failure("COUNT_ERROR", "获取未读数量失败");
        }
    }

    public async Task<Result> NotifySubscriptionChangeAsync(
        Guid userId, 
        string planName, 
        string changeType, 
        CancellationToken cancellationToken = default)
    {
        var title = changeType switch
        {
            "upgrade" => "订阅升级成功",
            "downgrade" => "订阅降级通知",
            "renewal" => "订阅续费成功",
            "expiring" => "订阅即将到期",
            "expired" => "订阅已过期",
            _ => "订阅变更通知"
        };
        
        var content = changeType switch
        {
            "upgrade" => $"您的订阅已成功升级到 {planName} 套餐。",
            "downgrade" => $"您的订阅已降级到 {planName} 套餐。",
            "renewal" => $"您的 {planName} 套餐已成功续费。",
            "expiring" => $"您的 {planName} 套餐即将到期，请及时续费。",
            "expired" => $"您的 {planName} 套餐已过期，请续费以继续使用。",
            _ => $"您的订阅套餐已变更为 {planName}。"
        };
        
        var notification = new SendNotificationDto
        {
            UserId = userId,
            Title = title,
            Content = content,
            Type = NotificationTypes.Subscription,
            Level = changeType == "expired" ? NotificationLevels.Warning : NotificationLevels.Info,
            Metadata = new Dictionary<string, object>
            {
                ["planName"] = planName,
                ["changeType"] = changeType
            }
        };
        
        return await SendNotificationAsync(notification, cancellationToken);
    }

    public async Task<Result> NotifyTokenUsageAsync(
        Guid userId, 
        int remainingTokens, 
        int totalTokens, 
        CancellationToken cancellationToken = default)
    {
        var percentage = (double)remainingTokens / totalTokens * 100;
        string title, content, level;
        
        if (percentage <= 10)
        {
            title = "Token额度严重不足";
            content = $"您的Token额度仅剩 {remainingTokens:N0} 个（{percentage:F1}%），请及时充值或升级套餐。";
            level = NotificationLevels.Error;
        }
        else if (percentage <= 20)
        {
            title = "Token额度不足提醒";
            content = $"您的Token额度仅剩 {remainingTokens:N0} 个（{percentage:F1}%），建议尽快充值。";
            level = NotificationLevels.Warning;
        }
        else
        {
            title = "Token使用提醒";
            content = $"您的Token额度剩余 {remainingTokens:N0} 个（{percentage:F1}%）。";
            level = NotificationLevels.Info;
        }
        
        var notification = new SendNotificationDto
        {
            UserId = userId,
            Title = title,
            Content = content,
            Type = NotificationTypes.Token,
            Level = level,
            Metadata = new Dictionary<string, object>
            {
                ["remainingTokens"] = remainingTokens,
                ["totalTokens"] = totalTokens,
                ["percentage"] = percentage
            }
        };
        
        return await SendNotificationAsync(notification, cancellationToken);
    }

    public async Task<Result> NotifyAgentPublishedAsync(
        Guid agentId, 
        string agentName, 
        Guid creatorId, 
        CancellationToken cancellationToken = default)
    {
        // 获取所有用户（排除创建者）
        var userRepository = _unitOfWork.Repository<Domain.Entities.User.CustomerUser>();
        var allUsers = await userRepository.GetAsync(u => u.Id != creatorId, cancellationToken);
        var userIds = allUsers.Select(u => u.Id).ToList();
        
        if (!userIds.Any())
        {
            return Result.Success();
        }
        
        var batchNotification = new SendBatchNotificationDto
        {
            UserIds = userIds,
            Title = "发现新的AI智能体",
            Content = $"新的智能体「{agentName}」已发布，快来体验吧！",
            Type = NotificationTypes.Agent,
            Level = NotificationLevels.Info,
            Metadata = new Dictionary<string, object>
            {
                ["agentId"] = agentId,
                ["agentName"] = agentName,
                ["creatorId"] = creatorId
            }
        };
        
        return await SendBatchNotificationsAsync(batchNotification, cancellationToken);
    }

    public async Task<Result> NotifyAnomalousLoginAsync(
        Guid adminId, 
        string location, 
        string ipAddress, 
        DateTime loginTime, 
        CancellationToken cancellationToken = default)
    {
        var notification = new SendNotificationDto
        {
            UserId = adminId,
            Title = "异地登录警报",
            Content = $"您的账号在 {location} 登录，IP地址：{ipAddress}。如非本人操作，请立即修改密码。",
            Type = NotificationTypes.Security,
            Level = NotificationLevels.Warning,
            Metadata = new Dictionary<string, object>
            {
                ["location"] = location,
                ["ipAddress"] = ipAddress,
                ["loginTime"] = loginTime.ToString("yyyy-MM-dd HH:mm:ss"),
                ["isAnomalous"] = true
            },
            RealTimePush = true  // 确保实时推送
        };
        
        _logger.LogWarning(
            "Sending anomalous login notification to admin {AdminId} for login from {Location} ({IpAddress})",
            adminId, location, ipAddress);
        
        return await SendNotificationAsync(notification, cancellationToken);
    }
}