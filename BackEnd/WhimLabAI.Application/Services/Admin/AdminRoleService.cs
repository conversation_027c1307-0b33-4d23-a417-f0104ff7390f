using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Auth;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Shared.Dtos.Admin.Rbac;
using WhimLabAI.Shared.Results;
using System.Linq.Expressions;

namespace WhimLabAI.Application.Services.Admin;

/// <summary>
/// 管理员角色服务实现
/// </summary>
public class AdminRoleService : IAdminRoleService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly ILogger<AdminRoleService> _logger;
    
    public AdminRoleService(
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        ILogger<AdminRoleService> logger)
    {
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _logger = logger;
    }
    
    public async Task<Result<PagedList<RoleDto>>> GetRolesAsync(RoleQueryDto query, CancellationToken cancellationToken = default)
    {
        try
        {
            var roleRepository = _unitOfWork.Roles;
            
            // 构建查询条件
            Expression<Func<Role, bool>> predicate = r => true;
            
            if (!string.IsNullOrWhiteSpace(query.SearchTerm))
            {
                predicate = r => r.Name.Contains(query.SearchTerm) || r.Code.Contains(query.SearchTerm);
            }
            
            if (query.IsSystem.HasValue)
            {
                var isSystem = query.IsSystem.Value;
                predicate = CombinePredicates(predicate, r => r.IsSystem == isSystem);
            }
            
            if (query.IsEnabled.HasValue)
            {
                var isEnabled = query.IsEnabled.Value;
                predicate = CombinePredicates(predicate, r => r.IsEnabled == isEnabled);
            }
            
            // 获取总数
            var total = await roleRepository.CountAsync(predicate, cancellationToken);
            
            // 执行分页查询
            var roles = await roleRepository.GetPagedAsync(
                query.PageNumber,
                query.PageSize,
                predicate,
                q => q.OrderBy(r => r.DisplayOrder),
                cancellationToken);
            
            // 转换为DTO
            var roleDtos = new List<RoleDto>();
            foreach (var role in roles)
            {
                var dto = await MapToRoleDto(role, cancellationToken);
                roleDtos.Add(dto);
            }
            
            return Result<PagedList<RoleDto>>.Success(new PagedList<RoleDto>(
                roleDtos,
                query.PageNumber,
                query.PageSize,
                total));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色列表失败");
            return Result<PagedList<RoleDto>>.Failure("ROLE_QUERY_ERROR", "获取角色列表失败");
        }
    }
    
    public async Task<Result<RoleDto>> GetRoleByIdAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _unitOfWork.Roles.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result<RoleDto>.Failure("ROLE_NOT_FOUND", "角色不存在");
            }
            
            var dto = await MapToRoleDto(role, cancellationToken);
            return Result<RoleDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色详情失败: RoleId={RoleId}", roleId);
            return Result<RoleDto>.Failure("ROLE_GET_ERROR", "获取角色详情失败");
        }
    }
    
    public async Task<Result<RoleDto>> GetRoleByNameAsync(string roleName, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _unitOfWork.Roles.GetByCodeAsync(roleName, cancellationToken);
            if (role == null)
            {
                return Result<RoleDto>.Failure("ROLE_NOT_FOUND", "角色不存在");
            }
            
            var dto = await MapToRoleDto(role, cancellationToken);
            return Result<RoleDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色详情失败: RoleName={RoleName}", roleName);
            return Result<RoleDto>.Failure("ROLE_GET_ERROR", "获取角色详情失败");
        }
    }
    
    public async Task<Result<RoleDto>> CreateRoleAsync(CreateRoleDto dto, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查角色代码是否已存在
            var existingRole = await _unitOfWork.Roles.GetByCodeAsync(dto.Code, cancellationToken);
            if (existingRole != null)
            {
                return Result<RoleDto>.Failure("ROLE_CODE_EXISTS", "角色代码已存在");
            }
            
            // 创建角色
            var role = new Role(dto.Name, dto.Code, dto.Description);
            
            // 分配权限
            if (dto.PermissionIds.Any())
            {
                var permissions = await _unitOfWork.Permissions.GetByIdsAsync(dto.PermissionIds, cancellationToken);
                if (permissions.Count != dto.PermissionIds.Count)
                {
                    return Result<RoleDto>.Failure("INVALID_PERMISSIONS", "部分权限不存在");
                }
                
                role.UpdatePermissions(dto.PermissionIds);
            }
            
            await _unitOfWork.Roles.AddAsync(role, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            var resultDto = await MapToRoleDto(role, cancellationToken);
            return Result<RoleDto>.Success(resultDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建角色失败");
            return Result<RoleDto>.Failure("ROLE_CREATE_ERROR", "创建角色失败");
        }
    }
    
    public async Task<Result<RoleDto>> UpdateRoleAsync(Guid roleId, UpdateRoleDto dto, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _unitOfWork.Roles.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result<RoleDto>.Failure("ROLE_NOT_FOUND", "角色不存在");
            }
            
            if (role.IsSystem)
            {
                return Result<RoleDto>.Failure("SYSTEM_ROLE_READONLY", "系统角色不能修改");
            }
            
            role.Update(dto.Name, dto.Description, dto.DisplayOrder);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            var resultDto = await MapToRoleDto(role, cancellationToken);
            return Result<RoleDto>.Success(resultDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新角色失败: RoleId={RoleId}", roleId);
            return Result<RoleDto>.Failure("ROLE_UPDATE_ERROR", "更新角色失败");
        }
    }
    
    public async Task<Result<bool>> DeleteRoleAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _unitOfWork.Roles.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result<bool>.Failure("ROLE_NOT_FOUND", "角色不存在");
            }
            
            if (role.IsSystem)
            {
                return Result<bool>.Failure("SYSTEM_ROLE_DELETE", "系统角色不能删除");
            }
            
            // 检查是否有用户分配了此角色
            var userRoleCount = await _unitOfWork.Repository<AdminUserRole>()
                .CountAsync(ur => ur.RoleId == roleId, cancellationToken);
            
            if (userRoleCount > 0)
            {
                return Result<bool>.Failure("ROLE_IN_USE", "角色已分配给用户，不能删除");
            }
            
            _unitOfWork.Roles.Remove(role);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除角色失败: RoleId={RoleId}", roleId);
            return Result<bool>.Failure("ROLE_DELETE_ERROR", "删除角色失败");
        }
    }
    
    public async Task<Result<bool>> SetRoleStatusAsync(Guid roleId, bool isActive, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _unitOfWork.Roles.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result<bool>.Failure("ROLE_NOT_FOUND", "角色不存在");
            }
            
            if (isActive)
            {
                role.Enable();
            }
            else
            {
                role.Disable();
            }
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 清除相关用户的权限缓存
            var userRoles = await _unitOfWork.Repository<AdminUserRole>()
                .GetAsync(ur => ur.RoleId == roleId, cancellationToken);
            
            foreach (var userRole in userRoles)
            {
                var cacheKey = $"user:permissions:{userRole.AdminUserId}";
                await _cacheService.RemoveAsync(cacheKey, cancellationToken);
            }
            
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置角色状态失败: RoleId={RoleId}", roleId);
            return Result<bool>.Failure("ROLE_STATUS_ERROR", "设置角色状态失败");
        }
    }
    
    public async Task<Result<List<RolePermissionDto>>> GetAllPermissionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _unitOfWork.Permissions.GetAllAsync(cancellationToken);
            
            // 按模块分组
            var groupedPermissions = permissions
                .GroupBy(p => p.Category)
                .Select(g => new RolePermissionDto
                {
                    Module = g.Key,
                    Permissions = g.Select(p => new SimplePermissionDto
                    {
                        Code = p.Code,
                        Name = p.Name,
                        Description = p.Description
                    }).ToList()
                })
                .OrderBy(g => g.Module)
                .ToList();
            
            return Result<List<RolePermissionDto>>.Success(groupedPermissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限列表失败");
            return Result<List<RolePermissionDto>>.Failure("PERMISSION_LIST_ERROR", "获取权限列表失败");
        }
    }
    
    public async Task<Result<bool>> AssignPermissionsToRoleAsync(Guid roleId, List<string> permissions, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _unitOfWork.Roles.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result<bool>.Failure("ROLE_NOT_FOUND", "角色不存在");
            }
            
            // 根据权限代码获取权限ID
            var permissionEntities = await _unitOfWork.Permissions.GetByCodesAsync(permissions, cancellationToken);
            if (permissionEntities.Count != permissions.Count)
            {
                return Result<bool>.Failure("INVALID_PERMISSIONS", "部分权限不存在");
            }
            
            var permissionIds = permissionEntities.Select(p => p.Id).ToList();
            role.UpdatePermissions(permissionIds);
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 清除相关用户的权限缓存
            var userRoles = await _unitOfWork.Repository<AdminUserRole>()
                .GetAsync(ur => ur.RoleId == roleId, cancellationToken);
            
            foreach (var userRole in userRoles)
            {
                var cacheKey = $"user:permissions:{userRole.AdminUserId}";
                await _cacheService.RemoveAsync(cacheKey, cancellationToken);
            }
            
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分配权限失败: RoleId={RoleId}", roleId);
            return Result<bool>.Failure("ASSIGN_PERMISSION_ERROR", "分配权限失败");
        }
    }
    
    public async Task<Result<List<string>>> GetRolePermissionsAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _unitOfWork.Roles.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result<List<string>>.Failure("ROLE_NOT_FOUND", "角色不存在");
            }
            
            var permissionIds = role.Permissions.Select(p => p.PermissionId).ToList();
            var permissions = await _unitOfWork.Permissions.GetByIdsAsync(permissionIds, cancellationToken);
            var permissionCodes = permissions.Select(p => p.Code).ToList();
            
            return Result<List<string>>.Success(permissionCodes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色权限失败: RoleId={RoleId}", roleId);
            return Result<List<string>>.Failure("GET_PERMISSION_ERROR", "获取角色权限失败");
        }
    }
    
    private async Task<RoleDto> MapToRoleDto(Role role, CancellationToken cancellationToken)
    {
        // 获取用户数量
        var userCount = await _unitOfWork.Repository<AdminUserRole>()
            .CountAsync(ur => ur.RoleId == role.Id, cancellationToken);
        
        // 获取权限代码
        var permissionIds = role.Permissions.Select(p => p.PermissionId).ToList();
        var permissions = await _unitOfWork.Permissions.GetByIdsAsync(permissionIds, cancellationToken);
        var permissionCodes = permissions.Select(p => p.Code).ToList();
        
        return new RoleDto
        {
            Id = role.Id,
            Name = role.Name,
            Code = role.Code,
            Description = role.Description,
            IsEnabled = role.IsEnabled,
            IsSystem = role.IsSystem,
            DisplayOrder = role.DisplayOrder,
            CreatedAt = role.CreatedAt,
            UpdatedAt = role.UpdatedAt
        };
    }
    
    private Expression<Func<T, bool>> CombinePredicates<T>(
        Expression<Func<T, bool>> first,
        Expression<Func<T, bool>> second)
    {
        var parameter = Expression.Parameter(typeof(T));
        
        var leftVisitor = new ReplaceExpressionVisitor(first.Parameters[0], parameter);
        var left = leftVisitor.Visit(first.Body);
        
        var rightVisitor = new ReplaceExpressionVisitor(second.Parameters[0], parameter);
        var right = rightVisitor.Visit(second.Body);
        
        var combined = Expression.AndAlso(left, right);
        return Expression.Lambda<Func<T, bool>>(combined, parameter);
    }
    
    private class ReplaceExpressionVisitor : ExpressionVisitor
    {
        private readonly Expression _oldValue;
        private readonly Expression _newValue;
        
        public ReplaceExpressionVisitor(Expression oldValue, Expression newValue)
        {
            _oldValue = oldValue;
            _newValue = newValue;
        }
        
        public override Expression Visit(Expression node)
        {
            return node == _oldValue ? _newValue : base.Visit(node);
        }
    }
}