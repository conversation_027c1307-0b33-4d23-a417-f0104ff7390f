using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Enums;
using System.Linq.Expressions;

namespace WhimLabAI.Application.Services.Agent;

public class AgentQueryService : IAgentQueryService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AgentQueryService> _logger;

    public AgentQueryService(
        IUnitOfWork unitOfWork,
        ILogger<AgentQueryService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<PagedResult<AgentListDto>> QueryAgentsAsync(AgentQueryDto query, CancellationToken cancellationToken = default)
    {
        try
        {
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            
            // Build filter expression
            Expression<Func<Domain.Entities.Agent.Agent, bool>> filter = a => true;
            
            if (!string.IsNullOrWhiteSpace(query.Keyword))
            {
                var keyword = query.Keyword.Trim().ToLower();
                filter = CombineWithAnd(filter, a => 
                    a.Name.ToLower().Contains(keyword) || 
                    (a.Description != null && a.Description.ToLower().Contains(keyword)));
            }
            
            if (!string.IsNullOrWhiteSpace(query.Category))
            {
                filter = CombineWithAnd(filter, a => a.Category != null && a.Category.Name == query.Category);
            }
            
            if (query.Tags != null && query.Tags.Any())
            {
                filter = CombineWithAnd(filter, a => a.Tags.Any(t => query.Tags.Contains(t.Name)));
            }
            
            if (!string.IsNullOrWhiteSpace(query.Status))
            {
                if (Enum.TryParse<AgentStatus>(query.Status, out var status))
                {
                    filter = CombineWithAnd(filter, a => a.Status == status);
                }
            }
            
            if (query.CreatorId.HasValue)
            {
                filter = CombineWithAnd(filter, a => a.CreatorId == query.CreatorId.Value);
            }
            
            // Get total count
            var totalCount = await agentRepository.CountAsync(filter, cancellationToken);
            
            // Get all matching agents
            var allAgents = await agentRepository.GetAsync(filter, cancellationToken);
            var agentList = allAgents.ToList();
            
            // Apply ordering
            agentList = ApplyOrdering(agentList, query.SortBy, query.Descending);
            
            // Apply paging
            var pagedAgents = agentList
                .Skip((query.PageNumber - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToList();
            
            // Map to DTOs
            var agentDtos = pagedAgents.Select(a => new AgentListDto
            {
                Id = a.Id,
                Name = a.Name,
                UniqueKey = a.UniqueKey,
                Description = a.Description,
                Category = a.Category?.DisplayName,
                Tags = a.Tags.Select(t => t.Name).ToList(),
                Icon = a.Icon,
                Cover = a.Cover,
                Status = a.Status.ToString(),
                UsageCount = a.UsageCount,
                Rating = a.AverageRating,
                CreatedAt = a.CreatedAt,
                UpdatedAt = a.UpdatedAt,
                Creator = new CreatorInfoDto
                {
                    Id = a.CreatorId,
                    Username = "WhimLabAI",
                    Nickname = "WhimLabAI",
                    Avatar = null
                }
            }).ToList();
            
            return new PagedResult<AgentListDto>
            {
                Items = agentDtos,
                TotalCount = totalCount,
                PageNumber = query.PageNumber,
                PageSize = query.PageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error querying agents");
            return new PagedResult<AgentListDto>
            {
                Items = new List<AgentListDto>(),
                TotalCount = 0,
                PageNumber = query.PageNumber,
                PageSize = query.PageSize
            };
        }
    }

    public async Task<Result<List<AgentDto>>> GetAgentsByCreatorAsync(Guid creatorId, CancellationToken cancellationToken = default)
    {
        try
        {
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var agents = await agentRepository.GetAsync(a => a.CreatorId == creatorId, cancellationToken);
            
            var agentDtos = agents.Select(MapToAgentDto).ToList();
            
            return Result<List<AgentDto>>.Success(agentDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting agents by creator: {CreatorId}", creatorId);
            return Result<List<AgentDto>>.Failure("GET_AGENTS_ERROR", "获取代理列表失败");
        }
    }

    public async Task<Result<List<AgentDto>>> GetPublishedAgentsAsync(int limit = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var allAgents = await agentRepository.GetAsync(
                a => a.Status == AgentStatus.Published,
                cancellationToken);
            
            var agents = allAgents
                .OrderByDescending(a => a.PublishedAt)
                .Take(limit)
                .ToList();
            
            var agentDtos = agents.Select(MapToAgentDto).ToList();
            
            return Result<List<AgentDto>>.Success(agentDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting published agents");
            return Result<List<AgentDto>>.Failure("GET_AGENTS_ERROR", "获取已发布代理失败");
        }
    }

    public async Task<Result<List<AgentDto>>> SearchAgentsAsync(string keyword, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return Result<List<AgentDto>>.Success(new List<AgentDto>());
            }
            
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var searchTerm = keyword.Trim().ToLower();
            
            var allAgents = await agentRepository.GetAsync(
                a => a.Status == AgentStatus.Published && 
                     (a.Name.ToLower().Contains(searchTerm) || 
                      (a.Description != null && a.Description.ToLower().Contains(searchTerm)) ||
                      a.Tags.Any(t => t.Name.ToLower().Contains(searchTerm))),
                cancellationToken);
            
            var agents = allAgents
                .OrderByDescending(a => a.UsageCount)
                .ThenByDescending(a => a.AverageRating)
                .Take(50)
                .ToList();
            
            var agentDtos = agents.Select(MapToAgentDto).ToList();
            
            return Result<List<AgentDto>>.Success(agentDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching agents with keyword: {Keyword}", keyword);
            return Result<List<AgentDto>>.Failure("SEARCH_ERROR", "搜索代理失败");
        }
    }
    
    private Expression<Func<Domain.Entities.Agent.Agent, bool>> CombineWithAnd(
        Expression<Func<Domain.Entities.Agent.Agent, bool>> expr1,
        Expression<Func<Domain.Entities.Agent.Agent, bool>> expr2)
    {
        var parameter = Expression.Parameter(typeof(Domain.Entities.Agent.Agent));
        
        var leftVisitor = new ReplaceExpressionVisitor(expr1.Parameters[0], parameter);
        var left = leftVisitor.Visit(expr1.Body);
        
        var rightVisitor = new ReplaceExpressionVisitor(expr2.Parameters[0], parameter);
        var right = rightVisitor.Visit(expr2.Body);
        
        var combined = Expression.AndAlso(left, right);
        
        return Expression.Lambda<Func<Domain.Entities.Agent.Agent, bool>>(combined, parameter);
    }
    
    private List<Domain.Entities.Agent.Agent> ApplyOrdering(
        List<Domain.Entities.Agent.Agent> agents, 
        string? sortBy, 
        bool descending)
    {
        return sortBy?.ToLower() switch
        {
            "name" => descending 
                ? agents.OrderByDescending(a => a.Name).ToList() 
                : agents.OrderBy(a => a.Name).ToList(),
            "usage" => descending 
                ? agents.OrderByDescending(a => a.UsageCount).ToList() 
                : agents.OrderBy(a => a.UsageCount).ToList(),
            "rating" => descending 
                ? agents.OrderByDescending(a => a.AverageRating).ToList() 
                : agents.OrderBy(a => a.AverageRating).ToList(),
            "updatedat" => descending 
                ? agents.OrderByDescending(a => a.UpdatedAt).ToList() 
                : agents.OrderBy(a => a.UpdatedAt).ToList(),
            _ => descending 
                ? agents.OrderByDescending(a => a.CreatedAt).ToList() 
                : agents.OrderBy(a => a.CreatedAt).ToList()
        };
    }
    
    private AgentDto MapToAgentDto(Domain.Entities.Agent.Agent agent)
    {
        var currentVersion = agent.CurrentVersion ?? agent.Versions.FirstOrDefault(v => v.Status == AgentStatus.Published);
        
        return new AgentDto
        {
            Id = agent.Id,
            Name = agent.Name,
            UniqueKey = agent.UniqueKey,
            Description = agent.Description,
            DetailedDescription = agent.DetailedIntro,
            Category = agent.Category?.DisplayName,
            CategoryId = agent.CategoryId,
            Tags = agent.Tags.Select(t => t.Name).ToList(),
            Icon = agent.Icon,
            Cover = agent.Cover,
            Status = agent.Status.ToString(),
            CreatorId = agent.CreatorId,
            ConversationCount = agent.UsageCount,
            Rating = agent.AverageRating,
            RatingCount = agent.RatingCount,
            IsFree = true, // All agents are free for now
            TokenConsumption = 100, // Default token consumption
            Features = ExtractFeaturesFromDescription(agent.DetailedIntro ?? agent.Description),
            PromptExamples = new List<string>(), // TODO: Implement prompt examples extraction
            SystemPrompt = currentVersion?.SystemPrompt ?? string.Empty,
            ApiEndpoint = $"/api/v1/agents/{agent.Id}/conversations",
            Config = currentVersion != null ? new AgentConfigDto
            {
                ModelType = currentVersion.ModelConfig?.ModelType ?? "OpenAI",
                ModelConfig = new Dictionary<string, object>
                {
                    ["model"] = currentVersion.ModelConfig?.ModelName ?? "gpt-3.5-turbo",
                    ["maxContextTokens"] = currentVersion.ModelConfig?.MaxTokens ?? 4096
                },
                SystemPrompt = currentVersion.SystemPrompt,
                UserPrompt = currentVersion.UserPrompt,
                Plugins = currentVersion.Plugins.ToList(),
                KnowledgeBases = currentVersion.KnowledgeBases.ToList(),
                MaxTokens = currentVersion.ModelConfig?.MaxTokens ?? 2048,
                Temperature = currentVersion.ModelConfig?.Temperature ?? 0.7
            } : new AgentConfigDto(),
            CreatedAt = agent.CreatedAt,
            UpdatedAt = agent.UpdatedAt
        };
    }
    
    private List<string> ExtractFeaturesFromDescription(string? description)
    {
        if (string.IsNullOrWhiteSpace(description))
            return new List<string>();
        
        var features = new List<string>();
        
        // Extract features based on keywords
        if (description.Contains("知识库", StringComparison.OrdinalIgnoreCase))
            features.Add("知识库支持");
        if (description.Contains("API", StringComparison.OrdinalIgnoreCase))
            features.Add("API集成");
        if (description.Contains("实时", StringComparison.OrdinalIgnoreCase))
            features.Add("实时响应");
        if (description.Contains("多语言", StringComparison.OrdinalIgnoreCase) || description.Contains("多语种", StringComparison.OrdinalIgnoreCase))
            features.Add("多语言支持");
        if (description.Contains("代码", StringComparison.OrdinalIgnoreCase) || description.Contains("编程", StringComparison.OrdinalIgnoreCase))
            features.Add("代码生成");
        if (description.Contains("分析", StringComparison.OrdinalIgnoreCase))
            features.Add("数据分析");
        if (description.Contains("图像", StringComparison.OrdinalIgnoreCase) || description.Contains("图片", StringComparison.OrdinalIgnoreCase))
            features.Add("图像处理");
            
        return features;
    }
    
    private sealed class ReplaceExpressionVisitor : ExpressionVisitor
    {
        private readonly Expression _oldValue;
        private readonly Expression _newValue;

        public ReplaceExpressionVisitor(Expression oldValue, Expression newValue)
        {
            _oldValue = oldValue;
            _newValue = newValue;
        }

        public override Expression Visit(Expression? node)
        {
            if (node == _oldValue)
                return _newValue;
            return base.Visit(node);
        }
    }
}