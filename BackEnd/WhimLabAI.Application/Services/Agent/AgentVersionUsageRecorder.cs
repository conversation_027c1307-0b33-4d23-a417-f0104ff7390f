using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.Application.Services.Agent;

/// <summary>
/// Service for recording agent version usage during conversations
/// </summary>
public class AgentVersionUsageRecorder : IAgentVersionUsageRecorder
{
    private readonly IAgentVersionService _versionService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AgentVersionUsageRecorder> _logger;

    public AgentVersionUsageRecorder(
        IAgentVersionService versionService,
        IUnitOfWork unitOfWork,
        ILogger<AgentVersionUsageRecorder> logger)
    {
        _versionService = versionService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task RecordConversationStartAsync(Guid agentId, Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get the current version of the agent
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var agent = await agentRepository.GetByIdAsync(agentId, cancellationToken);
            
            if (agent?.CurrentVersionId == null)
            {
                _logger.LogWarning("Agent {AgentId} has no current version", agentId);
                return;
            }

            // Increment conversation count
            var version = agent.Versions.FirstOrDefault(v => v.Id == agent.CurrentVersionId.Value);
            if (version != null)
            {
                // Record usage on the version entity directly
                version.RecordUsage(0, 0, 0); // This will create or update today's stats
                
                // Then increment conversation count
                var today = DateTime.UtcNow.Date;
                var todayStats = version.UsageStats.FirstOrDefault(s => s.Date == today);
                if (todayStats != null)
                {
                    todayStats.IncrementConversation();
                }
                
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording conversation start for agent {AgentId}", agentId);
            // Don't throw - usage recording should not break the conversation flow
        }
    }

    public async Task RecordMessageExchangeAsync(
        Guid agentId, 
        Guid conversationId, 
        Guid userId,
        int messageCount,
        long tokensConsumed,
        double responseTimeMs,
        bool hasError,
        string? errorMessage = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get the current version of the agent
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var agent = await agentRepository.GetByIdAsync(agentId, cancellationToken);
            
            if (agent?.CurrentVersionId == null)
            {
                _logger.LogWarning("Agent {AgentId} has no current version", agentId);
                return;
            }

            // Create usage record
            var usageRecord = new VersionUsageRecordDto
            {
                ConversationId = conversationId,
                UserId = userId,
                MessageCount = messageCount,
                TokensConsumed = tokensConsumed,
                ResponseTime = responseTimeMs,
                HasError = hasError,
                ErrorMessage = errorMessage
            };

            // Record the usage
            await _versionService.RecordVersionUsageAsync(agent.CurrentVersionId.Value, usageRecord, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording message exchange for agent {AgentId}", agentId);
            // Don't throw - usage recording should not break the conversation flow
        }
    }
}

public interface IAgentVersionUsageRecorder
{
    Task RecordConversationStartAsync(Guid agentId, Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task RecordMessageExchangeAsync(
        Guid agentId, 
        Guid conversationId, 
        Guid userId,
        int messageCount,
        long tokensConsumed,
        double responseTimeMs,
        bool hasError,
        string? errorMessage = null,
        CancellationToken cancellationToken = default);
}