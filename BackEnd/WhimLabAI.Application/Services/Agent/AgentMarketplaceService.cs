using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Agent;

public class AgentMarketplaceService : IAgentMarketplaceService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly ILogger<AgentMarketplaceService> _logger;

    public AgentMarketplaceService(
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        ILogger<AgentMarketplaceService> logger)
    {
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<Result<List<AgentCategoryDto>>> GetCategoriesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = "agent:categories";
            var cached = await _cacheService.GetAsync<List<AgentCategoryDto>>(cacheKey, cancellationToken);
            if (cached != null)
                return Result<List<AgentCategoryDto>>.Success(cached);

            var categories = await _unitOfWork.AgentCategories.GetAllAsync(cancellationToken);
            
            var categoryDtos = new List<AgentCategoryDto>();
            foreach (var category in categories)
            {
                var agentCount = await _unitOfWork.Agents.CountByCategoryAsync(category.Id, cancellationToken);
                categoryDtos.Add(new AgentCategoryDto
                {
                    Id = category.Id,
                    Name = category.Name,
                    Description = category.Description,
                    Icon = category.Icon,
                    AgentCount = agentCount,
                    DisplayOrder = category.SortOrder
                });
            }

            categoryDtos = categoryDtos.OrderBy(c => c.DisplayOrder).ToList();
            
            await _cacheService.SetAsync(cacheKey, categoryDtos, TimeSpan.FromHours(1), cancellationToken);
            
            return Result<List<AgentCategoryDto>>.Success(categoryDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting agent categories");
            return Result<List<AgentCategoryDto>>.Failure("获取分类失败");
        }
    }

    public async Task<Result<List<AgentTagDto>>> GetPopularTagsAsync(int count = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"agent:tags:popular:{count}";
            var cached = await _cacheService.GetAsync<List<AgentTagDto>>(cacheKey, cancellationToken);
            if (cached != null)
                return Result<List<AgentTagDto>>.Success(cached);

            var tags = await _unitOfWork.Agents.GetPopularTagsAsync(count, cancellationToken);
            var tagDtos = tags.Select(t => new AgentTagDto { Name = t.Tag, Count = t.Count }).ToList();
            
            await _cacheService.SetAsync(cacheKey, tagDtos, TimeSpan.FromMinutes(30), cancellationToken);
            
            return Result<List<AgentTagDto>>.Success(tagDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting popular tags");
            return Result<List<AgentTagDto>>.Failure("获取热门标签失败");
        }
    }

    public async Task<Result<AgentLikeResultDto>> LikeAgentAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查Agent是否存在
            var agent = await _unitOfWork.Agents.GetByIdAsync(agentId, cancellationToken);
            if (agent == null)
                return Result<AgentLikeResultDto>.Failure("Agent不存在");

            // 检查是否已经点赞
            var existingLike = await _unitOfWork.AgentLikes.GetByAgentAndUserAsync(agentId, userId, cancellationToken);
            if (existingLike != null)
            {
                return Result<AgentLikeResultDto>.Success(new AgentLikeResultDto
                {
                    IsLiked = true,
                    TotalLikes = agent.LikeCount
                });
            }

            // 创建点赞记录
            var like = new AgentLike(agentId, userId);
            await _unitOfWork.AgentLikes.AddAsync(like, cancellationToken);
            
            // 更新Agent点赞数
            agent.IncrementLikeCount();
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 清除缓存
            await ClearAgentCacheAsync(agentId);

            return Result<AgentLikeResultDto>.Success(new AgentLikeResultDto
            {
                IsLiked = true,
                TotalLikes = agent.LikeCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error liking agent {AgentId} by user {UserId}", agentId, userId);
            return Result<AgentLikeResultDto>.Failure("点赞失败");
        }
    }

    public async Task<Result> UnlikeAgentAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var existingLike = await _unitOfWork.AgentLikes.GetByAgentAndUserAsync(agentId, userId, cancellationToken);
            if (existingLike == null)
                return Result.Success();

            _unitOfWork.AgentLikes.Remove(existingLike);
            
            // 更新Agent点赞数
            var agent = await _unitOfWork.Agents.GetByIdAsync(agentId, cancellationToken);
            if (agent != null)
            {
                agent.DecrementLikeCount();
            }
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 清除缓存
            await ClearAgentCacheAsync(agentId);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unliking agent {AgentId} by user {UserId}", agentId, userId);
            return Result.Failure("取消点赞失败");
        }
    }

    public async Task<Result<bool>> IsLikedAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var like = await _unitOfWork.AgentLikes.GetByAgentAndUserAsync(agentId, userId, cancellationToken);
            return Result<bool>.Success(like != null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if agent {AgentId} is liked by user {UserId}", agentId, userId);
            return Result<bool>.Failure("检查点赞状态失败");
        }
    }

    public async Task<Result<AgentRatingDto>> RateAgentAsync(Guid agentId, Guid userId, CreateRatingDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查Agent是否存在
            var agent = await _unitOfWork.Agents.GetByIdAsync(agentId, cancellationToken);
            if (agent == null)
                return Result<AgentRatingDto>.Failure("Agent不存在");

            // 检查是否已经评价过
            var existingRating = await _unitOfWork.AgentRatings.GetByAgentAndUserAsync(agentId, userId, cancellationToken);
            if (existingRating != null)
                return Result<AgentRatingDto>.Failure("您已经评价过该Agent");

            // 检查是否使用过该Agent（验证购买）
            var hasUsed = await _unitOfWork.Conversations.HasUserUsedAgentAsync(userId, agentId, cancellationToken);
            
            // 创建评价
            var rating = new AgentRating(userId, request.Rating, request.Comment, hasUsed);
            await _unitOfWork.AgentRatings.AddAsync(rating, cancellationToken);
            agent.AddRating(userId, request.Rating, request.Comment);
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 清除缓存
            await ClearAgentCacheAsync(agentId);

            // 获取用户信息
            var user = await _unitOfWork.CustomerUsers.GetByIdAsync(userId, cancellationToken);
            
            return Result<AgentRatingDto>.Success(new AgentRatingDto
            {
                Id = rating.Id,
                AgentId = agentId,
                UserId = userId,
                UserName = user?.Username ?? "Unknown",
                UserAvatar = user?.Avatar,
                Score = rating.Score,
                Feedback = rating.Feedback,
                RatedAt = rating.RatedAt,
                IsVerifiedPurchase = rating.IsVerifiedPurchase,
                HelpfulCount = rating.HelpfulCount,
                UnhelpfulCount = rating.UnhelpfulCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rating agent {AgentId} by user {UserId}", agentId, userId);
            return Result<AgentRatingDto>.Failure("评价失败");
        }
    }

    public async Task<Result> UpdateRatingAsync(Guid ratingId, Guid userId, UpdateRatingDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var rating = await _unitOfWork.AgentRatings.GetByIdAsync(ratingId, cancellationToken);
            if (rating == null)
                return Result.Failure("评价不存在");

            if (rating.UserId != userId)
                return Result.Failure("无权修改此评价");

            rating.Update(request.Rating, request.Comment);
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 清除缓存
            var agentRating = await _unitOfWork.Agents.GetAgentByRatingIdAsync(ratingId, cancellationToken);
            if (agentRating != null)
            {
                await ClearAgentCacheAsync(agentRating.Id);
            }

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating rating {RatingId}", ratingId);
            return Result.Failure("更新评价失败");
        }
    }

    public async Task<Result<PagedResult<AgentRatingDto>>> GetAgentRatingsAsync(Guid agentId, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var agent = await _unitOfWork.Agents.GetByIdAsync(agentId, cancellationToken);
            if (agent == null)
                return Result<PagedResult<AgentRatingDto>>.Failure("Agent不存在");

            var ratings = await _unitOfWork.AgentRatings.GetByAgentIdAsync(agentId, pageNumber, pageSize, cancellationToken);
            var totalCount = await _unitOfWork.AgentRatings.CountByAgentIdAsync(agentId, cancellationToken);

            // 获取用户信息
            var userIds = ratings.Select(r => r.UserId).Distinct().ToList();
            var users = await _unitOfWork.CustomerUsers.GetByIdsAsync(userIds, cancellationToken);
            var userDict = users.ToDictionary(u => u.Id);

            var ratingDtos = ratings.Select(r => new AgentRatingDto
            {
                Id = r.Id,
                AgentId = agentId,
                UserId = r.UserId,
                UserName = userDict.TryGetValue(r.UserId, out var user) ? user.Username : "Unknown",
                UserAvatar = userDict.TryGetValue(r.UserId, out var u) ? u.Avatar : null,
                Score = r.Score,
                Feedback = r.Feedback,
                RatedAt = r.RatedAt,
                UpdatedAt = r.UpdatedAt,
                IsVerifiedPurchase = r.IsVerifiedPurchase,
                HelpfulCount = r.HelpfulCount,
                UnhelpfulCount = r.UnhelpfulCount
            }).ToList();

            return Result<PagedResult<AgentRatingDto>>.Success(new PagedResult<AgentRatingDto>(
                ratingDtos,
                totalCount,
                pageNumber,
                pageSize
            ));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ratings for agent {AgentId}", agentId);
            return Result<PagedResult<AgentRatingDto>>.Failure("获取评价列表失败");
        }
    }

    public async Task<Result<AgentRatingDto>> GetUserRatingAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var rating = await _unitOfWork.AgentRatings.GetByAgentAndUserAsync(agentId, userId, cancellationToken);
            if (rating == null)
                return Result<AgentRatingDto>.Failure("未找到评价");

            var user = await _unitOfWork.CustomerUsers.GetByIdAsync(userId, cancellationToken);
            
            return Result<AgentRatingDto>.Success(new AgentRatingDto
            {
                Id = rating.Id,
                AgentId = agentId,
                UserId = userId,
                UserName = user?.Username ?? "Unknown",
                UserAvatar = user?.Avatar,
                Score = rating.Score,
                Feedback = rating.Feedback,
                RatedAt = rating.RatedAt,
                UpdatedAt = rating.UpdatedAt,
                IsVerifiedPurchase = rating.IsVerifiedPurchase,
                HelpfulCount = rating.HelpfulCount,
                UnhelpfulCount = rating.UnhelpfulCount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user rating for agent {AgentId} by user {UserId}", agentId, userId);
            return Result<AgentRatingDto>.Failure("获取用户评价失败");
        }
    }

    public async Task<Result> MarkRatingAsHelpfulAsync(Guid ratingId, Guid userId, bool isHelpful, CancellationToken cancellationToken = default)
    {
        try
        {
            var rating = await _unitOfWork.AgentRatings.GetByIdAsync(ratingId, cancellationToken);
            if (rating == null)
                return Result.Failure("评价不存在");

            if (rating.UserId == userId)
                return Result.Failure("不能标记自己的评价");

            // 检查用户是否已经标记过
            var existingMark = await _unitOfWork.RatingHelpfulnesses.GetByRatingAndUserAsync(ratingId, userId, cancellationToken);
            
            if (existingMark != null)
            {
                if (existingMark.IsHelpful == isHelpful)
                    return Result.Success(); // 已经标记过相同的状态
                    
                // 更新标记
                if (existingMark.IsHelpful)
                {
                    rating.UnmarkAsHelpful();
                }
                else
                {
                    rating.UnmarkAsUnhelpful();
                }
                
                existingMark.Update(isHelpful);
            }
            else
            {
                // 创建新标记
                var helpfulness = new RatingHelpfulness(ratingId, userId, isHelpful);
                await _unitOfWork.RatingHelpfulnesses.AddAsync(helpfulness, cancellationToken);
            }
            
            // 更新计数
            if (isHelpful)
            {
                rating.MarkAsHelpful();
            }
            else
            {
                rating.MarkAsUnhelpful();
            }
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking rating {RatingId} as helpful by user {UserId}", ratingId, userId);
            return Result.Failure("标记失败");
        }
    }

    private async Task ClearAgentCacheAsync(Guid agentId)
    {
        var cacheKeys = new[]
        {
            $"agent:{agentId}",
            $"agent:{agentId}:detail",
            $"agent:{agentId}:ratings"
        };
        
        foreach (var key in cacheKeys)
        {
            await _cacheService.RemoveAsync(key);
        }
    }
}