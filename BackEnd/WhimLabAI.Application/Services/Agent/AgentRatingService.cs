using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;
using RatingHelpfulness = WhimLabAI.Domain.Entities.Agent.RatingHelpfulness;

namespace WhimLabAI.Application.Services.Agent;

/// <summary>
/// Agent评价服务实现
/// </summary>
public class AgentRatingService : IAgentRatingService
{
    private readonly IAgentRatingRepository _agentRatingRepository;
    private readonly IAgentRepository _agentRepository;
    private readonly ICustomerUserRepository _customerUserRepository;
    private readonly IRatingHelpfulnessRepository _ratingHelpfulnessRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly ILogger<AgentRatingService> _logger;
    
    public AgentRatingService(
        IAgentRatingRepository agentRatingRepository,
        IAgentRepository agentRepository,
        ICustomerUserRepository customerUserRepository,
        IRatingHelpfulnessRepository ratingHelpfulnessRepository,
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        ILogger<AgentRatingService> logger)
    {
        _agentRatingRepository = agentRatingRepository;
        _agentRepository = agentRepository;
        _customerUserRepository = customerUserRepository;
        _ratingHelpfulnessRepository = ratingHelpfulnessRepository;
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _logger = logger;
    }
    
    public async Task<Result<AgentRatingDto>> SubmitRatingAsync(Guid agentId, Guid userId, int score, string? feedback = null)
    {
        try
        {
            // 验证Agent是否存在
            var agent = await _agentRepository.GetByIdAsync(agentId);
            if (agent == null)
            {
                return Result<AgentRatingDto>.Failure("Agent不存在");
            }
            
            // 验证用户是否存在
            var user = await _customerUserRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return Result<AgentRatingDto>.Failure("用户不存在");
            }
            
            // 检查是否已有评价
            var existingRating = await _agentRatingRepository.GetByAgentAndUserAsync(agentId, userId);
            
            if (existingRating != null)
            {
                // 更新现有评价
                existingRating.Update(score, feedback);
                _agentRatingRepository.Update(existingRating);
            }
            else
            {
                // 创建新评价
                agent.AddRating(userId, score, feedback);
                _agentRepository.Update(agent);
            }
            
            await _unitOfWork.SaveChangesAsync();
            
            // 清除缓存
            await ClearAgentRatingsCacheAsync(agentId);
            
            // 获取更新后的评价
            var rating = existingRating ?? agent.Ratings.FirstOrDefault(r => r.UserId == userId);
            if (rating == null)
            {
                return Result<AgentRatingDto>.Failure("评价保存失败");
            }
            
            var dto = MapToDto(rating, user);
            
            _logger.LogInformation("User {UserId} submitted rating for agent {AgentId} with score {Score}", 
                userId, agentId, score);
            
            return Result<AgentRatingDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to submit rating for agent {AgentId} by user {UserId}", agentId, userId);
            return Result<AgentRatingDto>.Failure("提交评价失败");
        }
    }
    
    public async Task<Result<PagedResult<AgentRatingDto>>> GetAgentRatingsAsync(Guid agentId, int pageNumber = 1, int pageSize = 20)
    {
        try
        {
            var cacheKey = GetAgentRatingsCacheKey(agentId, pageNumber, pageSize);
            var cached = await _cacheService.GetAsync<PagedResult<AgentRatingDto>>(cacheKey);
            if (cached != null)
            {
                return Result<PagedResult<AgentRatingDto>>.Success(cached);
            }
            
            var ratings = await _agentRatingRepository.GetByAgentIdAsync(agentId, pageNumber, pageSize);
            var totalCount = await _agentRatingRepository.CountByAgentIdAsync(agentId);
            
            var userIds = ratings.Select(r => r.UserId).Distinct().ToList();
            var users = await _customerUserRepository.GetByIdsAsync(userIds);
            var userDict = users.ToDictionary(u => u.Id);
            
            var dtos = ratings.Select(r => MapToDto(r, userDict.GetValueOrDefault(r.UserId))).ToList();
            
            var result = new PagedResult<AgentRatingDto>
            {
                Items = dtos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
            
            await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(5));
            
            return Result<PagedResult<AgentRatingDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get ratings for agent {AgentId}", agentId);
            return Result<PagedResult<AgentRatingDto>>.Failure("获取评价列表失败");
        }
    }
    
    public async Task<Result<AgentRatingDto?>> GetUserRatingAsync(Guid agentId, Guid userId)
    {
        try
        {
            var rating = await _agentRatingRepository.GetByAgentAndUserAsync(agentId, userId);
            if (rating == null)
            {
                return Result<AgentRatingDto?>.Success(null);
            }
            
            var user = await _customerUserRepository.GetByIdAsync(userId);
            var dto = MapToDto(rating, user);
            
            return Result<AgentRatingDto?>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user rating for agent {AgentId} by user {UserId}", agentId, userId);
            return Result<AgentRatingDto?>.Failure("获取用户评价失败");
        }
    }
    
    public async Task<Result<PagedResult<AgentRatingDto>>> GetUserRatingsAsync(Guid userId, int pageNumber = 1, int pageSize = 20)
    {
        try
        {
            var ratings = await _agentRatingRepository.GetByUserIdAsync(userId, pageNumber, pageSize);
            var totalCount = ratings.Count;
            
            var user = await _customerUserRepository.GetByIdAsync(userId);
            var dtos = ratings.Select(r => MapToDto(r, user)).ToList();
            
            var result = new PagedResult<AgentRatingDto>
            {
                Items = dtos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
            
            return Result<PagedResult<AgentRatingDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get ratings by user {UserId}", userId);
            return Result<PagedResult<AgentRatingDto>>.Failure("获取用户评价列表失败");
        }
    }
    
    public async Task<Result> DeleteRatingAsync(Guid agentId, Guid userId)
    {
        try
        {
            var rating = await _agentRatingRepository.GetByAgentAndUserAsync(agentId, userId);
            if (rating == null)
            {
                return Result.Failure("评价不存在");
            }
            
            _agentRatingRepository.Remove(rating);
            await _unitOfWork.SaveChangesAsync();
            
            // 更新Agent的评分统计
            var agent = await _agentRepository.GetByIdAsync(agentId);
            if (agent != null)
            {
                var averageRating = await _agentRatingRepository.CalculateAverageRatingAsync(agentId);
                var ratingCount = await _agentRatingRepository.CountByAgentIdAsync(agentId);
                
                // 这里需要在Agent实体中添加方法来更新评分统计
                // agent.UpdateRatingStatistics(averageRating, ratingCount);
                _agentRepository.Update(agent);
                await _unitOfWork.SaveChangesAsync();
            }
            
            await ClearAgentRatingsCacheAsync(agentId);
            
            _logger.LogInformation("Deleted rating for agent {AgentId} by user {UserId}", agentId, userId);
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete rating for agent {AgentId} by user {UserId}", agentId, userId);
            return Result.Failure("删除评价失败");
        }
    }
    
    public async Task<Result> MarkRatingAsHelpfulAsync(Guid ratingId, Guid userId)
    {
        try
        {
            var rating = await _agentRatingRepository.GetByIdAsync(ratingId);
            if (rating == null)
            {
                return Result.Failure("评价不存在");
            }
            
            // 检查是否已标记
            var existingHelpfulness = await _ratingHelpfulnessRepository.GetByRatingAndUserAsync(ratingId, userId);
            
            if (existingHelpfulness != null)
            {
                if (existingHelpfulness.IsHelpful)
                {
                    return Result.Success(); // 已经标记为有帮助
                }
                
                // 从无帮助改为有帮助
                rating.UnmarkAsUnhelpful();
                rating.MarkAsHelpful();
                existingHelpfulness.Update(true);
                _ratingHelpfulnessRepository.Update(existingHelpfulness);
            }
            else
            {
                // 新标记
                rating.MarkAsHelpful();
                var helpfulness = new RatingHelpfulness(ratingId, userId, true);
                await _ratingHelpfulnessRepository.AddAsync(helpfulness);
            }
            
            _agentRatingRepository.Update(rating);
            await _unitOfWork.SaveChangesAsync();
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark rating {RatingId} as helpful by user {UserId}", ratingId, userId);
            return Result.Failure("标记失败");
        }
    }
    
    public async Task<Result> MarkRatingAsUnhelpfulAsync(Guid ratingId, Guid userId)
    {
        try
        {
            var rating = await _agentRatingRepository.GetByIdAsync(ratingId);
            if (rating == null)
            {
                return Result.Failure("评价不存在");
            }
            
            // 检查是否已标记
            var existingHelpfulness = await _ratingHelpfulnessRepository.GetByRatingAndUserAsync(ratingId, userId);
            
            if (existingHelpfulness != null)
            {
                if (!existingHelpfulness.IsHelpful)
                {
                    return Result.Success(); // 已经标记为无帮助
                }
                
                // 从有帮助改为无帮助
                rating.UnmarkAsHelpful();
                rating.MarkAsUnhelpful();
                existingHelpfulness.Update(false);
                _ratingHelpfulnessRepository.Update(existingHelpfulness);
            }
            else
            {
                // 新标记
                rating.MarkAsUnhelpful();
                var helpfulness = new RatingHelpfulness(ratingId, userId, false);
                await _ratingHelpfulnessRepository.AddAsync(helpfulness);
            }
            
            _agentRatingRepository.Update(rating);
            await _unitOfWork.SaveChangesAsync();
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark rating {RatingId} as unhelpful by user {UserId}", ratingId, userId);
            return Result.Failure("标记失败");
        }
    }
    
    public async Task<Result<AgentRatingStatisticsDto>> GetRatingStatisticsAsync(Guid agentId)
    {
        try
        {
            var cacheKey = $"agent:rating:stats:{agentId}";
            var cached = await _cacheService.GetAsync<AgentRatingStatisticsDto>(cacheKey);
            if (cached != null)
            {
                return Result<AgentRatingStatisticsDto>.Success(cached);
            }
            
            var agent = await _agentRepository.GetByIdAsync(agentId);
            if (agent == null)
            {
                return Result<AgentRatingStatisticsDto>.Failure("Agent不存在");
            }
            
            var ratings = await _agentRatingRepository.GetByAgentIdAsync(agentId, 1, int.MaxValue);
            
            var statistics = new AgentRatingStatisticsDto
            {
                AgentId = agentId,
                AverageRating = agent.AverageRating,
                TotalRatings = agent.RatingCount,
                RatingDistribution = CalculateRatingDistribution(ratings),
                RecentTrend = await CalculateRecentTrendAsync(agentId),
                RecommendationRate = CalculateRecommendationRate(ratings)
            };
            
            await _cacheService.SetAsync(cacheKey, statistics, TimeSpan.FromMinutes(30));
            
            return Result<AgentRatingStatisticsDto>.Success(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get rating statistics for agent {AgentId}", agentId);
            return Result<AgentRatingStatisticsDto>.Failure("获取评价统计失败");
        }
    }
    
    private AgentRatingDto MapToDto(AgentRating rating, Domain.Entities.User.CustomerUser? user)
    {
        return new AgentRatingDto
        {
            Id = rating.Id,
            AgentId = rating.Id, // 这里需要从关联中获取AgentId
            UserId = rating.UserId,
            UserName = user?.Username ?? "匿名用户",
            UserAvatar = user?.Avatar,
            Score = rating.Score,
            Feedback = rating.Feedback,
            RatedAt = rating.RatedAt,
            UpdatedAt = rating.UpdatedAt,
            IsVerifiedPurchase = rating.IsVerifiedPurchase,
            HelpfulCount = rating.HelpfulCount,
            UnhelpfulCount = rating.UnhelpfulCount
        };
    }
    
    private Dictionary<int, int> CalculateRatingDistribution(List<AgentRating> ratings)
    {
        var distribution = new Dictionary<int, int>
        {
            { 1, 0 }, { 2, 0 }, { 3, 0 }, { 4, 0 }, { 5, 0 }
        };
        
        foreach (var rating in ratings)
        {
            if (distribution.ContainsKey(rating.Score))
            {
                distribution[rating.Score]++;
            }
        }
        
        return distribution;
    }
    
    private async Task<List<RatingTrendDto>> CalculateRecentTrendAsync(Guid agentId)
    {
        var endDate = DateTime.UtcNow.Date;
        var startDate = endDate.AddDays(-30);
        var trends = new List<RatingTrendDto>();
        
        // 这里简化实现，实际应该从数据库聚合查询
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            trends.Add(new RatingTrendDto
            {
                Date = date,
                AverageScore = 0,
                Count = 0
            });
        }
        
        return trends;
    }
    
    private double CalculateRecommendationRate(List<AgentRating> ratings)
    {
        if (!ratings.Any())
            return 0;
            
        var recommendCount = ratings.Count(r => r.Score >= 4);
        return Math.Round((double)recommendCount / ratings.Count * 100, 1);
    }
    
    private string GetAgentRatingsCacheKey(Guid agentId, int pageNumber, int pageSize)
    {
        return $"agent:ratings:{agentId}:{pageNumber}:{pageSize}";
    }
    
    private async Task ClearAgentRatingsCacheAsync(Guid agentId)
    {
        var pattern = $"agent:ratings:{agentId}:*";
        await _cacheService.RemoveByPatternAsync(pattern);
        await _cacheService.RemoveAsync($"agent:rating:stats:{agentId}");
    }
}