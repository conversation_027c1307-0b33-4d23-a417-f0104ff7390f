using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Payment;

public class PaymentService : IPaymentService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPaymentGatewayManager _gatewayManager;
    private readonly IConfiguration _configuration;
    private readonly ILogger<PaymentService> _logger;
    private readonly INotificationService _notificationService;
    private readonly IPaymentNotificationService _paymentNotificationService;
    private readonly IPaymentMonitoringService _monitoringService;
    private readonly IPaymentValidationService _validationService;
    private readonly IPaymentMetricsCollector _metricsCollector;

    public PaymentService(
        IUnitOfWork unitOfWork,
        IPaymentGatewayManager gatewayManager,
        IConfiguration configuration,
        ILogger<PaymentService> logger,
        INotificationService notificationService,
        IPaymentNotificationService paymentNotificationService,
        IPaymentMonitoringService monitoringService,
        IPaymentValidationService validationService,
        IPaymentMetricsCollector metricsCollector)
    {
        _unitOfWork = unitOfWork;
        _gatewayManager = gatewayManager;
        _configuration = configuration;
        _logger = logger;
        _notificationService = notificationService;
        _paymentNotificationService = paymentNotificationService;
        _monitoringService = monitoringService;
        _validationService = validationService;
        _metricsCollector = metricsCollector;
    }

    public async Task<Result<PaymentResponseDto>> ProcessPaymentAsync(ProcessPaymentDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get order
            var orderObj = await _unitOfWork.Orders.GetByIdAsync(request.OrderId, cancellationToken);
            if (orderObj is not WhimLabAI.Domain.Entities.Payment.Order order)
            {
                return Result<PaymentResponseDto>.Failure("ORDER_NOT_FOUND", "订单不存在");
            }

            if (order.Status != OrderStatus.Pending)
            {
                return Result<PaymentResponseDto>.Failure("INVALID_ORDER_STATUS", "订单状态不允许支付");
            }

            if (DateTime.UtcNow > order.ExpireAt)
            {
                return Result<PaymentResponseDto>.Failure("ORDER_EXPIRED", "订单已过期");
            }

            // Enhanced amount validation
            if (request.Amount.HasValue)
            {
                // Verify the requested payment amount matches the order amount
                if (Math.Abs(request.Amount.Value - order.PayableAmount.Amount) > 0.01m)
                {
                    _logger.LogWarning("Payment amount mismatch: OrderId={OrderId}, OrderAmount={OrderAmount}, RequestAmount={RequestAmount}",
                        order.Id, order.PayableAmount.Amount, request.Amount.Value);
                    return Result<PaymentResponseDto>.Failure("AMOUNT_MISMATCH", 
                        $"支付金额不匹配：订单金额为 ¥{order.PayableAmount.Amount:F2}");
                }
            }

            // Additional security checks for payment amount
            if (order.PayableAmount.Amount <= 0)
            {
                _logger.LogError("Invalid order amount: OrderId={OrderId}, Amount={Amount}", 
                    order.Id, order.PayableAmount.Amount);
                return Result<PaymentResponseDto>.Failure("INVALID_AMOUNT", "订单金额无效");
            }

            // Check maximum payment amount limit
            var maxPaymentAmount = _configuration.GetValue<decimal>("Payment:Security:MaxPaymentAmount", 100000m);
            if (order.PayableAmount.Amount > maxPaymentAmount)
            {
                _logger.LogWarning("Payment amount exceeds limit: OrderId={OrderId}, Amount={Amount}, Limit={Limit}",
                    order.Id, order.PayableAmount.Amount, maxPaymentAmount);
                return Result<PaymentResponseDto>.Failure("AMOUNT_EXCEEDS_LIMIT", 
                    $"支付金额超过限制：最大允许金额为 ¥{maxPaymentAmount:F2}");
            }

            // Check for duplicate payment
            var duplicatePayment = await _unitOfWork.Payments.HasDuplicatePaymentAsync(
                order.Id, 
                request.PaymentMethod.ToString(), 
                order.PayableAmount.Amount, 
                TimeSpan.FromMinutes(5), 
                cancellationToken);

            if (duplicatePayment)
            {
                return Result<PaymentResponseDto>.Failure("DUPLICATE_PAYMENT", "请勿重复支付");
            }

            // Create payment record
            var payment = new Domain.Entities.Payment.PaymentTransaction(
                order.Id,
                order.PayableAmount,
                request.PaymentMethod,
                transactionId: null,
                paymentNo: GeneratePaymentNo(),
                expireAt: order.ExpireAt
            );

            await _unitOfWork.Payments.AddAsync(payment, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Call payment gateway
            var gateway = _gatewayManager.GetGateway(request.PaymentMethod);
            var paymentRequest = new PaymentCreateRequest
            {
                OrderNo = order.OrderNo,
                PaymentNo = payment.PaymentNo,
                Amount = payment.Amount.Amount,
                Currency = payment.Amount.Currency,
                Subject = GetOrderSubject(order),
                Description = order.Remark,
                UserId = order.CustomerUserId.ToString(),
                UserIp = request.ClientIp,
                ReturnUrl = request.ReturnUrl ?? GetDefaultReturnUrl(request.PaymentMethod),
                NotifyUrl = GetNotifyUrl(request.PaymentMethod),
                ExpireTime = payment.ExpireAt ?? DateTime.UtcNow.AddMinutes(30)
            };

            // Monitor the payment creation
            var gatewayResult = await _monitoringService.MonitorPaymentOperationAsync(
                async () => await gateway.CreatePaymentAsync(paymentRequest, cancellationToken),
                "CreatePayment",
                request.PaymentMethod,
                payment.PaymentNo);
            
            if (!gatewayResult.IsSuccess)
            {
                // Update payment status to failed
                payment.MarkAsFailed(gatewayResult.Error);
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                
                return Result<PaymentResponseDto>.Failure("GATEWAY_ERROR", gatewayResult.Error ?? "支付网关错误");
            }

            // Store gateway response if it's HTML form
            if (!string.IsNullOrEmpty(gatewayResult.Value!.PaymentUrl) && gatewayResult.Value.PaymentUrl.Contains("<form"))
            {
                payment.AddRawData("GatewayResponse", gatewayResult.Value.PaymentUrl);
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }

            var response = new PaymentResponseDto
            {
                PaymentId = payment.Id,
                PaymentNo = payment.PaymentNo,
                PaymentMethod = request.PaymentMethod,
                Amount = payment.Amount.Amount,
                PaymentUrl = gatewayResult.Value!.PaymentUrl,
                QrCode = gatewayResult.Value.QrCode,
                FormData = gatewayResult.Value.FormData,
                PaymentType = gatewayResult.Value.Type,
                ExpireAt = payment.ExpireAt ?? DateTime.UtcNow.AddMinutes(30)
            };

            _logger.LogInformation("Payment created: {PaymentNo} for order {OrderNo}", payment.PaymentNo, order.OrderNo);
            return Result<PaymentResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing payment for order {OrderId}", request.OrderId);
            return Result<PaymentResponseDto>.Failure("PAYMENT_ERROR", "处理支付失败");
        }
    }

    public async Task<Result<PaymentCallbackResultDto>> ProcessCallbackAsync(PaymentMethod paymentMethod, Dictionary<string, string> parameters, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            _logger.LogInformation("Processing payment callback for {PaymentMethod}", paymentMethod);

            // Get payment gateway
            var gateway = _gatewayManager.GetGateway(paymentMethod);
            var callbackResult = await gateway.ProcessCallbackAsync(parameters, cancellationToken);
            
            if (!callbackResult.IsSuccess)
            {
                return Result<PaymentCallbackResultDto>.Failure("CALLBACK_ERROR", callbackResult.Error ?? "回调处理失败");
            }

            var callback = callbackResult.Value!;
            
            // Validate callback data
            var validationResult = await _validationService.ValidatePaymentCallbackAsync(
                callback.PaymentNo,
                callback.Amount,
                callback.TransactionId,
                cancellationToken);
                
            if (!validationResult.IsSuccess)
            {
                return Result<PaymentCallbackResultDto>.Failure(
                    validationResult.ErrorCode!,
                    validationResult.Error!);
            }
            
            var validation = validationResult.Value!;
            if (validation.IsAlreadyProcessed)
            {
                _logger.LogInformation("Payment already processed: {PaymentNo}", callback.PaymentNo);
                return Result<PaymentCallbackResultDto>.Success(new PaymentCallbackResultDto
                {
                    Success = true,
                    PaymentNo = callback.PaymentNo,
                    TransactionId = callback.TransactionId,
                    Status = validation.CurrentStatus,
                    Amount = callback.Amount,
                    ResponseMessage = "Already processed"
                });
            }
            
            // Find payment
            var paymentObj = await _unitOfWork.Payments.GetByPaymentNoAsync(callback.PaymentNo, cancellationToken);
            if (paymentObj is not Domain.Entities.Payment.PaymentTransaction payment)
            {
                _logger.LogWarning("Payment not found for callback: {PaymentNo}", callback.PaymentNo);
                return Result<PaymentCallbackResultDto>.Failure("PAYMENT_NOT_FOUND", "支付记录不存在");
            }

            // Begin transaction
            await _unitOfWork.BeginTransactionAsync(cancellationToken: cancellationToken);

            try
            {
                // Update payment status
                var updated = await _unitOfWork.Payments.UpdatePaymentStatusAsync(
                    payment.Id, 
                    callback.Status, 
                    callback.TransactionId, 
                    cancellationToken);

                if (!updated)
                {
                    await _unitOfWork.RollbackAsync(cancellationToken);
                    return Result<PaymentCallbackResultDto>.Failure("UPDATE_FAILED", "更新支付状态失败");
                }

                // Save callback data
                await _unitOfWork.Payments.SavePaymentCallbackAsync(payment.Id, JsonSerializer.Serialize(parameters), cancellationToken);

                // Update order status if payment succeeded
                if (callback.Status == TransactionStatus.Success)
                {
                    // Get order and mark as paid
                    var orderObj = await _unitOfWork.Orders.GetByIdAsync(payment.OrderId, cancellationToken);
                    if (orderObj is Domain.Entities.Payment.Order orderToPay && callback.TransactionId != null)
                    {
                        orderToPay.MarkAsPaid(callback.TransactionId, callback.PaidAt);
                        await _unitOfWork.SaveChangesAsync(cancellationToken);
                    }
                    
                    // Handle order completion based on order type
                    await HandleOrderCompletionAsync(payment.OrderId, cancellationToken);
                    
                    // Send async notification
                    await _paymentNotificationService.SendPaymentNotificationAsync(
                        validation.CustomerUserId ?? Guid.Empty,
                        validation.OrderNo ?? "",
                        callback.Amount,
                        callback.Status,
                        cancellationToken: cancellationToken);
                }
                else if (callback.Status == TransactionStatus.Failed)
                {
                    // Send payment failure notification through notification service
                    await _paymentNotificationService.SendPaymentNotificationAsync(
                        validation.CustomerUserId ?? Guid.Empty,
                        validation.OrderNo ?? "",
                        callback.Amount,
                        callback.Status,
                        callback.ResponseMessage,
                        cancellationToken);
                }

                await _unitOfWork.CommitAsync(cancellationToken);

                var result = new PaymentCallbackResultDto
                {
                    Success = true,
                    PaymentNo = callback.PaymentNo,
                    TransactionId = callback.TransactionId,
                    Status = callback.Status,
                    Amount = callback.Amount,
                    PaidAt = callback.PaidAt,
                    ResponseMessage = callback.ResponseMessage
                };

                // Record metrics
                var duration = DateTime.UtcNow - startTime;
                _metricsCollector.RecordPayment(
                    paymentMethod,
                    callback.Amount,
                    callback.Status,
                    duration);
                    
                _logger.LogInformation("Payment callback processed: {PaymentNo}, Status: {Status}, Duration: {Duration}ms", 
                    callback.PaymentNo, callback.Status, duration.TotalMilliseconds);

                return Result<PaymentCallbackResultDto>.Success(result);
            }
            catch
            {
                await _unitOfWork.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing payment callback");
            _metricsCollector.RecordException(ex, "ProcessCallback", paymentMethod);
            
            // Alert on failure
            await _monitoringService.AlertOnPaymentFailureAsync(
                "UNKNOWN",
                paymentMethod,
                "Callback processing error",
                ex);
                
            return Result<PaymentCallbackResultDto>.Failure("CALLBACK_ERROR", "处理支付回调失败");
        }
    }

    public async Task<Result<RefundResponseDto>> ProcessRefundAsync(ProcessRefundDto request, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            // Validate refund request using validation service
            var validationResult = await _validationService.ValidateRefundRequestAsync(
                request.OrderId,
                request.PaymentId,
                request.RefundAmount,
                cancellationToken);
                
            if (!validationResult.IsSuccess)
            {
                return Result<RefundResponseDto>.Failure(
                    validationResult.ErrorCode!,
                    validationResult.Error!);
            }
            
            var validation = validationResult.Value!;
            
            // Get payment
            var paymentObj = await _unitOfWork.Payments.GetByIdAsync(request.PaymentId, cancellationToken);
            if (paymentObj is not Domain.Entities.Payment.PaymentTransaction payment)
            {
                return Result<RefundResponseDto>.Failure("PAYMENT_NOT_FOUND", "支付记录不存在");
            }

            // Begin transaction
            await _unitOfWork.BeginTransactionAsync(cancellationToken: cancellationToken);

            try
            {
                // Create refund record
                var refundAmount = Money.Create(request.RefundAmount, payment.Amount.Currency);
                var refund = new RefundRecord(
                    orderId: request.OrderId,
                    refundAmount: refundAmount,
                    reason: request.Reason,
                    refundNo: GenerateRefundNo(),
                    remark: null
                );

                await _unitOfWork.Refunds.AddAsync(refund, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                // Call payment gateway
                var gateway = _gatewayManager.GetGateway(payment.PaymentMethod);
                var refundRequest = new RefundCreateRequest
                {
                    RefundNo = refund.RefundNo,
                    PaymentNo = payment.PaymentNo,
                    TransactionId = payment.TransactionId!,
                    RefundAmount = request.RefundAmount,
                    TotalAmount = payment.Amount.Amount,
                    Reason = request.Reason,
                    NotifyUrl = GetRefundNotifyUrl(payment.PaymentMethod)
                };

                // Monitor the refund operation
                var gatewayResult = await _monitoringService.MonitorPaymentOperationAsync(
                    async () => await gateway.RefundAsync(refundRequest, cancellationToken),
                    "CreateRefund",
                    payment.PaymentMethod,
                    refund.RefundNo);
                
                if (!gatewayResult.IsSuccess)
                {
                    // Update refund status to failed
                    await _unitOfWork.Refunds.UpdateRefundStatusAsync(refund.Id, WhimLabAI.Shared.Enums.RefundStatus.Failed, null, gatewayResult.Error, cancellationToken);
                    await _unitOfWork.CommitAsync(cancellationToken);
                    
                    return Result<RefundResponseDto>.Failure(gatewayResult.Error ?? "Gateway refund failed", gatewayResult.ErrorCode);
                }

                // Update refund status
                await _unitOfWork.Refunds.UpdateRefundStatusAsync(
                    refund.Id, 
                    gatewayResult.Value!.Status, 
                    gatewayResult.Value.RefundTransactionId, 
                    null,
                    cancellationToken);

                // If refund is successful, update order status
                if (gatewayResult.Value.Status == WhimLabAI.Shared.Enums.RefundStatus.Completed)
                {
                    // Refund status will be updated by domain logic when all refunds are completed
                }

                await _unitOfWork.CommitAsync(cancellationToken);
                
                // Record metrics
                var duration = DateTime.UtcNow - startTime;
                _metricsCollector.RecordRefund(
                    payment.PaymentMethod,
                    request.RefundAmount,
                    gatewayResult.Value.Status,
                    duration);

                // Send refund notification
                var orderObj = await _unitOfWork.Orders.GetByIdAsync(request.OrderId, cancellationToken);
                if (orderObj is Domain.Entities.Payment.Order order)
                {
                    await _paymentNotificationService.SendRefundNotificationAsync(
                        order.CustomerUserId,
                        refund.RefundNo,
                        request.RefundAmount,
                        gatewayResult.Value.Status,
                        cancellationToken: cancellationToken);
                }

                var response = new RefundResponseDto
                {
                    RefundId = refund.Id,
                    RefundNo = refund.RefundNo,
                    Status = gatewayResult.Value.Status,
                    RefundAmount = request.RefundAmount,
                    ExpectedArrivalTime = gatewayResult.Value.RefundedAt
                };

                _logger.LogInformation("Refund initiated: {RefundNo} for order {OrderId}, Duration: {Duration}ms", 
                    refund.RefundNo, request.OrderId, duration.TotalMilliseconds);
                return Result<RefundResponseDto>.Success(response);
            }
            catch
            {
                await _unitOfWork.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing refund for order {OrderId}", request.OrderId);
            _metricsCollector.RecordException(ex, "ProcessRefund");
            return Result<RefundResponseDto>.Failure("REFUND_ERROR", "处理退款失败");
        }
    }

    public async Task<Result<PaymentDto>> GetPaymentAsync(Guid paymentId, CancellationToken cancellationToken = default)
    {
        try
        {
            var paymentObj = await _unitOfWork.Payments.GetByIdAsync(paymentId, cancellationToken);
            if (paymentObj is not Domain.Entities.Payment.PaymentTransaction payment)
            {
                return Result<PaymentDto>.Failure("PAYMENT_NOT_FOUND", "支付记录不存在");
            }

            var orderObj = await _unitOfWork.Orders.GetByIdAsync(payment.OrderId, cancellationToken);
            var order = orderObj as Domain.Entities.Payment.Order;

            var dto = MapToPaymentDto(payment, order?.OrderNo ?? "");
            return Result<PaymentDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment {PaymentId}", paymentId);
            return Result<PaymentDto>.Failure("GET_PAYMENT_ERROR", "获取支付记录失败");
        }
    }

    public async Task<Result<PaymentDto>> GetPaymentByNoAsync(string paymentNo, CancellationToken cancellationToken = default)
    {
        try
        {
            var paymentObj = await _unitOfWork.Payments.GetByPaymentNoAsync(paymentNo, cancellationToken);
            if (paymentObj is not Domain.Entities.Payment.PaymentTransaction payment)
            {
                return Result<PaymentDto>.Failure("PAYMENT_NOT_FOUND", "支付记录不存在");
            }

            var orderObj = await _unitOfWork.Orders.GetByIdAsync(payment.OrderId, cancellationToken);
            var order = orderObj as Domain.Entities.Payment.Order;

            var dto = MapToPaymentDto(payment, order?.OrderNo ?? "");
            return Result<PaymentDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment by no {PaymentNo}", paymentNo);
            return Result<PaymentDto>.Failure("GET_PAYMENT_ERROR", "获取支付记录失败");
        }
    }

    public async Task<Result<PagedResult<PaymentDto>>> GetOrderPaymentsAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        try
        {
            var paymentsObj = await _unitOfWork.Payments.GetOrderPaymentsAsync(orderId, cancellationToken);
            var payments = paymentsObj.Cast<Domain.Entities.Payment.PaymentTransaction>().ToList();

            var orderObj = await _unitOfWork.Orders.GetByIdAsync(orderId, cancellationToken);
            var order = orderObj as Domain.Entities.Payment.Order;

            var paymentDtos = payments.Select(p => MapToPaymentDto(p, order?.OrderNo ?? "")).ToList();
            var result = new PagedResult<PaymentDto>(paymentDtos, paymentDtos.Count, 1, paymentDtos.Count);
            
            return Result<PagedResult<PaymentDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order payments {OrderId}", orderId);
            return Result<PagedResult<PaymentDto>>.Failure("GET_PAYMENTS_ERROR", "获取支付记录失败");
        }
    }

    public async Task<Result<RefundDto>> GetRefundAsync(Guid refundId, CancellationToken cancellationToken = default)
    {
        try
        {
            var refundObj = await _unitOfWork.Refunds.GetByIdAsync(refundId, cancellationToken);
            if (refundObj is not RefundRecord refund)
            {
                return Result<RefundDto>.Failure("REFUND_NOT_FOUND", "退款记录不存在");
            }

            var dto = await MapToRefundDtoAsync(refund, cancellationToken);
            return Result<RefundDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting refund {RefundId}", refundId);
            return Result<RefundDto>.Failure("GET_REFUND_ERROR", "获取退款记录失败");
        }
    }

    public async Task<Result<PagedResult<RefundDto>>> GetOrderRefundsAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        try
        {
            var refundsObj = await _unitOfWork.Refunds.GetOrderRefundsAsync(orderId, cancellationToken);
            var refunds = refundsObj.Cast<RefundRecord>().ToList();

            var refundDtos = new List<RefundDto>();
            foreach (var refund in refunds)
            {
                var dto = await MapToRefundDtoAsync(refund, cancellationToken);
                refundDtos.Add(dto);
            }

            var result = new PagedResult<RefundDto>(refundDtos, refundDtos.Count, 1, refundDtos.Count);
            return Result<PagedResult<RefundDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order refunds {OrderId}", orderId);
            return Result<PagedResult<RefundDto>>.Failure("GET_REFUNDS_ERROR", "获取退款记录失败");
        }
    }

    public async Task<Result> CheckPaymentStatusAsync(string paymentNo, CancellationToken cancellationToken = default)
    {
        try
        {
            var paymentObj = await _unitOfWork.Payments.GetByPaymentNoAsync(paymentNo, cancellationToken);
            if (paymentObj is not Domain.Entities.Payment.PaymentTransaction payment)
            {
                return Result.Failure("PAYMENT_NOT_FOUND", "支付记录不存在");
            }

            // Query payment gateway for latest status
            var gateway = _gatewayManager.GetGateway(payment.PaymentMethod);
            var queryResult = await gateway.QueryPaymentAsync(paymentNo, cancellationToken);
            
            if (!queryResult.IsSuccess)
            {
                return Result.Failure(queryResult.Error ?? "Query failed", queryResult.ErrorCode);
            }

            // Update payment status if changed
            if (queryResult.Value!.Status != payment.Status)
            {
                await _unitOfWork.Payments.UpdatePaymentStatusAsync(
                    payment.Id, 
                    queryResult.Value!.Status, 
                    queryResult.Value.TransactionId, 
                    cancellationToken);

                // Update order status if payment succeeded
                if (queryResult.Value!.Status == TransactionStatus.Success)
                {
                    // Get order and mark as paid
                    var orderObj = await _unitOfWork.Orders.GetByIdAsync(payment.OrderId, cancellationToken);
                    if (orderObj is Domain.Entities.Payment.Order orderToPay && queryResult.Value.TransactionId != null)
                    {
                        orderToPay.MarkAsPaid(queryResult.Value.TransactionId);
                        await _unitOfWork.SaveChangesAsync(cancellationToken);
                        await HandleOrderCompletionAsync(payment.OrderId, cancellationToken);
                    }
                }
            }

            _logger.LogInformation("Checked payment status for {PaymentNo}: {Status}", paymentNo, queryResult.Value.Status);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking payment status {PaymentNo}", paymentNo);
            return Result.Failure("CHECK_STATUS_ERROR", "查询支付状态失败");
        }
    }

    public async Task<Result> CheckRefundStatusAsync(string refundNo, CancellationToken cancellationToken = default)
    {
        try
        {
            var refundObj = await _unitOfWork.Refunds.GetByRefundNoAsync(refundNo, cancellationToken);
            if (refundObj is not RefundRecord refund)
            {
                return Result.Failure("REFUND_NOT_FOUND", "退款记录不存在");
            }

            // Get payment through order
            var orderObj = await _unitOfWork.Orders.GetByIdAsync(refund.OrderId, cancellationToken);
            if (orderObj is not Domain.Entities.Payment.Order order)
            {
                return Result.Failure("ORDER_NOT_FOUND", "订单不存在");
            }
            
            var paymentObj = await _unitOfWork.Payments.GetByOrderIdAsync(order.Id, cancellationToken);
            if (paymentObj is not Domain.Entities.Payment.PaymentTransaction payment)
            {
                return Result.Failure("PAYMENT_NOT_FOUND", "支付记录不存在");
            }

            // Query payment gateway for latest refund status
            var gateway = _gatewayManager.GetGateway(payment.PaymentMethod);
            var queryResult = await gateway.QueryRefundAsync(refundNo, cancellationToken);
            
            if (!queryResult.IsSuccess)
            {
                return Result.Failure(queryResult.Error ?? "Query failed", queryResult.ErrorCode);
            }

            // Update refund status if changed
            if (queryResult.Value!.Status != refund.Status)
            {
                // Update refund status using domain methods
                switch (queryResult.Value.Status)
                {
                    case RefundStatus.Completed:
                        refund.MarkAsCompleted(queryResult.Value.RefundTransactionId);
                        break;
                    case RefundStatus.Failed:
                        refund.MarkAsFailed("退款失败");
                        break;
                    case RefundStatus.Processing:
                        refund.StartProcessing();
                        break;
                }
                _unitOfWork.Refunds.Update(refund);

                // Update order status if refund succeeded
                if (queryResult.Value.Status == WhimLabAI.Shared.Enums.RefundStatus.Completed)
                {
                    // Get order and complete the refund
                    var orderToRefundObj = await _unitOfWork.Orders.GetByIdAsync(refund.OrderId, cancellationToken);
                    if (orderToRefundObj is Domain.Entities.Payment.Order orderToRefund)
                    {
                        orderToRefund.CompleteRefund(refund.RefundNo);
                        await _unitOfWork.SaveChangesAsync(cancellationToken);
                    }
                }
            }

            _logger.LogInformation("Checked refund status for {RefundNo}: {Status}", refundNo, queryResult.Value.Status);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking refund status {RefundNo}", refundNo);
            return Result.Failure("CHECK_STATUS_ERROR", "查询退款状态失败");
        }
    }

    public async Task<Result<PaymentStatisticsDto>> GetPaymentStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var paymentStatsObj = await _unitOfWork.Payments.GetPaymentStatisticsAsync(startDate, endDate, cancellationToken);
            var paymentStats = paymentStatsObj as dynamic;

            // Calculate refund statistics manually
            var allRefunds = await _unitOfWork.Refunds.GetAsync(r => r.CreatedAt >= startDate && r.CreatedAt <= endDate, cancellationToken);
            var refundsList = allRefunds.ToList();
            
            var refundStats = new
            {
                TotalRefunds = refundsList.Count,
                SuccessfulRefunds = refundsList.Count(r => r.Status == RefundStatus.Completed),
                TotalRefundAmount = refundsList.Where(r => r.Status == RefundStatus.Completed).Sum(r => r.RefundAmount.Amount)
            };

            var dto = new PaymentStatisticsDto
            {
                TotalPayments = paymentStats?.TotalPayments ?? 0,
                SuccessfulPayments = paymentStats?.SuccessfulPayments ?? 0,
                FailedPayments = paymentStats?.FailedPayments ?? 0,
                TotalAmount = paymentStats?.TotalAmount ?? 0m,
                SuccessRate = paymentStats?.SuccessRate ?? 0,
                RefundStats = new RefundStatisticsDto
                {
                    TotalRefunds = refundStats?.TotalRefunds ?? 0,
                    SuccessfulRefunds = refundStats?.SuccessfulRefunds ?? 0,
                    TotalAmount = refundStats?.TotalRefundAmount ?? 0m,
                    AverageProcessingTime = 0 // Calculate average processing time if needed
                }
            };

            // Convert payment method stats
            if (paymentStats?.PaymentsByMethod != null)
            {
                foreach (var kvp in paymentStats.PaymentsByMethod)
                {
                    dto.PaymentsByMethod[kvp.Key] = new PaymentMethodStatDto
                    {
                        Count = kvp.Value.Count,
                        Amount = kvp.Value.Amount,
                        SuccessRate = (double)kvp.Value.Count / (paymentStats.TotalPayments ?? 1) * 100
                    };
                }
            }

            // Convert daily stats
            if (paymentStats?.DailyPayments != null)
            {
                foreach (var kvp in paymentStats.DailyPayments)
                {
                    dto.DailyPayments[kvp.Key] = new DailyPaymentStatDto
                    {
                        Count = kvp.Value.Count,
                        Amount = kvp.Value.Amount
                    };
                }
            }

            return Result<PaymentStatisticsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment statistics");
            return Result<PaymentStatisticsDto>.Failure("STATS_ERROR", "获取支付统计失败");
        }
    }

    private async Task<RefundDto> MapToRefundDtoAsync(RefundRecord refund, CancellationToken cancellationToken)
    {
        var orderObj = await _unitOfWork.Orders.GetByIdAsync(refund.OrderId, cancellationToken);
        var order = orderObj as Domain.Entities.Payment.Order;

        // RefundRecord doesn't have PaymentId property, need to get payment from order
        var payments = await _unitOfWork.Payments.GetOrderPaymentsAsync(refund.OrderId, cancellationToken);
        var payment = payments.FirstOrDefault() as Domain.Entities.Payment.PaymentTransaction;

        return new RefundDto
        {
            Id = refund.Id,
            OrderId = refund.OrderId,
            OrderNo = order?.OrderNo ?? "",
            PaymentId = payment?.Id ?? Guid.Empty,
            PaymentNo = payment?.PaymentNo ?? "",
            RefundNo = refund.RefundNo,
            RefundAmount = refund.RefundAmount.Amount,
            TotalAmount = payment?.Amount.Amount ?? 0,
            Status = refund.Status,
            Reason = refund.Reason,
            TransactionId = refund.RefundTransactionId,
            CreatedAt = refund.CreatedAt,
            ProcessedAt = refund.ProcessedAt,
            RefundedAt = refund.CompletedAt,
            FailedAt = null, // RefundRecord doesn't have FailedAt
            FailReason = refund.FailureReason,
            OperatorId = refund.ProcessedBy
        };
    }

    private async Task HandleOrderCompletionAsync(Guid orderId, CancellationToken cancellationToken)
    {
        var orderObj = await _unitOfWork.Orders.GetByIdAsync(orderId, cancellationToken);
        if (orderObj is not Domain.Entities.Payment.Order order)
        {
            return;
        }

        // Handle based on order type
        if (order.Type == OrderType.Subscription && order.ProductId.HasValue)
        {
            // Create or upgrade subscription
            await CreateOrUpgradeSubscriptionAsync(order.CustomerUserId, order.ProductId.Value, orderId, cancellationToken);
        }
        // Handle other product types...

        // Order status is already updated appropriately (Paid/Refunded)
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        // Send payment success notification
        try
        {
            var productName = "订阅服务";
            if (order.ProductId.HasValue && order.Type == OrderType.Subscription)
            {
                var planObj = await _unitOfWork.SubscriptionPlans.GetByIdAsync(order.ProductId.Value, cancellationToken);
                if (planObj is Domain.Entities.Subscription.SubscriptionPlan plan)
                {
                    productName = plan.Name;
                }
            }
            
            await _notificationService.SendNotificationAsync(new SendNotificationDto
            {
                UserId = order.CustomerUserId,
                Title = "支付成功",
                Content = $"您的订单 {order.OrderNo} 支付成功，金额：¥{order.FinalAmount}。感谢您的购买！",
                Type = "payment",
                Level = "success",
                Metadata = new Dictionary<string, object>
                {
                    ["orderId"] = order.Id,
                    ["orderNo"] = order.OrderNo,
                    ["amount"] = order.FinalAmount,
                    ["productName"] = productName,
                    ["orderType"] = order.Type.ToString()
                }
            }, cancellationToken);
        }
        catch (Exception notifyEx)
        {
            _logger.LogError(notifyEx, "Failed to send payment success notification for order {OrderId}", orderId);
        }
    }

    private async Task CreateOrUpgradeSubscriptionAsync(Guid userId, Guid planId, Guid orderId, CancellationToken cancellationToken)
    {
        try
        {
            var planObj = await _unitOfWork.SubscriptionPlans.GetByIdAsync(planId, cancellationToken);
            if (planObj is not Domain.Entities.Subscription.SubscriptionPlan plan)
            {
                _logger.LogWarning("Subscription plan not found: {PlanId}", planId);
                return;
            }

            // Check existing subscription
            var existingSubObj = await _unitOfWork.Subscriptions.GetActiveSubscriptionByUserIdAsync(userId, cancellationToken);
            
            if (existingSubObj != null)
            {
                // Upgrade existing subscription
                var existingSub = existingSubObj as Domain.Entities.Subscription.Subscription;
                if (existingSub != null)
                {
                    // Upgrade subscription by updating plan
                    existingSub.PlanId = planId;
                    existingSub.NextPlanId = planId;
                    _unitOfWork.Subscriptions.Update(existingSub);
                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Upgraded subscription for user {UserId} to plan {PlanId}", userId, planId);
                }
            }
            else
            {
                // Create new subscription
                var subscription = new Domain.Entities.Subscription.Subscription(
                    userId,
                    planId,
                    DateTime.UtcNow,
                    plan.Tier == SubscriptionTier.Free ? null : DateTime.UtcNow.AddDays(plan.DurationDays)
                );

                await _unitOfWork.Subscriptions.AddAsync(subscription, cancellationToken);
                _logger.LogInformation("Created subscription for user {UserId} with plan {PlanId}", userId, planId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating/upgrading subscription for user {UserId}", userId);
        }
    }

    private string GeneratePaymentNo()
    {
        return $"PAY{DateTime.UtcNow:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";
    }

    private string GenerateRefundNo()
    {
        return $"REF{DateTime.UtcNow:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";
    }

    private string GetOrderSubject(Domain.Entities.Payment.Order order)
    {
        return order.ProductName ?? "订单支付";
    }

    private string GetDefaultReturnUrl(PaymentMethod method)
    {
        var baseUrl = _configuration["Payment:BaseUrl"] ?? "https://whimlabai.com";
        return $"{baseUrl}/payment/return/{method.ToString().ToLower()}";
    }

    private string GetNotifyUrl(PaymentMethod method)
    {
        var baseUrl = _configuration["Payment:CallbackUrl"] ?? "https://api.whimlabai.com";
        return $"{baseUrl}/api/payment/callback/{method.ToString().ToLower()}";
    }

    private string GetRefundNotifyUrl(PaymentMethod method)
    {
        var baseUrl = _configuration["Payment:CallbackUrl"] ?? "https://api.whimlabai.com";
        return $"{baseUrl}/api/payment/refund-callback/{method.ToString().ToLower()}";
    }

    private PaymentDto MapToPaymentDto(PaymentTransaction payment, string orderNo)
    {
        return new PaymentDto
        {
            Id = payment.Id,
            OrderId = payment.OrderId,
            OrderNo = orderNo,
            PaymentNo = payment.PaymentNo ?? payment.Id.ToString(),
            PaymentMethod = payment.PaymentMethod,
            Amount = payment.Amount.Amount,
            Currency = payment.Amount.Currency,
            Status = payment.Status,
            TransactionId = payment.TransactionId,
            CreatedAt = payment.CreatedAt,
            PaidAt = payment.CompletedAt,
            FailedAt = payment.FailedAt,
            ExpireAt = payment.ExpireAt ?? DateTime.UtcNow.AddMinutes(30),
            FailReason = payment.FailReason,
            PayerAccount = payment.PayerAccount,
            GatewayResponse = payment.RawData.GetValueOrDefault("GatewayResponse")
        };
    }
    
    private TransactionStatus ConvertToTransactionStatus(PaymentStatus paymentStatus)
    {
        return paymentStatus switch
        {
            PaymentStatus.Pending => TransactionStatus.Pending,
            PaymentStatus.Processing => TransactionStatus.Pending,
            PaymentStatus.Success => TransactionStatus.Success,
            PaymentStatus.Failed => TransactionStatus.Failed,
            PaymentStatus.Cancelled => TransactionStatus.Cancelled,
            PaymentStatus.Refunded => TransactionStatus.Success, // Refunded is still a successful payment
            PaymentStatus.PartiallyRefunded => TransactionStatus.Success,
            _ => TransactionStatus.Failed
        };
    }
}