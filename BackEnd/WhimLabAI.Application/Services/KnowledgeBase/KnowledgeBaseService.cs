using System.Text.Json;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.KnowledgeBase;

/// <summary>
/// 知识库服务实现
/// </summary>
public class KnowledgeBaseService : IKnowledgeBaseService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IVectorDatabaseFactory _vectorDbFactory;
    private readonly IDocumentProcessor _documentProcessor;
    private readonly IAIProviderManager _aiProviderManager;
    private readonly ICacheService _cacheService;
    private readonly ILogger<KnowledgeBaseService> _logger;

    public KnowledgeBaseService(
        IUnitOfWork unitOfWork,
        IVectorDatabaseFactory vectorDbFactory,
        IDocumentProcessor documentProcessor,
        IAIProviderManager aiProviderManager,
        ICacheService cacheService,
        ILogger<KnowledgeBaseService> logger)
    {
        _unitOfWork = unitOfWork;
        _vectorDbFactory = vectorDbFactory;
        _documentProcessor = documentProcessor;
        _aiProviderManager = aiProviderManager;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<Result<KnowledgeBaseDto>> CreateKnowledgeBaseAsync(
        CreateKnowledgeBaseDto dto,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if knowledge base with same name already exists
            var existing = await _unitOfWork.KnowledgeBases.GetByNameAndOwnerAsync(
                dto.Name, userId, OwnerType.User, cancellationToken);
            
            if (existing != null)
            {
                return Result<KnowledgeBaseDto>.Failure("DUPLICATE_NAME", "同名知识库已存在");
            }

            // Create knowledge base entity
            var knowledgeBase = new Domain.Entities.KnowledgeBase.KnowledgeBase(
                dto.Name,
                userId,
                OwnerType.User,
                dto.VectorDbType,
                dto.EmbeddingModel,
                dto.Description);

            if (!string.IsNullOrEmpty(dto.VectorDbConfig))
            {
                knowledgeBase.UpdateVectorDbConfig(dto.VectorDbConfig);
            }

            if (!string.IsNullOrEmpty(dto.ChunkingConfig))
            {
                knowledgeBase.UpdateChunkingConfig(dto.ChunkingConfig);
            }

            knowledgeBase.SetPublic(dto.IsPublic);

            // Create vector database collection
            var vectorDb = _vectorDbFactory.GetVectorDatabase(dto.VectorDbType, dto.VectorDbConfig ?? "{}");
            var collectionName = $"kb_{knowledgeBase.Id}";
            
            var createResult = await vectorDb.CreateCollectionAsync(
                collectionName,
                knowledgeBase.VectorDimension,
                JsonSerializer.Deserialize<Dictionary<string, object>>(dto.VectorDbConfig ?? "{}"),
                cancellationToken);

            if (!createResult.IsSuccess)
            {
                return Result<KnowledgeBaseDto>.Failure("VECTOR_DB_ERROR", "创建向量数据库集合失败");
            }

            await _unitOfWork.KnowledgeBases.AddAsync(knowledgeBase, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            return Result<KnowledgeBaseDto>.Success(MapToDto(knowledgeBase));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating knowledge base");
            return Result<KnowledgeBaseDto>.Failure("CREATE_ERROR", "创建知识库失败");
        }
    }

    public async Task<Result<KnowledgeBaseDto>> UpdateKnowledgeBaseAsync(
        Guid knowledgeBaseId,
        UpdateKnowledgeBaseDto dto,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var knowledgeBase = await _unitOfWork.KnowledgeBases.GetByIdAsync(knowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<KnowledgeBaseDto>.Failure("NOT_FOUND", "知识库不存在");
            }

            // Check ownership
            if (knowledgeBase.OwnerId != userId)
            {
                return Result<KnowledgeBaseDto>.Failure("UNAUTHORIZED", "无权操作此知识库");
            }

            // Update fields
            if (!string.IsNullOrEmpty(dto.Name))
            {
                var existing = await _unitOfWork.KnowledgeBases.GetByNameAndOwnerAsync(
                    dto.Name, userId, OwnerType.User, cancellationToken);
                
                if (existing != null && existing.Id != knowledgeBaseId)
                {
                    return Result<KnowledgeBaseDto>.Failure("DUPLICATE_NAME", "同名知识库已存在");
                }
                
                knowledgeBase.UpdateName(dto.Name);
            }

            if (dto.Description != null)
            {
                knowledgeBase.UpdateDescription(dto.Description);
            }

            if (!string.IsNullOrEmpty(dto.VectorDbConfig))
            {
                knowledgeBase.UpdateVectorDbConfig(dto.VectorDbConfig);
            }

            if (!string.IsNullOrEmpty(dto.ChunkingConfig))
            {
                knowledgeBase.UpdateChunkingConfig(dto.ChunkingConfig);
            }

            if (dto.IsPublic.HasValue)
            {
                knowledgeBase.SetPublic(dto.IsPublic.Value);
            }

            if (dto.Status.HasValue)
            {
                knowledgeBase.UpdateStatus(dto.Status.Value);
            }

            _unitOfWork.KnowledgeBases.Update(knowledgeBase);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"kb:{knowledgeBaseId}", cancellationToken);

            return Result<KnowledgeBaseDto>.Success(MapToDto(knowledgeBase));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating knowledge base {KnowledgeBaseId}", knowledgeBaseId);
            return Result<KnowledgeBaseDto>.Failure("UPDATE_ERROR", "更新知识库失败");
        }
    }

    public async Task<Result> DeleteKnowledgeBaseAsync(
        Guid knowledgeBaseId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var knowledgeBase = await _unitOfWork.KnowledgeBases.GetByIdAsync(knowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result.Failure("NOT_FOUND", "知识库不存在");
            }

            // Check ownership
            if (knowledgeBase.OwnerId != userId)
            {
                return Result.Failure("UNAUTHORIZED", "无权操作此知识库");
            }

            // Soft delete
            knowledgeBase.Delete();
            _unitOfWork.KnowledgeBases.Update(knowledgeBase);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"kb:{knowledgeBaseId}", cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting knowledge base {KnowledgeBaseId}", knowledgeBaseId);
            return Result.Failure("DELETE_ERROR", "删除知识库失败");
        }
    }

    public async Task<Result<KnowledgeBaseDto>> GetKnowledgeBaseAsync(
        Guid knowledgeBaseId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Try cache first
            var cacheKey = $"kb:{knowledgeBaseId}";
            var cached = await _cacheService.GetAsync<KnowledgeBaseDto>(cacheKey, cancellationToken);
            if (cached != null)
            {
                return Result<KnowledgeBaseDto>.Success(cached);
            }

            var knowledgeBase = await _unitOfWork.KnowledgeBases.GetByIdAsync(knowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<KnowledgeBaseDto>.Failure("NOT_FOUND", "知识库不存在");
            }

            // Check access permission
            if (knowledgeBase.OwnerId != userId && !knowledgeBase.IsPublic)
            {
                return Result<KnowledgeBaseDto>.Failure("UNAUTHORIZED", "无权访问此知识库");
            }

            var dto = MapToDto(knowledgeBase);
            
            // Cache for 30 minutes
            await _cacheService.SetAsync(cacheKey, dto, TimeSpan.FromMinutes(30), cancellationToken);

            return Result<KnowledgeBaseDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting knowledge base {KnowledgeBaseId}", knowledgeBaseId);
            return Result<KnowledgeBaseDto>.Failure("GET_ERROR", "获取知识库失败");
        }
    }

    public async Task<Result<IEnumerable<KnowledgeBaseDto>>> GetUserKnowledgeBasesAsync(
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var knowledgeBases = await _unitOfWork.KnowledgeBases.GetByOwnerAsync(
                userId, OwnerType.User, false, cancellationToken);

            var dtos = knowledgeBases.Select(MapToDto);
            return Result<IEnumerable<KnowledgeBaseDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user knowledge bases for {UserId}", userId);
            return Result<IEnumerable<KnowledgeBaseDto>>.Failure("GET_ERROR", "获取用户知识库列表失败");
        }
    }

    public async Task<Result<PagedResult<KnowledgeBaseDto>>> SearchKnowledgeBasesAsync(
        SearchKnowledgeBaseDto dto,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var (items, totalCount) = await _unitOfWork.KnowledgeBases.SearchAsync(
                dto.Keyword,
                userId,
                OwnerType.User,
                dto.VectorDbType,
                dto.Status,
                dto.IsPublic,
                dto.PageIndex,
                dto.PageSize,
                cancellationToken);

            var dtos = items.Select(MapToDto).ToList();
            
            var pagedResult = new PagedResult<KnowledgeBaseDto>
            {
                Items = dtos,
                TotalCount = totalCount,
                PageNumber = dto.PageIndex,
                PageSize = dto.PageSize
            };

            return Result<PagedResult<KnowledgeBaseDto>>.Success(pagedResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching knowledge bases");
            return Result<PagedResult<KnowledgeBaseDto>>.Failure("SEARCH_ERROR", "搜索知识库失败");
        }
    }

    public async Task<Result<DocumentDto>> UploadDocumentAsync(
        Guid knowledgeBaseId,
        UploadDocumentDto dto,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var knowledgeBase = await _unitOfWork.KnowledgeBases.GetByIdAsync(knowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<DocumentDto>.Failure("KB_NOT_FOUND", "知识库不存在");
            }

            // Check ownership
            if (knowledgeBase.OwnerId != userId)
            {
                return Result<DocumentDto>.Failure("UNAUTHORIZED", "无权操作此知识库");
            }

            // Compute file hash
            dto.FileStream.Position = 0;
            using var reader = new StreamReader(dto.FileStream, leaveOpen: true);
            var content = await reader.ReadToEndAsync(cancellationToken);
            var fileHash = _documentProcessor.ComputeHash(content);
            dto.FileStream.Position = 0;

            // Check for duplicate
            var existing = await _unitOfWork.Documents.GetByFileHashAsync(fileHash, knowledgeBaseId, cancellationToken);
            if (existing != null)
            {
                return Result<DocumentDto>.Failure("DUPLICATE_FILE", "文件已存在");
            }

            // Determine document type
            var documentType = dto.DocumentType ?? DetermineDocumentType(dto.FileName, dto.ContentType);
            
            if (!_documentProcessor.IsDocumentTypeSupported(documentType))
            {
                return Result<DocumentDto>.Failure("UNSUPPORTED_TYPE", "不支持的文档类型");
            }

            // Create document entity
            var document = new Domain.Entities.KnowledgeBase.Document(
                knowledgeBaseId,
                dto.FileName,
                documentType,
                dto.ContentType,
                dto.FileSize,
                fileHash);

            if (dto.Metadata != null)
            {
                document.UpdateMetadata(JsonSerializer.Serialize(dto.Metadata));
            }

            await _unitOfWork.Documents.AddAsync(document, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Queue for processing
            _ = Task.Run(() => ProcessDocumentAsync(document.Id, cancellationToken), cancellationToken);

            return Result<DocumentDto>.Success(MapToDto(document));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document to knowledge base {KnowledgeBaseId}", knowledgeBaseId);
            return Result<DocumentDto>.Failure("UPLOAD_ERROR", "上传文档失败");
        }
    }

    public async Task<Result> DeleteDocumentAsync(
        Guid documentId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var document = await _unitOfWork.Documents.GetByIdAsync(documentId, cancellationToken);
            if (document == null)
            {
                return Result.Failure("NOT_FOUND", "文档不存在");
            }

            var knowledgeBase = await _unitOfWork.KnowledgeBases.GetByIdAsync(document.KnowledgeBaseId, cancellationToken);
            if (knowledgeBase == null || knowledgeBase.OwnerId != userId)
            {
                return Result.Failure("UNAUTHORIZED", "无权操作此文档");
            }

            // Delete chunks from vector database
            var vectorDb = _vectorDbFactory.GetVectorDatabase(knowledgeBase.VectorDbType, knowledgeBase.VectorDbConfig);
            var chunks = await _unitOfWork.DocumentChunks.GetByDocumentIdAsync(documentId, cancellationToken);
            var vectorIds = chunks.Where(c => c.IsEmbedded && !string.IsNullOrEmpty(c.VectorId))
                                  .Select(c => c.VectorId!)
                                  .ToList();

            if (vectorIds.Any())
            {
                await vectorDb.DeleteVectorsAsync($"kb_{knowledgeBase.Id}", vectorIds, cancellationToken);
            }

            // Delete chunks and document
            await _unitOfWork.DocumentChunks.DeleteByDocumentIdAsync(documentId, cancellationToken);
            document.Delete();
            _unitOfWork.Documents.Update(document);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Update knowledge base statistics
            var stats = await _unitOfWork.Documents.GetStatisticsAsync(knowledgeBase.Id, cancellationToken);
            knowledgeBase.UpdateStatistics(
                (int)stats["DocumentCount"],
                (int)stats["TotalVectors"],
                (long)stats["StorageSize"]);
            
            _unitOfWork.KnowledgeBases.Update(knowledgeBase);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document {DocumentId}", documentId);
            return Result.Failure("DELETE_ERROR", "删除文档失败");
        }
    }

    public async Task<Result<PagedResult<DocumentDto>>> GetDocumentsAsync(
        Guid knowledgeBaseId,
        GetDocumentsDto dto,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var knowledgeBase = await _unitOfWork.KnowledgeBases.GetByIdAsync(knowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<PagedResult<DocumentDto>>.Failure("NOT_FOUND", "知识库不存在");
            }

            // Check access permission
            if (knowledgeBase.OwnerId != userId && !knowledgeBase.IsPublic)
            {
                return Result<PagedResult<DocumentDto>>.Failure("UNAUTHORIZED", "无权访问此知识库");
            }

            var (items, totalCount) = await _unitOfWork.Documents.SearchAsync(
                knowledgeBaseId,
                dto.Keyword,
                dto.DocumentType,
                dto.Status,
                dto.StartDate,
                dto.EndDate,
                dto.PageIndex,
                dto.PageSize,
                cancellationToken);

            var dtos = items.Select(MapToDto).ToList();
            
            var pagedResult = new PagedResult<DocumentDto>
            {
                Items = dtos,
                TotalCount = totalCount,
                PageNumber = dto.PageIndex,
                PageSize = dto.PageSize
            };

            return Result<PagedResult<DocumentDto>>.Success(pagedResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents for knowledge base {KnowledgeBaseId}", knowledgeBaseId);
            return Result<PagedResult<DocumentDto>>.Failure("GET_ERROR", "获取文档列表失败");
        }
    }

    public async Task<Result> ProcessDocumentAsync(
        Guid documentId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var document = await _unitOfWork.Documents.GetByIdAsync(documentId, cancellationToken);
            if (document == null)
            {
                return Result.Failure("NOT_FOUND", "文档不存在");
            }

            var knowledgeBase = await _unitOfWork.KnowledgeBases.GetByIdAsync(document.KnowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result.Failure("KB_NOT_FOUND", "知识库不存在");
            }

            document.StartProcessing();
            _unitOfWork.Documents.Update(document);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            try
            {
                // Extract content
                // Note: In real implementation, you would retrieve the file stream from storage
                var extractResult = await _documentProcessor.ExtractContentAsync(
                    Stream.Null, // This should be the actual file stream
                    document.Name,
                    document.DocumentType,
                    cancellationToken);

                if (!extractResult.IsSuccess)
                {
                    throw new InvalidOperationException(extractResult.Error ?? "内容提取失败");
                }

                // Parse chunking config
                var chunkingConfig = JsonSerializer.Deserialize<ChunkingStrategy>(knowledgeBase.ChunkingConfig) 
                    ?? new ChunkingStrategy();

                // Chunk document
                var chunkResult = await _documentProcessor.ChunkDocumentAsync(
                    extractResult.Value!,
                    chunkingConfig,
                    cancellationToken);

                if (!chunkResult.IsSuccess)
                {
                    throw new InvalidOperationException(chunkResult.Error ?? "文档分块失败");
                }

                var chunks = chunkResult.Value!.ToList();
                var totalCharacters = chunks.Sum(c => c.CharacterCount);
                var totalTokens = chunks.Sum(c => c.TokenCount);

                // Save chunks
                foreach (var (chunkData, index) in chunks.Select((chunk, i) => (chunk, i)))
                {
                    var chunkEntity = new Domain.Entities.KnowledgeBase.DocumentChunk(
                        documentId,
                        knowledgeBase.Id,
                        index,
                        chunkData.Content,
                        chunkData.Hash,
                        chunkData.CharacterCount,
                        chunkData.TokenCount,
                        chunkData.Metadata.Any() ? JsonSerializer.Serialize(chunkData.Metadata) : null);

                    await _unitOfWork.DocumentChunks.AddAsync(chunkEntity, cancellationToken);
                }

                // Update document status
                document.CompleteProcessing(chunks.Count, totalCharacters, totalTokens);
                _unitOfWork.Documents.Update(document);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                // Queue chunks for embedding
                _ = Task.Run(() => EmbedDocumentChunksAsync(documentId, knowledgeBase, cancellationToken), cancellationToken);

                return Result.Success();
            }
            catch (Exception ex)
            {
                document.FailProcessing(ex.Message);
                _unitOfWork.Documents.Update(document);
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document {DocumentId}", documentId);
            return Result.Failure("PROCESS_ERROR", "处理文档失败");
        }
    }

    public async Task<Result<IEnumerable<SearchResultDto>>> SearchKnowledgeAsync(
        Guid knowledgeBaseId,
        string query,
        int topK,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var knowledgeBase = await _unitOfWork.KnowledgeBases.GetByIdAsync(knowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<IEnumerable<SearchResultDto>>.Failure("NOT_FOUND", "知识库不存在");
            }

            // Check access permission
            if (knowledgeBase.OwnerId != userId && !knowledgeBase.IsPublic)
            {
                return Result<IEnumerable<SearchResultDto>>.Failure("UNAUTHORIZED", "无权访问此知识库");
            }

            // Generate query embedding
            var provider = _aiProviderManager.GetProvider(AIProviderType.SemanticKernel);
            var embeddingResult = await provider.GetEmbeddingAsync(new AIEmbeddingRequest
            {
                Texts = new List<string> { query },
                Model = knowledgeBase.EmbeddingModel
            }, cancellationToken);

            if (!embeddingResult.IsSuccess)
            {
                return Result<IEnumerable<SearchResultDto>>.Failure("EMBEDDING_ERROR", "生成查询向量失败");
            }

            // Search in vector database
            var vectorDb = _vectorDbFactory.GetVectorDatabase(knowledgeBase.VectorDbType, knowledgeBase.VectorDbConfig);
            var searchResult = await vectorDb.SearchAsync(
                $"kb_{knowledgeBase.Id}",
                embeddingResult.Value!.Embeddings[0],
                topK,
                0.7f, // Score threshold
                null,
                cancellationToken);

            if (!searchResult.IsSuccess)
            {
                return Result<IEnumerable<SearchResultDto>>.Failure("SEARCH_ERROR", "向量搜索失败");
            }

            // Get chunk details
            var vectorIds = searchResult.Value!.Select(r => r.Id).ToList();
            var chunks = await _unitOfWork.DocumentChunks.GetByVectorIdsAsync(vectorIds, cancellationToken);
            var chunkDict = chunks.ToDictionary(c => c.VectorId!);

            var results = new List<SearchResultDto>();
            foreach (var searchItem in searchResult.Value!)
            {
                if (!chunkDict.TryGetValue(searchItem.Id, out var chunk))
                    continue;

                var document = await _unitOfWork.Documents.GetByIdAsync(chunk.DocumentId, cancellationToken);
                if (document == null)
                    continue;

                results.Add(new SearchResultDto
                {
                    ChunkId = chunk.Id.ToString(),
                    DocumentId = document.Id,
                    DocumentName = document.Name,
                    KnowledgeBaseId = knowledgeBase.Id,
                    KnowledgeBaseName = knowledgeBase.Name,
                    Content = chunk.Content,
                    Score = searchItem.Score,
                    Metadata = searchItem.Metadata
                });
            }

            return Result<IEnumerable<SearchResultDto>>.Success(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching knowledge base {KnowledgeBaseId}", knowledgeBaseId);
            return Result<IEnumerable<SearchResultDto>>.Failure("SEARCH_ERROR", "搜索知识库失败");
        }
    }

    public async Task<Result<IEnumerable<SearchResultDto>>> SearchMultipleKnowledgeBasesAsync(
        IEnumerable<Guid> knowledgeBaseIds,
        string query,
        int topK,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var allResults = new List<SearchResultDto>();

            foreach (var kbId in knowledgeBaseIds)
            {
                var searchResult = await SearchKnowledgeAsync(kbId, query, topK, userId, cancellationToken);
                if (searchResult.IsSuccess)
                {
                    allResults.AddRange(searchResult.Value!);
                }
            }

            // Sort by score and take top K
            var topResults = allResults.OrderByDescending(r => r.Score).Take(topK);

            return Result<IEnumerable<SearchResultDto>>.Success(topResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching multiple knowledge bases");
            return Result<IEnumerable<SearchResultDto>>.Failure("SEARCH_ERROR", "搜索多个知识库失败");
        }
    }

    public async Task<Result<KnowledgeBaseStatisticsDto>> GetStatisticsAsync(
        Guid knowledgeBaseId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var knowledgeBase = await _unitOfWork.KnowledgeBases.GetByIdAsync(knowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<KnowledgeBaseStatisticsDto>.Failure("NOT_FOUND", "知识库不存在");
            }

            // Check access permission
            if (knowledgeBase.OwnerId != userId && !knowledgeBase.IsPublic)
            {
                return Result<KnowledgeBaseStatisticsDto>.Failure("UNAUTHORIZED", "无权访问此知识库");
            }

            var stats = await _unitOfWork.KnowledgeBases.GetStatisticsAsync(knowledgeBaseId, cancellationToken);
            var docStats = await _unitOfWork.Documents.GetStatisticsAsync(knowledgeBaseId, cancellationToken);

            var dto = new KnowledgeBaseStatisticsDto
            {
                KnowledgeBaseId = knowledgeBaseId,
                TotalDocuments = (int)stats.GetValueOrDefault("DocumentCount", 0),
                ProcessedDocuments = (int)docStats.GetValueOrDefault("ProcessedCount", 0),
                FailedDocuments = (int)docStats.GetValueOrDefault("FailedCount", 0),
                TotalChunks = (int)stats.GetValueOrDefault("ChunkCount", 0),
                EmbeddedChunks = (int)stats.GetValueOrDefault("EmbeddedChunkCount", 0),
                TotalStorageSize = (long)stats.GetValueOrDefault("StorageSize", 0L),
                LastProcessedAt = stats.ContainsKey("LastProcessedAt") ? (DateTime?)stats["LastProcessedAt"] : null
            };

            // Add document type breakdown
            if (docStats.ContainsKey("DocumentsByType"))
            {
                dto.DocumentsByType = (Dictionary<DocumentType, int>)docStats["DocumentsByType"];
            }

            // Add document status breakdown
            if (docStats.ContainsKey("DocumentsByStatus"))
            {
                dto.DocumentsByStatus = (Dictionary<DocumentStatus, int>)docStats["DocumentsByStatus"];
            }

            // Calculate averages
            if (dto.TotalDocuments > 0)
            {
                dto.AverageChunksPerDocument = (double)dto.TotalChunks / dto.TotalDocuments;
            }

            if (dto.TotalChunks > 0 && stats.ContainsKey("TotalTokens"))
            {
                dto.AverageTokensPerChunk = (double)(int)stats["TotalTokens"] / dto.TotalChunks;
            }

            return Result<KnowledgeBaseStatisticsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting statistics for knowledge base {KnowledgeBaseId}", knowledgeBaseId);
            return Result<KnowledgeBaseStatisticsDto>.Failure("STATS_ERROR", "获取知识库统计信息失败");
        }
    }

    private async Task EmbedDocumentChunksAsync(
        Guid documentId,
        Domain.Entities.KnowledgeBase.KnowledgeBase knowledgeBase,
        CancellationToken cancellationToken)
    {
        try
        {
            var chunks = await _unitOfWork.DocumentChunks.GetUnembeddedChunksAsync(
                knowledgeBase.Id, 100, cancellationToken);

            if (!chunks.Any())
                return;

            var provider = _aiProviderManager.GetProvider(AIProviderType.SemanticKernel);
            var vectorDb = _vectorDbFactory.GetVectorDatabase(knowledgeBase.VectorDbType, knowledgeBase.VectorDbConfig);
            var collectionName = $"kb_{knowledgeBase.Id}";

            var updates = new List<(Guid ChunkId, string VectorId)>();

            foreach (var chunk in chunks.Where(c => c.DocumentId == documentId))
            {
                try
                {
                    // Generate embedding
                    var embeddingResult = await provider.GetEmbeddingAsync(new AIEmbeddingRequest
                    {
                        Texts = new List<string> { chunk.Content },
                        Model = knowledgeBase.EmbeddingModel
                    }, cancellationToken);

                    if (!embeddingResult.IsSuccess)
                    {
                        _logger.LogWarning("Failed to generate embedding for chunk {ChunkId}", chunk.Id);
                        continue;
                    }

                    // Store in vector database
                    var metadata = new Dictionary<string, object>
                    {
                        ["chunk_id"] = chunk.Id.ToString(),
                        ["document_id"] = chunk.DocumentId.ToString(),
                        ["chunk_index"] = chunk.ChunkIndex,
                        ["char_count"] = chunk.CharacterCount,
                        ["token_count"] = chunk.TokenCount
                    };

                    if (!string.IsNullOrEmpty(chunk.Metadata))
                    {
                        var chunkMetadata = JsonSerializer.Deserialize<Dictionary<string, object>>(chunk.Metadata);
                        if (chunkMetadata != null)
                        {
                            foreach (var (key, value) in chunkMetadata)
                            {
                                metadata[$"meta_{key}"] = value;
                            }
                        }
                    }

                    var insertResult = await vectorDb.InsertVectorAsync(
                        collectionName,
                        embeddingResult.Value!.Embeddings[0],
                        metadata,
                        chunk.Id.ToString(),
                        cancellationToken);

                    if (insertResult.IsSuccess)
                    {
                        updates.Add((chunk.Id, insertResult.Value!));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error embedding chunk {ChunkId}", chunk.Id);
                }
            }

            // Update chunk embedding status
            if (updates.Any())
            {
                await _unitOfWork.DocumentChunks.UpdateEmbeddingStatusBatchAsync(updates, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                // Update knowledge base statistics
                var stats = await _unitOfWork.Documents.GetStatisticsAsync(knowledgeBase.Id, cancellationToken);
                knowledgeBase.UpdateStatistics(
                    (int)stats["DocumentCount"],
                    (int)stats["TotalVectors"],
                    (long)stats["StorageSize"]);
                
                _unitOfWork.KnowledgeBases.Update(knowledgeBase);
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error embedding chunks for document {DocumentId}", documentId);
        }
    }

    private KnowledgeBaseDto MapToDto(Domain.Entities.KnowledgeBase.KnowledgeBase entity)
    {
        return new KnowledgeBaseDto
        {
            Id = entity.Id,
            Name = entity.Name,
            Description = entity.Description,
            OwnerId = entity.OwnerId,
            OwnerType = entity.OwnerType,
            VectorDbType = entity.VectorDbType,
            EmbeddingModel = entity.EmbeddingModel,
            VectorDimension = entity.VectorDimension,
            DocumentCount = entity.DocumentCount,
            TotalVectors = entity.TotalVectors,
            StorageSize = entity.StorageSize,
            IsPublic = entity.IsPublic,
            Status = entity.Status,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt
        };
    }

    private DocumentDto MapToDto(Domain.Entities.KnowledgeBase.Document entity)
    {
        return new DocumentDto
        {
            Id = entity.Id,
            KnowledgeBaseId = entity.KnowledgeBaseId,
            Name = entity.Name,
            DocumentType = entity.DocumentType,
            ContentType = entity.ContentType,
            OriginalFileUrl = entity.OriginalFileUrl,
            FileSize = entity.FileSize,
            Status = entity.Status,
            ProcessingStartedAt = entity.ProcessingStartedAt,
            ProcessingCompletedAt = entity.ProcessingCompletedAt,
            ProcessingError = entity.ProcessingError,
            ChunkCount = entity.ChunkCount,
            TotalCharacters = entity.TotalCharacters,
            TotalTokens = entity.TotalTokens,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt
        };
    }

    private DocumentType DetermineDocumentType(string fileName, string contentType)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        
        return extension switch
        {
            ".txt" => DocumentType.PlainText,
            ".md" => DocumentType.Markdown,
            ".pdf" => DocumentType.PDF,
            ".doc" or ".docx" => DocumentType.Word,
            ".xls" or ".xlsx" => DocumentType.Excel,
            ".ppt" or ".pptx" => DocumentType.PowerPoint,
            ".html" or ".htm" => DocumentType.HTML,
            ".json" => DocumentType.JSON,
            ".csv" => DocumentType.CSV,
            ".xml" => DocumentType.XML,
            _ => contentType switch
            {
                "text/plain" => DocumentType.PlainText,
                "text/markdown" => DocumentType.Markdown,
                "application/pdf" => DocumentType.PDF,
                "application/json" => DocumentType.JSON,
                "text/csv" => DocumentType.CSV,
                "text/xml" or "application/xml" => DocumentType.XML,
                _ => DocumentType.PlainText
            }
        };
    }
}