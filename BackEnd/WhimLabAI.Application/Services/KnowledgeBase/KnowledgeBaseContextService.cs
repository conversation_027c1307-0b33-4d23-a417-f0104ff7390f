using System.Diagnostics;
using System.Text;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.KnowledgeBase;

/// <summary>
/// 知识库上下文服务实现
/// </summary>
public class KnowledgeBaseContextService : IKnowledgeBaseContextService
{
    private readonly IVectorSearchService _vectorSearchService;
    private readonly IAgentRepository _agentRepository;
    private readonly IConversationRepository _conversationRepository;
    private readonly IKnowledgeBaseRepository _knowledgeBaseRepository;
    private readonly ILogger<KnowledgeBaseContextService> _logger;

    // 提示词模板
    private const string ContextPromptTemplate = @"
以下是与您的查询相关的参考信息：

{context}

请基于以上参考信息回答用户的问题。如果参考信息不足以回答问题，请诚实地说明这一点。";

    public KnowledgeBaseContextService(
        IVectorSearchService vectorSearchService,
        IAgentRepository agentRepository,
        IConversationRepository conversationRepository,
        IKnowledgeBaseRepository knowledgeBaseRepository,
        ILogger<KnowledgeBaseContextService> logger)
    {
        _vectorSearchService = vectorSearchService;
        _agentRepository = agentRepository;
        _conversationRepository = conversationRepository;
        _knowledgeBaseRepository = knowledgeBaseRepository;
        _logger = logger;
    }

    public async Task<Result<KnowledgeBaseContext>> GetContextAsync(
        Guid agentId,
        string query,
        KnowledgeBaseContextOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        options ??= new KnowledgeBaseContextOptions();

        try
        {
            // 获取智能体
            var agent = await _agentRepository.GetByIdAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result<KnowledgeBaseContext>.Failure("Agent not found");
            }

            // 获取智能体关联的知识库
            var knowledgeBaseIds = await GetAgentKnowledgeBasesAsync(agentId, cancellationToken);
            if (knowledgeBaseIds.Count == 0)
            {
                // 如果智能体没有关联知识库，返回空上下文
                return Result<KnowledgeBaseContext>.Success(new KnowledgeBaseContext
                {
                    KnowledgeBaseIds = new List<Guid>(),
                    ProcessingTime = stopwatch.Elapsed
                });
            }

            // 执行向量搜索
            Result<VectorSearchResponse> searchResult;
            if (options.UseHybridSearch)
            {
                // 使用混合搜索（如果只有一个知识库）
                if (knowledgeBaseIds.Count == 1)
                {
                    searchResult = await _vectorSearchService.HybridSearchAsync(
                        knowledgeBaseIds[0],
                        query,
                        new HybridSearchOptions
                        {
                            TopK = options.MaxChunks,
                            MinSimilarity = options.MinSimilarity,
                            IncludeContent = true,
                            IncludeMetadata = options.IncludeMetadata
                        },
                        cancellationToken);
                }
                else
                {
                    // 多个知识库使用普通向量搜索
                    searchResult = await _vectorSearchService.SearchInMultipleKnowledgeBasesAsync(
                        knowledgeBaseIds,
                        query,
                        new VectorSearchOptions
                        {
                            TopK = options.MaxChunks,
                            MinSimilarity = options.MinSimilarity,
                            IncludeContent = true,
                            IncludeMetadata = options.IncludeMetadata
                        },
                        cancellationToken);
                }
            }
            else
            {
                // 使用普通向量搜索
                searchResult = await _vectorSearchService.SearchInMultipleKnowledgeBasesAsync(
                    knowledgeBaseIds,
                    query,
                    new VectorSearchOptions
                    {
                        TopK = options.MaxChunks,
                        MinSimilarity = options.MinSimilarity,
                        IncludeContent = true,
                        IncludeMetadata = options.IncludeMetadata
                    },
                    cancellationToken);
            }

            if (!searchResult.IsSuccess)
            {
                return Result<KnowledgeBaseContext>.Failure($"Vector search failed: {searchResult.Error}");
            }

            // 构建上下文
            var context = await BuildContextFromSearchResultsAsync(
                searchResult.Value,
                options,
                stopwatch.Elapsed,
                cancellationToken);

            return Result<KnowledgeBaseContext>.Success(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get context for agent {AgentId}", agentId);
            return Result<KnowledgeBaseContext>.Failure($"Failed to get context: {ex.Message}");
        }
    }

    public async Task<Result<KnowledgeBaseContext>> GetConversationContextAsync(
        Guid conversationId,
        string query,
        KnowledgeBaseContextOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取对话信息
            var conversation = await _conversationRepository.GetByIdAsync(conversationId, cancellationToken);
            if (conversation == null)
            {
                return Result<KnowledgeBaseContext>.Failure("Conversation not found");
            }

            // 使用对话关联的智能体获取上下文
            return await GetContextAsync(conversation.AgentId, query, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get context for conversation {ConversationId}", conversationId);
            return Result<KnowledgeBaseContext>.Failure($"Failed to get context: {ex.Message}");
        }
    }

    public string BuildEnhancedPrompt(
        string systemPrompt,
        string userMessage,
        KnowledgeBaseContext context)
    {
        // 如果没有上下文，直接返回原始提示词
        if (context.Chunks.Count == 0)
        {
            return systemPrompt;
        }

        // 构建上下文文本
        var contextBuilder = new StringBuilder();
        var groupedChunks = context.Chunks
            .Where(c => c.IsUsed)
            .GroupBy(c => c.DocumentName)
            .OrderByDescending(g => g.Max(c => c.SimilarityScore));

        foreach (var group in groupedChunks)
        {
            contextBuilder.AppendLine($"【文档：{group.Key}】");
            
            foreach (var chunk in group.OrderBy(c => c.ChunkIndex))
            {
                contextBuilder.AppendLine($"- {chunk.Content}");
                
                if (chunk.SimilarityScore > 0)
                {
                    contextBuilder.AppendLine($"  (相关度: {chunk.SimilarityScore:P0})");
                }
            }
            
            contextBuilder.AppendLine();
        }

        // 将上下文插入到提示词模板中
        var contextPrompt = ContextPromptTemplate.Replace("{context}", contextBuilder.ToString().Trim());

        // 组合系统提示词和上下文
        return $"{systemPrompt}\n\n{contextPrompt}";
    }

    public async Task<Result> SaveFeedbackAsync(
        Guid contextId,
        ContextFeedback feedback,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: 实现反馈保存逻辑
            // 这里可以保存反馈信息用于改进检索质量
            _logger.LogInformation("Received feedback for context {ContextId}: Helpful={IsHelpful}, Score={Score}",
                contextId, feedback.IsHelpful, feedback.RelevanceScore);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save feedback for context {ContextId}", contextId);
            return Result.Failure($"Failed to save feedback: {ex.Message}");
        }
    }

    #region Private Methods

    private async Task<List<Guid>> GetAgentKnowledgeBasesAsync(
        Guid agentId,
        CancellationToken cancellationToken)
    {
        // 获取智能体
        var agent = await _agentRepository.GetByIdAsync(agentId, cancellationToken);
        if (agent == null)
        {
            return new List<Guid>();
        }

        // 获取当前版本的知识库ID列表
        var currentVersion = agent.Versions.FirstOrDefault(v => v.Id == agent.CurrentVersionId);
        if (currentVersion == null)
        {
            return new List<Guid>();
        }

        // 将字符串ID转换为Guid
        var knowledgeBaseIds = new List<Guid>();
        foreach (var kbIdString in currentVersion.KnowledgeBases)
        {
            if (Guid.TryParse(kbIdString, out var kbId))
            {
                knowledgeBaseIds.Add(kbId);
            }
        }

        return knowledgeBaseIds;
    }

    private async Task<KnowledgeBaseContext> BuildContextFromSearchResultsAsync(
        VectorSearchResponse searchResponse,
        KnowledgeBaseContextOptions options,
        TimeSpan processingTime,
        CancellationToken cancellationToken)
    {
        var context = new KnowledgeBaseContext
        {
            ProcessingTime = processingTime,
            KnowledgeBaseIds = searchResponse.SearchedKnowledgeBases
        };

        var totalCharacters = 0;
        var chunks = new List<ContextChunk>();

        foreach (var result in searchResponse.Results)
        {
            // 检查是否超过上下文窗口大小
            if (totalCharacters + (result.Content?.Length ?? 0) > options.ContextWindowSize)
            {
                break;
            }

            var chunk = new ContextChunk
            {
                ChunkId = result.ChunkId,
                DocumentId = result.DocumentId,
                DocumentName = result.DocumentName,
                KnowledgeBaseId = result.KnowledgeBaseId,
                KnowledgeBaseName = result.KnowledgeBaseName,
                Content = result.Content ?? string.Empty,
                SimilarityScore = result.CombinedScore ?? result.SimilarityScore,
                ChunkIndex = result.ChunkIndex,
                Metadata = options.IncludeMetadata ? result.Metadata : null
            };

            chunks.Add(chunk);
            totalCharacters += chunk.Content.Length;
        }

        context.Chunks = chunks;
        context.TotalCharacters = totalCharacters;

        // 添加元数据
        if (options.IncludeDocumentInfo)
        {
            context.Metadata = new Dictionary<string, object>
            {
                ["DocumentCount"] = chunks.Select(c => c.DocumentId).Distinct().Count(),
                ["AverageSimilarity"] = chunks.Count > 0 ? chunks.Average(c => c.SimilarityScore) : 0,
                ["SearchType"] = options.UseHybridSearch ? "Hybrid" : "Vector"
            };
        }

        return context;
    }

    #endregion
}