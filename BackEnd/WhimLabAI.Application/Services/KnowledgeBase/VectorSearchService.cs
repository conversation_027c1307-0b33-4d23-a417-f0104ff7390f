using System.Diagnostics;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Application.Services.KnowledgeBase;

/// <summary>
/// 向量搜索服务实现
/// </summary>
public class VectorSearchService : IVectorSearchService
{
    private readonly IKnowledgeBaseRepository _knowledgeBaseRepository;
    private readonly IDocumentRepository _documentRepository;
    private readonly IDocumentChunkRepository _chunkRepository;
    private readonly IEmbeddingService _embeddingService;
    private readonly IVectorDatabaseFactory _vectorDbFactory;
    private readonly ILogger<VectorSearchService> _logger;

    public VectorSearchService(
        IKnowledgeBaseRepository knowledgeBaseRepository,
        IDocumentRepository documentRepository,
        IDocumentChunkRepository chunkRepository,
        IEmbeddingService embeddingService,
        IVectorDatabaseFactory vectorDbFactory,
        ILogger<VectorSearchService> logger)
    {
        _knowledgeBaseRepository = knowledgeBaseRepository;
        _documentRepository = documentRepository;
        _chunkRepository = chunkRepository;
        _embeddingService = embeddingService;
        _vectorDbFactory = vectorDbFactory;
        _logger = logger;
    }

    public async Task<Result<VectorSearchResponse>> SearchInKnowledgeBaseAsync(
        Guid knowledgeBaseId,
        string query,
        VectorSearchOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        options ??= new VectorSearchOptions();

        try
        {
            // 获取知识库
            var knowledgeBase = await _knowledgeBaseRepository.GetByIdAsync(knowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<VectorSearchResponse>.Failure("Knowledge base not found");
            }

            // 生成查询向量
            var embeddingResult = await _embeddingService.GenerateEmbeddingAsync(
                query, 
                knowledgeBase.EmbeddingModel, 
                cancellationToken);
            
            if (!embeddingResult.IsSuccess)
            {
                return Result<VectorSearchResponse>.Failure($"Failed to generate query embedding: {embeddingResult.Error}");
            }

            var queryVector = embeddingResult.Value;

            // 获取向量数据库
            var vectorDb = _vectorDbFactory.GetVectorDatabase(knowledgeBase.VectorDbType, knowledgeBase.VectorDbConfig);

            // 构建元数据过滤器
            var filters = new Dictionary<string, object>(options.MetadataFilters ?? new Dictionary<string, object>())
            {
                ["KnowledgeBaseId"] = knowledgeBaseId.ToString()
            };

            // 执行向量搜索
            var searchResult = await vectorDb.SearchAsync(
                knowledgeBase.Name,
                queryVector,
                options.TopK,
                options.MinSimilarity,
                filters,
                cancellationToken);

            if (!searchResult.IsSuccess)
            {
                return Result<VectorSearchResponse>.Failure($"Vector search failed: {searchResult.Error}");
            }

            // 转换搜索结果
            var response = await BuildSearchResponseAsync(
                searchResult.Value,
                new List<Guid> { knowledgeBaseId },
                queryVector.Length,
                stopwatch.Elapsed,
                options,
                cancellationToken);

            return Result<VectorSearchResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search in knowledge base {KnowledgeBaseId}", knowledgeBaseId);
            return Result<VectorSearchResponse>.Failure($"Search failed: {ex.Message}");
        }
    }

    public async Task<Result<VectorSearchResponse>> SearchInMultipleKnowledgeBasesAsync(
        IList<Guid> knowledgeBaseIds,
        string query,
        VectorSearchOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        options ??= new VectorSearchOptions();

        try
        {
            var allResults = new List<WhimLabAI.Abstractions.Infrastructure.VectorSearchResult>();
            int vectorDimension = 0;

            // 对每个知识库执行搜索
            foreach (var knowledgeBaseId in knowledgeBaseIds)
            {
                var knowledgeBase = await _knowledgeBaseRepository.GetByIdAsync(knowledgeBaseId, cancellationToken);
                if (knowledgeBase == null)
                {
                    _logger.LogWarning("Knowledge base {KnowledgeBaseId} not found", knowledgeBaseId);
                    continue;
                }

                // 生成查询向量
                var embeddingResult = await _embeddingService.GenerateEmbeddingAsync(
                    query, 
                    knowledgeBase.EmbeddingModel, 
                    cancellationToken);
                
                if (!embeddingResult.IsSuccess)
                {
                    _logger.LogWarning("Failed to generate embedding for knowledge base {KnowledgeBaseId}: {Error}", 
                        knowledgeBaseId, embeddingResult.Error);
                    continue;
                }

                var queryVector = embeddingResult.Value;
                vectorDimension = queryVector.Length;

                // 获取向量数据库
                var vectorDb = _vectorDbFactory.GetVectorDatabase(knowledgeBase.VectorDbType, knowledgeBase.VectorDbConfig);

                // 构建过滤器
                var filters = new Dictionary<string, object>(options.MetadataFilters ?? new Dictionary<string, object>())
                {
                    ["KnowledgeBaseId"] = knowledgeBaseId.ToString()
                };

                // 执行搜索
                var searchResult = await vectorDb.SearchAsync(
                    knowledgeBase.Name,
                    queryVector,
                    options.TopK,
                    options.MinSimilarity,
                    filters,
                    cancellationToken);

                if (searchResult.IsSuccess)
                {
                    allResults.AddRange(searchResult.Value);
                }
            }

            // 按相似度排序并取前TopK个结果
            var topResults = allResults
                .OrderByDescending(r => r.Score)
                .Take(options.TopK)
                .ToList();

            // 构建响应
            var response = await BuildSearchResponseAsync(
                topResults,
                knowledgeBaseIds.ToList(),
                vectorDimension,
                stopwatch.Elapsed,
                options,
                cancellationToken);

            return Result<VectorSearchResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search in multiple knowledge bases");
            return Result<VectorSearchResponse>.Failure($"Search failed: {ex.Message}");
        }
    }

    public async Task<Result<VectorSearchResponse>> HybridSearchAsync(
        Guid knowledgeBaseId,
        string query,
        HybridSearchOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        options ??= new HybridSearchOptions();

        try
        {
            // 1. 执行向量搜索
            var vectorSearchResult = await SearchInKnowledgeBaseAsync(
                knowledgeBaseId,
                query,
                new VectorSearchOptions
                {
                    TopK = options.TopK * 2, // 获取更多结果用于混合排序
                    MinSimilarity = options.MinSimilarity,
                    MetadataFilters = options.MetadataFilters,
                    IncludeContent = true,
                    IncludeMetadata = options.IncludeMetadata
                },
                cancellationToken);

            if (!vectorSearchResult.IsSuccess)
            {
                return Result<VectorSearchResponse>.Failure($"Vector search failed: {vectorSearchResult.Error}");
            }

            var vectorResults = vectorSearchResult.Value.Results;

            // 2. 执行关键词搜索和评分
            var keywords = ExtractKeywords(query);
            var hybridResults = new List<WhimLabAI.Abstractions.Application.VectorSearchResult>();

            foreach (var result in vectorResults)
            {
                // 计算关键词匹配分数
                float keywordScore = 0f;
                if (!string.IsNullOrEmpty(result.Content))
                {
                    keywordScore = CalculateKeywordScore(result.Content, keywords, options.UseFuzzyMatching);
                    
                    // 生成高亮内容
                    if (keywordScore > 0)
                    {
                        result.HighlightedContent = HighlightKeywords(result.Content, keywords);
                    }
                }

                // 计算综合分数
                float combinedScore = (result.SimilarityScore * options.VectorWeight) + 
                                     (keywordScore * options.KeywordWeight);

                result.KeywordScore = keywordScore;
                result.CombinedScore = combinedScore;
                
                hybridResults.Add(result);
            }

            // 3. 按综合分数排序并返回TopK结果
            var topResults = hybridResults
                .OrderByDescending(r => r.CombinedScore)
                .Take(options.TopK)
                .ToList();

            var response = new VectorSearchResponse
            {
                Results = topResults,
                TotalResults = topResults.Count,
                QueryTime = stopwatch.Elapsed,
                VectorDimension = vectorSearchResult.Value.VectorDimension,
                SearchedKnowledgeBases = new List<Guid> { knowledgeBaseId }
            };

            return Result<VectorSearchResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform hybrid search in knowledge base {KnowledgeBaseId}", knowledgeBaseId);
            return Result<VectorSearchResponse>.Failure($"Hybrid search failed: {ex.Message}");
        }
    }

    public async Task<Result<float>> GetDocumentSimilarityAsync(
        Guid documentId1,
        Guid documentId2,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取两个文档的平均嵌入向量
            var embedding1Result = await GetDocumentAverageEmbeddingAsync(documentId1, cancellationToken);
            if (!embedding1Result.IsSuccess)
            {
                return Result<float>.Failure($"Failed to get embedding for document 1: {embedding1Result.Error}");
            }

            var embedding2Result = await GetDocumentAverageEmbeddingAsync(documentId2, cancellationToken);
            if (!embedding2Result.IsSuccess)
            {
                return Result<float>.Failure($"Failed to get embedding for document 2: {embedding2Result.Error}");
            }

            // 计算余弦相似度
            var similarity = CalculateCosineSimilarity(embedding1Result.Value, embedding2Result.Value);
            
            return Result<float>.Success(similarity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to calculate document similarity");
            return Result<float>.Failure($"Failed to calculate similarity: {ex.Message}");
        }
    }

    public async Task<Result<VectorSearchResponse>> FindSimilarDocumentsAsync(
        Guid documentId,
        VectorSearchOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        options ??= new VectorSearchOptions();

        try
        {
            // 获取源文档
            var document = await _documentRepository.GetByIdAsync(documentId, cancellationToken);
            if (document == null)
            {
                return Result<VectorSearchResponse>.Failure("Document not found");
            }

            // 获取文档的平均嵌入向量
            var embeddingResult = await GetDocumentAverageEmbeddingAsync(documentId, cancellationToken);
            if (!embeddingResult.IsSuccess)
            {
                return Result<VectorSearchResponse>.Failure($"Failed to get document embedding: {embeddingResult.Error}");
            }

            var queryVector = embeddingResult.Value;

            // 获取知识库
            var knowledgeBase = await _knowledgeBaseRepository.GetByIdAsync(document.KnowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<VectorSearchResponse>.Failure("Knowledge base not found");
            }

            // 获取向量数据库
            var vectorDb = _vectorDbFactory.GetVectorDatabase(knowledgeBase.VectorDbType, knowledgeBase.VectorDbConfig);

            // 构建过滤器（排除源文档）
            var filters = new Dictionary<string, object>(options.MetadataFilters ?? new Dictionary<string, object>())
            {
                ["KnowledgeBaseId"] = knowledgeBase.Id.ToString()
            };

            // 执行搜索（获取比需要多一个的结果，以便过滤掉源文档）
            var searchResult = await vectorDb.SearchAsync(
                knowledgeBase.Name,
                queryVector,
                options.TopK + 1,
                options.MinSimilarity,
                filters,
                cancellationToken);

            if (!searchResult.IsSuccess)
            {
                return Result<VectorSearchResponse>.Failure($"Vector search failed: {searchResult.Error}");
            }

            // 过滤掉源文档
            var filteredResults = searchResult.Value
                .Where(r => r.Metadata != null && 
                           r.Metadata.TryGetValue("DocumentId", out var docIdObj) &&
                           docIdObj.ToString() != documentId.ToString())
                .Take(options.TopK)
                .ToList();

            // 构建响应
            var response = await BuildSearchResponseAsync(
                filteredResults,
                new List<Guid> { knowledgeBase.Id },
                queryVector.Length,
                stopwatch.Elapsed,
                options,
                cancellationToken);

            return Result<VectorSearchResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to find similar documents for document {DocumentId}", documentId);
            return Result<VectorSearchResponse>.Failure($"Failed to find similar documents: {ex.Message}");
        }
    }

    #region Private Methods

    private async Task<VectorSearchResponse> BuildSearchResponseAsync(
        IEnumerable<WhimLabAI.Abstractions.Infrastructure.VectorSearchResult> vectorResults,
        List<Guid> searchedKnowledgeBases,
        int vectorDimension,
        TimeSpan queryTime,
        VectorSearchOptions options,
        CancellationToken cancellationToken)
    {
        var results = new List<WhimLabAI.Abstractions.Application.VectorSearchResult>();

        foreach (var vectorResult in vectorResults)
        {
            if (vectorResult.Metadata == null) continue;

            // 提取元数据
            vectorResult.Metadata.TryGetValue("ChunkId", out var chunkIdObj);
            vectorResult.Metadata.TryGetValue("DocumentId", out var documentIdObj);
            vectorResult.Metadata.TryGetValue("KnowledgeBaseId", out var kbIdObj);
            vectorResult.Metadata.TryGetValue("ChunkIndex", out var chunkIndexObj);

            if (!Guid.TryParse(chunkIdObj?.ToString(), out var chunkId) ||
                !Guid.TryParse(documentIdObj?.ToString(), out var documentId) ||
                !Guid.TryParse(kbIdObj?.ToString(), out var knowledgeBaseId))
            {
                continue;
            }

            var result = new WhimLabAI.Abstractions.Application.VectorSearchResult
            {
                ChunkId = chunkId,
                DocumentId = documentId,
                KnowledgeBaseId = knowledgeBaseId,
                SimilarityScore = vectorResult.Score,
                ChunkIndex = int.TryParse(chunkIndexObj?.ToString(), out var idx) ? idx : 0,
                Metadata = options.IncludeMetadata ? vectorResult.Metadata : null
            };

            // 获取文档和知识库信息
            var document = await _documentRepository.GetByIdAsync(documentId, cancellationToken);
            if (document != null)
            {
                result.DocumentName = document.Name;
                result.DocumentCreatedAt = document.CreatedAt;
                result.DocumentUpdatedAt = document.UpdatedAt;
            }

            var knowledgeBase = await _knowledgeBaseRepository.GetByIdAsync(knowledgeBaseId, cancellationToken);
            if (knowledgeBase != null)
            {
                result.KnowledgeBaseName = knowledgeBase.Name;
            }

            // 获取块内容
            if (options.IncludeContent)
            {
                var chunk = await _chunkRepository.GetByIdAsync(chunkId, cancellationToken);
                if (chunk != null)
                {
                    result.Content = options.MaxContentLength.HasValue && chunk.Content.Length > options.MaxContentLength.Value
                        ? chunk.Content.Substring(0, options.MaxContentLength.Value) + "..."
                        : chunk.Content;
                }
            }

            results.Add(result);
        }

        return new VectorSearchResponse
        {
            Results = results,
            TotalResults = results.Count,
            QueryTime = queryTime,
            VectorDimension = vectorDimension,
            SearchedKnowledgeBases = searchedKnowledgeBases
        };
    }

    private async Task<Result<float[]>> GetDocumentAverageEmbeddingAsync(
        Guid documentId,
        CancellationToken cancellationToken)
    {
        try
        {
            // 获取文档的所有块
            var chunks = (await _chunkRepository.GetByDocumentIdAsync(documentId, cancellationToken))
                .Where(c => c.IsEmbedded)
                .ToList();

            if (chunks.Count == 0)
            {
                return Result<float[]>.Failure("Document has no embedded chunks");
            }

            // 获取文档
            var document = await _documentRepository.GetByIdAsync(documentId, cancellationToken);
            if (document == null)
            {
                return Result<float[]>.Failure("Document not found");
            }

            // 获取知识库
            var knowledgeBase = await _knowledgeBaseRepository.GetByIdAsync(document.KnowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<float[]>.Failure("Knowledge base not found");
            }

            // 获取向量数据库
            var vectorDb = _vectorDbFactory.GetVectorDatabase(knowledgeBase.VectorDbType, knowledgeBase.VectorDbConfig);

            // 获取所有块的向量
            var vectors = new List<float[]>();
            foreach (var chunk in chunks)
            {
                var vectorResult = await vectorDb.GetVectorAsync(
                    knowledgeBase.Name,
                    chunk.VectorId!,
                    cancellationToken);

                if (vectorResult.IsSuccess && vectorResult.Value != null)
                {
                    vectors.Add(vectorResult.Value);
                }
            }

            if (vectors.Count == 0)
            {
                return Result<float[]>.Failure("No vectors found for document");
            }

            // 计算平均向量
            var avgVector = CalculateAverageVector(vectors);
            return Result<float[]>.Success(avgVector);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get document average embedding");
            return Result<float[]>.Failure($"Failed to get embedding: {ex.Message}");
        }
    }

    private float[] CalculateAverageVector(List<float[]> vectors)
    {
        if (vectors.Count == 0) return Array.Empty<float>();

        var dimension = vectors[0].Length;
        var avgVector = new float[dimension];

        for (int i = 0; i < dimension; i++)
        {
            float sum = 0;
            foreach (var vector in vectors)
            {
                sum += vector[i];
            }
            avgVector[i] = sum / vectors.Count;
        }

        return avgVector;
    }

    private float CalculateCosineSimilarity(float[] vector1, float[] vector2)
    {
        if (vector1.Length != vector2.Length)
            throw new ArgumentException("Vectors must have the same dimension");

        float dotProduct = 0;
        float magnitude1 = 0;
        float magnitude2 = 0;

        for (int i = 0; i < vector1.Length; i++)
        {
            dotProduct += vector1[i] * vector2[i];
            magnitude1 += vector1[i] * vector1[i];
            magnitude2 += vector2[i] * vector2[i];
        }

        magnitude1 = (float)Math.Sqrt(magnitude1);
        magnitude2 = (float)Math.Sqrt(magnitude2);

        if (magnitude1 == 0 || magnitude2 == 0)
            return 0;

        return dotProduct / (magnitude1 * magnitude2);
    }

    private List<string> ExtractKeywords(string query)
    {
        // 简单的关键词提取：分词并过滤停用词
        var stopWords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "的", "是", "在", "和", "了", "有", "我", "你", "他", "她", "它",
            "这", "那", "什么", "怎么", "哪", "为什么", "吗", "吧", "呢",
            "the", "is", "are", "was", "were", "been", "be", "have", "has", "had",
            "do", "does", "did", "will", "would", "could", "should", "may", "might",
            "a", "an", "and", "or", "but", "in", "on", "at", "to", "for"
        };

        var words = Regex.Split(query.ToLower(), @"\W+")
            .Where(w => !string.IsNullOrWhiteSpace(w) && !stopWords.Contains(w))
            .Distinct()
            .ToList();

        return words;
    }

    private float CalculateKeywordScore(string content, List<string> keywords, bool useFuzzyMatching)
    {
        if (keywords.Count == 0) return 0;

        var contentLower = content.ToLower();
        float matchCount = 0;

        foreach (var keyword in keywords)
        {
            if (useFuzzyMatching)
            {
                // 模糊匹配：包含即可
                if (contentLower.Contains(keyword))
                {
                    matchCount += 1;
                }
            }
            else
            {
                // 精确匹配：完整单词匹配
                var pattern = $@"\b{Regex.Escape(keyword)}\b";
                if (Regex.IsMatch(contentLower, pattern))
                {
                    matchCount += 1;
                }
            }
        }

        // 归一化分数到 0-1 范围
        return matchCount / keywords.Count;
    }

    private string HighlightKeywords(string content, List<string> keywords)
    {
        var highlightedContent = content;
        
        foreach (var keyword in keywords)
        {
            // 使用正则表达式进行不区分大小写的替换
            var pattern = $@"\b({Regex.Escape(keyword)})\b";
            highlightedContent = Regex.Replace(
                highlightedContent,
                pattern,
                "<mark>$1</mark>",
                RegexOptions.IgnoreCase);
        }

        return highlightedContent;
    }

    #endregion
}