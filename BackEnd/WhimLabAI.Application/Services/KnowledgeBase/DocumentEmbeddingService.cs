using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.KnowledgeBase;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Results;
using System.Diagnostics;

namespace WhimLabAI.Application.Services.KnowledgeBase;

/// <summary>
/// 文档嵌入服务实现
/// </summary>
public class DocumentEmbeddingService : IDocumentEmbeddingService
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IDocumentChunkRepository _chunkRepository;
    private readonly IKnowledgeBaseRepository _knowledgeBaseRepository;
    private readonly IEmbeddingService _embeddingService;
    private readonly IVectorDatabaseFactory _vectorDbFactory;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<DocumentEmbeddingService> _logger;

    // 批处理大小
    private const int BatchSize = 10;

    public DocumentEmbeddingService(
        IDocumentRepository documentRepository,
        IDocumentChunkRepository chunkRepository,
        IKnowledgeBaseRepository knowledgeBaseRepository,
        IEmbeddingService embeddingService,
        IVectorDatabaseFactory vectorDbFactory,
        IUnitOfWork unitOfWork,
        ILogger<DocumentEmbeddingService> logger)
    {
        _documentRepository = documentRepository;
        _chunkRepository = chunkRepository;
        _knowledgeBaseRepository = knowledgeBaseRepository;
        _embeddingService = embeddingService;
        _vectorDbFactory = vectorDbFactory;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<DocumentEmbeddingResult>> EmbedDocumentAsync(
        Guid documentId,
        bool forceRegenerate = false,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new DocumentEmbeddingResult { DocumentId = documentId };

        try
        {
            // 获取文档
            var document = await _documentRepository.GetByIdAsync(documentId, cancellationToken);
            if (document == null)
            {
                return Result<DocumentEmbeddingResult>.Failure("Document not found");
            }

            // 获取知识库配置
            var knowledgeBase = await _knowledgeBaseRepository.GetByIdAsync(document.KnowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<DocumentEmbeddingResult>.Failure("Knowledge base not found");
            }

            // 获取向量数据库
            var vectorDb = _vectorDbFactory.GetVectorDatabase(knowledgeBase.VectorDbType, knowledgeBase.VectorDbConfig);

            // 获取文档的所有块
            var chunks = (await _chunkRepository.GetByDocumentIdAsync(documentId, cancellationToken)).ToList();
            result.TotalChunks = chunks.Count;

            // 筛选需要嵌入的块
            var chunksToEmbed = forceRegenerate 
                ? chunks 
                : chunks.Where(c => !c.IsEmbedded).ToList();

            if (chunksToEmbed.Count == 0)
            {
                result.Success = true;
                result.EmbeddedChunks = chunks.Count(c => c.IsEmbedded);
                result.ProcessingTime = stopwatch.Elapsed;
                return Result<DocumentEmbeddingResult>.Success(result);
            }

            _logger.LogInformation("Starting embedding for document {DocumentId} with {ChunkCount} chunks", 
                documentId, chunksToEmbed.Count);

            // 批量处理嵌入
            for (int i = 0; i < chunksToEmbed.Count; i += BatchSize)
            {
                var batch = chunksToEmbed.Skip(i).Take(BatchSize).ToList();
                var texts = batch.Select(c => c.Content).ToList();

                try
                {
                    // 生成嵌入向量
                    var embeddingsResult = await _embeddingService.GenerateEmbeddingsAsync(
                        texts, 
                        knowledgeBase.EmbeddingModel,
                        cancellationToken);

                    if (!embeddingsResult.IsSuccess)
                    {
                        _logger.LogWarning("Failed to generate embeddings for batch: {Error}", 
                            embeddingsResult.Error);
                        
                        foreach (var chunk in batch)
                        {
                            result.Errors.Add(new ChunkEmbeddingError
                            {
                                ChunkId = chunk.Id,
                                ChunkIndex = chunk.ChunkIndex,
                                ErrorMessage = embeddingsResult.Error ?? "Unknown error"
                            });
                        }
                        result.FailedChunks += batch.Count;
                        continue;
                    }

                    var embeddings = embeddingsResult.Value;

                    // 存储向量到向量数据库
                    for (int j = 0; j < batch.Count; j++)
                    {
                        var chunk = batch[j];
                        var embedding = embeddings[j];

                        var metadata = new Dictionary<string, object>
                        {
                            ["ChunkId"] = chunk.Id.ToString(),
                            ["DocumentId"] = document.Id.ToString(),
                            ["KnowledgeBaseId"] = knowledgeBase.Id.ToString(),
                            ["ChunkIndex"] = chunk.ChunkIndex
                        };

                        var insertResult = await vectorDb.InsertVectorAsync(
                            knowledgeBase.Name,
                            embedding,
                            metadata,
                            chunk.Id.ToString(),
                            cancellationToken);

                        if (insertResult.IsSuccess)
                        {
                            // 更新块状态
                            chunk.MarkAsEmbedded(insertResult.Value);
                            result.EmbeddedChunks++;
                        }
                        else
                        {
                            result.Errors.Add(new ChunkEmbeddingError
                            {
                                ChunkId = chunk.Id,
                                ChunkIndex = chunk.ChunkIndex,
                                ErrorMessage = insertResult.Error ?? "Failed to insert vector"
                            });
                            result.FailedChunks++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing embedding batch");
                    foreach (var chunk in batch)
                    {
                        result.Errors.Add(new ChunkEmbeddingError
                        {
                            ChunkId = chunk.Id,
                            ChunkIndex = chunk.ChunkIndex,
                            ErrorMessage = ex.Message
                        });
                    }
                    result.FailedChunks += batch.Count;
                }
            }

            // 保存更改
            await _unitOfWork.CommitAsync(cancellationToken);

            // 更新文档状态
            if (result.EmbeddedChunks == result.TotalChunks)
            {
                document.MarkAsProcessed();
                await _unitOfWork.CommitAsync(cancellationToken);
            }

            result.Success = result.FailedChunks == 0;
            result.ProcessingTime = stopwatch.Elapsed;

            _logger.LogInformation("Completed embedding for document {DocumentId}. Embedded: {Embedded}, Failed: {Failed}", 
                documentId, result.EmbeddedChunks, result.FailedChunks);

            return Result<DocumentEmbeddingResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to embed document {DocumentId}", documentId);
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.ProcessingTime = stopwatch.Elapsed;
            return Result<DocumentEmbeddingResult>.Failure($"Failed to embed document: {ex.Message}");
        }
    }

    public async Task<Result<IList<DocumentEmbeddingResult>>> EmbedDocumentsAsync(
        IList<Guid> documentIds,
        bool forceRegenerate = false,
        CancellationToken cancellationToken = default)
    {
        var results = new List<DocumentEmbeddingResult>();

        foreach (var documentId in documentIds)
        {
            var result = await EmbedDocumentAsync(documentId, forceRegenerate, cancellationToken);
            results.Add(result.IsSuccess ? result.Value : new DocumentEmbeddingResult 
            { 
                DocumentId = documentId, 
                Success = false, 
                ErrorMessage = result.Error 
            });
        }

        return Result<IList<DocumentEmbeddingResult>>.Success(results);
    }

    public async Task<Result<KnowledgeBaseEmbeddingResult>> EmbedKnowledgeBaseAsync(
        Guid knowledgeBaseId,
        bool forceRegenerate = false,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new KnowledgeBaseEmbeddingResult { KnowledgeBaseId = knowledgeBaseId };

        try
        {
            // 获取知识库
            var knowledgeBase = await _knowledgeBaseRepository.GetByIdAsync(knowledgeBaseId, cancellationToken);
            if (knowledgeBase == null)
            {
                return Result<KnowledgeBaseEmbeddingResult>.Failure("Knowledge base not found");
            }

            // 获取所有文档
            var documents = (await _documentRepository.GetByKnowledgeBaseIdAsync(knowledgeBaseId, false, cancellationToken)).ToList();
            result.TotalDocuments = documents.Count;

            // 处理每个文档
            foreach (var document in documents)
            {
                var embeddingResult = await EmbedDocumentAsync(document.Id, forceRegenerate, cancellationToken);
                
                if (embeddingResult.IsSuccess)
                {
                    var docResult = embeddingResult.Value;
                    result.DocumentResults.Add(docResult);
                    result.TotalChunks += docResult.TotalChunks;
                    result.EmbeddedChunks += docResult.EmbeddedChunks;
                    
                    if (docResult.Success)
                    {
                        result.ProcessedDocuments++;
                    }
                    else
                    {
                        result.FailedDocuments++;
                    }
                }
                else
                {
                    result.FailedDocuments++;
                    result.DocumentResults.Add(new DocumentEmbeddingResult
                    {
                        DocumentId = document.Id,
                        Success = false,
                        ErrorMessage = embeddingResult.Error
                    });
                }
            }

            result.ProcessingTime = stopwatch.Elapsed;

            _logger.LogInformation("Completed embedding for knowledge base {KnowledgeBaseId}. Documents: {Processed}/{Total}, Chunks: {Embedded}/{Total}", 
                knowledgeBaseId, result.ProcessedDocuments, result.TotalDocuments, 
                result.EmbeddedChunks, result.TotalChunks);

            return Result<KnowledgeBaseEmbeddingResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to embed knowledge base {KnowledgeBaseId}", knowledgeBaseId);
            return Result<KnowledgeBaseEmbeddingResult>.Failure($"Failed to embed knowledge base: {ex.Message}");
        }
    }

    public async Task<Result<DocumentEmbeddingStatus>> GetEmbeddingStatusAsync(
        Guid documentId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var document = await _documentRepository.GetByIdAsync(documentId, cancellationToken);
            if (document == null)
            {
                return Result<DocumentEmbeddingStatus>.Failure("Document not found");
            }

            var chunks = (await _chunkRepository.GetByDocumentIdAsync(documentId, cancellationToken)).ToList();
            
            var status = new DocumentEmbeddingStatus
            {
                DocumentId = documentId,
                TotalChunks = chunks.Count,
                EmbeddedChunks = chunks.Count(c => c.IsEmbedded),
                PendingChunks = chunks.Count(c => !c.IsEmbedded),
                LastEmbeddedAt = chunks
                    .Where(c => c.EmbeddedAt.HasValue)
                    .OrderByDescending(c => c.EmbeddedAt)
                    .FirstOrDefault()?.EmbeddedAt
            };

            return Result<DocumentEmbeddingStatus>.Success(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get embedding status for document {DocumentId}", documentId);
            return Result<DocumentEmbeddingStatus>.Failure($"Failed to get embedding status: {ex.Message}");
        }
    }
}