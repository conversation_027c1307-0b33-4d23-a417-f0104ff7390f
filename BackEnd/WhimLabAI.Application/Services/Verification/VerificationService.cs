using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Constants;

namespace WhimLabAI.Application.Services.Verification;

public class VerificationService : IVerificationService
{
    private readonly ICacheService _cacheService;
    private readonly IMessageService _messageService;
    private readonly ILogger<VerificationService> _logger;
    private readonly VerificationCodeOptions _codeOptions;
    private readonly CaptchaOptions _captchaOptions;
    private readonly Random _random = new();

    public VerificationService(
        ICacheService cacheService,
        IMessageService messageService,
        ILogger<VerificationService> logger,
        IOptions<VerificationCodeOptions> codeOptions,
        IOptions<CaptchaOptions> captchaOptions)
    {
        _cacheService = cacheService;
        _messageService = messageService;
        _logger = logger;
        _codeOptions = codeOptions.Value;
        _captchaOptions = captchaOptions.Value;
    }

    public async Task<Result<CaptchaResult>> GenerateCaptchaAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // 生成验证码ID和文本
            var captchaId = Guid.NewGuid().ToString();
            var code = GenerateCaptchaCode();
            
            // 生成验证码图片
            var imageData = GenerateCaptchaImage(code);
            
            // 存储验证码到缓存
            var cacheKey = $"captcha:{captchaId}";
            await _cacheService.SetStringAsync(cacheKey, code.ToUpper(), 
                TimeSpan.FromMinutes(_captchaOptions.ExpirationMinutes), cancellationToken);
            
            var result = new CaptchaResult
            {
                CaptchaId = captchaId,
                ImageData = imageData,
                ContentType = "image/svg+xml",
                ExpiresAt = DateTime.UtcNow.AddMinutes(_captchaOptions.ExpirationMinutes)
            };
            
            _logger.LogInformation("图形验证码已生成: CaptchaId={CaptchaId}", captchaId);
            return Result<CaptchaResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成图形验证码失败");
            return Result<CaptchaResult>.Failure("生成验证码失败");
        }
    }

    public async Task<Result<bool>> VerifyCaptchaAsync(string captchaId, string code, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"captcha:{captchaId}";
            var cachedCode = await _cacheService.GetStringAsync(cacheKey, cancellationToken);
            
            if (string.IsNullOrEmpty(cachedCode))
            {
                return Result<bool>.Failure("验证码已过期或不存在");
            }
            
            var isValid = string.Equals(cachedCode, code.ToUpper(), StringComparison.OrdinalIgnoreCase);
            
            if (isValid)
            {
                // 验证成功，清除验证码
                await _cacheService.RemoveAsync(cacheKey, cancellationToken);
                _logger.LogInformation("图形验证码验证成功: CaptchaId={CaptchaId}", captchaId);
                return Result<bool>.Success(true);
            }
            else
            {
                _logger.LogWarning("图形验证码验证失败: CaptchaId={CaptchaId}, Code={Code}", captchaId, code);
                return Result<bool>.Failure("验证码错误");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图形验证码验证异常: CaptchaId={CaptchaId}", captchaId);
            return Result<bool>.Failure("验证码验证失败");
        }
    }

    public async Task<Result<bool>> SendEmailCodeAsync(string email, VerificationType type, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查发送频率限制
            var rateLimitKey = $"verification:rate:{email}:{type}";
            var lastSent = await _cacheService.GetAsync<DateTime?>(rateLimitKey, cancellationToken);
            
            if (lastSent.HasValue && DateTime.UtcNow - lastSent.Value < TimeSpan.FromSeconds(_codeOptions.ResendIntervalSeconds))
            {
                return Result<bool>.Failure($"请等待{_codeOptions.ResendIntervalSeconds}秒后再次发送");
            }
            
            // 生成验证码
            var code = GenerateVerificationCode();
            
            // 存储验证码
            var cacheKey = $"verification:code:{type}:{email}";
            await _cacheService.SetAsync(cacheKey, code, TimeSpan.FromMinutes(_codeOptions.ExpirationMinutes), cancellationToken);
            
            // 发送邮件
            var emailMessage = new EmailMessage
            {
                To = new List<string> { email },
                Subject = GetEmailSubject(type),
                Body = GetEmailBody(code, type),
                IsHtml = true
            };
            
            var sendResult = await _messageService.SendEmailAsync(emailMessage, cancellationToken);
            
            if (sendResult.Success)
            {
                // 记录发送时间
                await _cacheService.SetAsync(rateLimitKey, DateTime.UtcNow, 
                    TimeSpan.FromSeconds(_codeOptions.ResendIntervalSeconds), cancellationToken);
                
                _logger.LogInformation("邮件验证码发送成功: Email={Email}, Type={Type}", email, type);
                return Result<bool>.Success(true);
            }
            else
            {
                _logger.LogError("邮件验证码发送失败: Email={Email}, Type={Type}, Error={Error}", 
                    email, type, sendResult.ErrorMessage);
                return Result<bool>.Failure("邮件发送失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "邮件验证码发送异常: Email={Email}, Type={Type}", email, type);
            return Result<bool>.Failure("邮件发送失败");
        }
    }

    public async Task<Result<bool>> SendSmsCodeAsync(string phoneNumber, VerificationType type, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查发送频率限制
            var rateLimitKey = $"verification:rate:{phoneNumber}:{type}";
            var lastSent = await _cacheService.GetAsync<DateTime?>(rateLimitKey, cancellationToken);
            
            if (lastSent.HasValue && DateTime.UtcNow - lastSent.Value < TimeSpan.FromSeconds(_codeOptions.ResendIntervalSeconds))
            {
                return Result<bool>.Failure($"请等待{_codeOptions.ResendIntervalSeconds}秒后再次发送");
            }
            
            // 生成验证码
            var code = GenerateVerificationCode();
            
            // 存储验证码
            var cacheKey = $"verification:code:{type}:{phoneNumber}";
            await _cacheService.SetAsync(cacheKey, code, TimeSpan.FromMinutes(_codeOptions.ExpirationMinutes), cancellationToken);
            
            // 发送短信
            var smsMessage = new SmsMessage
            {
                PhoneNumber = phoneNumber,
                Content = GetSmsContent(code, type)
            };
            
            var sendResult = await _messageService.SendSmsAsync(smsMessage, cancellationToken);
            
            if (sendResult.Success)
            {
                // 记录发送时间
                await _cacheService.SetAsync(rateLimitKey, DateTime.UtcNow, 
                    TimeSpan.FromSeconds(_codeOptions.ResendIntervalSeconds), cancellationToken);
                
                _logger.LogInformation("短信验证码发送成功: Phone={Phone}, Type={Type}", phoneNumber, type);
                return Result<bool>.Success(true);
            }
            else
            {
                _logger.LogError("短信验证码发送失败: Phone={Phone}, Type={Type}, Error={Error}", 
                    phoneNumber, type, sendResult.ErrorMessage);
                return Result<bool>.Failure("短信发送失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "短信验证码发送异常: Phone={Phone}, Type={Type}", phoneNumber, type);
            return Result<bool>.Failure("短信发送失败");
        }
    }

    public async Task<Result<bool>> VerifyCodeAsync(string target, string code, VerificationType type, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"verification:code:{type}:{target}";
            var cachedCode = await _cacheService.GetStringAsync(cacheKey, cancellationToken);
            
            if (string.IsNullOrEmpty(cachedCode))
            {
                return Result<bool>.Failure("验证码已过期或不存在");
            }
            
            // 检查尝试次数
            var attemptKey = $"verification:attempt:{type}:{target}";
            var attempts = await _cacheService.GetAsync<int>(attemptKey, cancellationToken);
            
            if (attempts >= _codeOptions.MaxAttempts)
            {
                return Result<bool>.Failure("验证码尝试次数过多，请重新获取");
            }
            
            if (cachedCode == code)
            {
                // 验证成功，清除验证码和尝试次数
                await _cacheService.RemoveAsync(cacheKey, cancellationToken);
                await _cacheService.RemoveAsync(attemptKey, cancellationToken);
                
                // 标记为已验证
                var verifiedKey = $"verification:verified:{type}:{target}";
                await _cacheService.SetAsync(verifiedKey, true, TimeSpan.FromMinutes(30), cancellationToken);
                
                _logger.LogInformation("验证码验证成功: Target={Target}, Type={Type}", target, type);
                return Result<bool>.Success(true);
            }
            else
            {
                // 增加尝试次数
                await _cacheService.SetAsync(attemptKey, attempts + 1, 
                    TimeSpan.FromMinutes(_codeOptions.ExpirationMinutes), cancellationToken);
                
                _logger.LogWarning("验证码验证失败: Target={Target}, Type={Type}, Attempts={Attempts}", 
                    target, type, attempts + 1);
                return Result<bool>.Failure("验证码错误");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证码验证异常: Target={Target}, Type={Type}", target, type);
            return Result<bool>.Failure("验证码验证失败");
        }
    }

    public async Task<Result<bool>> IsVerifiedAsync(string target, VerificationType type, CancellationToken cancellationToken = default)
    {
        try
        {
            var verifiedKey = $"verification:verified:{type}:{target}";
            var isVerified = await _cacheService.GetAsync<bool>(verifiedKey, cancellationToken);
            
            return Result<bool>.Success(isVerified);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查验证状态异常: Target={Target}, Type={Type}", target, type);
            return Result<bool>.Failure("检查验证状态失败");
        }
    }

    public async Task<Result<bool>> ClearVerificationAsync(string target, VerificationType type, CancellationToken cancellationToken = default)
    {
        try
        {
            var verifiedKey = $"verification:verified:{type}:{target}";
            await _cacheService.RemoveAsync(verifiedKey, cancellationToken);
            
            _logger.LogInformation("验证状态已清除: Target={Target}, Type={Type}", target, type);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除验证状态异常: Target={Target}, Type={Type}", target, type);
            return Result<bool>.Failure("清除验证状态失败");
        }
    }

    private string GenerateCaptchaCode()
    {
        var code = new StringBuilder();
        for (int i = 0; i < _captchaOptions.CodeLength; i++)
        {
            code.Append(_captchaOptions.Characters[_random.Next(_captchaOptions.Characters.Length)]);
        }
        return code.ToString();
    }

    private string GenerateVerificationCode()
    {
        var min = (int)Math.Pow(10, _codeOptions.CodeLength - 1);
        var max = (int)Math.Pow(10, _codeOptions.CodeLength) - 1;
        return _random.Next(min, max).ToString();
    }

    private byte[] GenerateCaptchaImage(string code)
    {
        // 当前实现使用SVG格式生成验证码，这对大多数应用场景已经足够
        // 如需更复杂的图形验证码（如扭曲、噪点等），可以：
        // 1. 安装 SkiaSharp: Install-Package SkiaSharp
        // 2. 或安装 ImageSharp: Install-Package SixLabors.ImageSharp
        // 3. 实现基于位图的验证码生成
        
        var svg = GenerateSimpleSvgCaptcha(code);
        return Encoding.UTF8.GetBytes(svg);
    }
    
    private string GenerateSimpleSvgCaptcha(string code)
    {
        var width = _captchaOptions.Width;
        var height = _captchaOptions.Height;
        var fontSize = _captchaOptions.FontSize;
        
        var svg = new StringBuilder();
        svg.AppendLine($"<svg width=\"{width}\" height=\"{height}\" xmlns=\"http://www.w3.org/2000/svg\">");
        
        // 背景
        svg.AppendLine($"<rect width=\"{width}\" height=\"{height}\" fill=\"#f0f0f0\" stroke=\"#ccc\" stroke-width=\"1\"/>");
        
        // 噪点
        for (int i = 0; i < _captchaOptions.NoiseLevel * 5; i++)
        {
            var x = _random.Next(width);
            var y = _random.Next(height);
            var color = $"rgb({_random.Next(256)},{_random.Next(256)},{_random.Next(256)})";
            svg.AppendLine($"<circle cx=\"{x}\" cy=\"{y}\" r=\"1\" fill=\"{color}\"/>");
        }
        
        // 干扰线
        for (int i = 0; i < _captchaOptions.NoiseLevel; i++)
        {
            var x1 = _random.Next(width);
            var y1 = _random.Next(height);
            var x2 = _random.Next(width);
            var y2 = _random.Next(height);
            var color = $"rgb({_random.Next(200)},{_random.Next(200)},{_random.Next(200)})";
            svg.AppendLine($"<line x1=\"{x1}\" y1=\"{y1}\" x2=\"{x2}\" y2=\"{y2}\" stroke=\"{color}\" stroke-width=\"1\"/>");
        }
        
        // 文本
        var charWidth = width / code.Length;
        for (int i = 0; i < code.Length; i++)
        {
            var x = i * charWidth + charWidth / 2 + _random.Next(-5, 5);
            var y = height / 2 + _random.Next(-5, 5);
            var rotation = _random.Next(-15, 15);
            var color = $"rgb({_random.Next(100)},{_random.Next(100)},{_random.Next(100)})";
            
            svg.AppendLine($"<text x=\"{x}\" y=\"{y}\" font-family=\"{_captchaOptions.FontFamily}\" font-size=\"{fontSize}\" " +
                          $"fill=\"{color}\" text-anchor=\"middle\" transform=\"rotate({rotation} {x} {y})\">{code[i]}</text>");
        }
        
        svg.AppendLine("</svg>");
        return svg.ToString();
    }

    private string GetEmailSubject(VerificationType type)
    {
        return type switch
        {
            VerificationType.Register => "【WhimLabAI】注册验证码",
            VerificationType.Login => "【WhimLabAI】登录验证码",
            VerificationType.ResetPassword => "【WhimLabAI】重置密码验证码",
            VerificationType.ChangeEmail => "【WhimLabAI】更换邮箱验证码",
            VerificationType.ChangePhone => "【WhimLabAI】更换手机号验证码",
            VerificationType.DeleteAccount => "【WhimLabAI】删除账号验证码",
            VerificationType.TwoFactorAuth => "【WhimLabAI】两步验证码",
            _ => "【WhimLabAI】验证码"
        };
    }

    private string GetEmailBody(string code, VerificationType type)
    {
        var purpose = type switch
        {
            VerificationType.Register => "注册账号",
            VerificationType.Login => "登录账号",
            VerificationType.ResetPassword => "重置密码",
            VerificationType.ChangeEmail => "更换邮箱",
            VerificationType.ChangePhone => "更换手机号",
            VerificationType.DeleteAccount => "删除账号",
            VerificationType.TwoFactorAuth => "两步验证",
            _ => "验证身份"
        };

        return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>验证码邮件</title>
</head>
<body>
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
        <h2 style='color: #333;'>WhimLabAI 验证码</h2>
        <p>您正在{purpose}，验证码为：</p>
        <div style='font-size: 24px; font-weight: bold; color: #007bff; padding: 20px; background-color: #f8f9fa; border-radius: 5px; text-align: center; margin: 20px 0;'>
            {code}
        </div>
        <p style='color: #666;'>验证码有效期为{_codeOptions.ExpirationMinutes}分钟，请及时使用。</p>
        <p style='color: #666;'>如果您没有进行此操作，请忽略此邮件。</p>
        <hr style='border: none; border-top: 1px solid #eee; margin: 20px 0;'>
        <p style='color: #999; font-size: 12px;'>此邮件由系统自动发送，请勿回复。</p>
    </div>
</body>
</html>";
    }

    private string GetSmsContent(string code, VerificationType type)
    {
        var purpose = type switch
        {
            VerificationType.Register => "注册",
            VerificationType.Login => "登录",
            VerificationType.ResetPassword => "重置密码",
            VerificationType.ChangeEmail => "更换邮箱",
            VerificationType.ChangePhone => "更换手机号",
            VerificationType.DeleteAccount => "删除账号",
            VerificationType.TwoFactorAuth => "两步验证",
            _ => "验证"
        };

        return $"【WhimLabAI】您的{purpose}验证码是：{code}，有效期{_codeOptions.ExpirationMinutes}分钟。如非本人操作，请忽略此短信。";
    }
}