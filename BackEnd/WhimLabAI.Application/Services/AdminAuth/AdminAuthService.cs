using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Dtos.Auth;
using WhimLabAI.Shared.Dtos.Admin.Rbac;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Constants;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Utilities;
using OtpNet;
using System.Security.Claims;
using WhimLabAI.Shared.Dtos.Admin;
using WhimLabAI.Shared.Dtos;
using System.Linq.Expressions;
using WhimLabAI.Domain.Entities.System;

namespace WhimLabAI.Application.Services.AdminAuth;

public class AdminAuthService : IAdminAuthService
{
    private readonly IAdminUserRepository _adminRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IJwtTokenService _jwtTokenService;
    private readonly ICacheService _cacheService;
    private readonly IVerificationService _verificationService;
    private readonly IAuditLogger _auditLogger;
    private readonly ILogger<AdminAuthService> _logger;
    private readonly IIpGeolocationService _ipGeolocationService;
    private readonly IAdminSessionRepository _sessionRepository;
    private readonly INotificationService _notificationService;
    private readonly IConfiguration _configuration;

    public AdminAuthService(
        IAdminUserRepository adminRepository,
        IUnitOfWork unitOfWork,
        IJwtTokenService jwtTokenService,
        ICacheService cacheService,
        IVerificationService verificationService,
        IAuditLogger auditLogger,
        ILogger<AdminAuthService> logger,
        IIpGeolocationService ipGeolocationService,
        IAdminSessionRepository sessionRepository,
        INotificationService notificationService,
        IConfiguration configuration)
    {
        _adminRepository = adminRepository;
        _unitOfWork = unitOfWork;
        _jwtTokenService = jwtTokenService;
        _cacheService = cacheService;
        _verificationService = verificationService;
        _auditLogger = auditLogger;
        _logger = logger;
        _ipGeolocationService = ipGeolocationService;
        _sessionRepository = sessionRepository;
        _notificationService = notificationService;
        _configuration = configuration;
    }

    public async Task<Result<AdminAuthResponseDto>> LoginAsync(AdminLoginDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查是否需要验证图形验证码
            var disableCaptcha = _configuration.GetValue<bool>("Development:DisableCaptcha");
            if (!disableCaptcha)
            {
                // 验证图形验证码（管理员登录必须输入验证码）
                var captchaResult = await _verificationService.VerifyCaptchaAsync(request.CaptchaId, request.CaptchaCode, cancellationToken);
                if (!captchaResult.IsSuccess || !captchaResult.Value)
                {
                    // 记录审计日志 - 验证码错误
                    await _auditLogger.LogSecurityEventAsync(
                        "AdminLoginFailed",
                        $"管理员登录失败：验证码错误，账号：{request.Account}",
                        "Medium",
                        new { Account = request.Account, IpAddress = request.IpAddress, Reason = "验证码错误或已过期" });
                        
                    return Result<AdminAuthResponseDto>.Failure("验证码错误或已过期");
                }
            }
            else
            {
                _logger.LogDebug("QuickDev模式中管理员验证码已禁用");
            }

            // 查找管理员用户（支持用户名、邮箱、手机号登录）
            WhimLabAI.Domain.Entities.User.AdminUser? admin = null;
            
            // 判断账号类型并查询
            if (IsEmail(request.Account))
            {
                admin = await _adminRepository.GetByEmailAsync(request.Account, cancellationToken);
            }
            else if (IsPhoneNumber(request.Account))
            {
                admin = await _adminRepository.GetByPhoneAsync(request.Account, cancellationToken);
            }
            else
            {
                // 默认按用户名查询
                admin = await _adminRepository.GetByUsernameAsync(request.Account, cancellationToken);
            }

            if (admin == null)
            {
                // 记录审计日志 - 账号不存在
                await _auditLogger.LogSecurityEventAsync(
                    "AdminLoginFailed",
                    $"管理员登录失败：账号不存在，账号：{request.Account}",
                    "High",
                    new { Account = request.Account, IpAddress = request.IpAddress, Reason = "账号不存在" });
                    
                return Result<AdminAuthResponseDto>.Failure("账号或密码错误");
            }

            // 检查账号状态
            if (admin.Status != UserStatus.Active)
            {
                // 记录审计日志 - 账号被禁用
                await _auditLogger.LogSecurityEventAsync(
                    "AdminLoginFailed",
                    $"管理员登录失败：账号被禁用，用户：{admin.Username}",
                    "High",
                    new { UserId = admin.Id, Username = admin.Username, IpAddress = request.IpAddress, Status = admin.Status.ToString(), Reason = "账号已被禁用" });
                    
                return Result<AdminAuthResponseDto>.Failure("账号已被禁用");
            }

            // 检查账号是否被锁定
            if (admin.IsLocked())
            {
                var remainingTime = admin.GetLockRemainingMinutes();
                
                // 记录审计日志 - 账号被锁定
                await _auditLogger.LogSecurityEventAsync(
                    "AdminLoginFailed",
                    $"管理员登录失败：账号被锁定，用户：{admin.Username}",
                    "High",
                    new { UserId = admin.Id, Username = admin.Username, IpAddress = request.IpAddress, RemainingMinutes = remainingTime, Reason = "账号已被锁定" });
                    
                return Result<AdminAuthResponseDto>.Failure($"账号已被锁定，请{remainingTime}分钟后重试");
            }

            // 根据登录方式进行验证
            if (request.LoginMethod == AdminLoginMethod.EmailCode || request.LoginMethod == AdminLoginMethod.PhoneCode)
            {
                // 验证码登录
                if (string.IsNullOrEmpty(request.VerificationCode))
                {
                    return Result<AdminAuthResponseDto>.Failure("验证码不能为空");
                }

                var target = request.LoginMethod == AdminLoginMethod.EmailCode ? admin.Email?.Value : admin.Phone?.Value;
                if (string.IsNullOrEmpty(target))
                {
                    return Result<AdminAuthResponseDto>.Failure("账号未绑定对应的邮箱或手机号");
                }

                var verificationType = VerificationType.Login;
                var verifyResult = await _verificationService.VerifyCodeAsync(target, request.VerificationCode, verificationType, cancellationToken);
                
                if (!verifyResult.IsSuccess)
                {
                    admin.RecordFailedLogin();
                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                    
                    // 记录审计日志 - 验证码登录失败
                    await _auditLogger.LogSecurityEventAsync(
                        "AdminLoginFailed",
                        $"管理员登录失败：验证码错误，用户：{admin.Username}，登录方式：{request.LoginMethod}",
                        "High",
                        new { UserId = admin.Id, Username = admin.Username, IpAddress = request.IpAddress, LoginMethod = request.LoginMethod.ToString(), FailedAttempts = admin.FailedLoginAttempts, Reason = "验证码错误或已过期" });
                    
                    if (admin.IsLocked())
                    {
                        return Result<AdminAuthResponseDto>.Failure($"验证码错误次数过多，账号已被锁定{BusinessConstants.Account.AccountLockMinutes}分钟");
                    }
                    
                    return Result<AdminAuthResponseDto>.Failure("验证码错误或已过期");
                }

                // 清除验证状态
                await _verificationService.ClearVerificationAsync(target, verificationType, cancellationToken);
            }
            else
            {
                // 密码登录
                if (!PasswordHelper.VerifyPassword(request.Password, admin.PasswordHash.Hash))
                {
                    admin.RecordFailedLogin();
                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                    
                    // 记录审计日志 - 密码错误
                    await _auditLogger.LogSecurityEventAsync(
                        "AdminLoginFailed",
                        $"管理员登录失败：密码错误，用户：{admin.Username}",
                        "High",
                        new { UserId = admin.Id, Username = admin.Username, IpAddress = request.IpAddress, LoginMethod = "Password", FailedAttempts = admin.FailedLoginAttempts, Reason = "密码错误" });

                    if (admin.IsLocked())
                    {
                        return Result<AdminAuthResponseDto>.Failure($"密码错误次数过多，账号已被锁定{BusinessConstants.Account.AccountLockMinutes}分钟");
                    }

                    return Result<AdminAuthResponseDto>.Failure($"账号或密码错误，剩余尝试次数: {BusinessConstants.Account.MaxLoginAttempts - admin.FailedLoginAttempts}");
                }
            }

            // 检查IP白名单
            if (!admin.IsIpAllowed(request.IpAddress))
            {
                // 记录审计日志 - IP不在白名单
                await _auditLogger.LogSecurityEventAsync(
                    "AdminLoginFailed",
                    $"管理员登录失败：IP地址不在白名单中，用户：{admin.Username}，IP：{request.IpAddress}",
                    "Critical",
                    new { UserId = admin.Id, Username = admin.Username, IpAddress = request.IpAddress, Reason = "IP地址不在白名单中" });
                    
                return Result<AdminAuthResponseDto>.Failure("您的IP地址未授权访问");
            }

            // 检查是否需要双因素认证
            if (admin.TwoFactorEnabled)
            {
                if (string.IsNullOrEmpty(request.TwoFactorCode))
                {
                    // 生成Admin 2FA挑战令牌
                    var challengeToken = _jwtTokenService.GenerateAdminTwoFactorChallengeToken(admin.Id, 5);

                    return Result<AdminAuthResponseDto>.Success(new AdminAuthResponseDto
                    {
                        RequiresTwoFactor = true,
                        TwoFactorChallengeToken = challengeToken
                    });
                }
                else
                {
                    // 验证双因素认证码
                    if (string.IsNullOrEmpty(request.TwoFactorChallengeToken))
                    {
                        return Result<AdminAuthResponseDto>.Failure("缺少双因素认证挑战令牌");
                    }

                    try
                    {
                        var challengePrincipal = _jwtTokenService.ValidateToken(request.TwoFactorChallengeToken);
                        var tokenType = _jwtTokenService.GetTokenTypeFromToken(challengePrincipal);
                        var challengeAdminId = _jwtTokenService.GetUserIdFromToken(challengePrincipal);

                        if (tokenType != "AdminTwoFactorChallenge" || challengeAdminId != admin.Id)
                        {
                            return Result<AdminAuthResponseDto>.Failure("无效的双因素认证挑战令牌");
                        }

                        // 验证双因素认证码
                        if (!VerifyTotpCode(admin.TwoFactorSecret, request.TwoFactorCode))
                        {
                            admin.RecordFailedLogin();
                            await _unitOfWork.SaveChangesAsync(cancellationToken);
                            
                            // 记录审计日志 - 双因素认证失败
                            await _auditLogger.LogSecurityEventAsync(
                                "AdminLoginFailed",
                                $"管理员登录失败：双因素认证码错误，用户：{admin.Username}",
                                "High",
                                new { UserId = admin.Id, Username = admin.Username, IpAddress = request.IpAddress, LoginMethod = request.LoginMethod.ToString(), Has2FA = true, FailedAttempts = admin.FailedLoginAttempts, Reason = "双因素认证码错误" });
                            
                            if (admin.IsLocked())
                            {
                                return Result<AdminAuthResponseDto>.Failure($"双因素认证失败次数过多，账号已被锁定{BusinessConstants.Account.AccountLockMinutes}分钟");
                            }
                            
                            return Result<AdminAuthResponseDto>.Failure("双因素认证码错误");
                        }
                    }
                    catch (Exception)
                    {
                        return Result<AdminAuthResponseDto>.Failure("无效的双因素认证挑战令牌");
                    }
                }
            }

            // 检查密码是否过期
            if (admin.IsPasswordExpired())
            {
                // 记录审计日志 - 密码过期
                await _auditLogger.LogSecurityEventAsync(
                    "AdminLoginPasswordExpired",
                    $"管理员登录时密码已过期，需要修改密码：{admin.Username}",
                    "Medium",
                    new { UserId = admin.Id, Username = admin.Username, IpAddress = request.IpAddress });
                    
                // 返回特殊响应，提示需要修改密码
                return Result<AdminAuthResponseDto>.Success(new AdminAuthResponseDto
                {
                    RequirePasswordChange = true,
                    PasswordChangeToken = _jwtTokenService.GeneratePasswordChangeToken(admin.Id, 30), // 30分钟有效
                    Message = "您的密码已过期，请修改密码后继续"
                });
            }

            // 更新登录信息
            admin.RecordSuccessfulLogin(request.IpAddress);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("管理员登录成功: {Username}", admin.Username);
            
            // 记录审计日志 - 登录成功
            await _auditLogger.LogActionAsync(
                "AdminLoginSuccess",
                "Auth",
                $"管理员登录成功：{admin.Username}",
                new { 
                    UserId = admin.Id, 
                    Username = admin.Username, 
                    IpAddress = request.IpAddress, 
                    UserAgent = request.UserAgent,
                    LoginMethod = request.LoginMethod.ToString(),
                    Has2FA = admin.TwoFactorEnabled,
                    IsSuper = admin.IsSuperAdmin,
                    Roles = admin.UserRoles.Select(ur => ur.Role?.Name).Where(n => n != null).ToList(),
                    PasswordExpiryDays = admin.GetPasswordExpiryDays()
                });

            // 生成令牌和响应
            var response = await GenerateAuthResponseAsync(admin, request.IpAddress, request.UserAgent, cancellationToken);
            return Result<AdminAuthResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "管理员登录失败: {Account}", request.Account);
            
            // 记录审计日志 - 系统错误
            await _auditLogger.LogSecurityEventAsync(
                "AdminLoginError",
                $"管理员登录异常：{request.Account}，错误：{ex.Message}",
                "High",
                new { Account = request.Account, IpAddress = request.IpAddress, Error = ex.Message });
                
            return Result<AdminAuthResponseDto>.Failure("登录失败，请稍后重试");
        }
    }

    public async Task<Result> LogoutAsync(Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 清除缓存的权限信息
            await _cacheService.RemoveAsync($"{SystemConstants.CacheKeys.AdminPermissions}{adminId}", cancellationToken);
            await _cacheService.RemoveAsync($"{SystemConstants.CacheKeys.UserProfile}{adminId}", cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "AdminLogout",
                "Auth",
                $"管理员登出",
                new { UserId = adminId });

            _logger.LogInformation("管理员登出成功: AdminId={AdminId}", adminId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "管理员登出失败: AdminId={AdminId}", adminId);
            return Result.Failure("登出失败，请稍后重试");
        }
    }

    public async Task<Result<AdminAuthResponseDto>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        try
        {
            // 从缓存中获取刷新令牌信息
            var cacheKey = $"{SystemConstants.CacheKeys.RefreshToken}{refreshToken}";
            var adminId = await _cacheService.GetAsync<Guid?>(cacheKey, cancellationToken);
            
            if (!adminId.HasValue)
            {
                return Result<AdminAuthResponseDto>.Failure("无效的刷新令牌");
            }

            var admin = await _adminRepository.GetWithRolesAsync(adminId.Value, cancellationToken);
            if (admin == null || admin.Status != UserStatus.Active)
            {
                return Result<AdminAuthResponseDto>.Failure("用户不存在或已被禁用");
            }

            // 生成新的令牌
            var response = await GenerateAuthResponseAsync(admin, cancellationToken: cancellationToken);
            
            // 删除旧的刷新令牌
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);
            
            return Result<AdminAuthResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新令牌失败");
            return Result<AdminAuthResponseDto>.Failure("刷新令牌失败，请重新登录");
        }
    }

    public async Task<Result> ChangePasswordAsync(Guid adminId, string oldPassword, string newPassword, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("用户不存在");
            }

            // 验证旧密码
            if (!PasswordHelper.VerifyPassword(oldPassword, admin.PasswordHash.Hash))
            {
                return Result.Failure("原密码错误");
            }

            // 更新密码
            admin.ChangePassword(oldPassword, newPassword);
            admin.UpdatedBy = adminId.ToString();

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "AdminChangePassword",
                "Auth",
                $"管理员 {admin.Username} 修改密码",
                new { 
                    UserId = adminId, 
                    Username = admin.Username,
                    Reason = "用户主动修改"
                });

            _logger.LogInformation("管理员修改密码成功: AdminId={AdminId}", adminId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修改密码失败: AdminId={AdminId}", adminId);
            return Result.Failure("修改密码失败，请稍后重试");
        }
    }

    public async Task<Result<ForceChangePasswordResponseDto>> ForceChangePasswordAsync(ForceChangePasswordDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证密码修改令牌
            ClaimsPrincipal principal;
            try
            {
                principal = _jwtTokenService.ValidateToken(request.PasswordChangeToken);
                var tokenType = _jwtTokenService.GetTokenTypeFromToken(principal);
                if (tokenType != "PasswordChange")
                {
                    return Result<ForceChangePasswordResponseDto>.Failure("无效的密码修改令牌");
                }
            }
            catch (Exception)
            {
                return Result<ForceChangePasswordResponseDto>.Failure("密码修改令牌已过期或无效");
            }

            // 获取用户ID
            var adminId = _jwtTokenService.GetUserIdFromToken(principal);
            var admin = await _adminRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result<ForceChangePasswordResponseDto>.Failure("用户不存在");
            }

            // 检查密码是否真的过期
            if (!admin.IsPasswordExpired())
            {
                return Result<ForceChangePasswordResponseDto>.Failure("密码未过期，无需强制修改");
            }

            // 重置密码（不需要验证旧密码）
            admin.ResetPassword(request.NewPassword, false); // false表示密码修改后不需要再次修改
            admin.UpdatedBy = adminId.ToString();

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "AdminPasswordChangedDueToExpiry",
                "Auth",
                $"管理员因密码过期而修改密码成功：{admin.Username}",
                new { UserId = adminId, Username = admin.Username });

            _logger.LogInformation("管理员因密码过期修改密码成功: AdminId={AdminId}", adminId);
            
            return Result<ForceChangePasswordResponseDto>.Success(new ForceChangePasswordResponseDto
            {
                Success = true,
                Message = "密码修改成功，请重新登录"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "强制修改密码失败");
            return Result<ForceChangePasswordResponseDto>.Failure("修改密码失败，请稍后重试");
        }
    }

    public async Task<Result> ResetPasswordAsync(string account, string newPassword, string verificationCode, CancellationToken cancellationToken = default)
    {
        try
        {
            // 查找管理员账号（支持用户名、邮箱、手机号）
            Domain.Entities.User.AdminUser? admin = null;
            
            // 先尝试用户名
            admin = await _adminRepository.GetByUsernameAsync(account, cancellationToken);
            
            // 如果不是用户名，尝试邮箱
            if (admin == null && account.Contains('@'))
            {
                admin = await _adminRepository.GetByEmailAsync(account, cancellationToken);
            }
            
            // 如果不是邮箱，尝试手机号
            if (admin == null && ValidationHelper.IsValidPhoneNumber(account))
            {
                admin = await _adminRepository.GetByPhoneAsync(account, cancellationToken);
            }
            
            if (admin == null)
            {
                return Result.Failure("账号不存在");
            }

            if (admin.Status != UserStatus.Active)
            {
                return Result.Failure("账号已被禁用");
            }

            // 获取验证码缓存键
            var cacheKey = $"{SystemConstants.CacheKeys.VerificationCode}{account}:PasswordReset";
            
            // 验证验证码
            var verifyResult = await _verificationService.VerifyCodeAsync(
                target: account,
                code: verificationCode,
                type: VerificationType.ResetPassword,
                cancellationToken: cancellationToken
            );

            if (!verifyResult.IsSuccess)
            {
                return Result.Failure(verifyResult.Error ?? "验证码错误");
            }

            // 重置密码
            admin.ResetPassword(newPassword, false); // false表示密码修改后不需要再次修改
            admin.UpdatedBy = "System";
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清除验证码
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "AdminPasswordReset",
                "Auth",
                $"管理员密码重置成功：{admin.Username}",
                new 
                { 
                    UserId = admin.Id, 
                    Username = admin.Username,
                    Account = account,
                    ResetMethod = "VerificationCode"
                });

            _logger.LogInformation("管理员密码重置成功: AdminId={AdminId}, Account={Account}", admin.Id, account);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置密码失败: Account={Account}", account);
            
            // 记录审计日志 - 密码重置失败
            await _auditLogger.LogSecurityEventAsync(
                "AdminPasswordResetFailed",
                $"管理员密码重置失败：{account}，错误：{ex.Message}",
                "Medium",
                new { Account = account, Error = ex.Message });
                
            return Result.Failure("重置密码失败，请稍后重试");
        }
    }

    public async Task<Result<object>> GetUserPermissionsAsync(Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 先从缓存获取
            var cacheKey = $"{SystemConstants.CacheKeys.AdminPermissions}{adminId}";
            var cachedPermissions = await _cacheService.GetAsync<List<PermissionDto>>(cacheKey, cancellationToken);
            
            if (cachedPermissions != null)
            {
                return Result<object>.Success(cachedPermissions);
            }

            // 从数据库获取
            var permissions = await _adminRepository.GetUserPermissionsAsync(adminId, cancellationToken);
            
            var permissionDtos = permissions.Select(p => new PermissionDto
            {
                Id = p.Id,
                Name = p.Name,
                Code = p.Code,
                Module = p.Category,
                ModuleName = GetModuleName(p.Category),
                Type = "Operation",
                ParentId = p.ParentId,
                Url = null,
                Icon = null,
                SortOrder = p.DisplayOrder,
                IsActive = p.IsEnabled,
                CreatedAt = p.CreatedAt
            }).ToList();

            // 缓存权限信息（30分钟）
            await _cacheService.SetAsync(cacheKey, permissionDtos, TimeSpan.FromMinutes(30), cancellationToken);

            return Result<object>.Success(permissionDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户权限失败: AdminId={AdminId}", adminId);
            return Result<object>.Failure("获取权限失败");
        }
    }
    
    public async Task<Result<object>> EnableTwoFactorAsync(Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result<object>.Failure("用户不存在");
            }
            
            if (admin.TwoFactorEnabled)
            {
                return Result<object>.Failure("双因素认证已启用");
            }
            
            // 生成新的TOTP密钥
            var secret = GenerateTotpSecret();
            
            // 生成配置二维码URI
            var uri = GenerateTotpUri(secret, admin.Username);
            
            // 暂时保存密钥到缓存，等待用户确认
            var cacheKey = $"{SystemConstants.CacheKeys.TempTotpSecret}{adminId}";
            await _cacheService.SetAsync(cacheKey, secret, TimeSpan.FromMinutes(10), cancellationToken);
            
            var response = new
            {
                Secret = secret,
                QrCodeUri = uri,
                ManualEntryKey = FormatSecretForManualEntry(secret)
            };
            
            _logger.LogInformation("管理员请求启用双因素认证: AdminId={AdminId}", adminId);
            return Result<object>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启用双因素认证失败: AdminId={AdminId}", adminId);
            return Result<object>.Failure("启用双因素认证失败");
        }
    }
    
    public async Task<Result> ConfirmTwoFactorAsync(Guid adminId, string code, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("用户不存在");
            }
            
            if (admin.TwoFactorEnabled)
            {
                return Result.Failure("双因素认证已启用");
            }
            
            // 从缓存获取临时密钥
            var cacheKey = $"{SystemConstants.CacheKeys.TempTotpSecret}{adminId}";
            var secret = await _cacheService.GetAsync<string>(cacheKey, cancellationToken);
            
            if (string.IsNullOrEmpty(secret))
            {
                return Result.Failure("验证码已过期，请重新启用");
            }
            
            // 验证TOTP码
            if (!VerifyTotpCode(secret, code))
            {
                return Result.Failure("验证码错误");
            }
            
            // 启用双因素认证
            admin.EnableTwoFactor(secret);
            admin.UpdatedBy = adminId.ToString();
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 删除临时密钥
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);
            
            // 生成备用恢复码（可选）
            var recoveryCodes = GenerateRecoveryCodes(8);
            
            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "EnableTwoFactor",
                "Auth",
                $"管理员 {admin.Username} 启用双因素认证",
                new { 
                    UserId = adminId, 
                    Username = admin.Username,
                    RecoveryCodesGenerated = recoveryCodes.Count
                });
            
            _logger.LogInformation("管理员成功启用双因素认证: AdminId={AdminId}", adminId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认双因素认证失败: AdminId={AdminId}", adminId);
            return Result.Failure("确认双因素认证失败");
        }
    }
    
    public async Task<Result> DisableTwoFactorAsync(Guid adminId, string password, CancellationToken cancellationToken = default)
    {
        try
        {
            var admin = await _adminRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result.Failure("用户不存在");
            }
            
            if (!admin.TwoFactorEnabled)
            {
                return Result.Failure("双因素认证未启用");
            }
            
            // 验证密码
            if (!PasswordHelper.VerifyPassword(password, admin.PasswordHash.Hash))
            {
                return Result.Failure("密码错误");
            }
            
            // 禁用双因素认证
            admin.DisableTwoFactor();
            admin.UpdatedBy = adminId.ToString();
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "DisableTwoFactor",
                "Auth",
                $"管理员 {admin.Username} 禁用双因素认证",
                new { 
                    UserId = adminId, 
                    Username = admin.Username
                });
            
            _logger.LogInformation("管理员禁用双因素认证: AdminId={AdminId}", adminId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "禁用双因素认证失败: AdminId={AdminId}", adminId);
            return Result.Failure("禁用双因素认证失败");
        }
    }

    private async Task<AdminAuthResponseDto> GenerateAuthResponseAsync(WhimLabAI.Domain.Entities.User.AdminUser admin, string? ipAddress = null, string? userAgent = null, CancellationToken cancellationToken = default)
    {
        // 重新获取完整的用户信息（包含角色和权限）
        var fullAdmin = await _adminRepository.GetWithRolesAsync(admin.Id, cancellationToken);
        if (fullAdmin == null)
        {
            throw new InvalidOperationException($"无法获取管理员完整信息: {admin.Id}");
        }

        // 获取角色列表
        var roleNames = fullAdmin.UserRoles.Where(ur => ur.Role != null && ur.Role.IsEnabled).Select(ur => ur.Role.Name).ToList();
        
        // 创建会话
        var sessionToken = Guid.NewGuid().ToString("N");
        var sessionExpiry = DateTime.UtcNow.AddHours(8); // 8小时会话过期
        var refreshToken = _jwtTokenService.GenerateRefreshToken();
        var refreshTokenExpiry = DateTime.UtcNow.AddDays(30);
        
        // 创建会话实体
        var session = new Domain.Entities.System.AdminUserSession(
            admin.Id,
            sessionToken,
            ipAddress ?? "Unknown",
            userAgent ?? "Unknown",
            sessionExpiry,
            admin.TwoFactorEnabled,
            refreshToken,
            refreshTokenExpiry);
        
        // 解析IP地理位置
        if (!string.IsNullOrEmpty(ipAddress))
        {
            var location = await _ipGeolocationService.GetLocationAsync(ipAddress);
            if (location != null)
            {
                // 获取历史登录位置
                var recentSessions = await _sessionRepository.GetActiveSessionsAsync(admin.Id);
                var previousLocations = recentSessions
                    .Where(s => s.Latitude.HasValue && s.Longitude.HasValue)
                    .Select(s => new IpGeolocationInfo
                    {
                        Latitude = s.Latitude,
                        Longitude = s.Longitude,
                        City = s.City,
                        Country = s.Country
                    })
                    .ToList();
                
                // 检测是否异地登录
                var isAnomalous = _ipGeolocationService.IsAnomalousLocation(location, previousLocations);
                
                // 更新会话位置信息
                session.UpdateLocation(
                    location.City,
                    location.Country,
                    location.Region,
                    location.Latitude,
                    location.Longitude,
                    isAnomalous);
                
                // 如果是异地登录，记录警报
                if (isAnomalous)
                {
                    _logger.LogWarning(
                        "检测到异地登录：用户 {Username} 从 {City}, {Country} 登录，IP: {IpAddress}",
                        admin.Username, location.City, location.Country, ipAddress);
                    
                    // 发送异地登录通知
                    var locationString = $"{location.City}, {location.Country}";
                    await _notificationService.NotifyAnomalousLoginAsync(
                        admin.Id,
                        locationString,
                        ipAddress,
                        DateTime.UtcNow,
                        cancellationToken);
                }
            }
        }
        
        // 保存会话
        await _sessionRepository.AddAsync(session);
        await _unitOfWork.SaveChangesAsync();
        
        // 生成访问令牌（包含会话ID）
        var accessToken = _jwtTokenService.GenerateAccessToken(fullAdmin.Id, fullAdmin.Username, UserType.Admin, roleNames, session.Id);

        // 缓存刷新令牌（30天）
        var cacheKey = $"{SystemConstants.CacheKeys.RefreshToken}{refreshToken}";
        await _cacheService.SetAsync(cacheKey, fullAdmin.Id, TimeSpan.FromDays(30));

        // 获取权限代码列表（使用fullAdmin的GetPermissionCodes方法）
        var permissionCodes = fullAdmin.GetPermissionCodes().ToList();

        // 构建响应
        return new AdminAuthResponseDto
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            ExpiresIn = 3600, // 1小时
            User = new AdminUserInfoDto
            {
                Id = fullAdmin.Id,
                Username = fullAdmin.Username,
                Email = fullAdmin.Email.Value,
                Nickname = fullAdmin.Nickname,
                Avatar = fullAdmin.Avatar,
                IsSuper = fullAdmin.IsSuperAdmin,
                LastLoginAt = fullAdmin.LastLoginAt,
                LastLoginIp = fullAdmin.LastLoginIp,
                Roles = fullAdmin.UserRoles.Where(ur => ur.Role != null && ur.Role.IsEnabled).Select(ur => new AdminRoleInfoDto
                {
                    Id = ur.Role.Id,
                    Name = ur.Role.Name,
                    Code = ur.Role.Code
                }).ToList(),
                Permissions = permissionCodes
            }
        };
    }
    
    private static bool IsEmail(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return false;
            
        // 简单的邮箱格式判断：包含@符号且@后有.
        var atIndex = input.IndexOf('@');
        if (atIndex <= 0 || atIndex == input.Length - 1)
            return false;
            
        var dotIndex = input.LastIndexOf('.');
        return dotIndex > atIndex + 1 && dotIndex < input.Length - 1;
    }
    
    private static bool IsPhoneNumber(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return false;
            
        // 中国手机号：11位数字，以1开头，第二位是3-9
        if (input.Length == 11 && input.StartsWith("1") && char.IsDigit(input[1]) && input[1] >= '3' && input[1] <= '9')
        {
            return input.All(char.IsDigit);
        }
        
        // 带国家码的手机号：+86 或 86 开头
        if (input.StartsWith("+86") && input.Length == 14)
        {
            return input.Substring(3).All(char.IsDigit);
        }
        
        if (input.StartsWith("86") && input.Length == 13)
        {
            return input.Substring(2).All(char.IsDigit);
        }
        
        return false;
    }
    
    /// <summary>
    /// 验证TOTP码
    /// </summary>
    private static bool VerifyTotpCode(string? secret, string code)
    {
        if (string.IsNullOrEmpty(secret) || string.IsNullOrEmpty(code))
            return false;
            
        try
        {
            var totp = new Totp(Base32Encoding.ToBytes(secret));
            
            // 验证当前时间窗口和前后一个时间窗口的码（容许一定的时间偏差）
            var window = new VerificationWindow(previous: 1, future: 1);
            
            return totp.VerifyTotp(code, out long timeStepMatched, window);
        }
        catch (Exception)
        {
            return false;
        }
    }
    
    /// <summary>
    /// 生成新的TOTP密钥
    /// </summary>
    public static string GenerateTotpSecret()
    {
        var key = KeyGeneration.GenerateRandomKey(20);
        return Base32Encoding.ToString(key);
    }
    
    /// <summary>
    /// 生成配置二维码URI
    /// </summary>
    public static string GenerateTotpUri(string secret, string username, string issuer = "WhimLabAI Admin")
    {
        var otpUri = new OtpUri(OtpType.Totp, secret, username, issuer);
        return otpUri.ToString();
    }
    
    /// <summary>
    /// 格式化密钥以便手动输入
    /// </summary>
    private static string FormatSecretForManualEntry(string secret)
    {
        // 每4个字符添加一个空格，便于用户手动输入
        var formatted = string.Empty;
        for (int i = 0; i < secret.Length; i += 4)
        {
            if (i > 0) formatted += " ";
            var length = Math.Min(4, secret.Length - i);
            formatted += secret.Substring(i, length);
        }
        return formatted;
    }
    
    /// <summary>
    /// 生成恢复码
    /// </summary>
    private static List<string> GenerateRecoveryCodes(int count)
    {
        var codes = new List<string>();
        var random = new Random();
        
        for (int i = 0; i < count; i++)
        {
            var code = string.Empty;
            for (int j = 0; j < 8; j++)
            {
                code += random.Next(0, 10).ToString();
            }
            codes.Add(code);
        }
        
        return codes;
    }

    public async Task<Result> SendVerificationCodeAsync(SendAdminVerificationCodeDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查是否需要验证图形验证码
            var disableCaptcha = _configuration.GetValue<bool>("Development:DisableCaptcha");
            if (!disableCaptcha)
            {
                // 验证图形验证码
                var captchaResult = await _verificationService.VerifyCaptchaAsync(request.CaptchaId, request.CaptchaCode, cancellationToken);
                if (!captchaResult.IsSuccess || !captchaResult.Value)
                {
                    return Result.Failure("验证码错误或已过期");
                }
            }

            // 查找管理员
            WhimLabAI.Domain.Entities.User.AdminUser? admin = null;
            
            if (ValidationHelper.IsValidEmail(request.Account))
            {
                admin = await _adminRepository.GetByEmailAsync(request.Account, cancellationToken);
            }
            else if (ValidationHelper.IsValidPhoneNumber(request.Account))
            {
                admin = await _adminRepository.GetByPhoneAsync(request.Account, cancellationToken);
            }

            if (admin == null)
            {
                return Result.Failure("账号不存在");
            }

            if (admin.Status != UserStatus.Active)
            {
                return Result.Failure("账号已被禁用");
            }

            // 根据账号类型发送验证码
            var verificationType = request.Type == "login" ? VerificationType.Login : VerificationType.Login;
            Result<bool> sendResult;
            
            if (ValidationHelper.IsValidEmail(request.Account))
            {
                // 发送邮件验证码
                sendResult = await _verificationService.SendEmailCodeAsync(request.Account, verificationType, cancellationToken);
            }
            else if (ValidationHelper.IsValidPhoneNumber(request.Account))
            {
                // 发送短信验证码
                sendResult = await _verificationService.SendSmsCodeAsync(request.Account, verificationType, cancellationToken);
            }
            else
            {
                return Result.Failure("无效的账号格式");
            }
            
            if (!sendResult.IsSuccess)
            {
                return Result.Failure(sendResult.Error);
            }

            _logger.LogInformation("管理员验证码已发送: {Account}", request.Account);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送管理员验证码失败: {Account}", request.Account);
            return Result.Failure("发送验证码失败，请稍后重试");
        }
    }

    public async Task<Result<AdminQrCodeResponseDto>> GenerateQrCodeAsync(GenerateAdminQrCodeDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 生成唯一的票据
            var ticket = Guid.NewGuid().ToString("N");
            var expiresIn = 300; // 5分钟过期

            // 生成二维码内容
            var qrContent = new
            {
                type = "admin_login",
                ticket = ticket,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                client = request.ClientId ?? "web"
            };
            var jsonOptions = new System.Text.Json.JsonSerializerOptions
            {
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            };
            var qrCode = System.Text.Json.JsonSerializer.Serialize(qrContent, jsonOptions);

            // 将二维码信息存储到缓存中
            var cacheKey = $"{SystemConstants.CacheKeys.AdminQrLogin}{ticket}";
            var qrData = new AdminQrLoginData
            {
                Ticket = ticket,
                Status = "pending",
                CreatedAt = DateTime.UtcNow,
                ClientId = request.ClientId
            };
            
            await _cacheService.SetAsync(cacheKey, qrData, TimeSpan.FromSeconds(expiresIn), cancellationToken);

            var response = new AdminQrCodeResponseDto
            {
                Ticket = ticket,
                QrCode = qrCode,
                ExpiresIn = expiresIn
            };

            _logger.LogInformation("管理员扫码登录二维码已生成: {Ticket}", ticket);
            return Result<AdminQrCodeResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成管理员扫码登录二维码失败");
            return Result<AdminQrCodeResponseDto>.Failure("生成二维码失败，请稍后重试");
        }
    }

    public async Task<Result<AdminQrStatusResponseDto>> CheckQrStatusAsync(CheckAdminQrStatusDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"{SystemConstants.CacheKeys.AdminQrLogin}{request.Ticket}";
            var qrData = await _cacheService.GetAsync<AdminQrLoginData>(cacheKey, cancellationToken);

            if (qrData == null)
            {
                return Result<AdminQrStatusResponseDto>.Success(new AdminQrStatusResponseDto
                {
                    Status = "expired"
                });
            }

            var response = new AdminQrStatusResponseDto
            {
                Status = qrData.Status
            };

            if (qrData.Status == "scanned" && qrData.ScannerId.HasValue)
            {
                // 获取扫描者信息
                var scanner = await _adminRepository.GetByIdAsync(qrData.ScannerId.Value, cancellationToken);
                if (scanner != null)
                {
                    response.Scanner = new AdminScannerInfoDto
                    {
                        AdminId = scanner.Id,
                        Username = scanner.Username,
                        Nickname = scanner.Nickname,
                        Avatar = scanner.Avatar
                    };
                }
            }
            else if (qrData.Status == "confirmed" && qrData.AuthData != null)
            {
                response.AuthData = qrData.AuthData;
                
                // 确认后删除缓存
                await _cacheService.RemoveAsync(cacheKey, cancellationToken);
            }

            return Result<AdminQrStatusResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查管理员扫码状态失败: {Ticket}", request.Ticket);
            return Result<AdminQrStatusResponseDto>.Failure("检查扫码状态失败");
        }
    }

    public async Task<Result<AdminScannerInfoDto>> ScanQrCodeAsync(Guid adminId, ScanAdminQrCodeDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 解析二维码内容
            var jsonOptions = new System.Text.Json.JsonSerializerOptions
            {
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            };
            var qrContent = System.Text.Json.JsonSerializer.Deserialize<AdminQrContent>(request.QrCode, jsonOptions);
            if (qrContent == null || qrContent.Type != "admin_login" || string.IsNullOrEmpty(qrContent.Ticket))
            {
                return Result<AdminScannerInfoDto>.Failure("无效的二维码");
            }

            var cacheKey = $"{SystemConstants.CacheKeys.AdminQrLogin}{qrContent.Ticket}";
            var qrData = await _cacheService.GetAsync<AdminQrLoginData>(cacheKey, cancellationToken);

            if (qrData == null)
            {
                return Result<AdminScannerInfoDto>.Failure("二维码已过期");
            }

            if (qrData.Status != "pending")
            {
                return Result<AdminScannerInfoDto>.Failure("二维码已被使用");
            }

            // 获取扫描者信息
            var admin = await _adminRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null || admin.Status != UserStatus.Active)
            {
                return Result<AdminScannerInfoDto>.Failure("管理员账号异常");
            }

            // 更新扫码状态
            qrData.Status = "scanned";
            qrData.ScannerId = adminId;
            qrData.ScannedAt = DateTime.UtcNow;
            
            // 更新缓存，延长过期时间2分钟
            await _cacheService.SetAsync(cacheKey, qrData, TimeSpan.FromMinutes(2), cancellationToken);

            var response = new AdminScannerInfoDto
            {
                AdminId = admin.Id,
                Username = admin.Username,
                Nickname = admin.Nickname,
                Avatar = admin.Avatar
            };

            _logger.LogInformation("管理员扫描二维码成功: AdminId={AdminId}, Ticket={Ticket}", adminId, qrContent.Ticket);
            return Result<AdminScannerInfoDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "管理员扫描二维码失败: AdminId={AdminId}", adminId);
            return Result<AdminScannerInfoDto>.Failure("扫描二维码失败");
        }
    }

    public async Task<Result> ConfirmQrLoginAsync(Guid adminId, ConfirmAdminQrLoginDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"{SystemConstants.CacheKeys.AdminQrLogin}{request.Ticket}";
            var qrData = await _cacheService.GetAsync<AdminQrLoginData>(cacheKey, cancellationToken);

            if (qrData == null)
            {
                return Result.Failure("二维码已过期");
            }

            if (qrData.Status != "scanned" || qrData.ScannerId != adminId)
            {
                return Result.Failure("无效的操作");
            }

            if (!request.Approved)
            {
                // 拒绝登录
                await _cacheService.RemoveAsync(cacheKey, cancellationToken);
                _logger.LogInformation("管理员拒绝扫码登录: AdminId={AdminId}, Ticket={Ticket}", adminId, request.Ticket);
                return Result.Success();
            }

            // 获取管理员信息
            var admin = await _adminRepository.GetWithRolesAsync(adminId, cancellationToken);
            if (admin == null || admin.Status != UserStatus.Active)
            {
                return Result.Failure("管理员账号异常");
            }

            // 生成登录令牌
            var authResponse = await GenerateAuthResponseAsync(admin, "QR_LOGIN", "Mobile Scanner", cancellationToken);
            
            // 更新扫码状态
            qrData.Status = "confirmed";
            qrData.ConfirmedAt = DateTime.UtcNow;
            qrData.AuthData = authResponse;
            
            // 更新缓存，让Web端可以获取到登录信息
            await _cacheService.SetAsync(cacheKey, qrData, TimeSpan.FromSeconds(30), cancellationToken);

            // 记录登录
            admin.RecordSuccessfulLogin("QR_LOGIN");
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("管理员扫码登录成功: AdminId={AdminId}, Ticket={Ticket}", adminId, request.Ticket);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "管理员确认扫码登录失败: AdminId={AdminId}, Ticket={Ticket}", adminId, request.Ticket);
            return Result.Failure("确认登录失败");
        }
    }

    // 辅助类和数据结构
    private class AdminQrLoginData
    {
        public string Ticket { get; set; } = string.Empty;
        public string Status { get; set; } = "pending"; // pending, scanned, confirmed
        public DateTime CreatedAt { get; set; }
        public Guid? ScannerId { get; set; }
        public DateTime? ScannedAt { get; set; }
        public DateTime? ConfirmedAt { get; set; }
        public string? ClientId { get; set; }
        public AdminAuthResponseDto? AuthData { get; set; }
    }

    private class AdminQrContent
    {
        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;
        
        [System.Text.Json.Serialization.JsonPropertyName("ticket")]
        public string Ticket { get; set; } = string.Empty;
        
        [System.Text.Json.Serialization.JsonPropertyName("timestamp")]
        public long Timestamp { get; set; }
        
        [System.Text.Json.Serialization.JsonPropertyName("client")]
        public string? Client { get; set; }
    }
    
    private string GetModuleName(string category)
    {
        return category switch
        {
            "users" => "用户管理",
            "agents" => "智能体管理",
            "conversations" => "对话管理",
            "subscriptions" => "订阅管理",
            "analytics" => "数据分析",
            "system" => "系统管理",
            _ => category
        };
    }

    public async Task<Result<PagedResult<AdminLoginHistoryDto>>> GetLoginHistoryAsync(AdminLoginHistoryQueryDto query, CancellationToken cancellationToken = default)
    {
        try
        {
            // 构建查询表达式
            Expression<Func<AdminUserSession, bool>>? predicate = null;
            var predicates = new List<Expression<Func<AdminUserSession, bool>>>();

            // 管理员ID过滤
            if (query.AdminId.HasValue)
            {
                predicates.Add(s => s.AdminUserId == query.AdminId.Value);
            }

            // 时间范围过滤
            if (query.StartDate.HasValue)
            {
                predicates.Add(s => s.CreatedAt >= query.StartDate.Value);
            }
            if (query.EndDate.HasValue)
            {
                predicates.Add(s => s.CreatedAt <= query.EndDate.Value);
            }

            // 成功状态过滤（如果需要，可以通过LastLoginSuccessAt是否为null判断）
            if (query.IsSuccess.HasValue)
            {
                predicates.Add(s => s.IsActive == query.IsSuccess.Value);
            }

            // IP地址过滤
            if (!string.IsNullOrWhiteSpace(query.IpAddress))
            {
                predicates.Add(s => s.IpAddress.Contains(query.IpAddress));
            }

            // 异地登录过滤
            if (query.IsAnomalous.HasValue)
            {
                predicates.Add(s => s.IsAnomalousLocation == query.IsAnomalous.Value);
            }

            // 合并所有条件
            if (predicates.Any())
            {
                predicate = predicates.Aggregate((current, next) =>
                {
                    var parameter = Expression.Parameter(typeof(AdminUserSession), "s");
                    var left = Expression.Invoke(current, parameter);
                    var right = Expression.Invoke(next, parameter);
                    var body = Expression.AndAlso(left, right);
                    return Expression.Lambda<Func<AdminUserSession, bool>>(body, parameter);
                });
            }

            // 执行查询
            var (items, totalCount) = await _sessionRepository.GetLoginHistoryAsync(
                predicate,
                query.PageNumber - 1, // 转换为0基索引
                query.PageSize,
                query.SortBy,
                query.IsDescending,
                cancellationToken);

            // 获取管理员信息（用于补充用户名）
            var adminIds = items.Select(s => s.AdminUserId).Distinct().ToList();
            var admins = new Dictionary<Guid, WhimLabAI.Domain.Entities.User.AdminUser>();
            foreach (var adminId in adminIds)
            {
                var admin = await _adminRepository.GetByIdAsync(adminId, cancellationToken);
                if (admin != null)
                {
                    admins[adminId] = admin;
                }
            }

            // 转换为DTO
            var historyDtos = items.Select(session =>
            {
                var admin = admins.ContainsKey(session.AdminUserId) ? admins[session.AdminUserId] : null;
                
                return new AdminLoginHistoryDto
                {
                    Id = session.Id,
                    AdminId = session.AdminUserId,
                    Username = admin?.Username ?? "Unknown",
                    LoginTime = session.CreatedAt,
                    IpAddress = session.IpAddress,
                    UserAgent = session.UserAgent,
                    City = session.City,
                    Country = session.Country,
                    Region = session.Region,
                    Latitude = session.Latitude,
                    Longitude = session.Longitude,
                    IsAnomalousLocation = session.IsAnomalousLocation,
                    IsActive = session.IsActive,
                    LastActivityAt = session.LastActivityAt,
                    LogoutAt = session.IsActive ? null : session.UpdatedAt, // 如果会话非活跃，使用UpdatedAt作为登出时间
                    ExpiresAt = session.ExpiresAt,
                    LoginMethod = "Password", // 根据实际情况设置
                    Device = ParseDeviceInfo(session.UserAgent)
                };
            }).ToList();

            var result = new PagedResult<AdminLoginHistoryDto>(
                historyDtos,
                totalCount,
                query.PageNumber,
                query.PageSize);

            return Result<PagedResult<AdminLoginHistoryDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取登录历史失败");
            return Result<PagedResult<AdminLoginHistoryDto>>.Failure("获取登录历史失败");
        }
    }

    public async Task<Result<AdminLoginStatisticsDto>> GetLoginStatisticsAsync(Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取管理员信息
            var admin = await _adminRepository.GetByIdAsync(adminId, cancellationToken);
            if (admin == null)
            {
                return Result<AdminLoginStatisticsDto>.Failure("管理员不存在");
            }

            // 获取所有登录记录（最近90天）
            var cutoffDate = DateTime.UtcNow.AddDays(-90);
            var allSessions = await _sessionRepository.GetAsync(
                s => s.AdminUserId == adminId && s.CreatedAt >= cutoffDate,
                cancellationToken);

            // 获取活跃会话
            var activeSessions = await _sessionRepository.GetActiveSessionsAsync(adminId, cancellationToken);

            // 获取登录位置统计
            var locationStats = await _sessionRepository.GetLoginLocationStatisticsAsync(adminId, 30, cancellationToken);

            // 构建统计信息
            var statistics = new AdminLoginStatisticsDto
            {
                TotalLogins = allSessions.Count(),
                SuccessfulLogins = allSessions.Count(), // 所有会话记录都代表成功登录
                FailedLogins = admin.FailedLoginAttempts, // 从AdminUser获取失败次数
                AnomalousLogins = allSessions.Count(s => s.IsAnomalousLocation),
                LastLoginTime = admin.LastLoginAt,
                LastLoginIp = admin.LastLoginIp,
                LastLoginLocation = allSessions
                    .Where(s => !string.IsNullOrEmpty(s.City) && !string.IsNullOrEmpty(s.Country))
                    .OrderByDescending(s => s.CreatedAt)
                    .Select(s => $"{s.City}, {s.Country}")
                    .FirstOrDefault(),
                FrequentLocations = locationStats.Keys.Take(5).ToList(),
                ActiveSessions = activeSessions.Count()
            };

            return Result<AdminLoginStatisticsDto>.Success(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取登录统计信息失败: AdminId={AdminId}", adminId);
            return Result<AdminLoginStatisticsDto>.Failure("获取登录统计信息失败");
        }
    }

    public async Task<Result> TerminateSessionAsync(Guid adminId, Guid sessionId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证会话是否属于该管理员
            var session = await _sessionRepository.GetByIdAsync(sessionId, cancellationToken);
            if (session == null)
            {
                return Result.Failure("会话不存在");
            }

            if (session.AdminUserId != adminId)
            {
                return Result.Failure("无权终止此会话");
            }

            // 终止会话
            var result = await _sessionRepository.InvalidateSessionByIdAsync(sessionId, cancellationToken);
            if (!result)
            {
                return Result.Failure("终止会话失败");
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "AdminSessionTerminated",
                "Auth",
                $"管理员终止会话：SessionId={sessionId}",
                new { AdminId = adminId, SessionId = sessionId });

            _logger.LogInformation("管理员终止会话成功: AdminId={AdminId}, SessionId={SessionId}", adminId, sessionId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "终止会话失败: AdminId={AdminId}, SessionId={SessionId}", adminId, sessionId);
            return Result.Failure("终止会话失败");
        }
    }

    public async Task<Result> TerminateAllSessionsAsync(Guid adminId, bool exceptCurrent = true, CancellationToken cancellationToken = default)
    {
        try
        {
            bool result;
            
            if (exceptCurrent)
            {
                // 需要获取当前会话ID（从请求上下文中获取）
                // 这里假设当前会话ID可以从某处获取，实际实现时需要从HttpContext或JWT中提取
                // 暂时使用最新的活跃会话作为当前会话
                var activeSessions = await _sessionRepository.GetActiveSessionsAsync(adminId, cancellationToken);
                var currentSession = activeSessions.OrderByDescending(s => s.LastActivityAt).FirstOrDefault();
                
                if (currentSession != null)
                {
                    result = await _sessionRepository.InvalidateAllAdminSessionsExceptAsync(adminId, currentSession.Id, cancellationToken);
                }
                else
                {
                    // 如果找不到当前会话，则终止所有会话
                    result = await _sessionRepository.InvalidateAllAdminSessionsAsync(adminId, cancellationToken);
                }
            }
            else
            {
                // 终止所有会话
                result = await _sessionRepository.InvalidateAllAdminSessionsAsync(adminId, cancellationToken);
            }

            if (!result)
            {
                return Result.Failure("没有找到需要终止的会话");
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清除缓存
            await _cacheService.RemoveAsync($"{SystemConstants.CacheKeys.AdminPermissions}{adminId}", cancellationToken);
            await _cacheService.RemoveAsync($"{SystemConstants.CacheKeys.UserProfile}{adminId}", cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                "AdminAllSessionsTerminated",
                "Auth",
                $"管理员终止所有会话，保留当前会话：{exceptCurrent}",
                new { AdminId = adminId, ExceptCurrent = exceptCurrent });

            _logger.LogInformation("管理员终止所有会话成功: AdminId={AdminId}, ExceptCurrent={ExceptCurrent}", adminId, exceptCurrent);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "终止所有会话失败: AdminId={AdminId}", adminId);
            return Result.Failure("终止所有会话失败");
        }
    }

    private static DeviceInfo? ParseDeviceInfo(string userAgent)
    {
        if (string.IsNullOrEmpty(userAgent))
            return null;

        var deviceInfo = new DeviceInfo
        {
            IsMobile = userAgent.Contains("Mobile", StringComparison.OrdinalIgnoreCase) ||
                       userAgent.Contains("Android", StringComparison.OrdinalIgnoreCase) ||
                       userAgent.Contains("iPhone", StringComparison.OrdinalIgnoreCase) ||
                       userAgent.Contains("iPad", StringComparison.OrdinalIgnoreCase)
        };

        // 检测浏览器
        if (userAgent.Contains("Chrome", StringComparison.OrdinalIgnoreCase))
            deviceInfo.Browser = "Chrome";
        else if (userAgent.Contains("Firefox", StringComparison.OrdinalIgnoreCase))
            deviceInfo.Browser = "Firefox";
        else if (userAgent.Contains("Safari", StringComparison.OrdinalIgnoreCase))
            deviceInfo.Browser = "Safari";
        else if (userAgent.Contains("Edge", StringComparison.OrdinalIgnoreCase))
            deviceInfo.Browser = "Edge";
        else
            deviceInfo.Browser = "Other";

        // 检测操作系统
        if (userAgent.Contains("Windows", StringComparison.OrdinalIgnoreCase))
            deviceInfo.OperatingSystem = "Windows";
        else if (userAgent.Contains("Mac", StringComparison.OrdinalIgnoreCase))
            deviceInfo.OperatingSystem = "macOS";
        else if (userAgent.Contains("Linux", StringComparison.OrdinalIgnoreCase))
            deviceInfo.OperatingSystem = "Linux";
        else if (userAgent.Contains("Android", StringComparison.OrdinalIgnoreCase))
            deviceInfo.OperatingSystem = "Android";
        else if (userAgent.Contains("iOS", StringComparison.OrdinalIgnoreCase) || userAgent.Contains("iPhone", StringComparison.OrdinalIgnoreCase))
            deviceInfo.OperatingSystem = "iOS";
        else
            deviceInfo.OperatingSystem = "Other";

        // 设备类型
        if (userAgent.Contains("iPad", StringComparison.OrdinalIgnoreCase) || userAgent.Contains("Tablet", StringComparison.OrdinalIgnoreCase))
            deviceInfo.DeviceType = "Tablet";
        else if (deviceInfo.IsMobile)
            deviceInfo.DeviceType = "Mobile";
        else
            deviceInfo.DeviceType = "Desktop";

        return deviceInfo;
    }
}