using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Entities.Finance;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Dtos.Invoice;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;
using WhimLabAI.Abstractions.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace WhimLabAI.Application.Services.Finance;

/// <summary>
/// 发票服务实现
/// </summary>
public class InvoiceService : IInvoiceService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<InvoiceService> _logger;
    private readonly IPdfService _pdfService;
    private readonly IEmailService _emailService;
    private readonly IStorageService _storageService;
    private readonly ICustomerUserService _customerUserService;

    public InvoiceService(
        IUnitOfWork unitOfWork,
        ILogger<InvoiceService> logger,
        IPdfService pdfService,
        IEmailService emailService,
        IStorageService storageService,
        ICustomerUserService customerUserService)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _pdfService = pdfService;
        _emailService = emailService;
        _storageService = storageService;
        _customerUserService = customerUserService;
    }

    public async Task<Result<InvoiceDto>> CreateInvoiceAsync(CreateInvoiceDto dto, Guid customerUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Generate invoice number
            var invoiceNumber = await _unitOfWork.Invoices.GenerateInvoiceNumberAsync(cancellationToken);
            
            // Calculate due date
            var issueDate = DateTime.UtcNow;
            var dueDate = issueDate.AddDays(dto.DueDays);
            
            // Create company info if provided
            CompanyInfo? companyInfo = null;
            if (dto.CompanyInfo != null)
            {
                companyInfo = new CompanyInfo(
                    dto.CompanyInfo.CompanyName,
                    dto.CompanyInfo.TaxId,
                    dto.CompanyInfo.Address,
                    dto.CompanyInfo.Phone,
                    dto.CompanyInfo.BankName,
                    dto.CompanyInfo.BankAccount);
            }
            
            // Create invoice
            var invoice = new Invoice(
                invoiceNumber,
                customerUserId,
                issueDate,
                dueDate,
                Money.Create(0.06m, "CNY"), // Default tax rate
                dto.SubscriptionId,
                dto.OrderId,
                dto.Notes,
                companyInfo);
            
            // Add items
            foreach (var item in dto.Items)
            {
                invoice.AddItem(
                    item.Description,
                    item.Quantity,
                    Money.Create(item.UnitPrice, "CNY"),
                    item.TaxRate);
            }
            
            // Apply discount if provided
            if (dto.DiscountAmount.HasValue && dto.DiscountAmount > 0)
            {
                invoice.ApplyDiscount(Money.Create(dto.DiscountAmount.Value, "CNY"));
            }
            
            // Issue immediately if requested
            if (dto.IssueImmediately)
            {
                invoice.Issue();
            }
            
            await _unitOfWork.Invoices.AddAsync(invoice, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Invoice created: {InvoiceNumber} for user {UserId}", invoiceNumber, customerUserId);
            
            return Result<InvoiceDto>.Success(await MapToDtoAsync(invoice, cancellationToken));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating invoice for user {UserId}", customerUserId);
            return Result<InvoiceDto>.Failure("CREATE_INVOICE_ERROR", "创建发票失败");
        }
    }

    public async Task<Result<InvoiceDto>> GetInvoiceAsync(Guid invoiceId, Guid customerUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(invoiceId, cancellationToken);
            
            if (invoice == null)
            {
                return Result<InvoiceDto>.Failure("INVOICE_NOT_FOUND", "发票不存在");
            }
            
            if (invoice.CustomerUserId != customerUserId)
            {
                return Result<InvoiceDto>.Failure("UNAUTHORIZED", "无权访问此发票");
            }
            
            return Result<InvoiceDto>.Success(await MapToDtoAsync(invoice, cancellationToken));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting invoice {InvoiceId}", invoiceId);
            return Result<InvoiceDto>.Failure("GET_INVOICE_ERROR", "获取发票失败");
        }
    }

    public async Task<Result<PagedResult<InvoiceDto>>> GetInvoicesAsync(
        Guid customerUserId,
        int page = 1,
        int pageSize = 20,
        string? status = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _unitOfWork.Invoices.GetQueryable()
                .Where(i => i.CustomerUserId == customerUserId);
            
            // Apply filters
            if (!string.IsNullOrWhiteSpace(status) && Enum.TryParse<InvoiceStatus>(status, out var invoiceStatus))
            {
                query = query.Where(i => i.Status == invoiceStatus);
            }
            
            if (startDate.HasValue)
            {
                query = query.Where(i => i.IssueDate >= startDate.Value);
            }
            
            if (endDate.HasValue)
            {
                query = query.Where(i => i.IssueDate <= endDate.Value);
            }
            
            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);
            
            // Get paged results
            var invoices = await query
                .OrderByDescending(i => i.IssueDate)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);
            
            var invoiceDtos = new List<InvoiceDto>();
            foreach (var invoice in invoices)
            {
                invoiceDtos.Add(await MapToDtoAsync(invoice, cancellationToken));
            }
            
            var result = new PagedResult<InvoiceDto>(invoiceDtos, totalCount, page, pageSize);
            
            return Result<PagedResult<InvoiceDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting invoices for user {UserId}", customerUserId);
            return Result<PagedResult<InvoiceDto>>.Failure("GET_INVOICES_ERROR", "获取发票列表失败");
        }
    }

    public async Task<Result<byte[]>> DownloadInvoiceAsync(Guid invoiceId, Guid customerUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(invoiceId, cancellationToken);
            
            if (invoice == null)
            {
                return Result<byte[]>.Failure("INVOICE_NOT_FOUND", "发票不存在");
            }
            
            if (invoice.CustomerUserId != customerUserId)
            {
                return Result<byte[]>.Failure("UNAUTHORIZED", "无权访问此发票");
            }
            
            // Generate PDF
            var invoiceDto = await MapToDtoAsync(invoice, cancellationToken);
            var pdfBytes = await _pdfService.GenerateInvoicePdfAsync(invoiceDto, cancellationToken);
            
            return Result<byte[]>.Success(pdfBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading invoice {InvoiceId}", invoiceId);
            return Result<byte[]>.Failure("DOWNLOAD_INVOICE_ERROR", "下载发票失败");
        }
    }

    public async Task<Result> SendInvoiceEmailAsync(Guid invoiceId, Guid customerUserId, string email, CancellationToken cancellationToken = default)
    {
        try
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(invoiceId, cancellationToken);
            
            if (invoice == null)
            {
                return Result.Failure("INVOICE_NOT_FOUND", "发票不存在");
            }
            
            if (invoice.CustomerUserId != customerUserId)
            {
                return Result.Failure("UNAUTHORIZED", "无权访问此发票");
            }
            
            // Generate PDF
            var invoiceDto = await MapToDtoAsync(invoice, cancellationToken);
            var pdfBytes = await _pdfService.GenerateInvoicePdfAsync(invoiceDto, cancellationToken);
            
            // Send email
            await _emailService.SendInvoiceEmailAsync(
                email,
                invoice.InvoiceNumber,
                pdfBytes,
                cancellationToken);
            
            _logger.LogInformation("Invoice {InvoiceNumber} sent to {Email}", invoice.InvoiceNumber, email);
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending invoice {InvoiceId} email", invoiceId);
            return Result.Failure("SEND_EMAIL_ERROR", "发送发票邮件失败");
        }
    }

    public async Task<Result<InvoiceDto>> UpdateInvoiceAsync(Guid invoiceId, UpdateInvoiceDto dto, Guid customerUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(invoiceId, cancellationToken);
            
            if (invoice == null)
            {
                return Result<InvoiceDto>.Failure("INVOICE_NOT_FOUND", "发票不存在");
            }
            
            if (invoice.CustomerUserId != customerUserId)
            {
                return Result<InvoiceDto>.Failure("UNAUTHORIZED", "无权访问此发票");
            }
            
            if (invoice.Status != InvoiceStatus.Draft)
            {
                return Result<InvoiceDto>.Failure("INVALID_STATUS", "只能更新草稿状态的发票");
            }
            
            // Update company info if provided
            if (dto.CompanyInfo != null)
            {
                var companyInfo = new CompanyInfo(
                    dto.CompanyInfo.CompanyName,
                    dto.CompanyInfo.TaxId,
                    dto.CompanyInfo.Address,
                    dto.CompanyInfo.Phone,
                    dto.CompanyInfo.BankName,
                    dto.CompanyInfo.BankAccount);
                    
                invoice.UpdateCompanyInfo(companyInfo);
            }
            
            _unitOfWork.Invoices.Update(invoice);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            return Result<InvoiceDto>.Success(await MapToDtoAsync(invoice, cancellationToken));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating invoice {InvoiceId}", invoiceId);
            return Result<InvoiceDto>.Failure("UPDATE_INVOICE_ERROR", "更新发票失败");
        }
    }

    public async Task<Result<InvoiceDto>> IssueInvoiceAsync(Guid invoiceId, Guid customerUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(invoiceId, cancellationToken);
            
            if (invoice == null)
            {
                return Result<InvoiceDto>.Failure("INVOICE_NOT_FOUND", "发票不存在");
            }
            
            if (invoice.CustomerUserId != customerUserId)
            {
                return Result<InvoiceDto>.Failure("UNAUTHORIZED", "无权访问此发票");
            }
            
            invoice.Issue();
            
            _unitOfWork.Invoices.Update(invoice);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Invoice {InvoiceNumber} issued", invoice.InvoiceNumber);
            
            return Result<InvoiceDto>.Success(await MapToDtoAsync(invoice, cancellationToken));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error issuing invoice {InvoiceId}", invoiceId);
            return Result<InvoiceDto>.Failure("ISSUE_INVOICE_ERROR", "发出发票失败");
        }
    }

    public async Task<Result> CancelInvoiceAsync(Guid invoiceId, string reason, Guid customerUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(invoiceId, cancellationToken);
            
            if (invoice == null)
            {
                return Result.Failure("INVOICE_NOT_FOUND", "发票不存在");
            }
            
            if (invoice.CustomerUserId != customerUserId)
            {
                return Result.Failure("UNAUTHORIZED", "无权访问此发票");
            }
            
            invoice.Cancel(reason);
            
            _unitOfWork.Invoices.Update(invoice);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Invoice {InvoiceNumber} cancelled", invoice.InvoiceNumber);
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling invoice {InvoiceId}", invoiceId);
            return Result.Failure("CANCEL_INVOICE_ERROR", "取消发票失败");
        }
    }

    public async Task<Result<InvoiceDto>> MarkAsPaidAsync(Guid invoiceId, MarkInvoicePaidDto dto, CancellationToken cancellationToken = default)
    {
        try
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(invoiceId, cancellationToken);
            
            if (invoice == null)
            {
                return Result<InvoiceDto>.Failure("INVOICE_NOT_FOUND", "发票不存在");
            }
            
            if (!Enum.TryParse<PaymentMethod>(dto.PaymentMethod, out var paymentMethod))
            {
                return Result<InvoiceDto>.Failure("INVALID_PAYMENT_METHOD", "无效的支付方式");
            }
            
            invoice.MarkAsPaid(paymentMethod, dto.TransactionId);
            
            _unitOfWork.Invoices.Update(invoice);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Invoice {InvoiceNumber} marked as paid", invoice.InvoiceNumber);
            
            return Result<InvoiceDto>.Success(await MapToDtoAsync(invoice, cancellationToken));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking invoice {InvoiceId} as paid", invoiceId);
            return Result<InvoiceDto>.Failure("MARK_PAID_ERROR", "标记发票已支付失败");
        }
    }

    public async Task<Result<int>> ProcessOverdueInvoicesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var currentDate = DateTime.UtcNow;
            var overdueInvoices = await _unitOfWork.Invoices.GetOverdueInvoicesAsync(currentDate, cancellationToken);
            
            var count = 0;
            foreach (var invoice in overdueInvoices)
            {
                try
                {
                    invoice.MarkAsOverdue();
                    _unitOfWork.Invoices.Update(invoice);
                    count++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error marking invoice {InvoiceNumber} as overdue", invoice.InvoiceNumber);
                }
            }
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Processed {Count} overdue invoices", count);
            
            return Result<int>.Success(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing overdue invoices");
            return Result<int>.Failure("PROCESS_OVERDUE_ERROR", "处理逾期发票失败");
        }
    }

    public async Task<Result<InvoiceDto>> GenerateSubscriptionInvoiceAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var subscription = await _unitOfWork.Subscriptions.GetByIdAsync(subscriptionId, cancellationToken);
            
            if (subscription == null)
            {
                return Result<InvoiceDto>.Failure("SUBSCRIPTION_NOT_FOUND", "订阅不存在");
            }
            
            // Check if invoice already exists for this subscription
            var existingInvoices = await _unitOfWork.Invoices.GetBySubscriptionIdAsync(subscriptionId, cancellationToken);
            if (existingInvoices.Any(i => i.IssueDate.Month == DateTime.UtcNow.Month && i.IssueDate.Year == DateTime.UtcNow.Year))
            {
                return Result<InvoiceDto>.Failure("INVOICE_EXISTS", "本月发票已生成");
            }
            
            // Create invoice
            var dto = new CreateInvoiceDto
            {
                SubscriptionId = subscriptionId,
                Items = new List<CreateInvoiceItemDto>
                {
                    new CreateInvoiceItemDto
                    {
                        Description = $"订阅服务 - {subscription.Plan.Name}",
                        Quantity = 1,
                        UnitPrice = subscription.PaidAmount.Amount,
                        TaxRate = 0.06m
                    }
                },
                IssueImmediately = true
            };
            
            return await CreateInvoiceAsync(dto, subscription.CustomerUserId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating invoice for subscription {SubscriptionId}", subscriptionId);
            return Result<InvoiceDto>.Failure("GENERATE_INVOICE_ERROR", "生成订阅发票失败");
        }
    }

    public async Task<Result<int>> GenerateMonthlyInvoicesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Get all active subscriptions
            var activeSubscriptions = await _unitOfWork.Subscriptions
                .GetAsync(s => s.Status == SubscriptionStatus.Active, cancellationToken);
            
            var count = 0;
            foreach (var subscription in activeSubscriptions)
            {
                try
                {
                    var result = await GenerateSubscriptionInvoiceAsync(subscription.Id, cancellationToken);
                    if (result.IsSuccess)
                    {
                        count++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error generating invoice for subscription {SubscriptionId}", subscription.Id);
                }
            }
            
            _logger.LogInformation("Generated {Count} monthly invoices", count);
            
            return Result<int>.Success(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating monthly invoices");
            return Result<int>.Failure("GENERATE_MONTHLY_ERROR", "生成月度发票失败");
        }
    }

    public async Task<Result<InvoiceStatisticsDto>> GetInvoiceStatisticsAsync(Guid customerUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            var invoices = await _unitOfWork.Invoices.GetByCustomerUserIdAsync(customerUserId, cancellationToken);
            
            var statistics = new InvoiceStatisticsDto
            {
                TotalInvoices = invoices.Count,
                PaidInvoices = invoices.Count(i => i.Status == InvoiceStatus.Paid),
                PendingInvoices = invoices.Count(i => i.Status == InvoiceStatus.Issued),
                OverdueInvoices = invoices.Count(i => i.Status == InvoiceStatus.Overdue),
                TotalAmount = invoices.Sum(i => i.TotalAmount.Amount),
                PaidAmount = invoices.Where(i => i.Status == InvoiceStatus.Paid).Sum(i => i.TotalAmount.Amount),
                PendingAmount = invoices.Where(i => i.Status == InvoiceStatus.Issued).Sum(i => i.TotalAmount.Amount),
                OverdueAmount = invoices.Where(i => i.Status == InvoiceStatus.Overdue).Sum(i => i.TotalAmount.Amount)
            };
            
            // Calculate monthly stats for last 12 months
            var startDate = DateTime.UtcNow.AddMonths(-11).Date;
            var monthlyStats = invoices
                .Where(i => i.IssueDate >= startDate)
                .GroupBy(i => new { i.IssueDate.Year, i.IssueDate.Month })
                .Select(g => new MonthlyInvoiceStatDto
                {
                    Month = $"{g.Key.Year}-{g.Key.Month:D2}",
                    Count = g.Count(),
                    Amount = g.Sum(i => i.TotalAmount.Amount)
                })
                .OrderBy(s => s.Month)
                .ToList();
            
            statistics.MonthlyStats = monthlyStats;
            
            return Result<InvoiceStatisticsDto>.Success(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting invoice statistics for user {UserId}", customerUserId);
            return Result<InvoiceStatisticsDto>.Failure("GET_STATISTICS_ERROR", "获取发票统计失败");
        }
    }

    private async Task<InvoiceDto> MapToDtoAsync(Invoice invoice, CancellationToken cancellationToken = default)
    {
        // Fetch customer information
        var customerInfo = await _customerUserService.GetProfileAsync(invoice.CustomerUserId, cancellationToken);
        string customerName = string.Empty;
        string customerEmail = string.Empty;
        
        if (customerInfo.IsSuccess && customerInfo.Value != null)
        {
            customerName = customerInfo.Value.Nickname ?? customerInfo.Value.Username ?? string.Empty;
            customerEmail = customerInfo.Value.Email ?? string.Empty;
        }

        return new InvoiceDto
        {
            Id = invoice.Id,
            InvoiceNumber = invoice.InvoiceNumber,
            CustomerUserId = invoice.CustomerUserId,
            CustomerName = customerName,
            CustomerEmail = customerEmail,
            SubscriptionId = invoice.SubscriptionId,
            OrderId = invoice.OrderId,
            SubtotalAmount = invoice.SubtotalAmount.Amount,
            DiscountAmount = invoice.DiscountAmount?.Amount,
            TaxAmount = invoice.TaxAmount.Amount,
            TotalAmount = invoice.TotalAmount.Amount,
            Status = invoice.Status,
            IssueDate = invoice.IssueDate,
            DueDate = invoice.DueDate,
            PaidDate = invoice.PaidDate,
            PaymentMethod = invoice.PaymentMethod?.ToString(),
            TransactionId = invoice.PaymentTransactionId,
            Notes = invoice.Notes,
            CancellationReason = invoice.CancellationReason,
            RefundAmount = null, // Refund properties not in domain entity
            RefundReason = null, // Refund properties not in domain entity  
            RefundedAt = null, // Refund properties not in domain entity
            CompanyInfo = invoice.CompanyInfo != null ? new CompanyInfoDto
            {
                CompanyName = invoice.CompanyInfo.CompanyName,
                TaxId = invoice.CompanyInfo.TaxId,
                Address = invoice.CompanyInfo.Address,
                Phone = invoice.CompanyInfo.Phone,
                BankName = invoice.CompanyInfo.BankName,
                BankAccount = invoice.CompanyInfo.BankAccount
            } : null,
            Items = invoice.Items.Select(i => new InvoiceItemDto
            {
                Id = i.Id,
                Description = i.Description,
                Quantity = i.Quantity,
                UnitPrice = i.UnitPrice.Amount,
                TaxRate = i.TaxRate,
                Amount = i.Amount.Amount,
                TaxAmount = i.TaxAmount.Amount,
                TotalAmount = i.TotalAmount.Amount
            }).ToList(),
            CreatedAt = invoice.CreatedAt,
            UpdatedAt = invoice.UpdatedAt ?? invoice.CreatedAt
        };
    }
}