using System.Runtime.CompilerServices;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.AI;

/// <summary>
/// AI服务实现
/// </summary>
public class AIService : IAIService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IAIProviderManager _aiProviderManager;
    private readonly IContextManagementService _contextManagementService;
    private readonly ITokenMeteringService _tokenMeteringService;
    private readonly ILogger<AIService> _logger;

    public AIService(
        IUnitOfWork unitOfWork,
        IAIProviderManager aiProviderManager,
        IContextManagementService contextManagementService,
        ITokenMeteringService tokenMeteringService,
        ILogger<AIService> logger)
    {
        _unitOfWork = unitOfWork;
        _aiProviderManager = aiProviderManager;
        _contextManagementService = contextManagementService;
        _tokenMeteringService = tokenMeteringService;
        _logger = logger;
    }

    public async Task<Result<ConversationResponseDto>> ProcessMessageAsync(
        ProcessMessageDto request,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取对话信息
            var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(request.ConversationId, cancellationToken);
            if (conversationObj is not Domain.Entities.Conversation.Conversation conversation)
            {
                return Result<ConversationResponseDto>.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            // 验证用户权限
            if (conversation.CustomerUserId != userId)
            {
                return Result<ConversationResponseDto>.Failure("UNAUTHORIZED", "无权访问此对话");
            }

            // 检查Token配额
            var quotaResult = await _tokenMeteringService.CheckTokenQuotaAsync(userId, 100, cancellationToken); // 预估需要100个token
            if (!quotaResult.IsSuccess || !quotaResult.Value!.HasSufficientTokens)
            {
                return Result<ConversationResponseDto>.Failure("INSUFFICIENT_TOKENS", quotaResult.Value?.Message ?? "Token余额不足");
            }

            // 构建上下文
            var contextResult = await _contextManagementService.BuildContextAsync(request.ConversationId, cancellationToken: cancellationToken);
            if (!contextResult.IsSuccess)
            {
                return Result<ConversationResponseDto>.Failure(contextResult.Error ?? "Context build failed", contextResult.ErrorCode);
            }

            var context = contextResult.Value!;

            // 保存用户消息
            List<MessageAttachment>? attachments = null;
            if (request.Attachments?.Any() == true)
            {
                attachments = request.Attachments.Select(a => new MessageAttachment(
                    fileName: a.FileName ?? "attachment",
                    fileUrl: a.Url,
                    contentType: a.MimeType ?? "application/octet-stream",
                    fileSize: a.FileSize,
                    thumbnailUrl: null,
                    metadata: new Dictionary<string, object> { ["type"] = a.Type.ToString() }
                )).ToList();
            }
            
            var userTokenCount = EstimateTokenCount(request.Message);
            var userMessage = conversation.AddMessage(
                role: "user",
                content: request.Message,
                tokenCount: userTokenCount,
                attachments: attachments
            );

            // 获取AI提供商
            var model = conversation.Model ?? "gpt-3.5-turbo";
            var provider = _aiProviderManager.GetProviderByModel(model);

            // 获取当前版本的模型配置
            double temperature = 0.7;
            int maxTokens = 2048;
            if (conversation.AgentId != Guid.Empty)
            {
                var agentObj = await _unitOfWork.Agents.GetByIdAsync(conversation.AgentId, cancellationToken);
                if (agentObj is Domain.Entities.Agent.Agent agent && agent.CurrentVersion?.ModelConfig != null)
                {
                    temperature = agent.CurrentVersion.ModelConfig.Temperature;
                    maxTokens = agent.CurrentVersion.ModelConfig.MaxTokens;
                }
            }
            
            // 准备AI请求
            var aiRequest = new AIRequest
            {
                Model = model,
                Messages = context.Messages.Concat(new[] { new AIMessage { Role = "user", Content = request.Message } }).ToList(),
                Temperature = temperature,
                MaxTokens = maxTokens,
                AdditionalParameters = request.Parameters
            };

            // 调用AI
            var startTime = DateTime.UtcNow;
            var aiResult = await provider.SendMessageAsync(aiRequest, cancellationToken);
            var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            if (!aiResult.IsSuccess)
            {
                return Result<ConversationResponseDto>.Failure(aiResult.Error ?? "AI processing failed", aiResult.ErrorCode);
            }

            var aiResponse = aiResult.Value!;

            // 保存助手消息
            var assistantMessage = conversation.AddMessage(
                role: "assistant",
                content: aiResponse.Content,
                tokenCount: aiResponse.CompletionTokens
            );
            
            // 将响应时间和配置信息存储在元数据中
            conversation.SetMetadata("lastResponseTime", responseTime);
            conversation.SetMetadata("lastTemperature", temperature);
            conversation.SetMetadata("lastMaxTokens", maxTokens);

            // 对话统计已经在AddMessage中自动更新

            // 记录Token使用
            await _tokenMeteringService.RecordTokenUsageAsync(new TokenUsageRecord
            {
                UserId = userId,
                ConversationId = conversation.Id,
                MessageId = assistantMessage.Id,
                Model = conversation.Model,
                PromptTokens = aiResponse.PromptTokens,
                CompletionTokens = aiResponse.CompletionTokens,
                TotalTokens = aiResponse.TokensUsed,
                Timestamp = DateTime.UtcNow
            }, cancellationToken);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            return Result<ConversationResponseDto>.Success(new ConversationResponseDto
            {
                MessageId = assistantMessage.Id,
                Content = aiResponse.Content,
                TokenUsage = new TokenUsageDto
                {
                    PromptTokens = aiResponse.PromptTokens,
                    CompletionTokens = aiResponse.CompletionTokens,
                    TotalTokens = aiResponse.TokensUsed
                },
                ResponseTimeMs = (long)responseTime,
                Metadata = aiResponse.Metadata
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message for conversation {ConversationId}", request.ConversationId);
            return Result<ConversationResponseDto>.Failure("PROCESS_MESSAGE_ERROR", "处理消息失败");
        }
    }

    public async IAsyncEnumerable<Result<StreamMessageChunk>> ProcessMessageStreamAsync(
        ProcessMessageDto request,
        Guid userId,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        Result<StreamMessageChunk>? errorResult = null;
        ConversationMessage? userMessage = null;
        ConversationMessage? assistantMessage = null;
        Domain.Entities.Conversation.Conversation? conversation = null;
        AIRequest? aiRequest = null;
        ConversationContext? context = null;
        IAIProvider? provider = null;
        double temperature = 0.7;
        int maxTokens = 2048;
        
        // Phase 1: Setup and validation
        try
        {
            // 获取对话信息
            var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(request.ConversationId, cancellationToken);
            if (conversationObj is not Domain.Entities.Conversation.Conversation conv)
            {
                errorResult = Result<StreamMessageChunk>.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }
            else
            {
                conversation = conv;

                // 验证用户权限
                if (conversation.CustomerUserId != userId)
                {
                    errorResult = Result<StreamMessageChunk>.Failure("UNAUTHORIZED", "无权访问此对话");
                }
                else
                {
                    // 检查Token配额
                    var quotaResult = await _tokenMeteringService.CheckTokenQuotaAsync(userId, 100, cancellationToken);
                    if (!quotaResult.IsSuccess || !quotaResult.Value!.HasSufficientTokens)
                    {
                        errorResult = Result<StreamMessageChunk>.Failure("INSUFFICIENT_TOKENS", quotaResult.Value?.Message ?? "Token余额不足");
                    }
                    else
                    {
                        // 构建上下文
                        var contextResult = await _contextManagementService.BuildContextAsync(request.ConversationId, cancellationToken: cancellationToken);
                        if (!contextResult.IsSuccess)
                        {
                            errorResult = Result<StreamMessageChunk>.Failure(contextResult.Error ?? "Context build failed", contextResult.ErrorCode);
                        }
                        else
                        {
                            context = contextResult.Value!;
                            
                            // 保存用户消息
                            var userTokenCount = EstimateTokenCount(request.Message);
                            userMessage = conversation.AddMessage(
                                role: "user",
                                content: request.Message,
                                tokenCount: userTokenCount
                            );
                            
                            await _unitOfWork.SaveChangesAsync(cancellationToken);

                            // 获取模型配置
                            var model = conversation.Model ?? "gpt-3.5-turbo";
                            provider = _aiProviderManager.GetProviderByModel(model);
                            
                            if (conversation.AgentId != Guid.Empty)
                            {
                                var agentObj = await _unitOfWork.Agents.GetByIdAsync(conversation.AgentId, cancellationToken);
                                if (agentObj is Domain.Entities.Agent.Agent agent && agent.CurrentVersion?.ModelConfig != null)
                                {
                                    temperature = agent.CurrentVersion.ModelConfig.Temperature;
                                    maxTokens = agent.CurrentVersion.ModelConfig.MaxTokens;
                                }
                            }
                            
                            aiRequest = new AIRequest
                            {
                                Model = model,
                                Messages = context.Messages.Concat(new[] { new AIMessage { Role = "user", Content = request.Message } }).ToList(),
                                Temperature = temperature,
                                MaxTokens = maxTokens,
                                AdditionalParameters = request.Parameters
                            };

                            // 创建助手消息记录
                            assistantMessage = conversation.AddMessage(
                                role: "assistant",
                                content: "",  // 将在流式响应中更新
                                tokenCount: 0  // 将在流式响应完成后更新
                            );

                            await _unitOfWork.SaveChangesAsync(cancellationToken);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in setup phase for conversation {ConversationId}", request.ConversationId);
            errorResult = Result<StreamMessageChunk>.Failure("STREAM_ERROR", $"流式处理初始化失败: {ex.Message}");
        }

        // If we encountered an error during setup, yield it and return
        if (errorResult != null)
        {
            yield return errorResult;
            yield break;
        }

        // Phase 2: Streaming
        var contentBuilder = new System.Text.StringBuilder();
        var totalTokens = 0;
        var startTime = DateTime.UtcNow;
        var streamError = false;
        
        await foreach (var chunkResult in provider!.StreamMessageAsync(aiRequest!, cancellationToken))
        {
            if (!chunkResult.IsSuccess)
            {
                yield return Result<StreamMessageChunk>.Failure(chunkResult.Error ?? "Stream chunk failed", chunkResult.ErrorCode);
                streamError = true;
                break;
            }

            var chunk = chunkResult.Value!;
            
            if (chunk.Content != null)
            {
                contentBuilder.Append(chunk.Content);
                totalTokens = chunk.TokenCount ?? totalTokens;
            }

            yield return Result<StreamMessageChunk>.Success(new StreamMessageChunk
            {
                Content = chunk.Content,
                IsComplete = chunk.IsComplete,
                Error = chunk.Error
            });

            if (chunk.IsComplete && !streamError)
            {
                try
                {
                    // 更新助手消息内容和Token计数
                    assistantMessage!.UpdateContent(contentBuilder.ToString());
                    assistantMessage.UpdateTokenCount(totalTokens);
                    
                    // 将响应时间存储在元数据中
                    conversation!.SetMetadata("lastResponseTime", (DateTime.UtcNow - startTime).TotalMilliseconds);
                    conversation.SetMetadata("lastTemperature", temperature);
                    conversation.SetMetadata("lastMaxTokens", maxTokens);

                    // 记录Token使用
                    await _tokenMeteringService.RecordTokenUsageAsync(new TokenUsageRecord
                    {
                        UserId = userId,
                        ConversationId = conversation.Id,
                        MessageId = assistantMessage.Id,
                        Model = conversation.Model,
                        PromptTokens = context!.TotalTokens,
                        CompletionTokens = totalTokens,
                        TotalTokens = context.TotalTokens + totalTokens,
                        Timestamp = DateTime.UtcNow
                    }, cancellationToken);

                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error saving stream results for conversation {ConversationId}", request.ConversationId);
                    // Don't yield error here since we already streamed the content
                }
            }
        }
    }

    public async Task<Result<TokenCountDto>> CountTokensAsync(
        TokenCountRequestDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = _aiProviderManager.GetProviderByModel(request.Model);
            var result = await provider.GetTokenCountAsync(request.Text, request.Model, cancellationToken);
            
            if (!result.IsSuccess)
            {
                return Result<TokenCountDto>.Failure(result.Error ?? "Token count failed", result.ErrorCode);
            }

            return Result<TokenCountDto>.Success(new TokenCountDto
            {
                Count = result.Value,
                Model = request.Model
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error counting tokens");
            return Result<TokenCountDto>.Failure("COUNT_TOKENS_ERROR", "计算Token数量失败");
        }
    }

    public async Task<Result<List<AIModelDto>>> GetAvailableModelsAsync(
        AIProviderType? providerType = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var models = new List<AIModelDto>();
            var providers = providerType.HasValue 
                ? new[] { _aiProviderManager.GetProvider(providerType.Value) }
                : _aiProviderManager.GetAvailableProviders();

            foreach (var provider in providers)
            {
                var isAvailable = await provider.IsAvailableAsync(cancellationToken);
                
                foreach (var modelId in provider.SupportedModels)
                {
                    var modelInfoResult = await provider.GetModelInfoAsync(modelId, cancellationToken);
                    if (modelInfoResult.IsSuccess)
                    {
                        var modelInfo = modelInfoResult.Value!;
                        models.Add(new AIModelDto
                        {
                            Id = modelId,
                            Name = modelInfo.DisplayName,
                            Provider = provider.ProviderType,
                            MaxTokens = modelInfo.MaxTokens,
                            ContextLength = modelInfo.ContextLength,
                            SupportsStreaming = modelInfo.SupportsStreaming,
                            SupportsVision = modelInfo.SupportsVision,
                            SupportsEmbedding = modelInfo.SupportsEmbedding,
                            IsAvailable = isAvailable,
                            PricePerMillionTokens = modelInfo.PricePerMillionTokens
                        });
                    }
                }
            }

            return Result<List<AIModelDto>>.Success(models);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available models");
            return Result<List<AIModelDto>>.Failure("GET_MODELS_ERROR", "获取可用模型失败");
        }
    }

    public async Task<Result<AgentValidationResult>> ValidateAgentConfigurationAsync(
        Guid agentId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var agentObj = await _unitOfWork.Agents.GetByIdAsync(agentId, cancellationToken);
            if (agentObj is not Domain.Entities.Agent.Agent agent)
            {
                return Result<AgentValidationResult>.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            var result = new AgentValidationResult
            {
                IsValid = true
            };

            // 验证当前版本配置
            if (agent.CurrentVersion == null)
            {
                result.Errors.Add("Agent没有版本配置");
                result.IsValid = false;
                return Result<AgentValidationResult>.Success(result);
            }
            
            var version = agent.CurrentVersion;
            
            // 验证提示词
            if (string.IsNullOrWhiteSpace(version.SystemPrompt) && string.IsNullOrWhiteSpace(version.UserPrompt))
            {
                result.Errors.Add("系统提示词和用户提示词不能都为空");
                result.IsValid = false;
            }

            // 验证模型
            if (version.ModelConfig == null)
            {
                result.Errors.Add("未配置模型");
                result.IsValid = false;
            }
            else
            {
                try
                {
                    var provider = _aiProviderManager.GetProviderByModel(version.ModelConfig.ModelName);
                    var isAvailable = await provider.IsAvailableAsync(cancellationToken);
                    
                    if (!isAvailable)
                    {
                        result.Warnings.Add($"模型 {version.ModelConfig.ModelName} 当前不可用");
                    }
                }
                catch
                {
                    result.Errors.Add($"模型 {version.ModelConfig.ModelName} 不存在或未配置");
                    result.IsValid = false;
                }
                
                // 验证参数（ModelConfiguration已经在创建时验证过范围）
                if (version.ModelConfig.Temperature >= 1.5)
                {
                    result.Warnings.Add("温度参数较高（>=1.5），可能导致输出不稳定");
                }
                
                if (version.ModelConfig.MaxTokens > 8000)
                {
                    result.Warnings.Add("最大Token数较高（>8000），可能导致响应时间较长");
                }
            }

            return Result<AgentValidationResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating agent configuration for {AgentId}", agentId);
            return Result<AgentValidationResult>.Failure("VALIDATION_ERROR", "验证Agent配置失败");
        }
    }

    public async Task<Result<AgentExecutionResult>> ExecuteAgentFunctionAsync(
        AgentFunctionRequest request,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取Agent
            var agentObj = await _unitOfWork.Agents.GetByIdAsync(request.AgentId, cancellationToken);
            if (agentObj is not Domain.Entities.Agent.Agent agent)
            {
                return Result<AgentExecutionResult>.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            // 这里可以实现Agent的特定功能
            // 例如：工具调用、插件执行等
            
            var result = new AgentExecutionResult
            {
                Success = true,
                Result = new { message = "功能执行成功" },
                Metadata = new Dictionary<string, object>
                {
                    ["AgentId"] = agent.Id,
                    ["FunctionName"] = request.FunctionName,
                    ["ExecutedAt"] = DateTime.UtcNow
                }
            };

            return Result<AgentExecutionResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing agent function {FunctionName} for agent {AgentId}",
                request.FunctionName, request.AgentId);
            
            return Result<AgentExecutionResult>.Success(new AgentExecutionResult
            {
                Success = false,
                Error = ex.Message
            });
        }
    }
    
    /// <summary>
    /// 估算Token数量
    /// </summary>
    private int EstimateTokenCount(string text)
    {
        // 简单估算：平均每4个字符一个token
        return (int)Math.Ceiling(text.Length / 4.0);
    }
}