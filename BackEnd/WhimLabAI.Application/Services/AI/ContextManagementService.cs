using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.AI;

/// <summary>
/// 上下文管理服务实现
/// </summary>
public class ContextManagementService : IContextManagementService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IAIProviderManager _aiProviderManager;
    private readonly ICacheService _cacheService;
    private readonly IKnowledgeBaseContextService _knowledgeBaseContextService;
    private readonly ILogger<ContextManagementService> _logger;
    private const string ContextCacheKeyPrefix = "conversation_context:";
    private const int DefaultMaxMessages = 10;

    public ContextManagementService(
        IUnitOfWork unitOfWork,
        IAIProviderManager aiProviderManager,
        ICacheService cacheService,
        IKnowledgeBaseContextService knowledgeBaseContextService,
        ILogger<ContextManagementService> logger)
    {
        _unitOfWork = unitOfWork;
        _aiProviderManager = aiProviderManager;
        _cacheService = cacheService;
        _knowledgeBaseContextService = knowledgeBaseContextService;
        _logger = logger;
    }

    public async Task<Result<ConversationContext>> BuildContextAsync(
        Guid conversationId, 
        int maxMessages = 10, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 尝试从缓存获取
            var cacheKey = $"{ContextCacheKeyPrefix}{conversationId}";
            var cachedContext = await _cacheService.GetAsync<ConversationContext>(cacheKey, cancellationToken);
            
            if (cachedContext != null && cachedContext.Messages.Count >= maxMessages)
            {
                return Result<ConversationContext>.Success(cachedContext);
            }

            // 获取对话信息
            var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
            if (conversationObj is not Domain.Entities.Conversation.Conversation conversation)
            {
                return Result<ConversationContext>.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            // 获取最近的消息
            var messagesObj = await _unitOfWork.Conversations.GetConversationMessagesAsync(
                conversationId, 
                maxMessages, 
                0, 
                cancellationToken);
            
            var messages = messagesObj.Cast<ConversationMessage>().OrderBy(m => m.CreatedAt).ToList();

            // 获取Agent信息以添加系统提示
            var agentObj = await _unitOfWork.Agents.GetByIdAsync(conversation.AgentId, cancellationToken);
            var agent = agentObj as Domain.Entities.Agent.Agent;

            // 构建上下文
            var context = new ConversationContext
            {
                ConversationId = conversationId,
                CreatedAt = DateTime.UtcNow,
                Variables = new Dictionary<string, object>
                {
                    ["AgentId"] = conversation.AgentId,
                    ["Model"] = conversation.Model,
                    ["UserId"] = conversation.CustomerUserId
                }
            };

            // 添加系统消息
            if (agent?.CurrentVersion != null && !string.IsNullOrEmpty(agent.CurrentVersion.SystemPrompt))
            {
                var systemPrompt = agent.CurrentVersion.SystemPrompt;
                
                // 如果有最新的用户消息，尝试获取知识库上下文
                var latestUserMessage = messages.Where(m => m.Role == "user").LastOrDefault();
                if (latestUserMessage != null && agent.CurrentVersion.KnowledgeBases.Any())
                {
                    try
                    {
                        var kbContext = await _knowledgeBaseContextService.GetContextAsync(
                            agent.Id,
                            latestUserMessage.Content,
                            new KnowledgeBaseContextOptions
                            {
                                MaxChunks = 3,
                                MinSimilarity = 0.7f,
                                UseHybridSearch = true,
                                ContextWindowSize = 2000
                            },
                            cancellationToken);
                        
                        if (kbContext.IsSuccess && kbContext.Value.Chunks.Any())
                        {
                            // 使用知识库上下文增强系统提示词
                            systemPrompt = _knowledgeBaseContextService.BuildEnhancedPrompt(
                                systemPrompt,
                                latestUserMessage.Content,
                                kbContext.Value);
                                
                            // 将知识库上下文信息添加到变量中
                            context.Variables["KnowledgeBaseContext"] = kbContext.Value;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to get knowledge base context for agent {AgentId}", agent.Id);
                        // 继续使用原始系统提示词
                    }
                }
                
                context.Messages.Add(new AIMessage
                {
                    Role = "system",
                    Content = systemPrompt
                });
            }

            // 添加历史消息
            foreach (var message in messages)
            {
                context.Messages.Add(new AIMessage
                {
                    Role = message.Role,
                    Content = message.Content,
                    Attachments = message.Attachments?.Select(a => new AIAttachment
                    {
                        Type = "file", // Determine type from ContentType if needed
                        Url = a.FileUrl,
                        MimeType = a.ContentType
                    }).ToList()
                });
                
                context.TotalTokens += message.TokenCount;
            }

            // 缓存上下文
            await _cacheService.SetAsync(cacheKey, context, TimeSpan.FromMinutes(30), cancellationToken);

            return Result<ConversationContext>.Success(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error building context for conversation {ConversationId}", conversationId);
            return Result<ConversationContext>.Failure("BUILD_CONTEXT_ERROR", "构建对话上下文失败");
        }
    }

    public async Task<Result<ConversationContext>> OptimizeContextAsync(
        ConversationContext context, 
        int maxTokens, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (context.TotalTokens <= maxTokens)
            {
                return Result<ConversationContext>.Success(context);
            }

            var optimizedContext = new ConversationContext
            {
                ConversationId = context.ConversationId,
                Variables = new Dictionary<string, object>(context.Variables),
                CreatedAt = DateTime.UtcNow
            };

            // 保留系统消息
            var systemMessage = context.Messages.FirstOrDefault(m => m.Role == "system");
            if (systemMessage != null)
            {
                optimizedContext.Messages.Add(systemMessage);
                optimizedContext.TotalTokens += await EstimateTokensAsync(systemMessage.Content, cancellationToken);
            }

            // 使用滑动窗口策略，保留最新的消息
            var userAssistantMessages = context.Messages
                .Where(m => m.Role != "system")
                .ToList();

            // 从最新的消息开始添加，直到接近token限制
            for (int i = userAssistantMessages.Count - 1; i >= 0 && optimizedContext.TotalTokens < maxTokens * 0.9; i--)
            {
                var message = userAssistantMessages[i];
                var messageTokens = await EstimateTokensAsync(message.Content, cancellationToken);
                
                if (optimizedContext.TotalTokens + messageTokens <= maxTokens)
                {
                    optimizedContext.Messages.Insert(1, message); // 在系统消息后插入
                    optimizedContext.TotalTokens += messageTokens;
                }
                else
                {
                    break;
                }
            }

            // 如果消息被截断，添加一个总结
            if (userAssistantMessages.Count > optimizedContext.Messages.Count - 1)
            {
                var summary = await GenerateContextSummaryAsync(
                    userAssistantMessages.Take(userAssistantMessages.Count - (optimizedContext.Messages.Count - 1)).ToList(),
                    cancellationToken);
                
                if (!string.IsNullOrEmpty(summary))
                {
                    optimizedContext.Messages.Insert(1, new AIMessage
                    {
                        Role = "system",
                        Content = $"[对话历史摘要] {summary}"
                    });
                }
            }

            _logger.LogInformation("Optimized context from {OriginalTokens} to {OptimizedTokens} tokens",
                context.TotalTokens, optimizedContext.TotalTokens);

            return Result<ConversationContext>.Success(optimizedContext);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing context");
            return Result<ConversationContext>.Failure("OPTIMIZE_CONTEXT_ERROR", "优化上下文失败");
        }
    }

    public async Task<Result> SaveContextSnapshotAsync(
        Guid conversationId, 
        ConversationContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 保存到缓存，延长过期时间
            var cacheKey = $"{ContextCacheKeyPrefix}{conversationId}";
            await _cacheService.SetAsync(cacheKey, context, TimeSpan.FromHours(2), cancellationToken);

            // 可选：保存到数据库作为持久化快照
            // 这里可以实现将上下文快照保存到数据库的逻辑

            _logger.LogInformation("Saved context snapshot for conversation {ConversationId}", conversationId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving context snapshot for conversation {ConversationId}", conversationId);
            return Result.Failure("SAVE_SNAPSHOT_ERROR", "保存上下文快照失败");
        }
    }

    private async Task<int> EstimateTokensAsync(string text, CancellationToken cancellationToken)
    {
        try
        {
            // 使用默认的AI提供商进行token计数
            var provider = _aiProviderManager.GetProvider(Shared.Enums.AIProviderType.SemanticKernel);
            var result = await provider.GetTokenCountAsync(text, null, cancellationToken);
            
            return result.IsSuccess ? result.Value : text.Length / 4;
        }
        catch
        {
            // 如果失败，使用粗略估计
            return text.Length / 4;
        }
    }

    private async Task<string> GenerateContextSummaryAsync(List<AIMessage> messages, CancellationToken cancellationToken)
    {
        try
        {
            // 简单的摘要生成
            // 在实际应用中，可以使用AI模型生成更好的摘要
            var summary = string.Join(" ", messages
                .Where(m => m.Role == "user")
                .Take(3)
                .Select(m => m.Content.Length > 100 ? m.Content.Substring(0, 100) + "..." : m.Content));
            
            return await Task.FromResult($"之前讨论了: {summary}");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to generate context summary");
            return string.Empty;
        }
    }
}