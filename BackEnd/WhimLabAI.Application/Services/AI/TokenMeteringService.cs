using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.AI;

/// <summary>
/// Token计量服务实现
/// </summary>
public class TokenMeteringService : ITokenMeteringService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly ILogger<TokenMeteringService> _logger;
    private const string TokenQuotaCacheKeyPrefix = "token_quota:";
    private const int CacheDurationMinutes = 5;

    public TokenMeteringService(
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        ILogger<TokenMeteringService> logger)
    {
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<Result> RecordTokenUsageAsync(TokenUsageRecord record, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get conversation to find agent ID
            var conversation = await _unitOfWork.Conversations.GetByIdAsync(record.ConversationId, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }
            
            // Get subscription for the user
            var subscriptionObj = await _unitOfWork.Subscriptions.GetActiveSubscriptionByUserIdAsync(record.UserId, cancellationToken);
            if (subscriptionObj == null)
            {
                return Result.Failure("NO_ACTIVE_SUBSCRIPTION", "用户没有有效订阅");
            }
            var subscription = subscriptionObj as Domain.Entities.Subscription.Subscription;
            if (subscription == null)
            {
                return Result.Failure("INVALID_SUBSCRIPTION", "订阅数据无效");
            }
            
            // Extract provider from model name (e.g., "gpt-4" -> "OpenAI", "claude-3" -> "Anthropic")
            var modelProvider = ExtractProviderFromModel(record.Model);
            
            // Calculate cost
            var costAmount = record.TotalTokens * GetTokenPrice(record.Model) / 1000000m; // 按百万token计价
            
            // 记录使用详情
            var usageRecord = new UsageRecord(
                subscriptionId: subscription.Id,
                userId: record.UserId,
                conversationId: record.ConversationId,
                agentId: conversation.AgentId,
                modelName: record.Model,
                modelProvider: modelProvider,
                tokensUsed: record.TotalTokens,
                costAmount: costAmount
            );
            
            // Add metadata
            usageRecord.AddMetadata("MessageId", record.MessageId ?? Guid.Empty);
            usageRecord.AddMetadata("PromptTokens", record.PromptTokens);
            usageRecord.AddMetadata("CompletionTokens", record.CompletionTokens);

            await _unitOfWork.UsageRecords.AddAsync(usageRecord, cancellationToken);

            // 更新用户订阅的剩余Token
            var consumed = await _unitOfWork.Subscriptions.ConsumeTokensAsync(subscription.Id, record.TotalTokens, cancellationToken);
            if (!consumed)
            {
                _logger.LogWarning("Failed to consume tokens for user {UserId}, subscription may have insufficient tokens", record.UserId);
            }
            
            // 清除缓存的配额信息
            await _cacheService.RemoveAsync($"{TokenQuotaCacheKeyPrefix}{record.UserId}", cancellationToken);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Recorded token usage for user {UserId}: {TotalTokens} tokens", 
                record.UserId, record.TotalTokens);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording token usage for user {UserId}", record.UserId);
            return Result.Failure("RECORD_USAGE_ERROR", "记录Token使用失败");
        }
    }

    public async Task<Result<TokenUsageStatistics>> GetUserTokenUsageAsync(
        Guid userId, 
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var usageRecords = await _unitOfWork.UsageRecords.GetUserUsageHistoryAsync(
                userId, 
                startDate, 
                endDate, 
                cancellationToken);

            var statistics = new TokenUsageStatistics
            {
                UserId = userId,
                TotalTokensUsed = 0,
                TotalPromptTokens = 0,
                TotalCompletionTokens = 0,
                EstimatedCost = 0
            };

            foreach (var record in usageRecords.Cast<UsageRecord>())
            {
                statistics.TotalTokensUsed += record.TokensUsed;
                statistics.EstimatedCost += record.CostAmount;

                if (record.Metadata != null)
                {
                    if (record.Metadata.TryGetValue("PromptTokens", out var promptTokens))
                    {
                        statistics.TotalPromptTokens += Convert.ToInt32(promptTokens);
                    }

                    if (record.Metadata.TryGetValue("CompletionTokens", out var completionTokens))
                    {
                        statistics.TotalCompletionTokens += Convert.ToInt32(completionTokens);
                    }

                    if (record.Metadata.TryGetValue("Model", out var model) && model != null)
                    {
                        var modelName = model.ToString()!;
                        if (!statistics.TokensByModel.ContainsKey(modelName))
                        {
                            statistics.TokensByModel[modelName] = 0;
                        }
                        statistics.TokensByModel[modelName] += record.TokensUsed;
                    }
                }

                // 按日统计
                var date = record.CreatedAt.Date;
                if (!statistics.DailyUsage.ContainsKey(date))
                {
                    statistics.DailyUsage[date] = 0;
                }
                statistics.DailyUsage[date] += record.TokensUsed;
            }

            return Result<TokenUsageStatistics>.Success(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting token usage statistics for user {UserId}", userId);
            return Result<TokenUsageStatistics>.Failure("GET_USAGE_ERROR", "获取Token使用统计失败");
        }
    }

    public async Task<Result<TokenQuotaStatus>> CheckTokenQuotaAsync(
        Guid userId, 
        int requiredTokens, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 尝试从缓存获取配额状态
            var cacheKey = $"{TokenQuotaCacheKeyPrefix}{userId}";
            var cachedStatus = await _cacheService.GetAsync<TokenQuotaStatus>(cacheKey, cancellationToken);
            
            if (cachedStatus != null)
            {
                // 更新缓存的状态以反映新的需求
                cachedStatus.HasSufficientTokens = cachedStatus.RemainingTokens >= requiredTokens;
                return Result<TokenQuotaStatus>.Success(cachedStatus);
            }

            // 从数据库获取订阅信息
            var subscriptionObj = await _unitOfWork.Subscriptions.GetActiveSubscriptionByUserIdAsync(userId, cancellationToken);
            
            if (subscriptionObj is not Domain.Entities.Subscription.Subscription subscription)
            {
                return Result<TokenQuotaStatus>.Success(new TokenQuotaStatus
                {
                    HasSufficientTokens = false,
                    RemainingTokens = 0,
                    MonthlyLimit = 0,
                    Message = "未找到有效订阅"
                });
            }

            // 获取订阅计划信息
            var planObj = await _unitOfWork.SubscriptionPlans.GetByIdAsync(subscription.PlanId, cancellationToken);
            var plan = planObj as Domain.Entities.Subscription.SubscriptionPlan;

            var status = new TokenQuotaStatus
            {
                HasSufficientTokens = subscription.RemainingTokens >= requiredTokens,
                RemainingTokens = subscription.RemainingTokens,
                MonthlyLimit = plan?.MonthlyTokens ?? 0,
                ResetDate = subscription.NextResetDate,
                Message = subscription.RemainingTokens >= requiredTokens 
                    ? null 
                    : $"Token余额不足，当前余额: {subscription.RemainingTokens}, 需要: {requiredTokens}"
            };

            // 缓存配额状态
            await _cacheService.SetAsync(cacheKey, status, TimeSpan.FromMinutes(CacheDurationMinutes), cancellationToken);

            return Result<TokenQuotaStatus>.Success(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking token quota for user {UserId}", userId);
            return Result<TokenQuotaStatus>.Failure("CHECK_QUOTA_ERROR", "检查Token配额失败");
        }
    }

    private decimal GetTokenPrice(string model)
    {
        // 返回每百万token的价格
        return model switch
        {
            "gpt-4-turbo-preview" => 10m,
            "gpt-4" => 30m,
            "gpt-3.5-turbo" => 0.5m,
            "gpt-3.5-turbo-16k" => 3m,
            "text-embedding-ada-002" => 0.1m,
            "text-embedding-3-small" => 0.02m,
            "text-embedding-3-large" => 0.13m,
            _ => 1m // 默认价格
        };
    }
    
    private string ExtractProviderFromModel(string model)
    {
        // Extract provider from model name
        if (model.StartsWith("gpt") || model.Contains("turbo") || model.Contains("davinci") || model.Contains("ada") || model.Contains("embedding"))
            return "OpenAI";
        else if (model.StartsWith("claude"))
            return "Anthropic";
        else if (model.StartsWith("command") || model.StartsWith("embed"))
            return "Cohere";
        else if (model.StartsWith("palm") || model.StartsWith("gemini"))
            return "Google";
        else if (model.StartsWith("llama"))
            return "Meta";
        else
            return "Unknown";
    }
}