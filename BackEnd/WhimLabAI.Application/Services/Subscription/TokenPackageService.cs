using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Finance;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Subscription;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Subscription;

/// <summary>
/// Token包服务实现
/// </summary>
public class TokenPackageService : ITokenPackageService
{
    private readonly ITokenPackageRepository _tokenPackageRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly ICustomerUserRepository _customerUserRepository;
    private readonly ITokenUsageRepository _tokenUsageRepository;
    private readonly IPaymentRepository _paymentRepository;
    private readonly ICouponRepository _couponRepository;
    private readonly IInvoiceService _invoiceService;
    private readonly IPaymentService _paymentService;
    private readonly INotificationService _notificationService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly ILogger<TokenPackageService> _logger;
    
    public TokenPackageService(
        ITokenPackageRepository tokenPackageRepository,
        IOrderRepository orderRepository,
        ICustomerUserRepository customerUserRepository,
        ITokenUsageRepository tokenUsageRepository,
        IPaymentRepository paymentRepository,
        ICouponRepository couponRepository,
        IInvoiceService invoiceService,
        IPaymentService paymentService,
        INotificationService notificationService,
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        ILogger<TokenPackageService> logger)
    {
        _tokenPackageRepository = tokenPackageRepository;
        _orderRepository = orderRepository;
        _customerUserRepository = customerUserRepository;
        _tokenUsageRepository = tokenUsageRepository;
        _paymentRepository = paymentRepository;
        _couponRepository = couponRepository;
        _invoiceService = invoiceService;
        _paymentService = paymentService;
        _notificationService = notificationService;
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _logger = logger;
    }
    
    public async Task<Result<List<TokenPackageDto>>> GetAvailablePackagesAsync()
    {
        try
        {
            var cacheKey = "tokenpackages:available";
            var cached = await _cacheService.GetAsync<List<TokenPackageDto>>(cacheKey);
            if (cached != null)
            {
                return Result<List<TokenPackageDto>>.Success(cached);
            }
            
            var packages = await _tokenPackageRepository.GetAvailablePackagesAsync();
            var dtos = packages.Select(MapToDto).ToList();
            
            await _cacheService.SetAsync(cacheKey, dtos, TimeSpan.FromMinutes(10));
            
            return Result<List<TokenPackageDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get available token packages");
            return Result<List<TokenPackageDto>>.Failure("获取Token包列表失败");
        }
    }
    
    public async Task<Result<TokenPackageDto>> GetPackageDetailsAsync(Guid packageId)
    {
        try
        {
            var package = await _tokenPackageRepository.GetByIdAsync(packageId);
            if (package == null)
            {
                return Result<TokenPackageDto>.Failure("Token包不存在");
            }
            
            var dto = MapToDto(package);
            return Result<TokenPackageDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get token package details for {PackageId}", packageId);
            return Result<TokenPackageDto>.Failure("获取Token包详情失败");
        }
    }
    
    public async Task<Result<TokenPackageOrderDto>> CreatePurchaseOrderAsync(
        Guid userId, 
        Guid packageId, 
        string paymentMethod, 
        string? couponCode = null)
    {
        try
        {
            // 验证用户
            var user = await _customerUserRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return Result<TokenPackageOrderDto>.Failure("用户不存在");
            }
            
            // 验证Token包
            var package = await _tokenPackageRepository.GetByIdAsync(packageId);
            if (package == null)
            {
                return Result<TokenPackageOrderDto>.Failure("Token包不存在");
            }
            
            if (!package.IsAvailable())
            {
                return Result<TokenPackageOrderDto>.Failure("Token包暂不可购买");
            }
            
            // 检查购买限制
            if (package.MaxPurchasePerUser.HasValue)
            {
                var purchaseCount = await _tokenPackageRepository.GetUserPurchaseCountAsync(userId, packageId);
                if (purchaseCount >= package.MaxPurchasePerUser.Value)
                {
                    return Result<TokenPackageOrderDto>.Failure($"您已达到该Token包的购买上限（{package.MaxPurchasePerUser.Value}次）");
                }
            }
            
            // 计算价格
            var amount = package.Price;
            decimal? discountAmount = null;
            Domain.Entities.Payment.Coupon? coupon = null;
            
            if (!string.IsNullOrEmpty(couponCode))
            {
                var couponResult = await ValidateAndApplyCouponAsync(couponCode, amount, userId);
                if (couponResult.IsSuccess)
                {
                    coupon = couponResult.Value.Coupon;
                    discountAmount = couponResult.Value.DiscountAmount;
                    amount = couponResult.Value.FinalAmount;
                }
            }
            
            // 创建订单
            var orderNo = $"TP{DateTime.UtcNow:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";
            var orderAmount = Money.Create(package.Price, "CNY");
            var order = new Domain.Entities.Payment.Order(
                orderNo: orderNo,
                customerUserId: userId,
                type: OrderType.TokenPackage,
                amount: orderAmount,
                paymentMethod: Enum.Parse<PaymentMethod>(paymentMethod),
                productId: packageId,
                productName: package.Name,
                remark: $"购买Token包：{package.Name}"
            );
            
            // 应用优惠券
            if (coupon != null && discountAmount.HasValue)
            {
                var discountMoney = Money.Create(discountAmount.Value, "CNY");
                order.ApplyCoupon(coupon.Id, coupon.Code, discountMoney);
            }
            
            // 设置元数据
            order.Metadata["TokenAmount"] = package.TokenAmount;
            order.Metadata["PackageId"] = packageId;
            
            await _orderRepository.AddAsync(order);
            await _unitOfWork.SaveChangesAsync();
            
            // 创建支付
            var paymentResult = await _paymentService.ProcessPaymentAsync(new ProcessPaymentDto
            {
                OrderId = order.Id,
                PaymentMethod = Enum.Parse<PaymentMethod>(paymentMethod),
                Amount = order.FinalAmount.Amount,
                ReturnUrl = $"/payment/success?orderId={order.Id}",
                ClientIp = "127.0.0.1" // TODO: Get from HttpContext
            });
            
            if (!paymentResult.IsSuccess)
            {
                return Result<TokenPackageOrderDto>.Failure("创建支付失败");
            }
            
            var dto = new TokenPackageOrderDto
            {
                Id = order.Id,
                OrderNumber = order.OrderNo,
                UserId = userId,
                PackageId = packageId,
                Package = MapToDto(package),
                Amount = order.Amount.Amount,
                DiscountAmount = order.DiscountAmount?.Amount,
                FinalAmount = order.FinalAmount.Amount,
                PaymentMethod = paymentMethod,
                Status = order.Status.ToString(),
                CouponCode = couponCode,
                CreatedAt = order.CreatedAt,
                PaymentUrl = paymentResult.Value.PaymentUrl
            };
            
            _logger.LogInformation("Created token package order {OrderId} for user {UserId}", order.Id, userId);
            
            return Result<TokenPackageOrderDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create token package order for user {UserId}", userId);
            return Result<TokenPackageOrderDto>.Failure("创建订单失败");
        }
    }
    
    public async Task<Result> ProcessPurchaseSuccessAsync(Guid orderId, PaymentInfo paymentInfo)
    {
        try
        {
            var order = await _orderRepository.GetByIdAsync(orderId);
            if (order == null)
            {
                return Result.Failure("订单不存在");
            }
            
            if (order.Status != OrderStatus.Pending)
            {
                return Result.Success(); // 订单已处理
            }
            
            // Get package info from order
            if (order.Type != OrderType.TokenPackage || !order.ProductId.HasValue)
            {
                return Result.Failure("订单类型无效");
            }
            
            var packageId = order.ProductId.Value;
            var tokenAmount = order.Metadata.ContainsKey("TokenAmount") 
                ? Convert.ToInt32(order.Metadata["TokenAmount"]) 
                : 0;
            
            // 更新订单状态
            order.MarkAsPaid(paymentInfo.TransactionId, paymentInfo.PaymentTime);
            _orderRepository.Update(order);
            
            // 更新Token包销售数量
            var package = await _tokenPackageRepository.GetByIdAsync(packageId);
            if (package != null)
            {
                package.IncrementSoldCount();
                _tokenPackageRepository.Update(package);
            }
            
            // TODO: Update user token balance
            // Token balance management needs to be implemented
            // This would typically involve updating a separate TokenBalance entity
            // or using a dedicated token management service
            
            // TODO: Record token usage
            // Need to find user's subscription to record token usage
            // var subscription = await _subscriptionRepository.GetActiveSubscriptionByUserIdAsync(order.CustomerUserId);
            // if (subscription != null)
            // {
            //     var tokenUsage = new TokenUsage(
            //         subscriptionId: subscription.Id,
            //         tokens: tokenAmount,
            //         type: UsageType.Purchase,
            //         metadata: new Dictionary<string, object> 
            //         { 
            //             { "packageId", packageId },
            //             { "productName", order.ProductName ?? "Token包" }
            //         }
            //     );
            //     await _tokenUsageRepository.AddAsync(tokenUsage);
            // }
            
            await _unitOfWork.SaveChangesAsync();
            
            // TODO: Generate invoice for the order
            // await _invoiceService.CreateInvoiceAsync(...);
            
            // 发送通知
            await _notificationService.SendNotificationAsync(new SendNotificationDto
            {
                UserId = order.CustomerUserId,
                Title = "Token包购买成功",
                Content = $"您已成功购买{order.ProductName}，获得{tokenAmount:N0}个Token",
                Type = "payment",
                Level = "success"
            });
            
            _logger.LogInformation("Processed token package purchase success for order {OrderId}", orderId);
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process token package purchase for order {OrderId}", orderId);
            return Result.Failure("处理购买失败");
        }
    }
    
    public async Task<Result<PagedResult<TokenPackagePurchaseHistoryDto>>> GetUserPurchaseHistoryAsync(
        Guid userId, 
        int pageNumber = 1, 
        int pageSize = 20)
    {
        try
        {
            var allOrders = await _orderRepository.GetUserOrdersAsync(userId);
            var tokenPackageOrders = allOrders
                .Where(o => o.Type == OrderType.TokenPackage)
                .OrderByDescending(o => o.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();
            
            var totalCount = allOrders.Count(o => o.Type == OrderType.TokenPackage);
            
            var dtos = tokenPackageOrders.Select(order => new TokenPackagePurchaseHistoryDto
            {
                Id = order.Id,
                OrderNumber = order.OrderNo,
                PackageName = order.ProductName ?? "未知",
                TokenAmount = order.Metadata.ContainsKey("TokenAmount") 
                    ? Convert.ToInt32(order.Metadata["TokenAmount"]) 
                    : 0,
                Amount = order.FinalAmount.Amount,
                PaymentMethod = order.PaymentMethod.ToString(),
                Status = order.Status.ToString(),
                PurchaseDate = order.CreatedAt,
                InvoiceNumber = null // TODO: Add invoice support
            }).ToList();
            
            var result = new PagedResult<TokenPackagePurchaseHistoryDto>
            {
                Items = dtos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
            
            return Result<PagedResult<TokenPackagePurchaseHistoryDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get purchase history for user {UserId}", userId);
            return Result<PagedResult<TokenPackagePurchaseHistoryDto>>.Failure("获取购买历史失败");
        }
    }
    
    public async Task<Result<TokenPackageDto>> CreatePackageAsync(CreateTokenPackageRequest request)
    {
        try
        {
            var package = new TokenPackage(
                name: request.Name,
                tokenAmount: request.TokenAmount,
                price: request.Price,
                description: request.Description,
                originalPrice: request.OriginalPrice,
                isLimited: request.IsLimited
            );
            
            if (request.IsLimited && request.ValidFrom.HasValue && request.ValidTo.HasValue)
            {
                package.SetLimitedTimeOffer(request.ValidFrom.Value, request.ValidTo.Value);
            }
            
            if (request.MaxPurchasePerUser.HasValue)
            {
                package.SetPurchaseLimit(request.MaxPurchasePerUser.Value);
            }
            
            await _tokenPackageRepository.AddAsync(package);
            await _unitOfWork.SaveChangesAsync();
            
            await ClearPackagesCacheAsync();
            
            var dto = MapToDto(package);
            
            _logger.LogInformation("Created token package {PackageId} with name {PackageName}", 
                package.Id, package.Name);
            
            return Result<TokenPackageDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create token package");
            return Result<TokenPackageDto>.Failure("创建Token包失败");
        }
    }
    
    public async Task<Result<TokenPackageDto>> UpdatePackageAsync(Guid packageId, UpdateTokenPackageRequest request)
    {
        try
        {
            var package = await _tokenPackageRepository.GetByIdAsync(packageId);
            if (package == null)
            {
                return Result<TokenPackageDto>.Failure("Token包不存在");
            }
            
            package.Update(
                name: request.Name ?? package.Name,
                description: request.Description ?? package.Description,
                price: request.Price ?? package.Price,
                originalPrice: request.OriginalPrice ?? package.OriginalPrice,
                sortOrder: package.SortOrder
            );
            
            if (request.ValidFrom.HasValue && request.ValidTo.HasValue)
            {
                package.SetLimitedTimeOffer(request.ValidFrom.Value, request.ValidTo.Value);
            }
            
            if (request.MaxPurchasePerUser.HasValue)
            {
                package.SetPurchaseLimit(request.MaxPurchasePerUser.Value);
            }
            
            _tokenPackageRepository.Update(package);
            await _unitOfWork.SaveChangesAsync();
            
            await ClearPackagesCacheAsync();
            
            var dto = MapToDto(package);
            
            _logger.LogInformation("Updated token package {PackageId}", packageId);
            
            return Result<TokenPackageDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update token package {PackageId}", packageId);
            return Result<TokenPackageDto>.Failure("更新Token包失败");
        }
    }
    
    public async Task<Result> SetPackageStatusAsync(Guid packageId, bool isActive)
    {
        try
        {
            var package = await _tokenPackageRepository.GetByIdAsync(packageId);
            if (package == null)
            {
                return Result.Failure("Token包不存在");
            }
            
            if (isActive)
            {
                package.Activate();
            }
            else
            {
                package.Deactivate();
            }
            
            _tokenPackageRepository.Update(package);
            await _unitOfWork.SaveChangesAsync();
            
            await ClearPackagesCacheAsync();
            
            _logger.LogInformation("Set token package {PackageId} status to {Status}", 
                packageId, isActive ? "active" : "inactive");
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set token package {PackageId} status", packageId);
            return Result.Failure("设置Token包状态失败");
        }
    }
    
    public async Task<Result<TokenPackageSalesStatisticsDto>> GetSalesStatisticsAsync(
        DateTime? startDate = null, 
        DateTime? endDate = null)
    {
        try
        {
            var start = startDate ?? DateTime.UtcNow.AddMonths(-1);
            var end = endDate ?? DateTime.UtcNow;
            
            // Get all orders and filter by type and date range
            var allOrders = await _orderRepository.GetAllAsync();
            var orders = allOrders
                .Where(o => o.Type == OrderType.TokenPackage 
                    && o.CreatedAt >= start 
                    && o.CreatedAt <= end)
                .ToList();
            
            var statistics = new TokenPackageSalesStatisticsDto
            {
                TotalRevenue = orders.Where(o => o.Status == OrderStatus.Paid).Sum(o => o.FinalAmount.Amount),
                TotalOrders = orders.Count(o => o.Status == OrderStatus.Paid),
                TotalTokensSold = orders.Where(o => o.Status == OrderStatus.Paid)
                    .Sum(o => o.Metadata.ContainsKey("TokenAmount") 
                        ? Convert.ToInt64(o.Metadata["TokenAmount"]) 
                        : 0),
                AverageOrderValue = orders.Any(o => o.Status == OrderStatus.Paid) 
                    ? orders.Where(o => o.Status == OrderStatus.Paid).Average(o => o.FinalAmount.Amount) 
                    : 0
            };
            
            // 按包统计
            statistics.PackageSales = orders
                .Where(o => o.Status == OrderStatus.Paid && o.ProductId.HasValue)
                .GroupBy(o => new { 
                    PackageId = o.ProductId!.Value, 
                    PackageName = o.ProductName ?? "未知" 
                })
                .Select(g => new PackageSalesDto
                {
                    PackageId = g.Key.PackageId,
                    PackageName = g.Key.PackageName,
                    OrderCount = g.Count(),
                    TokensSold = g.Sum(o => o.Metadata.ContainsKey("TokenAmount") 
                        ? Convert.ToInt64(o.Metadata["TokenAmount"]) 
                        : 0),
                    Revenue = g.Sum(o => o.FinalAmount.Amount)
                })
                .ToList();
            
            // 按日统计
            statistics.DailySales = orders
                .Where(o => o.Status == OrderStatus.Paid)
                .GroupBy(o => o.CreatedAt.Date)
                .Select(g => new DailySalesDto
                {
                    Date = g.Key,
                    Orders = g.Count(),
                    Revenue = g.Sum(o => o.FinalAmount.Amount),
                    TokensSold = g.Sum(o => o.Metadata.ContainsKey("TokenAmount") 
                        ? Convert.ToInt64(o.Metadata["TokenAmount"]) 
                        : 0)
                })
                .OrderBy(d => d.Date)
                .ToList();
            
            // 按支付方式统计
            statistics.RevenueByPaymentMethod = orders
                .Where(o => o.Status == OrderStatus.Paid)
                .GroupBy(o => o.PaymentMethod.ToString())
                .ToDictionary(g => g.Key, g => g.Sum(o => o.FinalAmount.Amount));
            
            return Result<TokenPackageSalesStatisticsDto>.Success(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get token package sales statistics");
            return Result<TokenPackageSalesStatisticsDto>.Failure("获取销售统计失败");
        }
    }
    
    private TokenPackageDto MapToDto(TokenPackage package)
    {
        return new TokenPackageDto
        {
            Id = package.Id,
            Name = package.Name,
            Description = package.Description,
            TokenAmount = package.TokenAmount,
            Price = package.Price,
            OriginalPrice = package.OriginalPrice,
            PricePerThousandTokens = package.GetPricePerThousandTokens(),
            IsActive = package.IsActive,
            IsLimited = package.IsLimited,
            ValidFrom = package.ValidFrom,
            ValidTo = package.ValidTo,
            MaxPurchasePerUser = package.MaxPurchasePerUser,
            TotalStock = package.TotalStock,
            RemainingStock = package.GetRemainingStock(),
            SortOrder = package.SortOrder,
            Features = package.GetFeatures(),
            CreatedAt = package.CreatedAt
        };
    }
    
    private async Task<Result<CouponValidationResult>> ValidateAndApplyCouponAsync(
        string couponCode, 
        decimal amount, 
        Guid userId)
    {
        try
        {
            var coupon = await _couponRepository.GetByCodeAsync(couponCode);
            if (coupon == null || !coupon.IsValid())
            {
                return Result<CouponValidationResult>.Failure("优惠券无效或已过期");
            }
            
            if (!coupon.CanUseBy(userId))
            {
                return Result<CouponValidationResult>.Failure("您已使用过此优惠券");
            }
            
            var orderAmountMoney = Money.Create(amount, "CNY");
            var discountMoney = coupon.CalculateDiscount(orderAmountMoney);
            var discountAmount = discountMoney.Amount;
            var finalAmount = Math.Max(0, amount - discountAmount);
            
            return Result<CouponValidationResult>.Success(new CouponValidationResult
            {
                Coupon = coupon,
                DiscountAmount = discountAmount,
                FinalAmount = finalAmount
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate coupon {CouponCode}", couponCode);
            return Result<CouponValidationResult>.Failure("优惠券验证失败");
        }
    }
    
    private async Task ClearPackagesCacheAsync()
    {
        await _cacheService.RemoveAsync("tokenpackages:available");
    }
    
    private class CouponValidationResult
    {
        public Domain.Entities.Payment.Coupon Coupon { get; set; } = null!;
        public decimal DiscountAmount { get; set; }
        public decimal FinalAmount { get; set; }
    }
}