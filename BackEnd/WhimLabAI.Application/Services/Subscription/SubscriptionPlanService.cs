using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Shared.Dtos.Subscription;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Application.Services.Subscription;

public class SubscriptionPlanService : ISubscriptionPlanService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly ILogger<SubscriptionPlanService> _logger;

    public SubscriptionPlanService(
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        ILogger<SubscriptionPlanService> logger)
    {
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<Result<List<SubscriptionPlanDto>>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = "subscription-plans:all";
            var cachedPlans = await _cacheService.GetAsync<List<SubscriptionPlanDto>>(cacheKey, cancellationToken);
            if (cachedPlans != null)
            {
                return Result<List<SubscriptionPlanDto>>.Success(cachedPlans);
            }

            var planRepository = _unitOfWork.Repository<SubscriptionPlan>();
            var plans = await planRepository.GetAllAsync(cancellationToken);
            var planDtos = MapToDto(plans.ToList());

            await _cacheService.SetAsync(cacheKey, planDtos, TimeSpan.FromHours(1), cancellationToken);
            return Result<List<SubscriptionPlanDto>>.Success(planDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all subscription plans");
            return Result<List<SubscriptionPlanDto>>.Failure("GET_PLANS_ERROR", "获取订阅套餐失败");
        }
    }

    public async Task<Result<List<SubscriptionPlanDto>>> GetActiveAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = "subscription-plans:active";
            var cachedPlans = await _cacheService.GetAsync<List<SubscriptionPlanDto>>(cacheKey, cancellationToken);
            if (cachedPlans != null)
            {
                return Result<List<SubscriptionPlanDto>>.Success(cachedPlans);
            }

            var planRepository = _unitOfWork.Repository<SubscriptionPlan>();
            var plans = await planRepository.GetAsync(p => p.IsActive, cancellationToken);
            var planDtos = MapToDto(plans.ToList());

            await _cacheService.SetAsync(cacheKey, planDtos, TimeSpan.FromHours(1), cancellationToken);
            return Result<List<SubscriptionPlanDto>>.Success(planDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active subscription plans");
            return Result<List<SubscriptionPlanDto>>.Failure("GET_ACTIVE_PLANS_ERROR", "获取活跃订阅套餐失败");
        }
    }

    public async Task<Result<SubscriptionPlanDto>> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"subscription-plan:{id}";
            var cachedPlan = await _cacheService.GetAsync<SubscriptionPlanDto>(cacheKey, cancellationToken);
            if (cachedPlan != null)
            {
                return Result<SubscriptionPlanDto>.Success(cachedPlan);
            }

            var planRepository = _unitOfWork.Repository<SubscriptionPlan>();
            var plan = await planRepository.GetByIdAsync(id, cancellationToken);
            if (plan == null)
            {
                return Result<SubscriptionPlanDto>.Failure("PLAN_NOT_FOUND", "订阅套餐不存在");
            }

            var planDto = MapToDto(new List<SubscriptionPlan> { plan }).First();
            await _cacheService.SetAsync(cacheKey, planDto, TimeSpan.FromHours(1), cancellationToken);
            
            return Result<SubscriptionPlanDto>.Success(planDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription plan by id: {Id}", id);
            return Result<SubscriptionPlanDto>.Failure("GET_PLAN_ERROR", "获取订阅套餐详情失败");
        }
    }

    public async Task<Result<SubscriptionPlanDto>> GetByTierAsync(string tier, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!Enum.TryParse<SubscriptionTier>(tier, true, out var tierEnum))
            {
                return Result<SubscriptionPlanDto>.Failure("INVALID_TIER", "无效的订阅级别");
            }

            var cacheKey = $"subscription-plan:tier:{tier}";
            var cachedPlan = await _cacheService.GetAsync<SubscriptionPlanDto>(cacheKey, cancellationToken);
            if (cachedPlan != null)
            {
                return Result<SubscriptionPlanDto>.Success(cachedPlan);
            }

            var planRepository = _unitOfWork.Repository<SubscriptionPlan>();
            var plans = await planRepository.GetAsync(p => p.Tier == tierEnum && p.IsActive, cancellationToken);
            var plan = plans.FirstOrDefault();
            
            if (plan == null)
            {
                return Result<SubscriptionPlanDto>.Failure("PLAN_NOT_FOUND", $"找不到{tier}级别的订阅套餐");
            }

            var planDto = MapToDto(new List<SubscriptionPlan> { plan }).First();
            await _cacheService.SetAsync(cacheKey, planDto, TimeSpan.FromHours(1), cancellationToken);
            
            return Result<SubscriptionPlanDto>.Success(planDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription plan by tier: {Tier}", tier);
            return Result<SubscriptionPlanDto>.Failure("GET_PLAN_BY_TIER_ERROR", "获取指定级别订阅套餐失败");
        }
    }

    public async Task<Result<SubscriptionPlanDto>> CreateAsync(CreateSubscriptionPlanDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if plan with same tier already exists
            var existingPlans = await _unitOfWork.Repository<SubscriptionPlan>()
                .GetAsync(p => p.Tier == request.Tier && p.IsActive, cancellationToken);
            
            if (existingPlans.Any())
            {
                return Result<SubscriptionPlanDto>.Failure("PLAN_EXISTS", $"{request.Tier}级别的套餐已存在");
            }

            var money = Domain.ValueObjects.Money.Create(request.Price, "CNY");
            var plan = new SubscriptionPlan(
                request.Name,
                request.Tier,
                money,
                request.TokenQuota,
                request.BillingCycle,
                request.Description
            );

            // Add features
            foreach (var feature in request.Features)
            {
                plan.AddFeature(feature);
            }

            // Set limits
            plan.SetAdvancedLimits(
                request.MaxAgents,
                request.MaxConversationsPerDay,
                request.AllowCustomAgents,
                request.AllowPlugins,
                request.AllowKnowledgeBase
            );

            await _unitOfWork.Repository<SubscriptionPlan>().AddAsync(plan, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync("subscription-plans:all", cancellationToken);
            await _cacheService.RemoveAsync("subscription-plans:active", cancellationToken);

            _logger.LogInformation("Created subscription plan: {Id} - {Name}", plan.Id, plan.Name);
            
            var planDto = MapToDto(new List<SubscriptionPlan> { plan }).First();
            return Result<SubscriptionPlanDto>.Success(planDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating subscription plan");
            return Result<SubscriptionPlanDto>.Failure("CREATE_PLAN_ERROR", "创建订阅套餐失败");
        }
    }

    public async Task<Result> UpdateAsync(Guid id, UpdateSubscriptionPlanDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var plan = await _unitOfWork.Repository<SubscriptionPlan>().GetByIdAsync(id, cancellationToken);
            if (plan == null)
            {
                return Result.Failure("PLAN_NOT_FOUND", "订阅套餐不存在");
            }

            var money = Domain.ValueObjects.Money.Create(request.Price, "CNY");
            plan.Update(request.Name, request.Description, money, request.TokenQuota);

            // Update features
            plan.SetFeatures(request.Features);

            // Update limits
            plan.SetAdvancedLimits(
                request.MaxAgents,
                request.MaxConversationsPerDay,
                request.AllowCustomAgents,
                request.AllowPlugins,
                request.AllowKnowledgeBase
            );

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync("subscription-plans:all", cancellationToken);
            await _cacheService.RemoveAsync("subscription-plans:active", cancellationToken);
            await _cacheService.RemoveAsync($"subscription-plan:{id}", cancellationToken);
            await _cacheService.RemoveAsync($"subscription-plan:tier:{plan.Tier}", cancellationToken);

            _logger.LogInformation("Updated subscription plan: {Id}", id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subscription plan: {Id}", id);
            return Result.Failure("UPDATE_PLAN_ERROR", "更新订阅套餐失败");
        }
    }

    public async Task<Result> EnableAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var plan = await _unitOfWork.Repository<SubscriptionPlan>().GetByIdAsync(id, cancellationToken);
            if (plan == null)
            {
                return Result.Failure("PLAN_NOT_FOUND", "订阅套餐不存在");
            }

            plan.Activate();
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await ClearPlanCache(plan, cancellationToken);

            _logger.LogInformation("Enabled subscription plan: {Id}", id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling subscription plan: {Id}", id);
            return Result.Failure("ENABLE_PLAN_ERROR", "启用订阅套餐失败");
        }
    }

    public async Task<Result> DisableAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var plan = await _unitOfWork.Repository<SubscriptionPlan>().GetByIdAsync(id, cancellationToken);
            if (plan == null)
            {
                return Result.Failure("PLAN_NOT_FOUND", "订阅套餐不存在");
            }

            plan.Deactivate();
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await ClearPlanCache(plan, cancellationToken);

            _logger.LogInformation("Disabled subscription plan: {Id}", id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling subscription plan: {Id}", id);
            return Result.Failure("DISABLE_PLAN_ERROR", "禁用订阅套餐失败");
        }
    }

    public async Task<Result> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var plan = await _unitOfWork.Repository<SubscriptionPlan>().GetByIdAsync(id, cancellationToken);
            if (plan == null)
            {
                return Result.Failure("PLAN_NOT_FOUND", "订阅套餐不存在");
            }

            // Check if any active subscriptions use this plan
            var activeSubscriptions = await _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>()
                .GetAsync(s => s.PlanId == id && s.Status == SubscriptionStatus.Active, cancellationToken);
            
            if (activeSubscriptions.Any())
            {
                return Result.Failure("PLAN_IN_USE", "该套餐有活跃的订阅，无法删除");
            }

            _unitOfWork.Repository<SubscriptionPlan>().Remove(plan);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await ClearPlanCache(plan, cancellationToken);

            _logger.LogInformation("Deleted subscription plan: {Id}", id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting subscription plan: {Id}", id);
            return Result.Failure("DELETE_PLAN_ERROR", "删除订阅套餐失败");
        }
    }

    private List<SubscriptionPlanDto> MapToDto(List<SubscriptionPlan> plans)
    {
        return plans.Select(p => new SubscriptionPlanDto
        {
            Id = p.Id,
            Name = p.Name,
            Code = p.Tier.ToString().ToUpper(),
            Tier = p.Tier,
            Description = p.Description,
            Price = p.Price.Amount,
            Period = p.BillingCycle == BillingCycle.Monthly ? SubscriptionPeriod.Monthly : SubscriptionPeriod.Annual,
            TokenQuota = p.MonthlyTokens,
            MaxAgents = p.MaxAgents,
            MaxKnowledgeBases = p.AllowKnowledgeBase ? 10 : 0,  // Default values based on tier
            MaxDocuments = p.AllowKnowledgeBase ? 100 : 0,     // Default values based on tier
            IncludesSupport = p.Tier >= SubscriptionTier.Pro,
            IncludesTraining = p.Tier == SubscriptionTier.Ultra,
            Features = p.Features.ToList(),
            IsPopular = p.Tier == SubscriptionTier.Pro,
            IsActive = p.IsActive,
            SortOrder = (int)p.Tier
        }).OrderBy(p => p.SortOrder).ToList();
    }

    private async Task ClearPlanCache(SubscriptionPlan plan, CancellationToken cancellationToken)
    {
        await _cacheService.RemoveAsync("subscription-plans:all", cancellationToken);
        await _cacheService.RemoveAsync("subscription-plans:active", cancellationToken);
        await _cacheService.RemoveAsync($"subscription-plan:{plan.Id}", cancellationToken);
        await _cacheService.RemoveAsync($"subscription-plan:tier:{plan.Tier}", cancellationToken);
    }
}