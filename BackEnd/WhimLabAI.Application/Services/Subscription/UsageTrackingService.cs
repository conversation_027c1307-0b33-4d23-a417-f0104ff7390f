using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Subscription;

public class UsageTrackingService : IUsageTrackingService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly ILogger<UsageTrackingService> _logger;

    public UsageTrackingService(
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        ILogger<UsageTrackingService> logger)
    {
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<Result<bool>> ConsumeTokensAsync(Guid userId, Guid conversationId, int tokens, string model, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get user's active subscription
            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var subscription = await subscriptionRepository.FirstOrDefaultAsync(
                s => s.CustomerUserId == userId && s.Status == WhimLabAI.Shared.Enums.SubscriptionStatus.Active, cancellationToken);
            
            if (subscription == null)
            {
                return Result<bool>.Failure("INVALID_SUBSCRIPTION", "订阅数据无效");
            }

            // Check if user has enough tokens
            if (subscription.RemainingTokens < tokens)
            {
                _logger.LogWarning("User doesn't have enough tokens for the requested operation");
                return Result<bool>.Failure("INSUFFICIENT_TOKENS", "Token额度不足");
            }

            // Begin transaction
            await _unitOfWork.BeginTransactionAsync(cancellationToken: cancellationToken);

            try
            {
                // Consume tokens
                subscription.UseTokens(tokens);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                // Record usage
                // Get conversation to find agent ID
                var conversationRepository = _unitOfWork.Repository<Domain.Entities.Conversation.Conversation>();
                var conversation = await conversationRepository.GetByIdAsync(conversationId, cancellationToken);
                
                if (conversation != null)
                {
                    // Parse model string to get provider and model name
                    var modelParts = model.Split('/');
                    var modelProvider = modelParts.Length > 1 ? modelParts[0] : "unknown";
                    var modelName = modelParts.Length > 1 ? modelParts[1] : model;
                    
                    var usageRecord = new Domain.Entities.Subscription.UsageRecord(
                        subscription.Id,
                        userId,
                        conversationId,
                        conversation.AgentId,
                        modelName,
                        modelProvider,
                        tokens,
                        0); // Cost calculation not implemented yet
                    
                    await _unitOfWork.Repository<Domain.Entities.Subscription.UsageRecord>().AddAsync(usageRecord, cancellationToken);
                }

                await _unitOfWork.CommitAsync(cancellationToken);

                // Update cache
                await UpdateUsageCacheAsync(userId, subscription.Id, tokens, cancellationToken);

                _logger.LogDebug("Successfully consumed tokens for user request");

                return Result<bool>.Success(true);
            }
            catch
            {
                await _unitOfWork.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error consuming tokens for user");
            return Result<bool>.Failure("CONSUME_ERROR", "消费Token失败");
        }
    }

    public async Task<Result<int>> GetRemainingTokensAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Try cache first
            var cacheKey = $"user-tokens:{userId}";
            var cachedTokens = await _cacheService.GetAsync<int?>(cacheKey, cancellationToken);
            if (cachedTokens.HasValue)
            {
                return Result<int>.Success(cachedTokens.Value);
            }

            // Get from database
            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var subscription = await subscriptionRepository.FirstOrDefaultAsync(
                s => s.CustomerUserId == userId && s.Status == WhimLabAI.Shared.Enums.SubscriptionStatus.Active, cancellationToken);
            
            if (subscription == null)
            {
                return Result<int>.Success(0); // No subscription means no tokens
            }

            var remainingTokens = subscription.RemainingTokens;

            // Cache for 5 minutes
            await _cacheService.SetAsync(cacheKey, remainingTokens, TimeSpan.FromMinutes(5), cancellationToken);

            return Result<int>.Success(remainingTokens);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting remaining tokens for user");
            return Result<int>.Failure("GET_TOKENS_ERROR", "获取剩余Token失败");
        }
    }

    public async Task<Result<bool>> CheckQuotaAsync(Guid userId, int requiredTokens, CancellationToken cancellationToken = default)
    {
        try
        {
            var remainingResult = await GetRemainingTokensAsync(userId, cancellationToken);
            if (!remainingResult.IsSuccess)
            {
                return Result<bool>.Failure(remainingResult.Error!, remainingResult.ErrorCode);
            }

            var hasEnoughTokens = remainingResult.Value >= requiredTokens;
            
            if (!hasEnoughTokens)
            {
                _logger.LogWarning("User quota check failed - insufficient tokens");
            }

            return Result<bool>.Success(hasEnoughTokens);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking quota for user");
            return Result<bool>.Failure("CHECK_QUOTA_ERROR", "检查配额失败");
        }
    }

    public async Task<Result> ResetMonthlyQuotaAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var subscriptionObj = await _unitOfWork.Subscriptions.GetActiveSubscriptionByUserIdAsync(userId, cancellationToken);
            if (subscriptionObj == null)
            {
                return Result.Failure("NO_SUBSCRIPTION", "用户没有有效订阅");
            }

            var subscription = subscriptionObj as Domain.Entities.Subscription.Subscription;
            if (subscription == null)
            {
                return Result.Failure("INVALID_SUBSCRIPTION", "订阅数据无效");
            }

            // Check if it's time to reset
            var lastReset = subscription.LastResetDate ?? subscription.StartDate;
            var nextReset = lastReset.AddMonths(1);
            
            if (DateTime.UtcNow < nextReset)
            {
                return Result.Failure("NOT_TIME_TO_RESET", $"下次重置时间: {nextReset:yyyy-MM-dd}");
            }

            // Reset tokens
            if (subscription.Plan != null)
            {
                subscription.ResetTokens(subscription.Plan.MonthlyTokens);
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }
            else
            {
                return Result.Failure("NO_PLAN", "订阅没有关联的套餐");
            }

            // Clear cache
            var cacheKey = $"user-tokens:{userId}";
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            _logger.LogInformation("Reset monthly quota for user {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting monthly quota for user {UserId}", userId);
            return Result.Failure("RESET_ERROR", "重置配额失败");
        }
    }

    private async Task UpdateUsageCacheAsync(Guid userId, Guid subscriptionId, int tokensConsumed, CancellationToken cancellationToken)
    {
        try
        {
            // Update remaining tokens cache
            var tokensCacheKey = $"user-tokens:{userId}";
            var cachedTokens = await _cacheService.GetAsync<int?>(tokensCacheKey, cancellationToken);
            if (cachedTokens.HasValue)
            {
                var newValue = Math.Max(0, cachedTokens.Value - tokensConsumed);
                await _cacheService.SetAsync(tokensCacheKey, newValue, TimeSpan.FromMinutes(5), cancellationToken);
            }

            // Update daily usage cache
            var dateCacheKey = $"daily-usage:{subscriptionId}:{DateTime.UtcNow:yyyy-MM-dd}";
            var dailyUsage = await _cacheService.GetAsync<int>(dateCacheKey, cancellationToken);
            await _cacheService.SetAsync(dateCacheKey, dailyUsage + tokensConsumed, TimeSpan.FromHours(25), cancellationToken);
        }
        catch (Exception ex)
        {
            // Cache update failure should not affect the main operation
            _logger.LogWarning(ex, "Failed to update usage cache for user {UserId}", userId);
        }
    }
}