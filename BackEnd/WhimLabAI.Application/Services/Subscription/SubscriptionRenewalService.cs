using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.Application.Services.Subscription;

public class SubscriptionRenewalService : ISubscriptionRenewalService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<SubscriptionRenewalService> _logger;
    private readonly INotificationService _notificationService;
    private readonly IPaymentService _paymentService;
    private readonly IOrderService _orderService;

    public SubscriptionRenewalService(
        IUnitOfWork unitOfWork,
        ILogger<SubscriptionRenewalService> logger,
        INotificationService notificationService,
        IPaymentService paymentService,
        IOrderService orderService)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _notificationService = notificationService;
        _paymentService = paymentService;
        _orderService = orderService;
    }

    public async Task<Result<int>> ProcessExpiringSubscriptionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var processedCount = 0;
            var tomorrow = DateTime.UtcNow.AddDays(1);
            
            // Get subscriptions expiring today or tomorrow that are still active
            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var expiringSubscriptions = await subscriptionRepository.GetAsync(
                s => s.EndDate.HasValue && s.EndDate.Value <= tomorrow && s.Status == SubscriptionStatus.Active, cancellationToken);
            var subscriptionList = expiringSubscriptions.ToList();

            foreach (var subscription in subscriptionList)
            {
                try
                {
                    if (subscription.AutoRenew && subscription.Status == SubscriptionStatus.Active)
                    {
                        // Process auto-renewal
                        await ProcessAutoRenewalAsync(subscription, cancellationToken);
                    }
                    else if (subscription.EndDate <= DateTime.UtcNow && subscription.Status == SubscriptionStatus.Active)
                    {
                        // Expire the subscription
                        await ExpireSubscriptionAsync(subscription, cancellationToken);
                    }
                    
                    processedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing subscription for user");
                }
            }

            _logger.LogInformation("Processed {Count} expiring subscriptions", processedCount);
            return Result<int>.Success(processedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing expiring subscriptions");
            return Result<int>.Failure("PROCESS_ERROR", "处理过期订阅失败");
        }
    }

    public async Task<Result<int>> ProcessMonthlyResetAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var resetCount = 0;
            
            // Get subscriptions needing token reset
            var subscriptionRepository2 = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var oneMonthAgo = DateTime.UtcNow.AddMonths(-1);
            var subscriptions2 = await subscriptionRepository2.GetAsync(
                s => s.Status == SubscriptionStatus.Active && 
                     (s.LastResetDate == null || s.LastResetDate <= oneMonthAgo), 
                cancellationToken);
            var subscriptionList2 = subscriptions2.ToList();

            foreach (var subscription in subscriptionList2)
            {
                try
                {
                    if (subscription.Plan != null)
                    {
                        subscription.ResetTokens(subscription.Plan.MonthlyTokens);
                        await _unitOfWork.SaveChangesAsync(cancellationToken);
                        resetCount++;
                        _logger.LogInformation("Reset monthly tokens for active subscription");
                        
                        // Send notification to user about token reset
                        await _notificationService.NotifyTokenUsageAsync(
                            subscription.CustomerUserId,
                            subscription.Plan.MonthlyTokens,
                            subscription.Plan.MonthlyTokens,
                            cancellationToken);
                    }
                    else
                    {
                        _logger.LogWarning("Subscription has no plan associated");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error resetting tokens for subscription");
                }
            }

            _logger.LogInformation("Reset tokens for {Count} subscriptions", resetCount);
            return Result<int>.Success(resetCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing monthly reset");
            return Result<int>.Failure("RESET_ERROR", "月度重置失败");
        }
    }

    public async Task<Result> SendRenewalRemindersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var remindersSent = 0;
            
            // Get subscriptions expiring in 3 days
            var threeDaysLater = DateTime.UtcNow.AddDays(3);
            var subscriptionRepository3 = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var expiringSubscriptions3 = await subscriptionRepository3.GetAsync(
                s => s.EndDate.HasValue && 
                     s.EndDate.Value <= threeDaysLater && 
                     !s.AutoRenew && 
                     s.Status == SubscriptionStatus.Active, 
                cancellationToken);
            var subscriptions = expiringSubscriptions3.ToList();

            foreach (var subscription in subscriptions)
            {
                try
                {
                    // Get user info
                    var userRepository = _unitOfWork.Repository<Domain.Entities.User.CustomerUser>();
                    var user = await userRepository.GetByIdAsync(subscription.CustomerUserId, cancellationToken);
                    if (user != null)
                    {
                        // Send renewal reminder notification
                        var daysRemaining = subscription.EndDate.HasValue 
                            ? (subscription.EndDate.Value - DateTime.UtcNow).Days 
                            : 0;
                        
                        var notification = new SendNotificationDto
                        {
                            UserId = subscription.CustomerUserId,
                            Title = "订阅即将到期",
                            Content = $"您的{subscription.Plan?.Name ?? "订阅"}将在{daysRemaining}天后到期，请及时续费以继续享受服务。",
                            Type = "subscription",
                            Level = "warning",
                            Metadata = new Dictionary<string, object>
                            {
                                ["subscriptionId"] = subscription.Id,
                                ["planName"] = subscription.Plan?.Name ?? "订阅",
                                ["daysRemaining"] = daysRemaining,
                                ["renewUrl"] = $"/subscription/renew/{subscription.Id}"
                            }
                        };
                        
                        await _notificationService.SendNotificationAsync(notification, cancellationToken);
                        _logger.LogInformation("Sent renewal reminder for expiring subscription");
                        
                        remindersSent++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error sending renewal reminder");
                }
            }

            _logger.LogInformation("Sent {Count} renewal reminders", remindersSent);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending renewal reminders");
            return Result.Failure("REMINDER_ERROR", "发送续费提醒失败");
        }
    }

    public async Task<Result> CleanupExpiredSubscriptionsAsync(int daysToKeep = 90, CancellationToken cancellationToken = default)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
            var expiredCount = 0;
            
            // Get old expired subscriptions
            var subscriptionRepository4 = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var allSubscriptions = await subscriptionRepository4.GetAsync(
                s => s.Status == SubscriptionStatus.Expired && 
                     s.EndDate.HasValue && 
                     s.EndDate.Value < cutoffDate,
                cancellationToken);
            var oldExpired = allSubscriptions.ToList();

            foreach (var subscription in oldExpired)
            {
                // Delete old usage records
                var usageRepository = _unitOfWork.Repository<Domain.Entities.Subscription.UsageRecord>();
                var records = await usageRepository.GetAsync(
                    r => r.SubscriptionId == subscription.Id &&
                         r.UsageTime >= subscription.StartDate &&
                         r.UsageTime <= (subscription.EndDate ?? DateTime.UtcNow),
                    cancellationToken);
                
                foreach (var record in records)
                {
                    usageRepository.Remove(record);
                }
                
                expiredCount++;
            }

            if (expiredCount > 0)
            {
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }

            _logger.LogInformation("Cleaned up {Count} expired subscriptions", expiredCount);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired subscriptions");
            return Result.Failure("CLEANUP_ERROR", "清理过期订阅失败");
        }
    }

    private async Task ProcessAutoRenewalAsync(Domain.Entities.Subscription.Subscription subscription, CancellationToken cancellationToken)
    {
        try
        {
            // Check if user has payment method
            if (string.IsNullOrEmpty(subscription.PaymentMethod))
            {
                _logger.LogWarning("No payment method for auto-renewal of subscription {SubscriptionId}", subscription.Id);
                subscription.DisableAutoRenew();
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                return;
            }

            // Process payment
            _logger.LogInformation("Processing auto-renewal payment for subscription {SubscriptionId}", subscription.Id);
            
            var paymentSuccessful = false;
            
            try
            {
                // Create renewal order
                var createOrderDto = new CreateOrderDto
                {
                    Items = new List<OrderItemDto>
                    {
                        new()
                        {
                            ProductType = ProductType.SubscriptionPlan,
                            ProductId = subscription.PlanId,
                            ProductName = subscription.Plan?.Name ?? "订阅续费",
                            Quantity = 1,
                            UnitPrice = subscription.Plan?.Price.Amount ?? 0
                        }
                    },
                    PaymentMethod = Enum.Parse<PaymentMethod>(subscription.PaymentMethod),
                    Remark = "自动续费"
                };
                
                var orderResult = await _orderService.CreateOrderAsync(createOrderDto, subscription.CustomerUserId, cancellationToken);
                if (!orderResult.IsSuccess)
                {
                    _logger.LogError("Failed to create renewal order: {Error}", orderResult.Error);
                    return;
                }
                
                // Process payment
                var processPaymentDto = new ProcessPaymentDto
                {
                    OrderId = orderResult.Value.Id,
                    PaymentMethod = Enum.Parse<PaymentMethod>(subscription.PaymentMethod)
                };
                
                var paymentResult = await _paymentService.ProcessPaymentAsync(processPaymentDto, cancellationToken);
                if (paymentResult.IsSuccess)
                {
                    // Wait for payment confirmation (in real scenario, this would be handled by callback)
                    // For auto-renewal, we assume immediate payment processing
                    await Task.Delay(1000, cancellationToken); // Simulate payment processing
                    
                    // Check payment status
                    var statusResult = await _paymentService.CheckPaymentStatusAsync(paymentResult.Value.PaymentNo, cancellationToken);
                    paymentSuccessful = statusResult.IsSuccess;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing auto-renewal payment for subscription {SubscriptionId}", subscription.Id);
            }

            if (paymentSuccessful)
            {
                // Extend subscription
                var newEndDate = (subscription.EndDate ?? DateTime.UtcNow).AddDays(subscription.Plan?.DurationDays ?? 30);
                subscription.Renew(newEndDate);
                
                // Reset monthly tokens
                if (subscription.Plan != null)
                {
                    subscription.ResetTokens(subscription.Plan.MonthlyTokens);
                }
                
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation("Auto-renewed subscription {SubscriptionId} until {EndDate}", 
                    subscription.Id, subscription.EndDate);
                
                // Send renewal confirmation notification
                await _notificationService.NotifySubscriptionChangeAsync(
                    subscription.CustomerUserId,
                    subscription.Plan?.Name ?? "订阅",
                    "renewed",
                    cancellationToken);
            }
            else
            {
                // Payment failed, disable auto-renewal
                subscription.DisableAutoRenew();
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                
                _logger.LogWarning("Auto-renewal payment failed for subscription {SubscriptionId}", subscription.Id);
                
                // Send payment failure notification
                var failureNotification = new SendNotificationDto
                {
                    UserId = subscription.CustomerUserId,
                    Title = "自动续费失败",
                    Content = $"您的{subscription.Plan?.Name ?? "订阅"}自动续费失败，请检查支付方式或手动续费。",
                    Type = "subscription",
                    Level = "error",
                    Metadata = new Dictionary<string, object>
                    {
                        ["subscriptionId"] = subscription.Id,
                        ["planName"] = subscription.Plan?.Name ?? "订阅",
                        ["renewUrl"] = $"/subscription/renew/{subscription.Id}",
                        ["paymentSettingsUrl"] = "/settings/payment"
                    }
                };
                
                await _notificationService.SendNotificationAsync(failureNotification, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing auto-renewal for subscription {SubscriptionId}", subscription.Id);
            throw;
        }
    }

    private async Task ExpireSubscriptionAsync(Domain.Entities.Subscription.Subscription subscription, CancellationToken cancellationToken)
    {
        try
        {
            subscription.Expire();
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Expired subscription {SubscriptionId}", subscription.Id);
            
            // Send expiration notification
            var expirationNotification = new SendNotificationDto
            {
                UserId = subscription.CustomerUserId,
                Title = "订阅已过期",
                Content = $"您的{subscription.Plan?.Name ?? "订阅"}已过期，请续费以继续享受完整服务。",
                Type = "subscription",
                Level = "warning",
                Metadata = new Dictionary<string, object>
                {
                    ["subscriptionId"] = subscription.Id,
                    ["planName"] = subscription.Plan?.Name ?? "订阅",
                    ["renewUrl"] = $"/subscription/renew/{subscription.Id}"
                }
            };
            
            await _notificationService.SendNotificationAsync(expirationNotification, cancellationToken);
            
            // Check if user has any other active subscription
            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var otherActive = await subscriptionRepository.FirstOrDefaultAsync(
                s => s.CustomerUserId == subscription.CustomerUserId && 
                     s.Id != subscription.Id && 
                     s.Status == SubscriptionStatus.Active, 
                cancellationToken);
            var hasOtherActive = otherActive != null;
                
            if (!hasOtherActive)
            {
                // Downgrade user to free tier
                _logger.LogInformation("User {UserId} has no active subscriptions, downgrading to free tier", subscription.CustomerUserId);
                
                // Get free plan
                var planRepository = _unitOfWork.Repository<Domain.Entities.Subscription.SubscriptionPlan>();
                var freePlan = await planRepository.FirstOrDefaultAsync(
                    p => p.Tier == SubscriptionTier.Free && p.IsActive,
                    cancellationToken);
                
                if (freePlan != null)
                {
                    // Create free tier subscription
                    var freeSubscription = new Domain.Entities.Subscription.Subscription(
                        subscription.CustomerUserId,
                        freePlan.Id,
                        DateTime.UtcNow,
                        null, // No end date for free tier
                        null, // paymentMethod
                        null, // orderId
                        false); // autoRenew
                    
                    await subscriptionRepository.AddAsync(freeSubscription, cancellationToken);
                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                    
                    // Notify user about downgrade
                    await _notificationService.NotifySubscriptionChangeAsync(
                        subscription.CustomerUserId,
                        freePlan.Name,
                        "downgraded",
                        cancellationToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error expiring subscription {SubscriptionId}", subscription.Id);
            throw;
        }
    }
}