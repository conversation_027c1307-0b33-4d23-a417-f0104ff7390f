using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application.Services;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.DTOs.Common;
using WhimLabAI.Shared.DTOs.Coupon;
using WhimLabAI.Shared.Exceptions;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Coupon;

public class CouponService : ICouponService
{
    private readonly ICouponRepository _couponRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CouponService> _logger;

    public CouponService(
        ICouponRepository couponRepository,
        IUnitOfWork unitOfWork,
        ILogger<CouponService> logger)
    {
        _couponRepository = couponRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<CouponDto>> CreateAsync(CreateCouponDto dto, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if code already exists
            if (await _couponRepository.IsCodeExistsAsync(dto.Code, cancellationToken))
            {
                return Result<CouponDto>.Failure($"优惠券代码 {dto.Code} 已存在");
            }

            // Parse CouponType enum
            if (!Enum.TryParse<CouponType>(dto.Type, out var couponType))
            {
                return Result<CouponDto>.Failure($"无效的优惠券类型: {dto.Type}");
            }

            // Create coupon
            var coupon = new Domain.Entities.Payment.Coupon(
                dto.Code,
                dto.Name,
                couponType,
                dto.ValidFrom,
                dto.ValidTo,
                dto.TotalQuota,
                dto.UsagePerUser,
                dto.Description);

            // Set discount details based on type
            if (couponType == CouponType.FixedAmount)
            {
                if (!dto.DiscountAmount.HasValue || string.IsNullOrEmpty(dto.DiscountCurrency))
                {
                    return Result<CouponDto>.Failure("固定金额优惠券必须指定折扣金额和货币");
                }

                var discountMoney = Money.Create(dto.DiscountAmount.Value, dto.DiscountCurrency);
                Money? minimumMoney = null;
                if (dto.MinimumAmount.HasValue && !string.IsNullOrEmpty(dto.MinimumCurrency))
                {
                    minimumMoney = Money.Create(dto.MinimumAmount.Value, dto.MinimumCurrency);
                }
                
                coupon.SetFixedDiscount(discountMoney, minimumMoney);
            }
            else // Percentage
            {
                if (!dto.DiscountPercentage.HasValue)
                {
                    return Result<CouponDto>.Failure("百分比优惠券必须指定折扣百分比");
                }

                Money? minimumMoney = null;
                if (dto.MinimumAmount.HasValue && !string.IsNullOrEmpty(dto.MinimumCurrency))
                {
                    minimumMoney = Money.Create(dto.MinimumAmount.Value, dto.MinimumCurrency);
                }

                Money? maximumMoney = null;
                if (dto.MaximumDiscount.HasValue && !string.IsNullOrEmpty(dto.MaximumDiscountCurrency))
                {
                    maximumMoney = Money.Create(dto.MaximumDiscount.Value, dto.MaximumDiscountCurrency);
                }

                coupon.SetPercentageDiscount(dto.DiscountPercentage.Value, minimumMoney, maximumMoney);
            }

            // Set applicable scope
            if (!string.IsNullOrEmpty(dto.Scope))
            {
                if (Enum.TryParse<ApplicableScope>(dto.Scope, out var scope))
                {
                    coupon.SetApplicableScope(scope, dto.ApplicableProducts);
                }
            }

            await _couponRepository.AddAsync(coupon, cancellationToken);
            await _unitOfWork.CommitAsync(cancellationToken);

            _logger.LogInformation("Created coupon {Code} with ID {Id}", coupon.Code, coupon.Id);
            return Result<CouponDto>.Success(MapToDto(coupon));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create coupon {Code}", dto.Code);
            return Result<CouponDto>.Failure($"创建优惠券失败: {ex.Message}");
        }
    }

    public async Task<Result<CouponDto>> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var coupon = await _couponRepository.GetByIdAsync(id, cancellationToken);
            if (coupon == null)
            {
                return Result<CouponDto>.Failure("未找到优惠券");
            }

            return Result<CouponDto>.Success(MapToDto(coupon));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get coupon by ID {Id}", id);
            return Result<CouponDto>.Failure($"获取优惠券失败: {ex.Message}");
        }
    }

    public async Task<Result<CouponDto>> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        try
        {
            var coupon = await _couponRepository.GetByCodeAsync(code, cancellationToken);
            if (coupon == null)
            {
                return Result<CouponDto>.Failure($"未找到优惠券代码: {code}");
            }

            return Result<CouponDto>.Success(MapToDto(coupon));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get coupon by code {Code}", code);
            return Result<CouponDto>.Failure($"获取优惠券失败: {ex.Message}");
        }
    }

    public async Task<Result<PagedResult<CouponDto>>> GetPagedAsync(PagedRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var coupons = await _couponRepository.GetPagedAsync(
                request.PageNumber,
                request.PageSize,
                orderBy: q => q.OrderByDescending(c => c.CreatedAt),
                cancellationToken: cancellationToken);

            var totalCount = await _couponRepository.CountAsync(c => true, cancellationToken);
            var dtos = coupons.Select(c => MapToDto(c)).ToList();
            
            return Result<PagedResult<CouponDto>>.Success(new PagedResult<CouponDto>
            {
                Items = dtos,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get paged coupons");
            return Result<PagedResult<CouponDto>>.Failure($"获取优惠券列表失败: {ex.Message}");
        }
    }

    public async Task<Result<IEnumerable<CouponDto>>> GetActiveAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var coupons = await _couponRepository.GetActiveAsync(cancellationToken);
            var dtos = coupons.Select(c => MapToDto(c)).ToList();
            return Result<IEnumerable<CouponDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active coupons");
            return Result<IEnumerable<CouponDto>>.Failure($"获取活动优惠券失败: {ex.Message}");
        }
    }

    public async Task<Result<IEnumerable<CouponDto>>> GetUserCouponsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var coupons = await _couponRepository.GetByUserAsync(userId, cancellationToken);
            var dtos = coupons.Select(c => MapToDto(c, userId));
            return Result<IEnumerable<CouponDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user coupons for user {UserId}", userId);
            return Result<IEnumerable<CouponDto>>.Failure($"获取用户优惠券失败: {ex.Message}");
        }
    }

    public async Task<Result<CouponDto>> UpdateAsync(Guid id, UpdateCouponDto dto, CancellationToken cancellationToken = default)
    {
        try
        {
            var coupon = await _couponRepository.GetByIdAsync(id, cancellationToken);
            if (coupon == null)
            {
                return Result<CouponDto>.Failure("未找到优惠券");
            }

            // Update basic properties through reflection since domain entity doesn't expose setters
            // In a real scenario, we'd add update methods to the domain entity
            if (!string.IsNullOrEmpty(dto.Name))
            {
                typeof(Domain.Entities.Payment.Coupon).GetProperty(nameof(Domain.Entities.Payment.Coupon.Name))
                    ?.SetValue(coupon, dto.Name);
            }

            if (dto.Description != null)
            {
                typeof(Domain.Entities.Payment.Coupon).GetProperty(nameof(Domain.Entities.Payment.Coupon.Description))
                    ?.SetValue(coupon, dto.Description);
            }

            // Update scope if provided
            if (!string.IsNullOrEmpty(dto.Scope) && Enum.TryParse<ApplicableScope>(dto.Scope, out var scope))
            {
                coupon.SetApplicableScope(scope, dto.ApplicableProducts);
            }

            _couponRepository.Update(coupon);
            await _unitOfWork.CommitAsync(cancellationToken);

            _logger.LogInformation("Updated coupon {Id}", id);
            return Result<CouponDto>.Success(MapToDto(coupon));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update coupon {Id}", id);
            return Result<CouponDto>.Failure($"更新优惠券失败: {ex.Message}");
        }
    }

    public async Task<Result<bool>> ActivateAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var coupon = await _couponRepository.GetByIdAsync(id, cancellationToken);
            if (coupon == null)
            {
                return Result<bool>.Failure("未找到优惠券");
            }

            coupon.Activate();
            _couponRepository.Update(coupon);
            await _unitOfWork.CommitAsync(cancellationToken);

            _logger.LogInformation("Activated coupon {Id}", id);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to activate coupon {Id}", id);
            return Result<bool>.Failure($"激活优惠券失败: {ex.Message}");
        }
    }

    public async Task<Result<bool>> DeactivateAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var coupon = await _couponRepository.GetByIdAsync(id, cancellationToken);
            if (coupon == null)
            {
                return Result<bool>.Failure("未找到优惠券");
            }

            coupon.Deactivate();
            _couponRepository.Update(coupon);
            await _unitOfWork.CommitAsync(cancellationToken);

            _logger.LogInformation("Deactivated coupon {Id}", id);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to deactivate coupon {Id}", id);
            return Result<bool>.Failure($"停用优惠券失败: {ex.Message}");
        }
    }

    public async Task<Result<bool>> ExtendValidityAsync(Guid id, DateTime newValidTo, CancellationToken cancellationToken = default)
    {
        try
        {
            var coupon = await _couponRepository.GetByIdAsync(id, cancellationToken);
            if (coupon == null)
            {
                return Result<bool>.Failure("未找到优惠券");
            }

            coupon.ExtendValidity(newValidTo);
            _couponRepository.Update(coupon);
            await _unitOfWork.CommitAsync(cancellationToken);

            _logger.LogInformation("Extended coupon {Id} validity to {NewValidTo}", id, newValidTo);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extend coupon {Id} validity", id);
            return Result<bool>.Failure($"延长优惠券有效期失败: {ex.Message}");
        }
    }

    public async Task<Result<DiscountCalculationDto>> CalculateDiscountAsync(
        string code, 
        decimal orderAmount, 
        string currency, 
        string? productId = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var coupon = await _couponRepository.GetByCodeAsync(code, cancellationToken);
            if (coupon == null)
            {
                return Result<DiscountCalculationDto>.Failure("未找到优惠券");
            }

            var result = new DiscountCalculationDto
            {
                IsValid = coupon.IsValid(),
                Currency = currency
            };

            if (!result.IsValid)
            {
                result.Message = "优惠券无效或已过期";
                return Result<DiscountCalculationDto>.Success(result);
            }

            try
            {
                var orderMoney = Money.Create(orderAmount, currency);
                var discount = coupon.CalculateDiscount(orderMoney, productId);
                
                result.CanUse = true;
                result.DiscountAmount = discount.Amount;
                result.FinalAmount = orderAmount - discount.Amount;
                result.Message = "优惠券可用";
            }
            catch (InvalidOperationException ex)
            {
                result.CanUse = false;
                result.Message = ex.Message;
            }

            return Result<DiscountCalculationDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to calculate discount for coupon {Code}", code);
            return Result<DiscountCalculationDto>.Failure($"计算折扣失败: {ex.Message}");
        }
    }

    public async Task<Result<bool>> ApplyCouponAsync(
        string code, 
        Guid userId, 
        Guid orderId, 
        decimal discountAmount, 
        string currency, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var coupon = await _couponRepository.GetByCodeAsync(code, cancellationToken);
            if (coupon == null)
            {
                return Result<bool>.Failure("未找到优惠券");
            }

            var discountMoney = Money.Create(discountAmount, currency);
            coupon.Use(userId, orderId, discountMoney);
            
            _couponRepository.Update(coupon);
            await _unitOfWork.CommitAsync(cancellationToken);

            _logger.LogInformation("Applied coupon {Code} for user {UserId} on order {OrderId}", code, userId, orderId);
            return Result<bool>.Success(true);
        }
        catch (InvalidOperationException ex)
        {
            return Result<bool>.Failure(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply coupon {Code}", code);
            return Result<bool>.Failure($"应用优惠券失败: {ex.Message}");
        }
    }

    public async Task<Result<bool>> ValidateCouponAsync(string code, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var coupon = await _couponRepository.GetByCodeAsync(code, cancellationToken);
            if (coupon == null)
            {
                return Result<bool>.Failure("未找到优惠券");
            }

            bool canUse = coupon.CanUseBy(userId);
            return canUse 
                ? Result<bool>.Success(true) 
                : Result<bool>.Failure("该优惠券已达到使用次数限制");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate coupon {Code} for user {UserId}", code, userId);
            return Result<bool>.Failure($"验证优惠券失败: {ex.Message}");
        }
    }

    private CouponDto MapToDto(Domain.Entities.Payment.Coupon coupon, Guid? userId = null)
    {
        var dto = new CouponDto
        {
            Id = coupon.Id,
            Code = coupon.Code,
            Name = coupon.Name,
            Description = coupon.Description,
            Type = coupon.Type.ToString(),
            DiscountAmount = coupon.DiscountAmount?.Amount,
            DiscountCurrency = coupon.DiscountAmount?.Currency,
            DiscountPercentage = coupon.DiscountPercentage,
            MinimumAmount = coupon.MinimumAmount?.Amount,
            MinimumCurrency = coupon.MinimumAmount?.Currency,
            MaximumDiscount = coupon.MaximumDiscount?.Amount,
            MaximumDiscountCurrency = coupon.MaximumDiscount?.Currency,
            ValidFrom = coupon.ValidFrom,
            ValidTo = coupon.ValidTo,
            TotalQuota = coupon.TotalQuota,
            UsedCount = coupon.UsedCount,
            UsagePerUser = coupon.UsagePerUser,
            IsActive = coupon.IsActive,
            Scope = coupon.Scope.ToString(),
            ApplicableProducts = coupon.ApplicableProducts,
            Rules = coupon.Rules,
            Metadata = coupon.Metadata,
            CreatedAt = coupon.CreatedAt,
            UpdatedAt = coupon.UpdatedAt,
            IsValid = coupon.IsValid(),
            CanUse = userId.HasValue ? coupon.CanUseBy(userId.Value) : coupon.IsValid()
        };

        return dto;
    }
}