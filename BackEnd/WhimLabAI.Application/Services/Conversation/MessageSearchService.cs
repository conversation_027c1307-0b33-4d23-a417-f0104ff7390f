using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;
using System.Text.RegularExpressions;

namespace WhimLabAI.Application.Services.Conversation;

public class MessageSearchService : IMessageSearchService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly ILogger<MessageSearchService> _logger;

    public MessageSearchService(
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        ILogger<MessageSearchService> logger)
    {
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<Result<PagedResult<MessageSearchResultDto>>> SearchMessagesAsync(MessageSearchDto query, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get user's conversations
            var conversationRepository = _unitOfWork.Repository<Domain.Entities.Conversation.Conversation>();
            var conversations = await conversationRepository.GetAsync(c => c.CustomerUserId == userId, cancellationToken);
            var conversationList = conversations.ToList();

            // Filter by conversation or agent if specified
            if (query.ConversationId.HasValue)
            {
                conversationList = conversationList.Where(c => c.Id == query.ConversationId.Value).ToList();
            }

            if (query.AgentId.HasValue)
            {
                conversationList = conversationList.Where(c => c.AgentId == query.AgentId.Value).ToList();
            }

            // Get all messages from filtered conversations
            var messageRepository = _unitOfWork.Repository<ConversationMessage>();
            var conversationIds = conversationList.Select(c => c.Id).ToList();
            var allMessages = new List<ConversationMessage>();
            
            if (conversationIds.Any())
            {
                var messages = await messageRepository.GetAsync(
                    m => conversationIds.Contains(m.ConversationId), cancellationToken);
                allMessages = messages.ToList();
            }

            // Apply filters
            var filteredMessages = allMessages.AsQueryable();

            // Filter by keyword
            if (!string.IsNullOrWhiteSpace(query.Keyword))
            {
                filteredMessages = filteredMessages.Where(m => 
                    m.Content.Contains(query.Keyword, StringComparison.OrdinalIgnoreCase));
            }

            // Filter by role
            if (!string.IsNullOrWhiteSpace(query.Role))
            {
                filteredMessages = filteredMessages.Where(m => 
                    m.Role.Equals(query.Role, StringComparison.OrdinalIgnoreCase));
            }

            // Filter by date range
            if (query.DateRange != null)
            {
                filteredMessages = filteredMessages.Where(m => 
                    m.CreatedAt >= query.DateRange.StartDate && 
                    m.CreatedAt <= query.DateRange.EndDate);
            }

            // Exclude system messages unless specified
            if (!query.IncludeSystemMessages)
            {
                filteredMessages = filteredMessages.Where(m => 
                    m.Role != MessageRole.System.ToString().ToLower());
            }

            // Order by creation date descending
            var orderedMessages = filteredMessages.OrderByDescending(m => m.CreatedAt).ToList();

            // Get total count
            var totalCount = orderedMessages.Count;

            // Apply paging
            var pagedMessages = orderedMessages
                .Skip((query.PageNumber - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToList();

            // Map to DTOs
            var results = new List<MessageSearchResultDto>();
            foreach (var message in pagedMessages)
            {
                var conversation = conversationList.FirstOrDefault(c => c.Id == message.ConversationId);
                if (conversation != null)
                {
                    results.Add(new MessageSearchResultDto
                    {
                        MessageId = message.Id,
                        ConversationId = message.ConversationId,
                        ConversationTitle = conversation.Title,
                        Role = message.Role,
                        Content = message.Content,
                        HighlightedContent = HighlightKeyword(message.Content, query.Keyword),
                        CreatedAt = message.CreatedAt,
                        TokenCount = message.TokenCount
                    });
                }
            }

            var pagedResult = new PagedResult<MessageSearchResultDto>(
                results,
                totalCount,
                query.PageNumber,
                query.PageSize);

            return Result<PagedResult<MessageSearchResultDto>>.Success(pagedResult);
        }
        catch (Exception ex)
        {
            _logger.LogError("Error searching messages", ex);
            return Result<PagedResult<MessageSearchResultDto>>.Failure("SEARCH_ERROR", "搜索消息失败");
        }
    }

    public async Task<Result<List<MessageContextDto>>> GetMessageContextAsync(Guid messageId, int contextSize, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Find the message and verify user owns the conversation
            ConversationMessage? targetMessage = null;
            Domain.Entities.Conversation.Conversation? conversation = null;

            var conversationRepository = _unitOfWork.Repository<Domain.Entities.Conversation.Conversation>();
            var conversations = await conversationRepository.GetAsync(c => c.CustomerUserId == userId, cancellationToken);
            var conversationList = conversations.ToList();

            foreach (var conv in conversationList)
            {
                var messageRepository = _unitOfWork.Repository<ConversationMessage>();
                var messages = await messageRepository.GetAsync(m => m.ConversationId == conv.Id, cancellationToken);
                var messageList = messages.ToList();
                
                targetMessage = messageList.FirstOrDefault(m => m.Id == messageId);
                if (targetMessage != null)
                {
                    conversation = conv;
                    break;
                }
            }

            if (targetMessage == null || conversation == null)
            {
                return Result<List<MessageContextDto>>.Failure("MESSAGE_NOT_FOUND", "消息不存在");
            }

            // Get all messages in the conversation
            var messageRepo = _unitOfWork.Repository<ConversationMessage>();
            var allMessagesResult = await messageRepo.GetAsync(m => m.ConversationId == conversation.Id, cancellationToken);
            var allMessages = allMessagesResult
                .OrderBy(m => m.CreatedAt)
                .ToList();

            // Find the target message index
            var targetIndex = allMessages.FindIndex(m => m.Id == messageId);
            if (targetIndex == -1)
            {
                return Result<List<MessageContextDto>>.Failure("MESSAGE_NOT_FOUND", "消息不存在");
            }

            // Get context messages
            var startIndex = Math.Max(0, targetIndex - contextSize);
            var endIndex = Math.Min(allMessages.Count - 1, targetIndex + contextSize);

            var contextMessages = allMessages
                .Skip(startIndex)
                .Take(endIndex - startIndex + 1)
                .Where(m => m.Role != MessageRole.System.ToString().ToLower())
                .Select(m => new MessageContextDto
                {
                    Role = m.Role,
                    Content = m.Content
                })
                .ToList();

            return Result<List<MessageContextDto>>.Success(contextMessages);
        }
        catch (Exception ex)
        {
            _logger.LogError("Error getting message context: {MessageId}", messageId, ex);
            return Result<List<MessageContextDto>>.Failure("GET_CONTEXT_ERROR", "获取消息上下文失败");
        }
    }

    public async Task<Result<Dictionary<string, int>>> GetKeywordFrequencyAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify user owns the conversation
            var conversationRepository = _unitOfWork.Repository<Domain.Entities.Conversation.Conversation>();
            var conversation = await conversationRepository.FirstOrDefaultAsync(
                c => c.Id == conversationId && c.CustomerUserId == userId, cancellationToken);
            
            if (conversation == null)
            {
                return Result<Dictionary<string, int>>.Failure("UNAUTHORIZED", "无权访问此对话");
            }

            // Get all messages in the conversation
            var messageRepository = _unitOfWork.Repository<ConversationMessage>();
            var messagesResult = await messageRepository.GetAsync(
                m => m.ConversationId == conversationId, cancellationToken);
            var messages = messagesResult
                .Where(m => m.Role != MessageRole.System.ToString().ToLower())
                .ToList();

            // Extract keywords and count frequency
            var keywordFrequency = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            var stopWords = GetStopWords();

            foreach (var message in messages)
            {
                var words = ExtractWords(message.Content)
                    .Where(w => w.Length > 2 && !stopWords.Contains(w.ToLower()));

                foreach (var word in words)
                {
                    if (keywordFrequency.ContainsKey(word))
                    {
                        keywordFrequency[word]++;
                    }
                    else
                    {
                        keywordFrequency[word] = 1;
                    }
                }
            }

            // Sort by frequency and take top 50
            var topKeywords = keywordFrequency
                .OrderByDescending(kvp => kvp.Value)
                .Take(50)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            return Result<Dictionary<string, int>>.Success(topKeywords);
        }
        catch (Exception ex)
        {
            _logger.LogError("Error getting keyword frequency: {ConversationId}", conversationId, ex);
            return Result<Dictionary<string, int>>.Failure("FREQUENCY_ERROR", "获取关键词频率失败");
        }
    }

    public async Task<Result<ConversationSummaryDto>> GenerateConversationSummaryAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify user owns the conversation
            var conversationRepository = _unitOfWork.Repository<Domain.Entities.Conversation.Conversation>();
            var conversation = await conversationRepository.FirstOrDefaultAsync(
                c => c.Id == conversationId && c.CustomerUserId == userId, cancellationToken);
            
            if (conversation == null)
            {
                return Result<ConversationSummaryDto>.Failure("UNAUTHORIZED", "无权访问此对话或对话不存在");
            }

            // Check cache
            var cacheKey = $"conversation-summary:{conversationId}";
            var cachedSummary = await _cacheService.GetAsync<ConversationSummaryDto>(cacheKey, cancellationToken);
            if (cachedSummary != null)
            {
                return Result<ConversationSummaryDto>.Success(cachedSummary);
            }

            // Get all messages
            var messageRepository = _unitOfWork.Repository<ConversationMessage>();
            var messagesResult = await messageRepository.GetAsync(
                m => m.ConversationId == conversationId, cancellationToken);
            var messages = messagesResult
                .Where(m => m.Role != MessageRole.System.ToString().ToLower())
                .OrderBy(m => m.CreatedAt)
                .ToList();

            // Generate summary (simplified version - in production, use AI)
            var keyPointsResult = await GetKeywordFrequencyAsync(conversationId, userId, cancellationToken);
            var keywords = keyPointsResult.IsSuccess ? keyPointsResult.Value : new Dictionary<string, int>();

            var summary = new ConversationSummaryDto
            {
                ConversationId = conversationId,
                Title = conversation.Title,
                Summary = GenerateSimpleSummary(messages),
                KeyPoints = ExtractKeyPoints(messages),
                Keywords = keywords.Take(10).ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                GeneratedAt = DateTime.UtcNow
            };

            // Cache for 30 minutes
            await _cacheService.SetAsync(cacheKey, summary, TimeSpan.FromMinutes(30), cancellationToken);

            return Result<ConversationSummaryDto>.Success(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError("Error generating conversation summary: {ConversationId}", conversationId, ex);
            return Result<ConversationSummaryDto>.Failure("SUMMARY_ERROR", "生成对话摘要失败");
        }
    }

    // Helper methods
    private string HighlightKeyword(string content, string? keyword)
    {
        if (string.IsNullOrWhiteSpace(keyword))
        {
            return content;
        }

        var pattern = $@"\b({Regex.Escape(keyword)})\b";
        return Regex.Replace(content, pattern, "<mark>$1</mark>", RegexOptions.IgnoreCase);
    }

    private List<string> ExtractWords(string text)
    {
        // Simple word extraction - in production, use a proper tokenizer
        return Regex.Matches(text, @"\b\w+\b")
            .Cast<Match>()
            .Select(m => m.Value)
            .ToList();
    }

    private HashSet<string> GetStopWords()
    {
        // Common Chinese and English stop words
        return new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去",
            "the", "is", "at", "which", "on", "a", "an", "as", "are", "been", "be", "have", "has", "had", "were", "was", "this", "that",
            "i", "you", "he", "she", "it", "we", "they", "them", "their", "what", "so", "up", "out", "if", "about", "who", "get", "which",
            "me", "when", "make", "can", "like", "no", "just", "him", "know", "take", "into", "your", "some", "could", "them", "see",
            "other", "than", "then", "now", "look", "only", "come", "its", "over", "think", "also", "back", "after", "use", "two"
        };
    }

    private string GenerateSimpleSummary(List<ConversationMessage> messages)
    {
        if (messages.Count == 0)
        {
            return "空对话";
        }

        var userMessageCount = messages.Count(m => m.Role == MessageRole.User.ToString().ToLower());
        var assistantMessageCount = messages.Count(m => m.Role == MessageRole.Assistant.ToString().ToLower());
        var totalTokens = messages.Sum(m => m.TokenCount);

        var firstUserMessage = messages.FirstOrDefault(m => m.Role == MessageRole.User.ToString().ToLower());
        var lastAssistantMessage = messages.LastOrDefault(m => m.Role == MessageRole.Assistant.ToString().ToLower());

        var summary = $"对话包含 {userMessageCount} 条用户消息和 {assistantMessageCount} 条助手回复，共使用 {totalTokens} 个令牌。";
        
        if (firstUserMessage != null)
        {
            var topic = firstUserMessage.Content.Length > 50 
                ? firstUserMessage.Content.Substring(0, 50) + "..." 
                : firstUserMessage.Content;
            summary += $" 对话主题：{topic}";
        }

        return summary;
    }

    private List<string> ExtractKeyPoints(List<ConversationMessage> messages)
    {
        var keyPoints = new List<string>();

        // Extract first few user questions as key points
        var userQuestions = messages
            .Where(m => m.Role == MessageRole.User.ToString().ToLower())
            .Take(5)
            .Select(m => m.Content.Length > 100 ? m.Content.Substring(0, 100) + "..." : m.Content)
            .ToList();

        keyPoints.AddRange(userQuestions);

        return keyPoints;
    }
}