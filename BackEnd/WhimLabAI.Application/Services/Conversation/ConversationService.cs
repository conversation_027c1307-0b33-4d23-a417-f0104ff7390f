using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Conversation;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;
using BC = BCrypt.Net.BCrypt;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace WhimLabAI.Application.Services.Conversation;

public class ConversationService : IConversationService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IAgentService _agentService;
    private readonly IAIService _aiService;
    private readonly ICacheService _cacheService;
    private readonly IStorageService _storageService;
    private readonly ILogger<ConversationService> _logger;

    public ConversationService(
        IUnitOfWork unitOfWork,
        IAgentService agentService,
        IAIService aiService,
        ICacheService cacheService,
        IStorageService storageService,
        ILogger<ConversationService> logger)
    {
        _unitOfWork = unitOfWork;
        _agentService = agentService;
        _aiService = aiService;
        _cacheService = cacheService;
        _storageService = storageService;
        _logger = logger;
    }

    public async Task<Result<Guid>> StartConversationAsync(StartConversationDto request, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get agent details
            var agentResult = await _agentService.GetAgentDetailAsync(request.AgentId, cancellationToken);
            if (!agentResult.IsSuccess)
            {
                return Result<Guid>.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            var agent = agentResult.Value;
            
            // Create conversation
            var model = agent.Config.ModelConfig.GetValueOrDefault("model", "gpt-3.5-turbo")?.ToString() ?? "gpt-3.5-turbo";
            
            // Create conversation using domain constructor
            var conversation = new Domain.Entities.Conversation.Conversation(
                customerUserId: userId,
                agentId: request.AgentId,
                title: request.Title ?? $"与{agent.Name}的对话",
                model: model,
                metadata: request.Metadata
            );

            // Add system message using domain method
            var systemPrompt = agent.Config.SystemPrompt ?? "You are a helpful AI assistant.";
            var systemTokenCount = EstimateTokenCount(systemPrompt);
            conversation.AddMessage(
                role: MessageRole.System.ToString().ToLower(),
                content: systemPrompt,
                tokenCount: systemTokenCount
            );

            // Add welcome message if agent has one
            if (!string.IsNullOrWhiteSpace(agent.DetailedIntro))
            {
                var welcomeTokenCount = EstimateTokenCount(agent.DetailedIntro);
                conversation.AddMessage(
                    role: MessageRole.Assistant.ToString().ToLower(),
                    content: agent.DetailedIntro,
                    tokenCount: welcomeTokenCount
                );
            }

            await _unitOfWork.Conversations.AddAsync(conversation, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Agent usage is tracked via domain events (ConversationStartedEvent)

            _logger.LogInformation("Conversation started: {ConversationId} for user {UserId}", conversation.Id, userId);
            return Result<Guid>.Success(conversation.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting conversation");
            return Result<Guid>.Failure("START_CONVERSATION_ERROR", "启动对话失败");
        }
    }

    public async Task<Result<ConversationMessageDto>> SendMessageAsync(WhimLabAI.Shared.Dtos.SendMessageDto request, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Create ProcessMessageDto for AI service
            var processRequest = new WhimLabAI.Abstractions.Application.ProcessMessageDto
            {
                ConversationId = request.ConversationId,
                Message = request.Message,
                Attachments = request.Attachments?.Select(a => new MessageAttachmentDto
                {
                    Type = a.Type ?? "other",
                    Url = a.Url ?? string.Empty,
                    FileName = a.FileName,
                    FileSize = a.FileSize,
                    MimeType = a.MimeType
                }).ToList(),
                Parameters = null, // SendMessageDto doesn't have metadata
                EnableStreaming = false
            };

            // Use AI service to process the message
            var aiResult = await _aiService.ProcessMessageAsync(processRequest, userId, cancellationToken);
            
            if (!aiResult.IsSuccess)
            {
                return Result<ConversationMessageDto>.Failure(aiResult.Error ?? "AI processing failed", aiResult.ErrorCode);
            }

            // Get the conversation to retrieve the last message
            var conversation = await GetConversationWithMessagesAsync(request.ConversationId, cancellationToken);
            if (conversation == null)
            {
                return Result<ConversationMessageDto>.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            // Get the assistant's last message
            var assistantMessage = conversation.Messages
                .Where(m => m.Role == MessageRole.Assistant.ToString().ToLower())
                .OrderByDescending(m => m.CreatedAt)
                .FirstOrDefault();

            if (assistantMessage == null)
            {
                return Result<ConversationMessageDto>.Failure("MESSAGE_NOT_FOUND", "未找到助手回复");
            }

            // Map to DTO
            var messageDto = MapToMessageDto(assistantMessage);

            _logger.LogInformation("Message sent in conversation: {ConversationId}", conversation.Id);
            return Result<ConversationMessageDto>.Success(messageDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending message");
            return Result<ConversationMessageDto>.Failure("SEND_MESSAGE_ERROR", "发送消息失败");
        }
    }

    public async Task<IAsyncEnumerable<StreamMessageChunk>> SendMessageStreamAsync(WhimLabAI.Shared.Dtos.SendMessageDto request, Guid userId, CancellationToken cancellationToken = default)
    {
        // Create ProcessMessageDto for AI service
        var processRequest = new WhimLabAI.Abstractions.Application.ProcessMessageDto
        {
            ConversationId = request.ConversationId,
            Message = request.Message,
            Attachments = request.Attachments?.Select(a => new MessageAttachmentDto
            {
                Type = a.Type ?? "other",
                Url = a.Url ?? string.Empty,
                FileName = a.FileName,
                FileSize = a.FileSize,
                MimeType = a.MimeType
            }).ToList(),
            Parameters = null, // SendMessageDto doesn't have metadata
            EnableStreaming = true
        };

        // Use AI service for streaming response and unwrap the results
        return await Task.FromResult(UnwrapStreamResults(_aiService.ProcessMessageStreamAsync(processRequest, userId, cancellationToken)));
    }

    private async IAsyncEnumerable<StreamMessageChunk> UnwrapStreamResults(IAsyncEnumerable<Result<StreamMessageChunk>> results)
    {
        await foreach (var result in results)
        {
            if (result.IsSuccess)
            {
                yield return result.Value!;
            }
            else
            {
                // Convert error to stream chunk
                yield return new StreamMessageChunk
                {
                    Error = result.Error ?? "Unknown error",
                    IsComplete = true
                };
                yield break; // Stop streaming on error
            }
        }
    }

    public async Task<Result<PagedResult<ConversationListDto>>> GetConversationHistoryAsync(Guid userId, ConversationQueryDto query, CancellationToken cancellationToken = default)
    {
        try
        {
            var conversationsObj = await _unitOfWork.Conversations.GetUserConversationsAsync(userId, query.IncludeArchived, cancellationToken);
            var conversations = conversationsObj.Cast<Domain.Entities.Conversation.Conversation>().ToList();

            // Apply filtering
            if (!string.IsNullOrWhiteSpace(query.Keyword))
            {
                conversations = conversations.Where(c => 
                    c.Title.Contains(query.Keyword, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            if (query.AgentId.HasValue)
            {
                conversations = conversations.Where(c => c.AgentId == query.AgentId.Value).ToList();
            }

            if (query.DateRange != null)
            {
                if (query.DateRange.StartDate != null)
                {
                    conversations = conversations.Where(c => c.CreatedAt >= query.DateRange.StartDate).ToList();
                }
                if (query.DateRange.EndDate != null)
                {
                    conversations = conversations.Where(c => c.CreatedAt <= query.DateRange.EndDate).ToList();
                }
            }

            // Get total count
            var totalCount = conversations.Count;

            // Apply paging
            var pagedConversations = conversations
                .Skip((query.PageNumber - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToList();

            // Map to DTOs
            var conversationDtos = await MapToConversationListDtosAsync(pagedConversations, cancellationToken);

            var result = new PagedResult<ConversationListDto>(
                conversationDtos,
                totalCount,
                query.PageNumber,
                query.PageSize);

            return Result<PagedResult<ConversationListDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversation history");
            return Result<PagedResult<ConversationListDto>>.Failure("GET_HISTORY_ERROR", "获取对话历史失败");
        }
    }

    public async Task<Result<ConversationDetailDto>> GetConversationDetailAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result<ConversationDetailDto>.Failure("UNAUTHORIZED", "无权访问此对话");
            }

            var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
            if (conversationObj == null)
            {
                return Result<ConversationDetailDto>.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            var conversation = conversationObj as Domain.Entities.Conversation.Conversation;
            if (conversation == null)
            {
                return Result<ConversationDetailDto>.Failure("CONVERSATION_CAST_ERROR", "对话类型转换错误");
            }

            // Load full details
            var fullConversation = await GetConversationWithFullDetailsAsync(conversationId, cancellationToken);
            if (fullConversation == null)
            {
                return Result<ConversationDetailDto>.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            var detailDto = await MapToConversationDetailDtoAsync(fullConversation, cancellationToken);

            return Result<ConversationDetailDto>.Success(detailDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversation detail: {ConversationId}", conversationId);
            return Result<ConversationDetailDto>.Failure("GET_DETAIL_ERROR", "获取对话详情失败");
        }
    }

    public async Task<Result> DeleteConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result.Failure("UNAUTHORIZED", "无权删除此对话");
            }

            var conversation = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            _unitOfWork.Conversations.Remove(conversation);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"conversation:{conversationId}", cancellationToken);

            _logger.LogInformation("Conversation deleted: {ConversationId}", conversationId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting conversation: {ConversationId}", conversationId);
            return Result.Failure("DELETE_ERROR", "删除对话失败");
        }
    }

    public async Task<Result<string>> ExportConversationAsync(Guid conversationId, Guid userId, string format, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result<string>.Failure("UNAUTHORIZED", "无权导出此对话");
            }

            var conversation = await GetConversationWithFullDetailsAsync(conversationId, cancellationToken);
            if (conversation == null)
            {
                return Result<string>.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            string exportContent = format.ToLower() switch
            {
                "markdown" => ExportToMarkdown(conversation),
                "json" => ExportToJson(conversation),
                "txt" => ExportToText(conversation),
                _ => throw new NotSupportedException($"不支持的导出格式: {format}")
            };

            return Result<string>.Success(exportContent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting conversation: {ConversationId}", conversationId);
            return Result<string>.Failure("EXPORT_ERROR", "导出对话失败");
        }
    }

    public async Task<Result> RateResponseAsync(RateResponseDto request, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get message and verify it belongs to user's conversation
            var message = await GetMessageAsync(request.MessageId, cancellationToken);
            if (message == null)
            {
                return Result.Failure("MESSAGE_NOT_FOUND", "消息不存在");
            }

            // Verify user owns the conversation
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, message.ConversationId, cancellationToken))
            {
                return Result.Failure("UNAUTHORIZED", "无权评价此消息");
            }

            // Add or update rating using domain method
            message.AddRating(request.Score, request.Feedback);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Message rated: {MessageId} with score {Score}", request.MessageId, request.Score);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rating response: {MessageId}", request.MessageId);
            return Result.Failure("RATE_ERROR", "评价失败");
        }
    }

    // Helper methods
    private async Task<Domain.Entities.Conversation.Conversation?> GetConversationWithMessagesAsync(Guid conversationId, CancellationToken cancellationToken)
    {
        var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
        return conversationObj as Domain.Entities.Conversation.Conversation;
    }

    private async Task<Domain.Entities.Conversation.Conversation?> GetConversationWithFullDetailsAsync(Guid conversationId, CancellationToken cancellationToken)
    {
        // Get conversation with all messages, attachments and ratings included
        var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
        if (conversationObj == null)
        {
            return null;
        }
        
        var conversation = conversationObj as Domain.Entities.Conversation.Conversation;
        if (conversation == null)
        {
            return null;
        }
        
        // Get all messages for the conversation with their attachments and ratings
        var messages = await _unitOfWork.Conversations.GetConversationMessagesAsync(conversationId, 1, int.MaxValue, cancellationToken);
        
        // Note: The messages should already be loaded via Include in the repository
        // This is a fallback to ensure we have all the data
        return conversation;
    }

    private async Task<ConversationMessage?> GetMessageAsync(Guid messageId, CancellationToken cancellationToken)
    {
        // Use generic repository to get message by ID
        var messageRepo = _unitOfWork.Repository<ConversationMessage>();
        return await messageRepo.GetByIdAsync(messageId, cancellationToken);
    }

    // Note: These methods are now handled by IAIService
    // private async Task<(string Content, int TokenCount)> GenerateAIResponseAsync(Domain.Entities.Conversation.Conversation conversation, ConversationMessage userMessage, CancellationToken cancellationToken)
    // {
    //     // Now handled by IAIService
    // }

    // private async IAsyncEnumerable<StreamMessageChunk> GenerateStreamingResponseAsync(
    //     Domain.Entities.Conversation.Conversation conversation, 
    //     ConversationMessage userMessage, 
    //     CancellationToken cancellationToken)
    // {
    //     // Now handled by IAIService
    // }

    // private async IAsyncEnumerable<StreamMessageChunk> CreateErrorStream(string error)
    // {
    //     // Now handled by IAIService
    // }

    private int EstimateTokenCount(string text)
    {
        // Simple estimation: ~4 characters per token
        return (int)Math.Ceiling(text.Length / 4.0);
    }

    private ConversationMessageDto MapToMessageDto(ConversationMessage message)
    {
        return new ConversationMessageDto
        {
            Id = message.Id,
            Role = message.Role,
            Content = message.Content,
            Attachments = message.Attachments.Select(a => new MessageAttachmentDetailDto
            {
                Id = a.Id,
                FileName = a.FileName,
                Url = a.FileUrl,
                MimeType = a.ContentType,
                Type = DetermineAttachmentType(a.ContentType),
                FileSize = a.FileSize,
                ThumbnailUrl = a.ThumbnailUrl,
                Metadata = a.Metadata
            }).ToList(),
            TokenCount = message.TokenCount,
            CreatedAt = message.CreatedAt,
            IsDeleted = false,
            Rating = message.Rating != null ? new MessageRatingDisplayDto
            {
                Score = message.Rating.Score,
                Feedback = message.Rating.Feedback,
                RatedAt = message.Rating.RatedAt
            } : null
        };
    }

    private async Task<List<ConversationListDto>> MapToConversationListDtosAsync(List<Domain.Entities.Conversation.Conversation> conversations, CancellationToken cancellationToken)
    {
        var dtos = new List<ConversationListDto>();

        foreach (var conversation in conversations)
        {
            // Get agent info
            var agentResult = await _agentService.GetAgentDetailAsync(conversation.AgentId, cancellationToken);
            var agent = agentResult.IsSuccess ? agentResult.Value : null;

            var lastMessage = conversation.Messages
                .Where(m => m.Role != MessageRole.System.ToString().ToLower())
                .OrderByDescending(m => m.CreatedAt)
                .FirstOrDefault();

            dtos.Add(new ConversationListDto
            {
                Id = conversation.Id,
                Title = conversation.Title,
                Agent = new AgentInfoDto
                {
                    Id = conversation.AgentId,
                    Name = agent?.Name ?? "Unknown Agent",
                    Icon = agent?.Icon
                },
                LastMessage = lastMessage?.Content,
                StartedAt = conversation.CreatedAt,
                LastMessageAt = conversation.LastMessageAt,
                MessageCount = conversation.MessageCount,
                IsArchived = conversation.IsArchived,
                IsPinned = conversation.Metadata.ContainsKey("IsPinned") && (bool)conversation.Metadata["IsPinned"]
            });
        }

        return dtos;
    }

    private async Task<ConversationDetailDto> MapToConversationDetailDtoAsync(Domain.Entities.Conversation.Conversation conversation, CancellationToken cancellationToken)
    {
        // Get agent info
        var agentResult = await _agentService.GetAgentDetailAsync(conversation.AgentId, cancellationToken);
        var agent = agentResult.IsSuccess ? agentResult.Value : null;

        var userMessageCount = conversation.Messages.Count(m => m.Role == MessageRole.User.ToString().ToLower());
        var assistantMessageCount = conversation.Messages.Count(m => m.Role == MessageRole.Assistant.ToString().ToLower());

        // Calculate average response time based on message timestamps
        var avgResponseTime = CalculateAverageResponseTime(conversation.Messages.OrderBy(m => m.CreatedAt).ToList());

        return new ConversationDetailDto
        {
            Id = conversation.Id,
            Title = conversation.Title,
            Agent = new AgentInfoDto
            {
                Id = conversation.AgentId,
                Name = agent?.Name ?? "Unknown Agent",
                Icon = agent?.Icon
            },
            LastMessage = conversation.Messages
                .Where(m => m.Role != MessageRole.System.ToString().ToLower())
                .OrderByDescending(m => m.CreatedAt)
                .FirstOrDefault()?.Content,
            StartedAt = conversation.CreatedAt,
            LastMessageAt = conversation.LastMessageAt,
            MessageCount = conversation.MessageCount,
            IsArchived = conversation.IsArchived,
            IsPinned = conversation.Metadata.ContainsKey("IsPinned") && (bool)conversation.Metadata["IsPinned"],
            Messages = conversation.Messages
                .Where(m => m.Role != MessageRole.System.ToString().ToLower())
                .OrderBy(m => m.CreatedAt)
                .Select(MapToMessageDto)
                .ToList(),
            Stats = new ConversationStatsDto
            {
                TotalMessages = conversation.MessageCount,
                TotalTokens = conversation.TotalTokens,
                AverageResponseTime = avgResponseTime,
                UserMessages = userMessageCount,
                AssistantMessages = assistantMessageCount
            }
        };
    }

    private string ExportToMarkdown(Domain.Entities.Conversation.Conversation conversation)
    {
        var markdown = $"# {conversation.Title}\n\n";
        markdown += $"**开始时间**: {conversation.CreatedAt:yyyy-MM-dd HH:mm:ss}\n\n";
        markdown += "---\n\n";

        foreach (var message in conversation.Messages.Where(m => m.Role != MessageRole.System.ToString().ToLower()).OrderBy(m => m.CreatedAt))
        {
            var role = message.Role == MessageRole.User.ToString().ToLower() ? "用户" : "助手";
            markdown += $"### {role} ({message.CreatedAt:HH:mm:ss})\n\n";
            markdown += $"{message.Content}\n\n";
        }

        return markdown;
    }

    private string ExportToJson(Domain.Entities.Conversation.Conversation conversation)
    {
        var export = new
        {
            conversation.Id,
            conversation.Title,
            conversation.CreatedAt,
            Messages = conversation.Messages
                .Where(m => m.Role != MessageRole.System.ToString().ToLower())
                .OrderBy(m => m.CreatedAt)
                .Select(m => new
                {
                    m.Id,
                    m.Role,
                    m.Content,
                    m.TokenCount,
                    m.CreatedAt
                })
        };

        return System.Text.Json.JsonSerializer.Serialize(export, new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = true
        });
    }

    private AttachmentType DetermineAttachmentType(string mimeType)
    {
        if (string.IsNullOrEmpty(mimeType))
            return AttachmentType.Other;
            
        var lowerMimeType = mimeType.ToLower();
        
        if (lowerMimeType.StartsWith("image/"))
            return AttachmentType.Image;
        else if (lowerMimeType.StartsWith("audio/"))
            return AttachmentType.Audio;
        else if (lowerMimeType.StartsWith("video/"))
            return AttachmentType.Video;
        else if (lowerMimeType.Contains("pdf") || lowerMimeType.Contains("document") || 
                 lowerMimeType.Contains("word") || lowerMimeType.Contains("excel") || 
                 lowerMimeType.Contains("powerpoint") || lowerMimeType.Contains("text"))
            return AttachmentType.Document;
        else if (lowerMimeType.Contains("code") || lowerMimeType.Contains("javascript") || 
                 lowerMimeType.Contains("python") || lowerMimeType.Contains("java") || 
                 lowerMimeType.Contains("csharp") || lowerMimeType.Contains("xml") || 
                 lowerMimeType.Contains("json"))
            return AttachmentType.Code;
        else
            return AttachmentType.Other;
    }

    private string ExportToText(Domain.Entities.Conversation.Conversation conversation)
    {
        var text = $"{conversation.Title}\n";
        text += $"开始时间: {conversation.CreatedAt:yyyy-MM-dd HH:mm:ss}\n";
        text += new string('-', 50) + "\n\n";

        foreach (var message in conversation.Messages.Where(m => m.Role != MessageRole.System.ToString().ToLower()).OrderBy(m => m.CreatedAt))
        {
            var role = message.Role == MessageRole.User.ToString().ToLower() ? "用户" : "助手";
            text += $"{role} ({message.CreatedAt:HH:mm:ss}):\n";
            text += $"{message.Content}\n\n";
        }

        return text;
    }

    private double CalculateAverageResponseTime(List<ConversationMessage> messages)
    {
        if (messages.Count < 2)
            return 0.0;

        var responseTimes = new List<double>();
        ConversationMessage? lastUserMessage = null;

        foreach (var message in messages)
        {
            if (message.Role == MessageRole.User.ToString().ToLower())
            {
                lastUserMessage = message;
            }
            else if (message.Role == MessageRole.Assistant.ToString().ToLower() && lastUserMessage != null)
            {
                var responseTime = (message.CreatedAt - lastUserMessage.CreatedAt).TotalSeconds;
                responseTimes.Add(responseTime);
                lastUserMessage = null;
            }
        }

        return responseTimes.Any() ? responseTimes.Average() : 0.0;
    }

    public async Task<Result> ArchiveConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result.Failure("UNAUTHORIZED", "无权归档此对话");
            }

            var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
            if (conversationObj is not Domain.Entities.Conversation.Conversation conversation)
            {
                return Result.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            // Use domain method to archive
            conversation.Archive();
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"conversation:{conversationId}", cancellationToken);

            _logger.LogInformation("Conversation archived: {ConversationId} by user {UserId}", conversationId, userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error archiving conversation: {ConversationId}", conversationId);
            return Result.Failure("ARCHIVE_ERROR", "归档对话失败");
        }
    }

    public async Task<Result> UnarchiveConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result.Failure("UNAUTHORIZED", "无权取消归档此对话");
            }

            var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
            if (conversationObj is not Domain.Entities.Conversation.Conversation conversation)
            {
                return Result.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            // Use domain method to unarchive
            conversation.Unarchive();
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"conversation:{conversationId}", cancellationToken);

            _logger.LogInformation("Conversation unarchived: {ConversationId} by user {UserId}", conversationId, userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unarchiving conversation: {ConversationId}", conversationId);
            return Result.Failure("UNARCHIVE_ERROR", "取消归档失败");
        }
    }

    public async Task<Result> PinConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result.Failure("UNAUTHORIZED", "无权置顶此对话");
            }

            var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
            if (conversationObj is not Domain.Entities.Conversation.Conversation conversation)
            {
                return Result.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            // Check if already pinned
            if (conversation.Metadata.ContainsKey("IsPinned") && (bool)conversation.Metadata["IsPinned"])
            {
                return Result.Failure("ALREADY_PINNED", "对话已经置顶");
            }

            // Check pinned count limit (max 10 pinned conversations per user)
            var pinnedCount = await _unitOfWork.Conversations.GetPinnedConversationCountAsync(userId, cancellationToken);
            if (pinnedCount >= 10)
            {
                return Result.Failure("PIN_LIMIT_EXCEEDED", "最多只能置顶10个对话");
            }

            // Set pinned metadata
            conversation.SetMetadata("IsPinned", true);
            conversation.SetMetadata("PinnedAt", DateTime.UtcNow);
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"conversation:{conversationId}", cancellationToken);
            await _cacheService.RemoveAsync($"conversations:user:{userId}", cancellationToken);

            _logger.LogInformation("Conversation pinned: {ConversationId} by user {UserId}", conversationId, userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pinning conversation: {ConversationId}", conversationId);
            return Result.Failure("PIN_ERROR", "置顶对话失败");
        }
    }

    public async Task<Result> UnpinConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result.Failure("UNAUTHORIZED", "无权取消置顶此对话");
            }

            var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
            if (conversationObj is not Domain.Entities.Conversation.Conversation conversation)
            {
                return Result.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            // Check if actually pinned
            if (!conversation.Metadata.ContainsKey("IsPinned") || !(bool)conversation.Metadata["IsPinned"])
            {
                return Result.Failure("NOT_PINNED", "对话未置顶");
            }

            // Remove pinned metadata
            conversation.RemoveMetadata("IsPinned");
            conversation.RemoveMetadata("PinnedAt");
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"conversation:{conversationId}", cancellationToken);
            await _cacheService.RemoveAsync($"conversations:user:{userId}", cancellationToken);

            _logger.LogInformation("Conversation unpinned: {ConversationId} by user {UserId}", conversationId, userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unpinning conversation: {ConversationId}", conversationId);
            return Result.Failure("UNPIN_ERROR", "取消置顶失败");
        }
    }

    public async Task<Result> ClearConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result.Failure("UNAUTHORIZED", "无权清空此对话");
            }

            var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
            if (conversationObj is not Domain.Entities.Conversation.Conversation conversation)
            {
                return Result.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            // Archive all messages (keeps conversation but compresses/hides messages)
            conversation.ArchiveMessages();
            
            // Alternatively, could delete all messages except system message
            // foreach (var message in conversation.Messages.Where(m => m.Role != MessageRole.System.ToString().ToLower()).ToList())
            // {
            //     conversation.DeleteMessage(message.Id);
            // }
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"conversation:{conversationId}", cancellationToken);

            _logger.LogInformation("Conversation cleared: {ConversationId} by user {UserId}", conversationId, userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing conversation: {ConversationId}", conversationId);
            return Result.Failure("CLEAR_ERROR", "清空对话失败");
        }
    }

    public async Task<Result> RenameConversationAsync(Guid conversationId, string newTitle, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result.Failure("UNAUTHORIZED", "无权重命名此对话");
            }

            var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
            if (conversationObj is not Domain.Entities.Conversation.Conversation conversation)
            {
                return Result.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            // Use domain method to update title
            conversation.UpdateTitle(newTitle.Trim());
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"conversation:{conversationId}", cancellationToken);
            await _cacheService.RemoveAsync($"conversations:user:{userId}", cancellationToken);

            _logger.LogInformation("Conversation renamed: {ConversationId} to '{NewTitle}' by user {UserId}", conversationId, newTitle, userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error renaming conversation: {ConversationId}", conversationId);
            return Result.Failure("RENAME_ERROR", "重命名对话失败");
        }
    }

    public async Task<Result<PagedResult<ConversationListDto>>> SearchConversationsAsync(ConversationSearchDto searchRequest, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var (conversations, totalCount) = await _unitOfWork.Conversations.SearchConversationsAsync(
                userId,
                searchRequest.Keyword,
                searchRequest.AgentIds,
                searchRequest.DateRange?.StartDate,
                searchRequest.DateRange?.EndDate,
                searchRequest.IncludeArchived,
                searchRequest.OnlyPinned,
                searchRequest.SearchInContent,
                searchRequest.SortBy ?? "lastMessage",
                searchRequest.SortDescending,
                (searchRequest.PageNumber - 1) * searchRequest.PageSize,
                searchRequest.PageSize,
                cancellationToken);

            var conversationList = conversations.Cast<Domain.Entities.Conversation.Conversation>().ToList();
            var conversationDtos = await MapToConversationListDtosAsync(conversationList, cancellationToken);

            var result = new PagedResult<ConversationListDto>(
                conversationDtos,
                totalCount,
                searchRequest.PageNumber,
                searchRequest.PageSize);

            return Result<PagedResult<ConversationListDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching conversations");
            return Result<PagedResult<ConversationListDto>>.Failure("SEARCH_ERROR", "搜索对话失败");
        }
    }

    public async Task<Result<byte[]>> ExportConversationsAsync(ConversationExportOptionsDto exportOptions, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var conversations = new List<Domain.Entities.Conversation.Conversation>();
            
            foreach (var conversationId in exportOptions.ConversationIds)
            {
                // Verify ownership
                if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
                {
                    continue; // Skip conversations user doesn't own
                }

                var conversation = await GetConversationWithFullDetailsAsync(conversationId, cancellationToken);
                if (conversation != null)
                {
                    conversations.Add(conversation);
                }
            }

            if (!conversations.Any())
            {
                return Result<byte[]>.Failure("NO_CONVERSATIONS", "没有可导出的对话");
            }

            byte[] exportData = exportOptions.Format.ToLower() switch
            {
                "json" => await ExportToJsonBytesAsync(conversations, exportOptions),
                "markdown" => await ExportToMarkdownBytesAsync(conversations, exportOptions),
                "pdf" => await ExportToPdfBytesAsync(conversations, exportOptions),
                "csv" => await ExportToCsvBytesAsync(conversations, exportOptions),
                _ => throw new NotSupportedException($"不支持的导出格式: {exportOptions.Format}")
            };

            return Result<byte[]>.Success(exportData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting conversations");
            return Result<byte[]>.Failure("EXPORT_ERROR", "导出对话失败");
        }
    }

    public async Task<Result<int>> CleanupOldConversationsAsync(ConversationCleanupPolicyDto policy, CancellationToken cancellationToken = default)
    {
        try
        {
            var conversationsToCleanup = await _unitOfWork.Conversations.GetConversationsForCleanupAsync(
                policy.DaysToKeep,
                policy.KeepPinnedConversations,
                policy.KeepConversationsWithAttachments,
                1000, // Process up to 1000 conversations at a time
                cancellationToken);

            var cleanupCount = 0;
            
            foreach (var conversationObj in conversationsToCleanup)
            {
                if (conversationObj is not Domain.Entities.Conversation.Conversation conversation)
                    continue;

                if (policy.ArchiveBeforeDelete && !conversation.IsArchived)
                {
                    conversation.Archive();
                    conversation.ArchiveMessages();
                }
                else
                {
                    _unitOfWork.Conversations.Remove(conversation);
                    cleanupCount++;
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Cleaned up {Count} old conversations", cleanupCount);
            return Result<int>.Success(cleanupCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up old conversations");
            return Result<int>.Failure("CLEANUP_ERROR", "清理旧对话失败");
        }
    }

    public async Task<Result<int>> CleanupUserConversationsAsync(Guid userId, ConversationCleanupPolicyDto policy, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check user storage usage if limit is set
            if (policy.MaxStorageBytesPerUser.HasValue)
            {
                var storageUsage = await _unitOfWork.Conversations.GetUserStorageUsageAsync(userId, cancellationToken);
                if (storageUsage <= policy.MaxStorageBytesPerUser.Value)
                {
                    return Result<int>.Success(0); // No cleanup needed
                }
            }

            // Get user conversations for cleanup
            var userConversations = await _unitOfWork.Conversations.GetUserConversationsAsync(userId, true, cancellationToken);
            var conversationsList = userConversations.Cast<Domain.Entities.Conversation.Conversation>()
                .OrderBy(c => c.LastMessageAt)
                .ToList();

            var cleanupCount = 0;
            var currentCount = conversationsList.Count;

            // Apply max conversations limit
            if (policy.MaxConversationsPerUser.HasValue && currentCount > policy.MaxConversationsPerUser.Value)
            {
                var toRemove = currentCount - policy.MaxConversationsPerUser.Value;
                var conversationsToRemove = conversationsList
                    .Where(c => !c.Metadata.ContainsKey("IsPinned") || !(bool)c.Metadata["IsPinned"])
                    .Take(toRemove)
                    .ToList();

                foreach (var conversation in conversationsToRemove)
                {
                    if (policy.ArchiveBeforeDelete && !conversation.IsArchived)
                    {
                        conversation.Archive();
                        conversation.ArchiveMessages();
                    }
                    else
                    {
                        _unitOfWork.Conversations.Remove(conversation);
                        cleanupCount++;
                    }
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Cleaned up {Count} conversations for user {UserId}", cleanupCount, userId);
            return Result<int>.Success(cleanupCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up user conversations");
            return Result<int>.Failure("CLEANUP_ERROR", "清理用户对话失败");
        }
    }

    public async Task<Result<ConversationStatisticsDto>> GetUserConversationStatisticsAsync(Guid userId, DateRangeDto? dateRange = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var stats = await _unitOfWork.Conversations.GetUserStatisticsAsync(
                userId,
                dateRange?.StartDate,
                dateRange?.EndDate,
                cancellationToken);

            var statisticsDto = new ConversationStatisticsDto
            {
                TotalConversations = (int)(stats.ContainsKey("totalConversations") ? stats["totalConversations"] : 0),
                ActiveConversations = (int)(stats.ContainsKey("activeConversations") ? stats["activeConversations"] : 0),
                TotalMessages = (int)(stats.ContainsKey("totalMessages") ? stats["totalMessages"] : 0),
                TotalTokensUsed = (int)(stats.ContainsKey("totalTokensUsed") ? stats["totalTokensUsed"] : 0),
                ConversationsByAgent = stats.ContainsKey("conversationsByAgent") 
                    ? (Dictionary<string, int>)stats["conversationsByAgent"] 
                    : new Dictionary<string, int>(),
                ConversationsByDate = stats.ContainsKey("conversationsByDay") 
                    ? (Dictionary<string, int>)stats["conversationsByDay"] 
                    : new Dictionary<string, int>()
            };

            return Result<ConversationStatisticsDto>.Success(statisticsDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user conversation statistics");
            return Result<ConversationStatisticsDto>.Failure("STATS_ERROR", "获取对话统计失败");
        }
    }

    public async Task<Result<Dictionary<string, object>>> GetConversationAnalyticsAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result<Dictionary<string, object>>.Failure("UNAUTHORIZED", "无权访问此对话的分析数据");
            }

            var analytics = await _unitOfWork.Conversations.GetConversationStatisticsAsync(conversationId, cancellationToken);
            return Result<Dictionary<string, object>>.Success(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversation analytics");
            return Result<Dictionary<string, object>>.Failure("ANALYTICS_ERROR", "获取对话分析失败");
        }
    }

    public async Task<Result<string>> ShareConversationAsync(ConversationShareDto shareRequest, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify ownership
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, shareRequest.ConversationId, cancellationToken))
            {
                return Result<string>.Failure("UNAUTHORIZED", "无权分享此对话");
            }

            var conversation = await GetConversationWithFullDetailsAsync(shareRequest.ConversationId, cancellationToken);
            if (conversation == null)
            {
                return Result<string>.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            // Generate share ID
            var shareId = GenerateShareId();
            
            // Store share information in conversation metadata
            var shareInfo = new Dictionary<string, object>
            {
                ["shareId"] = shareId,
                ["shareType"] = shareRequest.ShareType,
                ["sharedBy"] = userId,
                ["sharedAt"] = DateTime.UtcNow,
                ["expiresAt"] = shareRequest.ExpiresAt,
                ["allowComments"] = shareRequest.AllowComments,
                ["requirePassword"] = shareRequest.RequirePassword,
                ["anonymizeUserInfo"] = shareRequest.AnonymizeUserInfo
            };

            if (shareRequest.RequirePassword && !string.IsNullOrEmpty(shareRequest.Password))
            {
                // Hash the password
                shareInfo["passwordHash"] = BC.HashPassword(shareRequest.Password);
            }

            if (shareRequest.AllowedEmails != null && shareRequest.AllowedEmails.Any())
            {
                shareInfo["allowedEmails"] = shareRequest.AllowedEmails;
            }

            conversation.SetMetadata($"share_{shareId}", shareInfo);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"conversation:{shareRequest.ConversationId}", cancellationToken);

            _logger.LogInformation("Conversation shared: {ConversationId} with share ID {ShareId}", shareRequest.ConversationId, shareId);
            return Result<string>.Success(shareId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sharing conversation");
            return Result<string>.Failure("SHARE_ERROR", "分享对话失败");
        }
    }

    public async Task<Result> RevokeConversationShareAsync(string shareId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Find conversation with this share ID
            var conversations = await _unitOfWork.Conversations.GetUserConversationsAsync(userId, true, cancellationToken);
            var conversationWithShare = conversations
                .Cast<Domain.Entities.Conversation.Conversation>()
                .FirstOrDefault(c => c.Metadata.ContainsKey($"share_{shareId}"));

            if (conversationWithShare == null)
            {
                return Result.Failure("SHARE_NOT_FOUND", "分享链接不存在");
            }

            // Remove share metadata
            conversationWithShare.RemoveMetadata($"share_{shareId}");
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"conversation:{conversationWithShare.Id}", cancellationToken);
            await _cacheService.RemoveAsync($"share:{shareId}", cancellationToken);

            _logger.LogInformation("Conversation share revoked: {ShareId}", shareId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking conversation share");
            return Result.Failure("REVOKE_ERROR", "撤销分享失败");
        }
    }

    public async Task<Result<ConversationDetailDto>> GetSharedConversationAsync(string shareId, string? password = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // Try to get from cache first
            var cacheKey = $"share:{shareId}";
            var cachedConversationId = await _cacheService.GetAsync<Guid?>(cacheKey, cancellationToken);
            
            Domain.Entities.Conversation.Conversation? conversation = null;
            Dictionary<string, object>? shareInfo = null;

            if (cachedConversationId.HasValue)
            {
                var conversationObj = await _unitOfWork.Conversations.GetByIdAsync(cachedConversationId.Value, cancellationToken);
                conversation = conversationObj as Domain.Entities.Conversation.Conversation;
                if (conversation != null && conversation.Metadata.ContainsKey($"share_{shareId}"))
                {
                    shareInfo = conversation.Metadata[$"share_{shareId}"] as Dictionary<string, object>;
                }
            }
            else
            {
                // Search for conversation with this share ID
                // This is inefficient but necessary without a dedicated share table
                var allConversations = await _unitOfWork.Repository<Domain.Entities.Conversation.Conversation>()
                    .GetAllAsync(cancellationToken);
                
                foreach (var conv in allConversations)
                {
                    if (conv.Metadata.ContainsKey($"share_{shareId}"))
                    {
                        conversation = conv;
                        shareInfo = conv.Metadata[$"share_{shareId}"] as Dictionary<string, object>;
                        
                        // Cache the conversation ID for future lookups
                        await _cacheService.SetAsync(cacheKey, conv.Id, TimeSpan.FromHours(1), cancellationToken);
                        break;
                    }
                }
            }

            if (conversation == null || shareInfo == null)
            {
                return Result<ConversationDetailDto>.Failure("SHARE_NOT_FOUND", "分享链接不存在或已失效");
            }

            // Check expiration
            if (shareInfo.ContainsKey("expiresAt") && shareInfo["expiresAt"] is DateTime expiresAt)
            {
                if (DateTime.UtcNow > expiresAt)
                {
                    return Result<ConversationDetailDto>.Failure("SHARE_EXPIRED", "分享链接已过期");
                }
            }

            // Check password
            if (shareInfo.ContainsKey("requirePassword") && (bool)shareInfo["requirePassword"])
            {
                if (string.IsNullOrEmpty(password))
                {
                    return Result<ConversationDetailDto>.Failure("PASSWORD_REQUIRED", "需要密码才能访问");
                }

                if (shareInfo.ContainsKey("passwordHash") && shareInfo["passwordHash"] is string passwordHash)
                {
                    if (!BC.Verify(password, passwordHash))
                    {
                        return Result<ConversationDetailDto>.Failure("INVALID_PASSWORD", "密码错误");
                    }
                }
            }

            // Get full conversation details
            var fullConversation = await GetConversationWithFullDetailsAsync(conversation.Id, cancellationToken);
            if (fullConversation == null)
            {
                return Result<ConversationDetailDto>.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }

            var detailDto = await MapToConversationDetailDtoAsync(fullConversation, cancellationToken);
            
            // Anonymize user info if requested
            if (shareInfo.ContainsKey("anonymizeUserInfo") && (bool)shareInfo["anonymizeUserInfo"])
            {
                foreach (var message in detailDto.Messages)
                {
                    if (message.Role == "user")
                    {
                        message.Metadata = new Dictionary<string, object> { ["anonymized"] = true };
                    }
                }
            }

            return Result<ConversationDetailDto>.Success(detailDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shared conversation");
            return Result<ConversationDetailDto>.Failure("GET_SHARE_ERROR", "获取分享对话失败");
        }
    }

    public async Task<Result<List<ConversationListDto>>> GetDeletedConversationsAsync(Guid userId, DateRangeDto? dateRange = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var deletedConversations = await _unitOfWork.Conversations.GetDeletedConversationsAsync(
                userId,
                dateRange?.StartDate,
                cancellationToken);

            var conversationList = deletedConversations.Cast<Domain.Entities.Conversation.Conversation>().ToList();
            var conversationDtos = await MapToConversationListDtosAsync(conversationList, cancellationToken);

            return Result<List<ConversationListDto>>.Success(conversationDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting deleted conversations");
            return Result<List<ConversationListDto>>.Failure("GET_DELETED_ERROR", "获取已删除对话失败");
        }
    }

    public async Task<Result<int>> RecoverDeletedConversationsAsync(ConversationRecoveryDto recoveryRequest, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var recoveredCount = 0;
            
            foreach (var conversationId in recoveryRequest.ConversationIds)
            {
                var conversationObj = await _unitOfWork.Repository<Domain.Entities.Conversation.Conversation>()
                    .GetByIdAsync(conversationId, cancellationToken);
                    
                if (conversationObj is Domain.Entities.Conversation.Conversation conversation && 
                    conversation.CustomerUserId == userId && 
                    conversation.IsDeleted)
                {
                    // Restore conversation
                    conversation.Restore();
                    
                    if (recoveryRequest.RestoreMessages)
                    {
                        // Restore messages if they were soft deleted
                        foreach (var message in conversation.Messages.Where(m => m.IsDeleted))
                        {
                            message.Restore();
                        }
                    }
                    
                    recoveredCount++;
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Recovered {Count} conversations for user {UserId}", recoveredCount, userId);
            return Result<int>.Success(recoveredCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recovering deleted conversations");
            return Result<int>.Failure("RECOVERY_ERROR", "恢复已删除对话失败");
        }
    }

    public async Task<Result<Dictionary<Guid, bool>>> BulkOperationAsync(BulkConversationOperationDto operation, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var results = new Dictionary<Guid, bool>();

            switch (operation.Operation.ToLower())
            {
                case "archive":
                    var archivedCount = await _unitOfWork.Conversations.BatchArchiveAsync(
                        operation.ConversationIds,
                        userId,
                        cancellationToken);
                    foreach (var id in operation.ConversationIds)
                    {
                        results[id] = true;
                    }
                    break;

                case "delete":
                    var deletedCount = await _unitOfWork.Conversations.BatchDeleteAsync(
                        operation.ConversationIds,
                        userId,
                        cancellationToken);
                    foreach (var id in operation.ConversationIds)
                    {
                        results[id] = true;
                    }
                    break;

                case "tag":
                    if (operation.Parameters != null && 
                        operation.Parameters.ContainsKey("tag") && 
                        operation.Parameters["tag"] is string tag)
                    {
                        results = await _unitOfWork.Conversations.BatchUpdateMetadataAsync(
                            operation.ConversationIds,
                            "tags",
                            tag,
                            cancellationToken);
                    }
                    break;

                case "export":
                    // Handled by ExportConversationsAsync
                    foreach (var id in operation.ConversationIds)
                    {
                        results[id] = await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, id, cancellationToken);
                    }
                    break;

                default:
                    return Result<Dictionary<Guid, bool>>.Failure("INVALID_OPERATION", $"不支持的操作: {operation.Operation}");
            }

            // Clear cache for affected conversations
            foreach (var conversationId in operation.ConversationIds)
            {
                await _cacheService.RemoveAsync($"conversation:{conversationId}", cancellationToken);
            }
            await _cacheService.RemoveAsync($"conversations:user:{userId}", cancellationToken);

            return Result<Dictionary<Guid, bool>>.Success(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing bulk operation");
            return Result<Dictionary<Guid, bool>>.Failure("BULK_OPERATION_ERROR", "批量操作失败");
        }
    }

    // Helper methods for export formats
    private async Task<byte[]> ExportToJsonBytesAsync(List<Domain.Entities.Conversation.Conversation> conversations, ConversationExportOptionsDto options)
    {
        var export = conversations.Select(c => new
        {
            c.Id,
            c.Title,
            c.CreatedAt,
            c.LastMessageAt,
            Messages = c.Messages
                .Where(m => (options.IncludeSystemMessages || m.Role != "system") && 
                           (options.IncludeDeletedMessages || !m.IsDeleted))
                .Select(m => new
                {
                    m.Id,
                    m.Role,
                    m.Content,
                    m.TokenCount,
                    m.CreatedAt,
                    Attachments = options.IncludeAttachments ? m.Attachments.Select(a => new
                    {
                        a.FileName,
                        a.FileUrl,
                        a.ContentType,
                        a.FileSize
                    }) : null,
                    Rating = m.Rating != null ? new { m.Rating.Score, m.Rating.Feedback } : null
                }),
            Metadata = options.IncludeMetadata ? c.Metadata : null
        });

        var json = System.Text.Json.JsonSerializer.Serialize(export, new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = true
        });

        return System.Text.Encoding.UTF8.GetBytes(json);
    }

    private async Task<byte[]> ExportToMarkdownBytesAsync(List<Domain.Entities.Conversation.Conversation> conversations, ConversationExportOptionsDto options)
    {
        var markdown = new System.Text.StringBuilder();
        
        foreach (var conversation in conversations)
        {
            markdown.AppendLine($"# {conversation.Title}");
            markdown.AppendLine();
            markdown.AppendLine($"**开始时间**: {conversation.CreatedAt:yyyy-MM-dd HH:mm:ss}");
            markdown.AppendLine($"**最后更新**: {conversation.LastMessageAt:yyyy-MM-dd HH:mm:ss}");
            markdown.AppendLine($"**消息数量**: {conversation.MessageCount}");
            markdown.AppendLine($"**Token使用量**: {conversation.TotalTokens}");
            markdown.AppendLine();
            markdown.AppendLine("---");
            markdown.AppendLine();

            foreach (var message in conversation.Messages
                .Where(m => (options.IncludeSystemMessages || m.Role != "system") && 
                           (options.IncludeDeletedMessages || !m.IsDeleted))
                .OrderBy(m => m.CreatedAt))
            {
                var role = message.Role switch
                {
                    "user" => "用户",
                    "assistant" => "助手",
                    "system" => "系统",
                    _ => message.Role
                };

                markdown.AppendLine($"### {role} ({message.CreatedAt:HH:mm:ss})");
                markdown.AppendLine();
                markdown.AppendLine(message.Content);
                
                if (options.IncludeAttachments && message.Attachments.Any())
                {
                    markdown.AppendLine();
                    markdown.AppendLine("**附件**:");
                    foreach (var attachment in message.Attachments)
                    {
                        markdown.AppendLine($"- [{attachment.FileName}]({attachment.FileUrl})");
                    }
                }
                
                if (message.Rating != null)
                {
                    markdown.AppendLine();
                    markdown.AppendLine($"**评分**: {message.Rating.Score}/5");
                    if (!string.IsNullOrEmpty(message.Rating.Feedback))
                    {
                        markdown.AppendLine($"**反馈**: {message.Rating.Feedback}");
                    }
                }
                
                markdown.AppendLine();
            }
            
            markdown.AppendLine();
            markdown.AppendLine("=" + new string('=', 50));
            markdown.AppendLine();
        }

        return System.Text.Encoding.UTF8.GetBytes(markdown.ToString());
    }

    private async Task<byte[]> ExportToPdfBytesAsync(List<Domain.Entities.Conversation.Conversation> conversations, ConversationExportOptionsDto options)
    {
        QuestPDF.Settings.License = LicenseType.Community;
        
        var document = Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(2, Unit.Centimetre);
                page.PageColor(Colors.White);
                page.DefaultTextStyle(x => x.FontSize(11).FontFamily("Microsoft YaHei", "SimHei", "Arial"));
                
                page.Header()
                    .Height(100)
                    .Background(Colors.Grey.Lighten4)
                    .AlignCenter()
                    .AlignMiddle()
                    .Text(text =>
                    {
                        text.Span("WhimLabAI 对话导出").FontSize(20).SemiBold();
                        text.AlignCenter();
                    });
                
                page.Content()
                    .PaddingVertical(1, Unit.Centimetre)
                    .Column(column =>
                    {
                        column.Spacing(20);
                        
                        // Export info
                        column.Item().Background(Colors.Grey.Lighten5).Padding(10).Column(infoColumn =>
                        {
                            infoColumn.Item().Text($"导出时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                            infoColumn.Item().Text($"对话数量: {conversations.Count}");
                            infoColumn.Item().Text($"导出格式: PDF");
                            
                            if (options.DateRange?.StartDate.HasValue == true || options.DateRange?.EndDate.HasValue == true)
                            {
                                var dateRange = "";
                                if (options.DateRange?.StartDate.HasValue == true)
                                    dateRange += $"从 {options.DateRange.StartDate.Value:yyyy-MM-dd}";
                                if (options.DateRange?.EndDate.HasValue == true)
                                    dateRange += $" 到 {options.DateRange.EndDate.Value:yyyy-MM-dd}";
                                infoColumn.Item().Text($"时间范围: {dateRange}");
                            }
                        });
                        
                        // Conversations
                        foreach (var conversation in conversations)
                        {
                            column.Item().PageBreak();
                            column.Item().Element(container => ComposeConversation(container, conversation, options));
                        }
                    });
                
                page.Footer()
                    .Height(50)
                    .AlignCenter()
                    .Text(text =>
                    {
                        text.CurrentPageNumber();
                        text.Span(" / ");
                        text.TotalPages();
                    });
            });
        });
        
        return await Task.FromResult(document.GeneratePdf());
    }
    
    private void ComposeConversation(IContainer container, Domain.Entities.Conversation.Conversation conversation, ConversationExportOptionsDto options)
    {
        container.Column(column =>
        {
            // Conversation header
            column.Item().Background(Colors.Blue.Lighten5).Padding(10).Column(headerColumn =>
            {
                headerColumn.Item().Text(conversation.Title).FontSize(16).SemiBold();
                headerColumn.Item().Text($"ID: {conversation.Id}");
                headerColumn.Item().Text($"创建时间: {conversation.CreatedAt:yyyy-MM-dd HH:mm:ss}");
                headerColumn.Item().Text($"最后更新: {conversation.UpdatedAt:yyyy-MM-dd HH:mm:ss}");
                headerColumn.Item().Text($"消息数量: {conversation.Messages.Count}");
                headerColumn.Item().Text($"总Token数: {conversation.TotalTokens}");
                
                // Summary feature not yet implemented
                // TODO: Add summary generation for conversations
            });
            
            // Messages
            column.Item().PaddingTop(10).Column(messagesColumn =>
            {
                var messages = conversation.Messages
                    .Where(m => (options.IncludeSystemMessages || m.Role != "system") && 
                               (options.IncludeDeletedMessages || !m.IsDeleted))
                    .OrderBy(m => m.CreatedAt);
                
                foreach (var message in messages)
                {
                    messagesColumn.Item().Border(1).BorderColor(Colors.Grey.Lighten2).Padding(10).Column(messageColumn =>
                    {
                        // Message header
                        messageColumn.Item().Row(row =>
                        {
                            row.RelativeItem().Text(message.Role == "user" ? "用户" : message.Role == "assistant" ? "助手" : "系统")
                                .SemiBold()
                                .FontColor(message.Role == "user" ? Colors.Blue.Darken2 : message.Role == "assistant" ? Colors.Green.Darken2 : Colors.Grey.Darken2);
                            row.ConstantItem(150).AlignRight().Text(message.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"))
                                .FontSize(9).FontColor(Colors.Grey.Darken1);
                        });
                        
                        // Message content
                        messageColumn.Item().PaddingTop(5).Text(message.Content);
                        
                        // Message metadata
                        if (options.IncludeMetadata)
                        {
                            messageColumn.Item().PaddingTop(5).Row(row =>
                            {
                                row.RelativeItem().Text($"Token数: {message.TokenCount}")
                                    .FontSize(9).FontColor(Colors.Grey.Darken1);
                                if (message.Rating != null)
                                {
                                    row.ConstantItem(100).AlignRight().Text($"评分: {message.Rating.Score}/5")
                                        .FontSize(9).FontColor(Colors.Grey.Darken1);
                                }
                            });
                        }
                        
                        // Attachments
                        if (message.Attachments.Any() && options.IncludeAttachments)
                        {
                            messageColumn.Item().PaddingTop(5).Text("附件:").FontSize(9).SemiBold();
                            foreach (var attachment in message.Attachments)
                            {
                                messageColumn.Item().Text($"• {attachment.FileName} ({attachment.ContentType})")
                                    .FontSize(9).FontColor(Colors.Grey.Darken1);
                            }
                        }
                    });
                    
                    messagesColumn.Item().PaddingBottom(5); // Spacing between messages
                }
            });
        });
    }

    private async Task<byte[]> ExportToCsvBytesAsync(List<Domain.Entities.Conversation.Conversation> conversations, ConversationExportOptionsDto options)
    {
        var csv = new System.Text.StringBuilder();
        csv.AppendLine("ConversationId,ConversationTitle,MessageId,Role,Content,TokenCount,CreatedAt,Rating");

        foreach (var conversation in conversations)
        {
            foreach (var message in conversation.Messages
                .Where(m => (options.IncludeSystemMessages || m.Role != "system") && 
                           (options.IncludeDeletedMessages || !m.IsDeleted))
                .OrderBy(m => m.CreatedAt))
            {
                csv.AppendLine($"\"{conversation.Id}\",\"{EscapeCsv(conversation.Title)}\",\"{message.Id}\",\"{message.Role}\",\"{EscapeCsv(message.Content)}\",{message.TokenCount},\"{message.CreatedAt:yyyy-MM-dd HH:mm:ss}\",{message.Rating?.Score ?? 0}");
            }
        }

        return System.Text.Encoding.UTF8.GetBytes(csv.ToString());
    }

    private string EscapeCsv(string value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;
            
        // Escape quotes and wrap in quotes if needed
        if (value.Contains("\"") || value.Contains(",") || value.Contains("\n"))
        {
            return value.Replace("\"", "\"\"");
        }
        
        return value;
    }

    private string GenerateShareId()
    {
        // Generate a secure random share ID
        var bytes = new byte[16];
        using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
        {
            rng.GetBytes(bytes);
        }
        return Convert.ToBase64String(bytes)
            .Replace("+", "-")
            .Replace("/", "_")
            .TrimEnd('=');
    }

    public async Task<Result<MessageAttachmentDetailDto>> UploadAttachmentAsync(Guid conversationId, IFormFile file, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verify user owns the conversation
            if (!await _unitOfWork.Conversations.UserOwnsConversationAsync(userId, conversationId, cancellationToken))
            {
                return Result<MessageAttachmentDetailDto>.Failure("UNAUTHORIZED", "您没有权限访问此对话");
            }

            // Validate file
            if (file == null || file.Length == 0)
            {
                return Result<MessageAttachmentDetailDto>.Failure("INVALID_FILE", "请选择要上传的文件");
            }

            // File size limits
            const long maxFileSize = 100 * 1024 * 1024; // 100MB
            const long maxImageSize = 10 * 1024 * 1024; // 10MB

            if (file.Length > maxFileSize)
            {
                return Result<MessageAttachmentDetailDto>.Failure("FILE_TOO_LARGE", $"文件大小不能超过 {maxFileSize / 1024 / 1024}MB");
            }

            // Get file extension
            var extension = Path.GetExtension(file.FileName)?.ToLowerInvariant();
            if (string.IsNullOrEmpty(extension))
            {
                return Result<MessageAttachmentDetailDto>.Failure("INVALID_FILE_TYPE", "文件必须有扩展名");
            }

            // Allowed extensions
            var allowedExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", // Images
                ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", // Documents
                ".txt", ".md", ".json", ".xml", ".csv", // Text
                ".zip", ".rar", ".7z", ".tar", ".gz", // Archives
                ".mp3", ".wav", ".ogg", ".m4a", // Audio
                ".mp4", ".avi", ".mkv", ".mov", ".webm" // Video
            };

            if (!allowedExtensions.Contains(extension))
            {
                return Result<MessageAttachmentDetailDto>.Failure("UNSUPPORTED_FILE_TYPE", "不支持的文件类型");
            }

            // Check image size
            var isImage = IsImageFile(extension);
            if (isImage && file.Length > maxImageSize)
            {
                return Result<MessageAttachmentDetailDto>.Failure("IMAGE_TOO_LARGE", $"图片大小不能超过 {maxImageSize / 1024 / 1024}MB");
            }

            // Build object path
            var objectPath = $"conversations/{conversationId:N}/{DateTime.UtcNow:yyyy/MM/dd}/{Guid.NewGuid()}{extension}";

            // Upload to storage
            using var stream = file.OpenReadStream();
            var uploadRequest = new UploadFileRequest
            {
                FileStream = stream,
                FileName = file.FileName,
                ContentType = file.ContentType,
                ObjectPath = objectPath,
                Metadata = new Dictionary<string, string>
                {
                    ["conversation-id"] = conversationId.ToString(),
                    ["uploaded-by"] = userId.ToString(),
                    ["upload-time"] = DateTime.UtcNow.ToString("O")
                }
            };

            var uploadResult = await _storageService.UploadFileAsync(uploadRequest);
            if (!uploadResult.Success)
            {
                _logger.LogWarning("文件上传失败: {FileName}, 错误: {Error}", 
                    file.FileName, uploadResult.ErrorMessage);
                return Result<MessageAttachmentDetailDto>.Failure("UPLOAD_FAILED", uploadResult.ErrorMessage ?? "文件上传失败");
            }

            // Create attachment DTO
            var attachmentDto = new MessageAttachmentDetailDto
            {
                FileName = file.FileName,
                Url = uploadResult.FileUrl!,
                MimeType = file.ContentType,
                FileSize = file.Length,
                Type = DetermineAttachmentTypeFromExtension(extension),
                Metadata = new Dictionary<string, object>
                {
                    ["fileKey"] = uploadResult.FileKey!,
                    ["uploadTime"] = DateTime.UtcNow
                }
            };

            // Generate thumbnail URL for images
            if (isImage)
            {
                // For now, use the same URL. In production, you might generate actual thumbnails
                attachmentDto.ThumbnailUrl = uploadResult.FileUrl;
            }

            _logger.LogInformation("附件上传成功: ConversationId={ConversationId}, FileName={FileName}, FileKey={FileKey}", 
                conversationId, file.FileName, uploadResult.FileKey);

            return Result<MessageAttachmentDetailDto>.Success(attachmentDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "上传附件时发生错误: ConversationId={ConversationId}", conversationId);
            return Result<MessageAttachmentDetailDto>.Failure("UPLOAD_ERROR", "上传附件失败");
        }
    }

    private bool IsImageFile(string extension)
    {
        return extension == ".jpg" || extension == ".jpeg" || extension == ".png" || 
               extension == ".gif" || extension == ".bmp" || extension == ".webp";
    }

    private AttachmentType DetermineAttachmentTypeFromExtension(string extension)
    {
        return extension.ToLowerInvariant() switch
        {
            ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".webp" => AttachmentType.Image,
            ".mp4" or ".avi" or ".mkv" or ".mov" or ".webm" => AttachmentType.Video,
            ".mp3" or ".wav" or ".ogg" or ".m4a" => AttachmentType.Audio,
            ".pdf" or ".doc" or ".docx" or ".xls" or ".xlsx" or ".ppt" or ".pptx" or ".txt" or ".md" => AttachmentType.Document,
            _ => AttachmentType.Other
        };
    }
}