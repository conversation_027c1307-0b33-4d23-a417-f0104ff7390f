using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos.Device;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Device;

public class DeviceManagementService : IDeviceManagementService
{
    private readonly ICustomerUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<DeviceManagementService> _logger;

    public DeviceManagementService(
        ICustomerUserRepository userRepository,
        IUnitOfWork unitOfWork,
        ILogger<DeviceManagementService> logger)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<DeviceListDto>> GetUserDevicesAsync(Guid userId, string? currentDeviceId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetWithDevicesAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<DeviceListDto>.Failure("用户不存在");
            }

            var devices = user.DeviceAuthorizations.Select(device => new DeviceDto
            {
                Id = device.Id,
                DeviceId = device.DeviceId,
                DeviceName = device.DeviceName,
                DeviceInfo = device.DeviceInfo,
                DeviceType = device.DeviceType,
                OperatingSystem = device.OperatingSystem,
                AuthorizedAt = device.AuthorizedAt,
                LastAccessAt = device.LastAccessAt,
                IsActive = device.IsActive,
                IpAddress = device.IpAddress,
                IsCurrent = device.DeviceId == currentDeviceId
            }).OrderByDescending(d => d.LastAccessAt).ToList();

            var result = new DeviceListDto
            {
                Devices = devices,
                TotalCount = devices.Count,
                ActiveCount = devices.Count(d => d.IsActive)
            };

            return Result<DeviceListDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户设备列表失败: UserId={UserId}", userId);
            return Result<DeviceListDto>.Failure("获取设备列表失败");
        }
    }

    public async Task<Result> RevokeDeviceAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetWithDevicesAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            var device = user.DeviceAuthorizations.FirstOrDefault(d => d.DeviceId == deviceId);
            if (device == null)
            {
                return Result.Failure("设备不存在");
            }

            user.RevokeDevice(deviceId);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("设备授权已撤销: UserId={UserId}, DeviceId={DeviceId}", userId, deviceId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "撤销设备授权失败: UserId={UserId}, DeviceId={DeviceId}", userId, deviceId);
            return Result.Failure("撤销设备授权失败");
        }
    }

    public async Task<Result> UpdateDeviceAsync(Guid userId, UpdateDeviceDto updateDeviceDto, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetWithDevicesAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            var device = user.DeviceAuthorizations.FirstOrDefault(d => d.DeviceId == updateDeviceDto.DeviceId);
            if (device == null)
            {
                return Result.Failure("设备不存在");
            }

            device.UpdateDeviceName(updateDeviceDto.DeviceName);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("设备信息已更新: UserId={UserId}, DeviceId={DeviceId}", userId, updateDeviceDto.DeviceId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备信息失败: UserId={UserId}, DeviceId={DeviceId}", userId, updateDeviceDto.DeviceId);
            return Result.Failure("更新设备信息失败");
        }
    }

    public async Task<Result> ActivateDeviceAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetWithDevicesAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            var device = user.DeviceAuthorizations.FirstOrDefault(d => d.DeviceId == deviceId);
            if (device == null)
            {
                return Result.Failure("设备不存在");
            }

            device.Activate();
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("设备已激活: UserId={UserId}, DeviceId={DeviceId}", userId, deviceId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "激活设备失败: UserId={UserId}, DeviceId={DeviceId}", userId, deviceId);
            return Result.Failure("激活设备失败");
        }
    }

    public async Task<Result> DeactivateDeviceAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetWithDevicesAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            var device = user.DeviceAuthorizations.FirstOrDefault(d => d.DeviceId == deviceId);
            if (device == null)
            {
                return Result.Failure("设备不存在");
            }

            device.Deactivate();
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("设备已停用: UserId={UserId}, DeviceId={DeviceId}", userId, deviceId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停用设备失败: UserId={UserId}, DeviceId={DeviceId}", userId, deviceId);
            return Result.Failure("停用设备失败");
        }
    }

    public async Task<Result<DeviceStatsDto>> GetDeviceStatsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetWithDevicesAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<DeviceStatsDto>.Failure("用户不存在");
            }

            var devices = user.DeviceAuthorizations.ToList();
            var activeDevices = devices.Where(d => d.IsActive).ToList();
            var inactiveDevices = devices.Where(d => !d.IsActive).ToList();

            var deviceTypeStats = devices
                .Where(d => !string.IsNullOrEmpty(d.DeviceType))
                .GroupBy(d => d.DeviceType!)
                .ToDictionary(g => g.Key, g => g.Count());

            var osStats = devices
                .Where(d => !string.IsNullOrEmpty(d.OperatingSystem))
                .GroupBy(d => d.OperatingSystem!)
                .ToDictionary(g => g.Key, g => g.Count());

            var lastAccessTime = devices.Any() ? devices.Max(d => d.LastAccessAt) : DateTime.MinValue;

            var stats = new DeviceStatsDto
            {
                TotalDevices = devices.Count,
                ActiveDevices = activeDevices.Count,
                InactiveDevices = inactiveDevices.Count,
                DeviceTypeStats = deviceTypeStats,
                OperatingSystemStats = osStats,
                LastAccessTime = lastAccessTime
            };

            return Result<DeviceStatsDto>.Success(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备统计信息失败: UserId={UserId}", userId);
            return Result<DeviceStatsDto>.Failure("获取设备统计信息失败");
        }
    }

    public async Task<Result> RevokeMultipleDevicesAsync(Guid userId, List<string> deviceIds, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetWithDevicesAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            var revokedCount = 0;
            foreach (var deviceId in deviceIds)
            {
                var device = user.DeviceAuthorizations.FirstOrDefault(d => d.DeviceId == deviceId);
                if (device != null)
                {
                    user.RevokeDevice(deviceId);
                    revokedCount++;
                }
            }

            if (revokedCount > 0)
            {
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }

            _logger.LogInformation("批量撤销设备授权完成: UserId={UserId}, 撤销数量={RevokedCount}", userId, revokedCount);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量撤销设备授权失败: UserId={UserId}", userId);
            return Result.Failure("批量撤销设备授权失败");
        }
    }

    public async Task<Result<int>> CleanupInactiveDevicesAsync(Guid userId, int daysInactive = 90, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetWithDevicesAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<int>.Failure("用户不存在");
            }

            var cutoffDate = DateTime.UtcNow.AddDays(-daysInactive);
            var inactiveDevices = user.DeviceAuthorizations
                .Where(d => d.LastAccessAt < cutoffDate)
                .ToList();

            var cleanedCount = 0;
            foreach (var device in inactiveDevices)
            {
                user.RevokeDevice(device.DeviceId);
                cleanedCount++;
            }

            if (cleanedCount > 0)
            {
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }

            _logger.LogInformation("清理非活跃设备完成: UserId={UserId}, 清理数量={CleanedCount}", userId, cleanedCount);
            return Result<int>.Success(cleanedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理非活跃设备失败: UserId={UserId}", userId);
            return Result<int>.Failure("清理非活跃设备失败");
        }
    }
}