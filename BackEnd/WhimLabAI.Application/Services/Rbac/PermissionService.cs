using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Auth;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos.Admin.Rbac;

namespace WhimLabAI.Application.Services.Rbac;

public class PermissionService : IPermissionService
{
    private readonly IPermissionRepository _permissionRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<PermissionService> _logger;

    public PermissionService(
        IPermissionRepository permissionRepository,
        IUnitOfWork unitOfWork,
        ILogger<PermissionService> logger)
    {
        _permissionRepository = permissionRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<List<PermissionTreeDto>>> GetPermissionTreeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _permissionRepository.GetAllWithHierarchyAsync(cancellationToken);
            var tree = BuildPermissionTree(permissions);
            return Result<List<PermissionTreeDto>>.Success(tree);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限树失败");
            return Result<List<PermissionTreeDto>>.Failure("获取权限树失败");
        }
    }

    public async Task<Result<List<PermissionDto>>> GetPermissionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _permissionRepository.GetAllAsync(cancellationToken);
            var dtos = permissions.Select(MapToDto).ToList();
            return Result<List<PermissionDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限列表失败");
            return Result<List<PermissionDto>>.Failure("获取权限列表失败");
        }
    }

    public async Task<Result<List<PermissionDto>>> GetPermissionsByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _permissionRepository.GetByCategoryAsync(category, cancellationToken);
            var dtos = permissions.Select(MapToDto).ToList();
            return Result<List<PermissionDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "按分类获取权限失败，分类: {Category}", category);
            return Result<List<PermissionDto>>.Failure("获取权限失败");
        }
    }

    public async Task<Result<PermissionDto>> GetPermissionAsync(Guid permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _permissionRepository.GetByIdAsync(permissionId, cancellationToken);
            if (permission == null)
            {
                return Result<PermissionDto>.Failure("权限不存在");
            }

            var dto = MapToDto(permission);
            return Result<PermissionDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限详情失败，权限ID: {PermissionId}", permissionId);
            return Result<PermissionDto>.Failure("获取权限详情失败");
        }
    }

    public async Task<Result<PermissionDto>> CreatePermissionAsync(CreatePermissionDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查权限代码是否已存在
            if (await _permissionRepository.ExistsAsync(request.Code, null, cancellationToken))
            {
                return Result<PermissionDto>.Failure("权限代码已存在");
            }

            // 创建权限
            var permission = new Permission(
                request.Code,
                request.Name,
                request.Category,
                request.Description,
                request.ParentId);

            await _permissionRepository.AddAsync(permission, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("创建权限成功，权限代码: {Code}", request.Code);
            
            var dto = MapToDto(permission);
            return Result<PermissionDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建权限失败");
            return Result<PermissionDto>.Failure("创建权限失败");
        }
    }

    public async Task<Result> UpdatePermissionAsync(Guid permissionId, UpdatePermissionDto request, Guid operatorId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _permissionRepository.GetByIdAsync(permissionId, cancellationToken);
            if (permission == null)
            {
                return Result.Failure("权限不存在");
            }

            permission.Update(request.Name, request.Description, request.DisplayOrder);
            
            _permissionRepository.Update(permission);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("更新权限成功，权限ID: {PermissionId}, 操作者ID: {OperatorId}", permissionId, operatorId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新权限失败，权限ID: {PermissionId}", permissionId);
            return Result.Failure("更新权限失败");
        }
    }

    public async Task<Result> EnablePermissionAsync(Guid permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _permissionRepository.GetByIdAsync(permissionId, cancellationToken);
            if (permission == null)
            {
                return Result.Failure("权限不存在");
            }

            permission.Enable();
            
            _permissionRepository.Update(permission);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("启用权限成功，权限ID: {PermissionId}", permissionId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启用权限失败，权限ID: {PermissionId}", permissionId);
            return Result.Failure("启用权限失败");
        }
    }

    public async Task<Result> DisablePermissionAsync(Guid permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _permissionRepository.GetByIdAsync(permissionId, cancellationToken);
            if (permission == null)
            {
                return Result.Failure("权限不存在");
            }

            permission.Disable();
            
            _permissionRepository.Update(permission);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("禁用权限成功，权限ID: {PermissionId}", permissionId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "禁用权限失败，权限ID: {PermissionId}", permissionId);
            return Result.Failure("禁用权限失败");
        }
    }

    public async Task<Result> DeletePermissionAsync(Guid permissionId, Guid operatorId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _permissionRepository.GetByIdAsync(permissionId, cancellationToken);
            if (permission == null)
            {
                return Result.Failure("权限不存在");
            }

            if (permission.IsSystem)
            {
                return Result.Failure("系统权限不可删除");
            }

            _permissionRepository.Remove(permission);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("删除权限成功，权限ID: {PermissionId}, 操作者ID: {OperatorId}", permissionId, operatorId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除权限失败，权限ID: {PermissionId}", permissionId);
            return Result.Failure("删除权限失败");
        }
    }

    public async Task<Result> InitializeSystemPermissionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var systemPermissions = GetSystemPermissions();
            
            foreach (var (code, name, category, description) in systemPermissions)
            {
                if (!await _permissionRepository.ExistsAsync(code, null, cancellationToken))
                {
                    var permission = new Permission(code, name, category, description, null, true);
                    await _permissionRepository.AddAsync(permission, cancellationToken);
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("初始化系统权限成功");
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化系统权限失败");
            return Result.Failure("初始化系统权限失败");
        }
    }

    private List<PermissionTreeDto> BuildPermissionTree(IReadOnlyList<Permission> permissions)
    {
        var lookup = permissions.ToDictionary(p => p.Id);
        var roots = new List<PermissionTreeDto>();

        foreach (var permission in permissions)
        {
            var dto = MapToTreeDto(permission);
            
            if (permission.ParentId == null)
            {
                roots.Add(dto);
            }
            else if (lookup.TryGetValue(permission.ParentId.Value, out var parent))
            {
                var parentDto = roots.FirstOrDefault(r => r.Id == parent.Id) 
                    ?? FindInTree(roots, parent.Id);
                    
                if (parentDto != null)
                {
                    parentDto.Children.Add(dto);
                }
            }
        }

        return roots.OrderBy(r => r.DisplayOrder).ThenBy(r => r.Code).ToList();
    }

    private PermissionTreeDto? FindInTree(List<PermissionTreeDto> nodes, Guid id)
    {
        foreach (var node in nodes)
        {
            if (node.Id == id) return node;
            
            var found = FindInTree(node.Children, id);
            if (found != null) return found;
        }
        
        return null;
    }

    private PermissionDto MapToDto(Permission permission)
    {
        return new PermissionDto
        {
            Id = permission.Id,
            Code = permission.Code,
            Name = permission.Name,
            Description = permission.Description,
            Category = permission.Category,
            ParentId = permission.ParentId,
            DisplayOrder = permission.DisplayOrder,
            IsEnabled = permission.IsEnabled,
            IsSystem = permission.IsSystem
        };
    }

    private PermissionTreeDto MapToTreeDto(Permission permission)
    {
        return new PermissionTreeDto
        {
            Id = permission.Id,
            Code = permission.Code,
            Name = permission.Name,
            Description = permission.Description,
            Category = permission.Category,
            ParentId = permission.ParentId,
            DisplayOrder = permission.DisplayOrder,
            IsEnabled = permission.IsEnabled,
            IsSystem = permission.IsSystem,
            Children = new List<PermissionTreeDto>()
        };
    }

    private List<(string code, string name, string category, string description)> GetSystemPermissions()
    {
        return new List<(string, string, string, string)>
        {
            // 用户管理权限
            ("users:customers:read", "查看客户列表", "users", "查看客户用户列表和详情"),
            ("users:customers:update_status", "修改客户状态", "users", "启用、禁用、封禁客户账号"),
            ("users:customers:update", "编辑客户信息", "users", "修改客户资料"),
            ("users:customers:reset_password", "重置客户密码", "users", "重置客户登录密码"),
            
            ("users:admins:read", "查看管理员列表", "users", "查看管理员列表和详情"),
            ("users:admins:create", "创建管理员", "users", "创建新的管理员账号"),
            ("users:admins:update", "编辑管理员", "users", "修改管理员信息"),
            ("users:admins:delete", "删除管理员", "users", "删除管理员账号"),
            ("users:admins:update_status", "修改管理员状态", "users", "启用、禁用管理员账号"),
            ("users:admins:assign_roles", "分配角色", "users", "为管理员分配角色"),
            
            // 角色权限管理
            ("roles:read", "查看角色", "roles", "查看角色列表和详情"),
            ("roles:create", "创建角色", "roles", "创建新角色"),
            ("roles:update", "编辑角色", "roles", "修改角色信息"),
            ("roles:delete", "删除角色", "roles", "删除角色"),
            ("roles:assign_permissions", "分配权限", "roles", "为角色分配权限"),
            
            ("permissions:read", "查看权限", "permissions", "查看权限列表和详情"),
            ("permissions:create", "创建权限", "permissions", "创建新权限"),
            ("permissions:update", "编辑权限", "permissions", "修改权限信息"),
            ("permissions:delete", "删除权限", "permissions", "删除权限"),
            
            // 智能体管理
            ("agents:read", "查看智能体", "agents", "查看智能体列表和详情"),
            ("agents:create", "创建智能体", "agents", "创建新的智能体"),
            ("agents:update", "编辑智能体", "agents", "修改智能体配置"),
            ("agents:delete", "删除智能体", "agents", "删除智能体"),
            ("agents:publish", "发布智能体", "agents", "发布智能体到市场"),
            ("agents:test", "测试智能体", "agents", "测试智能体功能"),
            
            // 订阅管理
            ("subscriptions:read", "查看订阅", "subscriptions", "查看订阅列表和详情"),
            ("subscriptions:plans:read", "查看套餐", "subscriptions", "查看订阅套餐"),
            ("subscriptions:plans:create", "创建套餐", "subscriptions", "创建订阅套餐"),
            ("subscriptions:plans:update", "编辑套餐", "subscriptions", "修改订阅套餐"),
            ("subscriptions:plans:delete", "删除套餐", "subscriptions", "删除订阅套餐"),
            
            // 订单支付
            ("orders:read", "查看订单", "orders", "查看订单列表和详情"),
            ("orders:refund", "订单退款", "orders", "处理订单退款"),
            ("payments:read", "查看支付记录", "payments", "查看支付流水"),
            
            // 系统管理
            ("system:config:read", "查看系统配置", "system", "查看系统配置项"),
            ("system:config:update", "修改系统配置", "system", "修改系统配置项"),
            ("system:logs:read", "查看系统日志", "system", "查看操作日志和系统日志"),
            ("system:analytics:read", "查看数据分析", "system", "查看数据分析和报表"),
            ("system:monitoring:read", "查看系统监控", "system", "查看系统监控信息")
        };
    }

    public async Task<Result<List<RolePermissionDto>>> GetGroupedPermissionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _unitOfWork.Permissions.GetAllAsync(cancellationToken);
            
            // 按模块分组
            var groupedPermissions = permissions
                .GroupBy(p => p.Category)
                .Select(g => new RolePermissionDto
                {
                    Module = g.Key,
                    Permissions = g.Select(p => new SimplePermissionDto
                    {
                        Code = p.Code,
                        Name = p.Name,
                        Description = p.Description
                    }).ToList()
                })
                .OrderBy(g => g.Module)
                .ToList();

            return Result<List<RolePermissionDto>>.Success(groupedPermissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分组权限列表失败");
            return Result<List<RolePermissionDto>>.Failure("PERMISSION_GROUP_ERROR", "获取分组权限列表失败");
        }
    }

    public async Task<Result<List<PermissionDto>>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _permissionRepository.GetAllAsync(cancellationToken);
            var dtos = permissions.Select(MapToDto).ToList();
            return Result<List<PermissionDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有权限失败");
            return Result<List<PermissionDto>>.Failure("GET_ALL_PERMISSIONS_ERROR", "获取所有权限失败");
        }
    }

    public async Task<Result<PermissionSyncResultDto>> SyncPermissionsAsync(Guid operatorId, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement permission sync from code attributes/annotations
            // This would scan the codebase for permission attributes and sync with database
            
            var result = new PermissionSyncResultDto
            {
                TotalScanned = 0,
                NewPermissions = 0,
                UpdatedPermissions = 0,
                DeletedPermissions = 0
            };

            _logger.LogInformation("同步权限完成，操作者ID: {OperatorId}", operatorId);
            return Result<PermissionSyncResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步权限失败");
            return Result<PermissionSyncResultDto>.Failure("同步权限失败");
        }
    }

    public async Task<WhimLabAI.Shared.Dtos.Admin.Permission.UserPermissionDto> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement getting user permissions through role assignments
            // This would query user's roles and aggregate all permissions
            
            var userPermissions = new WhimLabAI.Shared.Dtos.Admin.Permission.UserPermissionDto
            {
                UserId = userId,
                PermissionCodes = new List<string>()
            };

            _logger.LogInformation("获取用户权限成功，用户ID: {UserId}", userId);
            return userPermissions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户权限失败，用户ID: {UserId}", userId);
            // Return empty permissions on error
            return new WhimLabAI.Shared.Dtos.Admin.Permission.UserPermissionDto
            {
                UserId = userId,
                PermissionCodes = new List<string>()
            };
        }
    }
}