using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Auth;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos.Admin.Rbac;

namespace WhimLabAI.Application.Services.Rbac;

public class RoleService : IRoleService
{
    private readonly IRoleRepository _roleRepository;
    private readonly IPermissionRepository _permissionRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<RoleService> _logger;

    public RoleService(
        IRoleRepository roleRepository,
        IPermissionRepository permissionRepository,
        IUnitOfWork unitOfWork,
        ILogger<RoleService> logger)
    {
        _roleRepository = roleRepository;
        _permissionRepository = permissionRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<List<RoleDto>>> GetRolesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var roles = await _roleRepository.GetAllAsync(cancellationToken);
            var dtos = roles.Select(MapToDto).ToList();
            return Result<List<RoleDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色列表失败");
            return Result<List<RoleDto>>.Failure("获取角色列表失败");
        }
    }

    public async Task<Result<List<RoleDto>>> GetEnabledRolesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var roles = await _roleRepository.GetEnabledRolesAsync(cancellationToken);
            var dtos = roles.Select(MapToDto).ToList();
            return Result<List<RoleDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取启用的角色列表失败");
            return Result<List<RoleDto>>.Failure("获取角色列表失败");
        }
    }

    public async Task<Result<RoleDetailDto>> GetRoleDetailAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetWithPermissionsAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result<RoleDetailDto>.Failure("角色不存在");
            }

            var dto = MapToDetailDto(role);
            return Result<RoleDetailDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色详情失败，角色ID: {RoleId}", roleId);
            return Result<RoleDetailDto>.Failure("获取角色详情失败");
        }
    }

    public async Task<Result<RoleDto>> CreateRoleAsync(CreateRoleDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查角色代码是否已存在
            if (await _roleRepository.ExistsAsync(request.Code, null, cancellationToken))
            {
                return Result<RoleDto>.Failure("角色代码已存在");
            }

            // 检查角色名称是否已存在
            if (await _roleRepository.NameExistsAsync(request.Name, null, cancellationToken))
            {
                return Result<RoleDto>.Failure("角色名称已存在");
            }

            // 创建角色
            var role = new Role(request.Name, request.Code, request.Description);

            // 分配权限
            if (request.PermissionIds.Any())
            {
                var permissions = await _permissionRepository.GetByIdsAsync(request.PermissionIds, cancellationToken);
                foreach (var permissionId in request.PermissionIds)
                {
                    if (permissions.Any(p => p.Id == permissionId))
                    {
                        role.AssignPermission(permissionId);
                    }
                }
            }

            await _roleRepository.AddAsync(role, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("创建角色成功，角色代码: {Code}", request.Code);
            
            var dto = MapToDto(role);
            return Result<RoleDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建角色失败");
            return Result<RoleDto>.Failure("创建角色失败");
        }
    }

    public async Task<Result> UpdateRoleAsync(Guid roleId, UpdateRoleDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result.Failure("角色不存在");
            }

            // 检查角色名称是否已被其他角色使用
            if (await _roleRepository.NameExistsAsync(request.Name, roleId, cancellationToken))
            {
                return Result.Failure("角色名称已存在");
            }

            role.Update(request.Name, request.Description, request.DisplayOrder);
            
            _roleRepository.Update(role);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("更新角色成功，角色ID: {RoleId}", roleId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新角色失败，角色ID: {RoleId}", roleId);
            return Result.Failure("更新角色失败");
        }
    }

    public async Task<Result> UpdateRolePermissionsAsync(Guid roleId, UpdateRolePermissionsDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetWithPermissionsAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result.Failure("角色不存在");
            }

            // 验证所有权限ID是否有效
            var permissions = await _permissionRepository.GetByIdsAsync(request.PermissionIds, cancellationToken);
            var validPermissionIds = permissions.Select(p => p.Id).ToList();
            var invalidIds = request.PermissionIds.Except(validPermissionIds).ToList();
            
            if (invalidIds.Any())
            {
                return Result.Failure($"无效的权限ID: {string.Join(", ", invalidIds)}");
            }

            // 更新角色权限
            role.UpdatePermissions(request.PermissionIds);
            
            _roleRepository.Update(role);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("更新角色权限成功，角色ID: {RoleId}", roleId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新角色权限失败，角色ID: {RoleId}", roleId);
            return Result.Failure("更新角色权限失败");
        }
    }

    public async Task<Result> EnableRoleAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result.Failure("角色不存在");
            }

            role.Enable();
            
            _roleRepository.Update(role);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("启用角色成功，角色ID: {RoleId}", roleId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启用角色失败，角色ID: {RoleId}", roleId);
            return Result.Failure("启用角色失败");
        }
    }

    public async Task<Result> DisableRoleAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result.Failure("角色不存在");
            }

            role.Disable();
            
            _roleRepository.Update(role);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("禁用角色成功，角色ID: {RoleId}", roleId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "禁用角色失败，角色ID: {RoleId}", roleId);
            return Result.Failure("禁用角色失败");
        }
    }

    public async Task<Result> DeleteRoleAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Result.Failure("角色不存在");
            }

            if (role.IsSystem)
            {
                return Result.Failure("系统角色不可删除");
            }

            _roleRepository.Remove(role);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("删除角色成功，角色ID: {RoleId}", roleId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除角色失败，角色ID: {RoleId}", roleId);
            return Result.Failure("删除角色失败");
        }
    }

    public async Task<Result> InitializeSystemRolesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var systemRoles = GetSystemRoles();
            
            foreach (var (code, name, description, permissionCodes) in systemRoles)
            {
                if (!await _roleRepository.ExistsAsync(code, null, cancellationToken))
                {
                    var role = new Role(name, code, description, true);
                    
                    // 分配权限
                    foreach (var permissionCode in permissionCodes)
                    {
                        var permission = await _permissionRepository.GetByCodeAsync(permissionCode, cancellationToken);
                        if (permission != null)
                        {
                            role.AssignPermission(permission.Id);
                        }
                    }
                    
                    await _roleRepository.AddAsync(role, cancellationToken);
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("初始化系统角色成功");
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化系统角色失败");
            return Result.Failure("初始化系统角色失败");
        }
    }

    private RoleDto MapToDto(Role role)
    {
        return new RoleDto
        {
            Id = role.Id,
            Name = role.Name,
            Code = role.Code,
            Description = role.Description,
            IsEnabled = role.IsEnabled,
            IsSystem = role.IsSystem,
            DisplayOrder = role.DisplayOrder,
            CreatedAt = role.CreatedAt,
            UpdatedAt = role.UpdatedAt
        };
    }

    private RoleDetailDto MapToDetailDto(Role role)
    {
        var dto = new RoleDetailDto
        {
            Id = role.Id,
            Name = role.Name,
            Code = role.Code,
            Description = role.Description,
            IsEnabled = role.IsEnabled,
            IsSystem = role.IsSystem,
            DisplayOrder = role.DisplayOrder,
            CreatedAt = role.CreatedAt,
            UpdatedAt = role.UpdatedAt,
            Permissions = new List<PermissionDto>()
        };

        if (role.Permissions != null)
        {
            dto.Permissions = role.Permissions
                .Where(rp => rp.Permission != null)
                .Select(rp => new PermissionDto
                {
                    Id = rp.Permission!.Id,
                    Code = rp.Permission.Code,
                    Name = rp.Permission.Name,
                    Description = rp.Permission.Description,
                    Category = rp.Permission.Category,
                    ParentId = rp.Permission.ParentId,
                    DisplayOrder = rp.Permission.DisplayOrder,
                    IsEnabled = rp.Permission.IsEnabled,
                    IsSystem = rp.Permission.IsSystem
                })
                .ToList();
        }

        return dto;
    }

    private List<(string code, string name, string description, List<string> permissions)> GetSystemRoles()
    {
        return new List<(string, string, string, List<string>)>
        {
            (
                "super_admin",
                "超级管理员",
                "拥有系统所有权限，可管理其他管理员",
                new List<string>() // 超级管理员在代码中特殊处理，拥有所有权限
            ),
            (
                "operations_admin",
                "运营管理员",
                "管理用户、智能体市场、订阅套餐",
                new List<string>
                {
                    "users:customers:read",
                    "users:customers:update_status",
                    "users:customers:update",
                    "users:customers:reset_password",
                    "agents:read",
                    "agents:create",
                    "agents:update",
                    "agents:publish",
                    "agents:test",
                    "subscriptions:read",
                    "subscriptions:plans:read",
                    "subscriptions:plans:create",
                    "subscriptions:plans:update",
                    "system:analytics:read"
                }
            ),
            (
                "finance_admin",
                "财务管理员",
                "管理订单、支付、退款、财务报表",
                new List<string>
                {
                    "users:customers:read",
                    "subscriptions:read",
                    "subscriptions:plans:read",
                    "orders:read",
                    "orders:refund",
                    "payments:read",
                    "system:analytics:read"
                }
            ),
            (
                "technical_support",
                "技术支持",
                "查看用户信息、处理技术问题",
                new List<string>
                {
                    "users:customers:read",
                    "users:customers:reset_password",
                    "agents:read",
                    "agents:test",
                    "subscriptions:read",
                    "system:logs:read",
                    "system:monitoring:read"
                }
            ),
            (
                "read_only_observer",
                "只读观察员",
                "只能查看数据，不能进行任何修改操作",
                new List<string>
                {
                    "users:customers:read",
                    "users:admins:read",
                    "roles:read",
                    "permissions:read",
                    "agents:read",
                    "subscriptions:read",
                    "subscriptions:plans:read",
                    "orders:read",
                    "payments:read",
                    "system:config:read",
                    "system:logs:read",
                    "system:analytics:read",
                    "system:monitoring:read"
                }
            )
        };
    }
}