using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MathNet.Numerics;
using MathNet.Numerics.LinearAlgebra;
using MathNet.Numerics.Statistics;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.DTOs.Analytics;

namespace WhimLabAI.Application.Services.Analytics;

/// <summary>
/// 预测分析服务实现
/// </summary>
public class PredictiveAnalyticsService : IPredictiveAnalyticsService
{
    private readonly ILogger<PredictiveAnalyticsService> _logger;
    
    public PredictiveAnalyticsService(ILogger<PredictiveAnalyticsService> logger)
    {
        _logger = logger;
    }
    
    public async Task<LTVPrediction> PredictUserLTVAsync(
        Guid userId, 
        UserLTVHistoricalData historicalData,
        CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            _logger.LogInformation("Predicting LTV for user {UserId}", userId);
            
            // 计算历史价值指标
            var totalHistoricalRevenue = historicalData.Orders.Sum(o => o.Amount);
            var activeMonths = CalculateActiveMonths(historicalData);
            var averageMonthlyRevenue = activeMonths > 0 ? totalHistoricalRevenue / activeMonths : 0;
            
            // 计算订阅价值
            var activeSubscription = historicalData.Subscriptions
                .FirstOrDefault(s => !s.EndDate.HasValue || s.EndDate > DateTime.UtcNow);
            var subscriptionValue = activeSubscription?.MonthlyAmount ?? 0;
            
            // 计算用户参与度分数
            var engagementScore = CalculateEngagementScore(historicalData);
            
            // 使用加权平均和趋势分析预测LTV
            var trendFactor = CalculateTrendFactor(historicalData.Orders);
            var baseMonthlyValue = (averageMonthlyRevenue * 0.6m + subscriptionValue * 0.4m);
            
            // 应用参与度和趋势调整
            var adjustedMonthlyValue = baseMonthlyValue * (decimal)engagementScore * (decimal)trendFactor;
            
            // 计算不同时期的LTV，考虑流失率
            var churnRate = EstimateChurnRate(historicalData);
            var retention3Month = Math.Pow(1 - churnRate, 3);
            var retention6Month = Math.Pow(1 - churnRate, 6);
            var retention12Month = Math.Pow(1 - churnRate, 12);
            
            var predicted3Month = CalculateLTVWithRetention(adjustedMonthlyValue, 3, retention3Month);
            var predicted6Month = CalculateLTVWithRetention(adjustedMonthlyValue, 6, retention6Month);
            var predicted12Month = CalculateLTVWithRetention(adjustedMonthlyValue, 12, retention12Month);
            
            // 计算置信度
            var confidenceLevel = CalculateConfidenceLevel(historicalData);
            
            return new LTVPrediction
            {
                Predicted3MonthLTV = (double)predicted3Month,
                Predicted6MonthLTV = (double)predicted6Month,
                Predicted12MonthLTV = (double)predicted12Month,
                ConfidenceLevel = confidenceLevel,
                Model = "WeightedAverageWithTrendAndRetention"
            };
        }, cancellationToken);
    }
    
    public async Task<RevenueForecast> PredictRevenueAsync(
        List<RevenueDataPoint> historicalRevenue,
        bool includeSeasonality = true,
        CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            _logger.LogInformation("Predicting revenue with {DataPoints} historical data points", historicalRevenue.Count);
            
            if (historicalRevenue.Count < 7)
            {
                // 数据点太少，使用简单预测
                return CreateSimpleRevenueForecast(historicalRevenue);
            }
            
            // 提取收入序列
            var revenueValues = historicalRevenue.OrderBy(r => r.Date).Select(r => (double)r.Revenue).ToList();
            
            // 分解时间序列
            var trendAnalysis = AnalyzeTimeSeries(revenueValues, 3);
            
            // 检测季节性
            SeasonalityAnalysis seasonality = null;
            if (includeSeasonality && revenueValues.Count >= 14)
            {
                // 尝试检测周季节性和月季节性
                seasonality = DetectSeasonality(revenueValues, 7) ?? DetectSeasonality(revenueValues, 30);
            }
            
            // 计算增长率
            var growthRate = CalculateGrowthRate(revenueValues);
            
            // 生成预测
            var lastRevenue = revenueValues.Last();
            var trend = trendAnalysis.Trend;
            
            var nextMonthForecast = lastRevenue * (1 + growthRate);
            var nextQuarterForecast = 0.0;
            var nextYearForecast = 0.0;
            
            // 应用季节性调整
            if (seasonality != null && seasonality.HasSeasonality)
            {
                var seasonalFactors = seasonality.SeasonalFactors;
                nextMonthForecast *= seasonalFactors[DateTime.UtcNow.Month % seasonalFactors.Count];
            }
            
            // 计算多期预测
            for (int i = 1; i <= 12; i++)
            {
                var monthlyForecast = lastRevenue * Math.Pow(1 + growthRate, i);
                
                if (seasonality != null && seasonality.HasSeasonality)
                {
                    var monthIndex = (DateTime.UtcNow.Month + i - 1) % seasonality.SeasonalFactors.Count;
                    monthlyForecast *= seasonality.SeasonalFactors[monthIndex];
                }
                
                if (i <= 3) nextQuarterForecast += monthlyForecast;
                nextYearForecast += monthlyForecast;
            }
            
            // 计算置信度
            var confidenceLevel = CalculateRevenueConfidence(trendAnalysis, historicalRevenue);
            
            var assumptions = new List<string>
            {
                $"基于{historicalRevenue.Count}个历史数据点",
                $"月增长率: {growthRate:P2}",
                includeSeasonality && seasonality != null ? "包含季节性因素" : "未考虑季节性",
                $"R²: {trendAnalysis.RSquared:F3}"
            };
            
            return new RevenueForecast
            {
                NextMonthForecast = (decimal)nextMonthForecast,
                NextQuarterForecast = (decimal)nextQuarterForecast,
                NextYearForecast = (decimal)nextYearForecast,
                ConfidenceLevel = confidenceLevel,
                Assumptions = assumptions
            };
        }, cancellationToken);
    }
    
    public async Task<ChurnPrediction> PredictUserChurnAsync(
        Guid userId,
        UserActivityData userActivityData,
        CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            _logger.LogInformation("Predicting churn for user {UserId}", userId);
            
            var riskFactors = new List<string>();
            var riskScore = 0.0;
            
            // 1. 最近登录时间分析
            var daysSinceLastLogin = (DateTime.UtcNow - userActivityData.LastLoginDate).TotalDays;
            if (daysSinceLastLogin > 30)
            {
                riskScore += 0.3;
                riskFactors.Add($"超过{daysSinceLastLogin:F0}天未登录");
            }
            else if (daysSinceLastLogin > 14)
            {
                riskScore += 0.2;
                riskFactors.Add("登录频率下降");
            }
            else if (daysSinceLastLogin > 7)
            {
                riskScore += 0.1;
            }
            
            // 2. 登录频率分析
            var expectedLogins = userActivityData.DaysSinceRegistration > 30 ? 10 : 5;
            var loginRatio = (double)userActivityData.LoginFrequencyLast30Days / expectedLogins;
            if (loginRatio < 0.3)
            {
                riskScore += 0.25;
                riskFactors.Add("登录频率极低");
            }
            else if (loginRatio < 0.6)
            {
                riskScore += 0.15;
                riskFactors.Add("登录频率偏低");
            }
            
            // 3. 使用活跃度分析
            if (userActivityData.ConversationsLast30Days == 0)
            {
                riskScore += 0.2;
                riskFactors.Add("30天内无对话");
            }
            else if (userActivityData.ConversationsLast30Days < 5)
            {
                riskScore += 0.1;
                riskFactors.Add("对话活跃度低");
            }
            
            // 4. Token使用分析
            if (userActivityData.TokensUsedLast30Days == 0)
            {
                riskScore += 0.15;
                riskFactors.Add("30天内未使用Token");
            }
            
            // 5. 订阅状态分析
            if (!userActivityData.HasActiveSubscription)
            {
                riskScore += 0.1;
                riskFactors.Add("无活跃订阅");
            }
            
            // 6. 会话时长分析
            if (userActivityData.AverageSessionDuration < 60) // 少于1分钟
            {
                riskScore += 0.1;
                riskFactors.Add("平均会话时长过短");
            }
            
            // 计算最终流失概率（使用逻辑回归模型）
            var churnProbability = 1.0 / (1.0 + Math.Exp(-2.5 * (riskScore - 0.5)));
            
            // 确定风险等级
            string riskLevel;
            if (churnProbability >= 0.7)
                riskLevel = "High";
            else if (churnProbability >= 0.4)
                riskLevel = "Medium";
            else
                riskLevel = "Low";
            
            // 计算置信度
            var confidenceLevel = CalculateChurnConfidence(userActivityData);
            
            return new ChurnPrediction
            {
                ChurnProbability = churnProbability,
                RiskLevel = riskLevel,
                RiskFactors = riskFactors,
                PredictionDate = DateTime.UtcNow,
                ConfidenceLevel = confidenceLevel
            };
        }, cancellationToken);
    }
    
    public async Task<TokenUsagePrediction> PredictTokenUsageAsync(
        Guid? userId,
        List<TokenUsageDataPoint> historicalUsage,
        CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            _logger.LogInformation("Predicting token usage for {Target}", 
                userId.HasValue ? $"user {userId}" : "overall");
            
            if (historicalUsage.Count < 7)
            {
                return CreateSimpleTokenPrediction(historicalUsage);
            }
            
            // 提取使用量序列
            var usageValues = historicalUsage.OrderBy(u => u.Date).Select(u => (double)u.TokensUsed).ToList();
            var costValues = historicalUsage.OrderBy(u => u.Date).Select(u => (double)u.Cost).ToList();
            
            // 分析趋势
            var trendAnalysis = AnalyzeTimeSeries(usageValues, 3);
            
            // 计算增长率（使用指数平滑）
            var growthRate = CalculateExponentialGrowthRate(usageValues);
            
            // 检测使用模式（工作日vs周末）
            var weekdayPattern = AnalyzeWeekdayPattern(historicalUsage);
            
            // 基础预测
            var lastUsage = usageValues.Last();
            var avgCostPerToken = costValues.Sum() / usageValues.Sum();
            
            // 应用增长率和模式调整
            var weeklyMultiplier = CalculateWeeklyMultiplier(weekdayPattern);
            var nextWeekPrediction = lastUsage * 7 * weeklyMultiplier * (1 + growthRate / 4);
            var nextMonthPrediction = lastUsage * 30 * weeklyMultiplier * Math.Pow(1 + growthRate, 1);
            var nextQuarterPrediction = lastUsage * 90 * weeklyMultiplier * Math.Pow(1 + growthRate, 3);
            
            // 计算成本预测
            var estimatedCostNextMonth = nextMonthPrediction * avgCostPerToken;
            var estimatedCostNextQuarter = nextQuarterPrediction * avgCostPerToken;
            
            // 计算置信度
            var confidenceLevel = CalculateUsageConfidence(trendAnalysis, historicalUsage);
            
            var assumptions = new List<string>
            {
                $"基于{historicalUsage.Count}天的历史数据",
                $"增长率: {growthRate:P2}",
                weekdayPattern != null ? "检测到工作日使用模式" : "使用模式均匀",
                $"平均Token成本: ¥{avgCostPerToken:F6}"
            };
            
            return new TokenUsagePrediction
            {
                NextWeekPrediction = (long)nextWeekPrediction,
                NextMonthPrediction = (long)nextMonthPrediction,
                NextQuarterPrediction = (long)nextQuarterPrediction,
                EstimatedCostNextMonth = (decimal)estimatedCostNextMonth,
                EstimatedCostNextQuarter = (decimal)estimatedCostNextQuarter,
                GrowthRate = growthRate,
                ConfidenceLevel = confidenceLevel,
                Assumptions = assumptions
            };
        }, cancellationToken);
    }
    
    public TimeSeriesTrendAnalysis AnalyzeTimeSeries(List<double> dataPoints, int forecastPeriods = 3)
    {
        if (dataPoints.Count < 3)
        {
            return new TimeSeriesTrendAnalysis
            {
                Trend = 0,
                Seasonality = 0,
                Noise = 0,
                ForecastValues = Enumerable.Repeat(dataPoints.LastOrDefault(), forecastPeriods).ToList(),
                MeanAbsoluteError = 0,
                RSquared = 0
            };
        }
        
        // 使用线性回归分析趋势
        var xValues = Enumerable.Range(0, dataPoints.Count).Select(i => (double)i).ToArray();
        var yValues = dataPoints.ToArray();
        
        var regression = Fit.Line(xValues, yValues);
        var intercept = regression.Item1;
        var slope = regression.Item2;
        
        // 计算趋势线值
        var trendValues = xValues.Select(x => intercept + slope * x).ToList();
        
        // 计算季节性成分（残差）
        var residuals = dataPoints.Zip(trendValues, (actual, trend) => actual - trend).ToList();
        var seasonality = residuals.Count > 0 ? residuals.StandardDeviation() : 0;
        
        // 计算噪声
        var noise = CalculateNoise(residuals);
        
        // 生成预测
        var forecastValues = new List<double>();
        for (int i = 0; i < forecastPeriods; i++)
        {
            var nextX = dataPoints.Count + i;
            var forecast = intercept + slope * nextX;
            forecastValues.Add(Math.Max(0, forecast)); // 确保非负
        }
        
        // 计算误差指标
        var mae = CalculateMeanAbsoluteError(dataPoints, trendValues);
        var rSquared = CalculateRSquared(dataPoints, trendValues);
        
        return new TimeSeriesTrendAnalysis
        {
            Trend = slope,
            Seasonality = seasonality,
            Noise = noise,
            ForecastValues = forecastValues,
            MeanAbsoluteError = mae,
            RSquared = rSquared
        };
    }
    
    public SeasonalityAnalysis DetectSeasonality(List<double> dataPoints, int periodLength)
    {
        if (dataPoints.Count < periodLength * 2)
        {
            return new SeasonalityAnalysis
            {
                HasSeasonality = false,
                SeasonalityStrength = 0,
                SeasonalFactors = new List<double>(),
                DetectedPeriod = 0
            };
        }
        
        // 计算每个周期位置的平均值
        var seasonalFactors = new List<double>();
        for (int i = 0; i < periodLength; i++)
        {
            var values = new List<double>();
            for (int j = i; j < dataPoints.Count; j += periodLength)
            {
                values.Add(dataPoints[j]);
            }
            seasonalFactors.Add(values.Average());
        }
        
        // 标准化季节因子
        var overallMean = dataPoints.Average();
        if (overallMean > 0)
        {
            seasonalFactors = seasonalFactors.Select(f => f / overallMean).ToList();
        }
        
        // 计算季节性强度
        var seasonalityStrength = seasonalFactors.StandardDeviation();
        var hasSeasonality = seasonalityStrength > 0.1; // 阈值
        
        return new SeasonalityAnalysis
        {
            HasSeasonality = hasSeasonality,
            SeasonalityStrength = seasonalityStrength,
            SeasonalFactors = seasonalFactors,
            DetectedPeriod = periodLength
        };
    }
    
    #region 辅助方法
    
    private int CalculateActiveMonths(UserLTVHistoricalData historicalData)
    {
        var firstDate = historicalData.FirstActivityDate;
        var lastDate = DateTime.UtcNow;
        
        if (historicalData.Orders.Any())
        {
            var lastOrderDate = historicalData.Orders.Max(o => o.OrderDate);
            if (lastOrderDate > lastDate)
                lastDate = lastOrderDate;
        }
        
        return Math.Max(1, (int)((lastDate - firstDate).TotalDays / 30));
    }
    
    private double CalculateEngagementScore(UserLTVHistoricalData historicalData)
    {
        var score = 1.0;
        
        // 基于对话频率
        var avgConversationsPerMonth = historicalData.TotalConversations / (double)CalculateActiveMonths(historicalData);
        if (avgConversationsPerMonth > 50) score *= 1.3;
        else if (avgConversationsPerMonth > 20) score *= 1.15;
        else if (avgConversationsPerMonth < 5) score *= 0.8;
        
        // 基于Token使用
        var avgTokensPerConversation = historicalData.TotalConversations > 0 
            ? historicalData.TotalTokensUsed / (double)historicalData.TotalConversations 
            : 0;
        if (avgTokensPerConversation > 1000) score *= 1.2;
        else if (avgTokensPerConversation < 100) score *= 0.9;
        
        return Math.Min(2.0, Math.Max(0.5, score));
    }
    
    private double CalculateTrendFactor(List<OrderData> orders)
    {
        if (orders.Count < 2) return 1.0;
        
        var orderedByDate = orders.OrderBy(o => o.OrderDate).ToList();
        var recentOrders = orderedByDate.TakeLast(Math.Min(6, orders.Count)).ToList();
        var olderOrders = orderedByDate.Take(Math.Max(1, orders.Count - 6)).ToList();
        
        var recentAvg = recentOrders.Average(o => o.Amount);
        var olderAvg = olderOrders.Average(o => o.Amount);
        
        if (olderAvg == 0) return 1.0;
        
        var trend = (double)recentAvg / (double)olderAvg;
        return Math.Min(2.0, Math.Max(0.5, trend));
    }
    
    private double EstimateChurnRate(UserLTVHistoricalData historicalData)
    {
        // 基于活跃度估算流失率
        var baseChurnRate = 0.1; // 10%基础流失率
        
        // 有活跃订阅降低流失率
        if (historicalData.Subscriptions.Any(s => !s.EndDate.HasValue))
        {
            baseChurnRate *= 0.5;
        }
        
        // 基于最近活动调整
        if (historicalData.Orders.Any())
        {
            var daysSinceLastOrder = (DateTime.UtcNow - historicalData.Orders.Max(o => o.OrderDate)).TotalDays;
            if (daysSinceLastOrder > 90) baseChurnRate *= 1.5;
            else if (daysSinceLastOrder < 30) baseChurnRate *= 0.7;
        }
        
        return Math.Min(0.5, Math.Max(0.05, baseChurnRate));
    }
    
    private decimal CalculateLTVWithRetention(decimal monthlyValue, int months, double retention)
    {
        decimal total = 0;
        for (int i = 0; i < months; i++)
        {
            total += monthlyValue * (decimal)Math.Pow(retention, i);
        }
        return total;
    }
    
    private double CalculateConfidenceLevel(UserLTVHistoricalData historicalData)
    {
        var confidence = 0.5; // 基础置信度
        
        // 数据点越多，置信度越高
        if (historicalData.Orders.Count > 10) confidence += 0.2;
        else if (historicalData.Orders.Count > 5) confidence += 0.1;
        
        // 活跃时间越长，置信度越高
        var activeMonths = CalculateActiveMonths(historicalData);
        if (activeMonths > 6) confidence += 0.2;
        else if (activeMonths > 3) confidence += 0.1;
        
        // 有订阅增加置信度
        if (historicalData.Subscriptions.Any(s => !s.EndDate.HasValue))
            confidence += 0.1;
        
        return Math.Min(0.95, confidence);
    }
    
    private RevenueForecast CreateSimpleRevenueForecast(List<RevenueDataPoint> historicalRevenue)
    {
        var avgRevenue = historicalRevenue.Average(r => r.Revenue);
        return new RevenueForecast
        {
            NextMonthForecast = avgRevenue * 30,
            NextQuarterForecast = avgRevenue * 90,
            NextYearForecast = avgRevenue * 365,
            ConfidenceLevel = 0.3,
            Assumptions = new List<string> { "数据点不足，使用简单平均预测" }
        };
    }
    
    private double CalculateGrowthRate(List<double> values)
    {
        if (values.Count < 2) return 0;
        
        // 使用复合年增长率(CAGR)方法
        var firstValue = values.First();
        var lastValue = values.Last();
        var periods = values.Count - 1;
        
        if (firstValue <= 0) return 0;
        
        var growthRate = Math.Pow(lastValue / firstValue, 1.0 / periods) - 1;
        return Math.Min(0.5, Math.Max(-0.5, growthRate)); // 限制在-50%到50%之间
    }
    
    private double CalculateExponentialGrowthRate(List<double> values)
    {
        if (values.Count < 3) return 0;
        
        // 使用指数平滑计算增长率，给予近期数据更高权重
        var alpha = 0.3; // 平滑因子
        var smoothedGrowthRates = new List<double>();
        
        for (int i = 1; i < values.Count; i++)
        {
            if (values[i - 1] > 0)
            {
                var growthRate = (values[i] - values[i - 1]) / values[i - 1];
                smoothedGrowthRates.Add(growthRate);
            }
        }
        
        if (!smoothedGrowthRates.Any()) return 0;
        
        // 指数加权平均
        var weightedSum = 0.0;
        var weightSum = 0.0;
        for (int i = 0; i < smoothedGrowthRates.Count; i++)
        {
            var weight = Math.Pow(1 - alpha, smoothedGrowthRates.Count - i - 1);
            weightedSum += smoothedGrowthRates[i] * weight;
            weightSum += weight;
        }
        
        return weightSum > 0 ? weightedSum / weightSum : 0;
    }
    
    private Dictionary<DayOfWeek, double>? AnalyzeWeekdayPattern(List<TokenUsageDataPoint> usage)
    {
        if (usage.Count < 14) return null;
        
        var weekdayUsage = usage.GroupBy(u => u.Date.DayOfWeek)
            .ToDictionary(g => g.Key, g => g.Average(u => u.TokensUsed));
        
        var avgUsage = weekdayUsage.Values.Average();
        if (avgUsage == 0) return null;
        
        // 标准化为相对使用率
        var pattern = weekdayUsage.ToDictionary(kvp => kvp.Key, kvp => kvp.Value / avgUsage);
        
        // 检查是否有明显的模式
        var variance = pattern.Values.StandardDeviation();
        if (variance < 0.1) return null; // 模式不明显
        
        return pattern;
    }
    
    private double CalculateWeeklyMultiplier(Dictionary<DayOfWeek, double>? pattern)
    {
        if (pattern == null) return 1.0;
        
        // 计算加权平均，考虑未来一周的日期分布
        var today = DateTime.UtcNow.DayOfWeek;
        var multiplier = 0.0;
        
        for (int i = 0; i < 7; i++)
        {
            var dayOfWeek = (DayOfWeek)(((int)today + i) % 7);
            multiplier += pattern.GetValueOrDefault(dayOfWeek, 1.0) / 7.0;
        }
        
        return multiplier;
    }
    
    private double CalculateRevenueConfidence(TimeSeriesTrendAnalysis trend, List<RevenueDataPoint> historical)
    {
        var baseConfidence = 0.5;
        
        // R²值越高，置信度越高
        baseConfidence += trend.RSquared * 0.3;
        
        // 数据点越多，置信度越高
        if (historical.Count > 30) baseConfidence += 0.15;
        else if (historical.Count > 14) baseConfidence += 0.1;
        else if (historical.Count > 7) baseConfidence += 0.05;
        
        // 数据稳定性
        var cv = historical.Select(h => (double)h.Revenue).StandardDeviation() / 
                 historical.Average(h => (double)h.Revenue);
        if (cv < 0.2) baseConfidence += 0.1; // 低变异系数
        
        return Math.Min(0.95, baseConfidence);
    }
    
    private double CalculateChurnConfidence(UserActivityData activityData)
    {
        var confidence = 0.6; // 基础置信度
        
        // 注册时间越长，预测越准确
        if (activityData.DaysSinceRegistration > 180) confidence += 0.2;
        else if (activityData.DaysSinceRegistration > 90) confidence += 0.1;
        else if (activityData.DaysSinceRegistration < 30) confidence -= 0.2;
        
        // 有足够的活动数据
        if (activityData.LoginFrequencyLast30Days > 0) confidence += 0.1;
        if (activityData.ConversationsLast30Days > 0) confidence += 0.1;
        
        return Math.Min(0.95, Math.Max(0.3, confidence));
    }
    
    private double CalculateUsageConfidence(TimeSeriesTrendAnalysis trend, List<TokenUsageDataPoint> historical)
    {
        var baseConfidence = 0.5;
        
        // 趋势拟合度
        baseConfidence += trend.RSquared * 0.25;
        
        // 数据完整性
        if (historical.Count > 30) baseConfidence += 0.15;
        else if (historical.Count > 14) baseConfidence += 0.1;
        
        // 使用稳定性
        var usageValues = historical.Select(h => (double)h.TokensUsed).ToList();
        if (usageValues.Any())
        {
            var cv = usageValues.StandardDeviation() / usageValues.Average();
            if (cv < 0.3) baseConfidence += 0.1;
        }
        
        return Math.Min(0.9, baseConfidence);
    }
    
    private TokenUsagePrediction CreateSimpleTokenPrediction(List<TokenUsageDataPoint> usage)
    {
        var avgDailyUsage = usage.Average(u => u.TokensUsed);
        var avgDailyCost = usage.Average(u => u.Cost);
        
        return new TokenUsagePrediction
        {
            NextWeekPrediction = (long)(avgDailyUsage * 7),
            NextMonthPrediction = (long)(avgDailyUsage * 30),
            NextQuarterPrediction = (long)(avgDailyUsage * 90),
            EstimatedCostNextMonth = avgDailyCost * 30,
            EstimatedCostNextQuarter = avgDailyCost * 90,
            GrowthRate = 0,
            ConfidenceLevel = 0.3,
            Assumptions = new List<string> { "数据点不足，使用简单平均预测" }
        };
    }
    
    private double CalculateNoise(List<double> residuals)
    {
        if (residuals.Count < 2) return 0;
        
        // 计算残差的自相关性
        var lag1Correlation = CalculateAutoCorrelation(residuals, 1);
        
        // 噪声水平与自相关性成反比
        return Math.Max(0, 1 - Math.Abs(lag1Correlation));
    }
    
    private double CalculateAutoCorrelation(List<double> series, int lag)
    {
        if (series.Count <= lag) return 0;
        
        var mean = series.Average();
        var variance = series.Select(x => Math.Pow(x - mean, 2)).Average();
        
        if (variance == 0) return 0;
        
        var covariance = 0.0;
        for (int i = lag; i < series.Count; i++)
        {
            covariance += (series[i] - mean) * (series[i - lag] - mean);
        }
        covariance /= (series.Count - lag);
        
        return covariance / variance;
    }
    
    private double CalculateMeanAbsoluteError(List<double> actual, List<double> predicted)
    {
        if (actual.Count != predicted.Count || actual.Count == 0) return 0;
        
        var errors = actual.Zip(predicted, (a, p) => Math.Abs(a - p));
        return errors.Average();
    }
    
    private double CalculateRSquared(List<double> actual, List<double> predicted)
    {
        if (actual.Count != predicted.Count || actual.Count == 0) return 0;
        
        var meanActual = actual.Average();
        var totalSumOfSquares = actual.Sum(a => Math.Pow(a - meanActual, 2));
        var residualSumOfSquares = actual.Zip(predicted, (a, p) => Math.Pow(a - p, 2)).Sum();
        
        if (totalSumOfSquares == 0) return 0;
        
        return 1 - (residualSumOfSquares / totalSumOfSquares);
    }
    
    #endregion
}