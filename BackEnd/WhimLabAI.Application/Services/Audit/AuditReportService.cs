using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos.Audit;

namespace WhimLabAI.Application.Services.Audit;

/// <summary>
/// 审计报表生成服务
/// </summary>
public class AuditReportService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AuditReportService> _logger;
    private readonly AuditAnomalyDetectionService _anomalyDetectionService;

    public AuditReportService(
        IUnitOfWork unitOfWork,
        ILogger<AuditReportService> logger,
        AuditAnomalyDetectionService anomalyDetectionService)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _anomalyDetectionService = anomalyDetectionService;
    }

    /// <summary>
    /// 生成综合审计报表
    /// </summary>
    public async Task<AuditReportDto> GenerateComprehensiveReportAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var report = new AuditReportDto
        {
            ReportType = "Comprehensive",
            GeneratedAt = DateTime.UtcNow,
            StartDate = startDate,
            EndDate = endDate
        };

        // 获取统计数据
        var stats = await _unitOfWork.AuditLogs.GetStatsAsync(startDate, endDate, cancellationToken);
        var userActivities = await _unitOfWork.AuditLogs.GetUserActivitySummaryAsync(startDate, endDate, 20, cancellationToken);

        // 填充摘要
        report.Summary = new AuditReportSummary
        {
            TotalEvents = stats.TotalActions,
            UniqueUsers = stats.UniqueUsers,
            TopOperations = stats.TopActions.Take(10).ToList(),
            TopUsers = userActivities.Take(10)
                .Select(u => new KeyValuePair<string, int>(u.UserName, u.ActionCount))
                .ToList()
        };

        // 添加各个报表章节
        await AddExecutiveSummarySection(report, stats);
        await AddUserActivitySection(report, userActivities);
        await AddSecuritySection(report, startDate, endDate, cancellationToken);
        await AddPerformanceSection(report, startDate, endDate, cancellationToken);
        await AddComplianceSection(report, startDate, endDate, cancellationToken);
        await AddAnomalySection(report, startDate, endDate, cancellationToken);

        return report;
    }

    /// <summary>
    /// 生成安全审计报表
    /// </summary>
    public async Task<AuditReportDto> GenerateSecurityReportAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var report = new AuditReportDto
        {
            ReportType = "Security",
            GeneratedAt = DateTime.UtcNow,
            StartDate = startDate,
            EndDate = endDate
        };

        // 获取安全相关的审计日志
        var securityLogs = await GetSecurityRelatedLogs(startDate, endDate, cancellationToken);
        
        // 统计安全事件
        var securityStats = new
        {
            TotalSecurityEvents = securityLogs.Count,
            FailedLoginAttempts = securityLogs.Count(l => l.Action.Contains("Login") && !l.IsSuccess),
            PasswordChanges = securityLogs.Count(l => l.Action.Contains("Password")),
            PermissionChanges = securityLogs.Count(l => l.Action.Contains("Permission") || l.Action.Contains("Role")),
            HighRiskEvents = securityLogs.Count(l => l.RiskLevel == "High" || l.RiskLevel == "Critical"),
            SuspiciousActivities = securityLogs.Count(l => l.IsSensitive)
        };

        report.Summary = new AuditReportSummary
        {
            SecurityEvents = securityStats.TotalSecurityEvents,
            HighRiskEvents = securityStats.HighRiskEvents,
            FailedOperations = securityStats.FailedLoginAttempts
        };

        // 添加安全报表章节
        report.Sections.Add(new AuditReportSection
        {
            Title = "安全事件概览",
            Type = "chart",
            Data = securityStats
        });

        // 添加失败登录分析
        await AddFailedLoginAnalysis(report, securityLogs);

        // 添加权限变更追踪
        await AddPermissionChangeTracking(report, securityLogs);

        // 添加高风险操作详情
        await AddHighRiskOperationDetails(report, securityLogs);

        return report;
    }

    /// <summary>
    /// 生成合规性审计报表
    /// </summary>
    public async Task<AuditReportDto> GenerateComplianceReportAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var report = new AuditReportDto
        {
            ReportType = "Compliance",
            GeneratedAt = DateTime.UtcNow,
            StartDate = startDate,
            EndDate = endDate
        };

        // 获取合规相关的审计日志
        var complianceLogs = await GetComplianceRelatedLogs(startDate, endDate, cancellationToken);

        // 数据访问审计
        var dataAccessStats = new
        {
            TotalDataAccess = complianceLogs.Count(l => l.Action.Contains("Read") || l.Action.Contains("Export")),
            SensitiveDataAccess = complianceLogs.Count(l => l.IsSensitive),
            DataExports = complianceLogs.Count(l => l.Action.Contains("Export")),
            DataDeletions = complianceLogs.Count(l => l.Action.Contains("Delete")),
            UserDataAccess = complianceLogs.Count(l => l.EntityType == "CustomerUser")
        };

        report.Sections.Add(new AuditReportSection
        {
            Title = "数据访问合规性",
            Type = "table",
            Data = dataAccessStats
        });

        // GDPR合规性检查
        await AddGDPRComplianceSection(report, complianceLogs);

        // 数据保留政策执行情况
        await AddDataRetentionSection(report, startDate, endDate, cancellationToken);

        return report;
    }

    /// <summary>
    /// 生成性能分析报表
    /// </summary>
    public async Task<AuditReportDto> GeneratePerformanceReportAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var report = new AuditReportDto
        {
            ReportType = "Performance",
            GeneratedAt = DateTime.UtcNow,
            StartDate = startDate,
            EndDate = endDate
        };

        var performanceLogs = await GetPerformanceLogs(startDate, endDate, cancellationToken);

        // 计算性能指标
        var performanceMetrics = new
        {
            AverageResponseTime = performanceLogs.Where(l => l.ExecutionDuration.HasValue)
                .Average(l => l.ExecutionDuration!.Value),
            P95ResponseTime = CalculatePercentile(performanceLogs
                .Where(l => l.ExecutionDuration.HasValue)
                .Select(l => l.ExecutionDuration!.Value).ToList(), 95),
            SlowRequests = performanceLogs.Count(l => l.ExecutionDuration > 1000), // > 1秒
            TimeoutRequests = performanceLogs.Count(l => l.ExecutionDuration > 30000), // > 30秒
            SuccessRate = performanceLogs.Count(l => l.IsSuccess) / (double)performanceLogs.Count * 100
        };

        report.Summary.AverageResponseTime = performanceMetrics.AverageResponseTime;

        report.Sections.Add(new AuditReportSection
        {
            Title = "性能指标概览",
            Type = "chart",
            Data = performanceMetrics
        });

        // 添加慢请求分析
        await AddSlowRequestAnalysis(report, performanceLogs);

        // 添加按模块的性能分析
        await AddModulePerformanceAnalysis(report, performanceLogs);

        return report;
    }

    #region Private Helper Methods

    private async Task AddExecutiveSummarySection(AuditReportDto report, dynamic stats)
    {
        var summary = new
        {
            Overview = $"在 {report.StartDate:yyyy-MM-dd} 至 {report.EndDate:yyyy-MM-dd} 期间，系统共记录了 {stats.TotalActions} 条审计日志，涉及 {stats.UniqueUsers} 个独立用户。",
            KeyFindings = new List<string>
            {
                $"最活跃的操作类型：{stats.TopActions.FirstOrDefault().Key ?? "N/A"}",
                $"日均操作数：{stats.TotalActions / Math.Max(1, (report.EndDate - report.StartDate).Days)}",
                $"独立用户数：{stats.UniqueUsers}"
            },
            Recommendations = new List<string>()
        };

        if (stats.TotalActions > 100000)
        {
            summary.Recommendations.Add("建议增加审计日志归档频率以优化性能");
        }

        report.Sections.Add(new AuditReportSection
        {
            Title = "执行摘要",
            Type = "text",
            Data = summary
        });
    }

    private async Task AddUserActivitySection(AuditReportDto report, List<UserActivitySummary> userActivities)
    {
        var topUsers = userActivities.Take(10).Select(u => new
        {
            UserId = u.UserId,
            UserName = u.UserName,
            ActionCount = u.ActionCount,
            LastActivity = u.LastActivityTime
        }).ToList();

        report.Sections.Add(new AuditReportSection
        {
            Title = "用户活动TOP10",
            Type = "table",
            Data = topUsers
        });

        // 添加用户活动分布图
        var activityDistribution = userActivities
            .GroupBy(u => GetActivityLevel(u.ActionCount))
            .Select(g => new { Level = g.Key, Count = g.Count() })
            .ToList();

        report.Sections.Add(new AuditReportSection
        {
            Title = "用户活动分布",
            Type = "chart",
            Data = activityDistribution
        });
    }

    private async Task AddSecuritySection(AuditReportDto report, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var securityLogs = await GetSecurityRelatedLogs(startDate, endDate, cancellationToken);

        var securityMetrics = new
        {
            TotalSecurityEvents = securityLogs.Count,
            CriticalEvents = securityLogs.Count(l => l.RiskLevel == "Critical"),
            HighRiskEvents = securityLogs.Count(l => l.RiskLevel == "High"),
            FailedAuthentications = securityLogs.Count(l => l.Action.Contains("Login") && !l.IsSuccess),
            SuccessfulAuthentications = securityLogs.Count(l => l.Action.Contains("Login") && l.IsSuccess)
        };

        report.Summary.SecurityEvents = securityMetrics.TotalSecurityEvents;
        report.Summary.HighRiskEvents = securityMetrics.HighRiskEvents + securityMetrics.CriticalEvents;

        report.Sections.Add(new AuditReportSection
        {
            Title = "安全指标",
            Type = "chart",
            Data = securityMetrics
        });
    }

    private async Task AddPerformanceSection(AuditReportDto report, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var performanceLogs = await GetPerformanceLogs(startDate, endDate, cancellationToken);

        // 按小时计算平均响应时间
        var hourlyPerformance = performanceLogs
            .Where(l => l.ExecutionDuration.HasValue)
            .GroupBy(l => l.CreatedAt.Hour)
            .Select(g => new
            {
                Hour = g.Key,
                AverageResponseTime = g.Average(l => l.ExecutionDuration!.Value),
                RequestCount = g.Count()
            })
            .OrderBy(h => h.Hour)
            .ToList();

        report.Sections.Add(new AuditReportSection
        {
            Title = "按小时性能分析",
            Type = "chart",
            Data = hourlyPerformance
        });
    }

    private async Task AddComplianceSection(AuditReportDto report, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var complianceLogs = await GetComplianceRelatedLogs(startDate, endDate, cancellationToken);

        var complianceMetrics = new
        {
            DataAccessRequests = complianceLogs.Count(l => l.Action.Contains("Read")),
            DataExportRequests = complianceLogs.Count(l => l.Action.Contains("Export")),
            DataDeletionRequests = complianceLogs.Count(l => l.Action.Contains("Delete")),
            SensitiveDataAccess = complianceLogs.Count(l => l.IsSensitive),
            ComplianceViolations = 0 // 这里应该基于实际的合规规则计算
        };

        report.Sections.Add(new AuditReportSection
        {
            Title = "合规性指标",
            Type = "table",
            Data = complianceMetrics
        });
    }

    private async Task AddAnomalySection(AuditReportDto report, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        // 运行异常检测
        var anomalyRequest = new AnomalyDetectionRequestDto
        {
            StartDate = startDate,
            EndDate = endDate,
            SensitivityLevel = 3
        };

        var anomalies = await _anomalyDetectionService.DetectAnomaliesAsync(anomalyRequest, cancellationToken);

        if (anomalies.Anomalies.Any())
        {
            report.Sections.Add(new AuditReportSection
            {
                Title = "检测到的异常行为",
                Type = "table",
                Data = anomalies.Anomalies.Take(10).Select(a => new
                {
                    a.UserName,
                    a.AnomalyType,
                    a.Description,
                    a.Severity,
                    a.DetectedAt
                })
            });

            report.Sections.Add(new AuditReportSection
            {
                Title = "异常行为统计",
                Type = "chart",
                Data = anomalies.AnomaliesByType
            });
        }
    }

    private async Task AddFailedLoginAnalysis(AuditReportDto report, List<Domain.Entities.Audit.AuditLog> securityLogs)
    {
        var failedLogins = securityLogs
            .Where(l => l.Action.Contains("Login") && !l.IsSuccess)
            .GroupBy(l => l.UserName)
            .Select(g => new
            {
                UserName = g.Key,
                FailedAttempts = g.Count(),
                LastAttempt = g.Max(l => l.CreatedAt),
                IpAddresses = g.Select(l => l.IpAddress).Distinct().Count()
            })
            .OrderByDescending(f => f.FailedAttempts)
            .Take(10)
            .ToList();

        report.Sections.Add(new AuditReportSection
        {
            Title = "失败登录分析TOP10",
            Type = "table",
            Data = failedLogins
        });
    }

    private async Task AddPermissionChangeTracking(AuditReportDto report, List<Domain.Entities.Audit.AuditLog> securityLogs)
    {
        var permissionChanges = securityLogs
            .Where(l => l.Action.Contains("Permission") || l.Action.Contains("Role"))
            .Select(l => new
            {
                l.CreatedAt,
                l.UserName,
                l.Action,
                l.EntityType,
                l.EntityId,
                l.Description
            })
            .OrderByDescending(p => p.CreatedAt)
            .Take(20)
            .ToList();

        report.Sections.Add(new AuditReportSection
        {
            Title = "权限变更记录",
            Type = "table",
            Data = permissionChanges
        });
    }

    private async Task AddHighRiskOperationDetails(AuditReportDto report, List<Domain.Entities.Audit.AuditLog> securityLogs)
    {
        var highRiskOps = securityLogs
            .Where(l => l.RiskLevel == "High" || l.RiskLevel == "Critical")
            .Select(l => new
            {
                l.CreatedAt,
                l.UserName,
                l.Action,
                l.Module,
                l.RiskLevel,
                l.IpAddress,
                l.IsSuccess
            })
            .OrderByDescending(h => h.CreatedAt)
            .Take(20)
            .ToList();

        report.Sections.Add(new AuditReportSection
        {
            Title = "高风险操作详情",
            Type = "table",
            Data = highRiskOps
        });
    }

    private async Task AddGDPRComplianceSection(AuditReportDto report, List<Domain.Entities.Audit.AuditLog> complianceLogs)
    {
        var gdprMetrics = new
        {
            DataAccessRequests = complianceLogs.Count(l => l.EntityType == "CustomerUser" && l.Action.Contains("Read")),
            DataExportRequests = complianceLogs.Count(l => l.EntityType == "CustomerUser" && l.Action.Contains("Export")),
            DataDeletionRequests = complianceLogs.Count(l => l.EntityType == "CustomerUser" && l.Action.Contains("Delete")),
            ConsentRecords = complianceLogs.Count(l => l.Action.Contains("Consent")),
            DataBreaches = 0 // 应该基于实际的数据泄露检测
        };

        report.Sections.Add(new AuditReportSection
        {
            Title = "GDPR合规性指标",
            Type = "table",
            Data = gdprMetrics
        });
    }

    private async Task AddDataRetentionSection(AuditReportDto report, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        // 这里应该查询实际的数据保留执行记录
        var retentionMetrics = new
        {
            ScheduledDeletions = 0,
            CompletedDeletions = 0,
            PendingDeletions = 0,
            DataRetentionPolicy = "90天自动删除非敏感审计日志"
        };

        report.Sections.Add(new AuditReportSection
        {
            Title = "数据保留政策执行",
            Type = "text",
            Data = retentionMetrics
        });
    }

    private async Task AddSlowRequestAnalysis(AuditReportDto report, List<Domain.Entities.Audit.AuditLog> performanceLogs)
    {
        var slowRequests = performanceLogs
            .Where(l => l.ExecutionDuration > 1000)
            .Select(l => new
            {
                l.CreatedAt,
                l.Action,
                l.Module,
                ExecutionTime = l.ExecutionDuration,
                l.RequestUrl,
                l.UserName
            })
            .OrderByDescending(s => s.ExecutionTime)
            .Take(20)
            .ToList();

        report.Sections.Add(new AuditReportSection
        {
            Title = "慢请求分析TOP20",
            Type = "table",
            Data = slowRequests
        });
    }

    private async Task AddModulePerformanceAnalysis(AuditReportDto report, List<Domain.Entities.Audit.AuditLog> performanceLogs)
    {
        var modulePerformance = performanceLogs
            .Where(l => l.ExecutionDuration.HasValue)
            .GroupBy(l => l.Module)
            .Select(g => new
            {
                Module = g.Key,
                AverageResponseTime = g.Average(l => l.ExecutionDuration!.Value),
                RequestCount = g.Count(),
                P95ResponseTime = CalculatePercentile(g.Select(l => l.ExecutionDuration!.Value).ToList(), 95),
                SuccessRate = g.Count(l => l.IsSuccess) / (double)g.Count() * 100
            })
            .OrderByDescending(m => m.RequestCount)
            .ToList();

        report.Sections.Add(new AuditReportSection
        {
            Title = "模块性能分析",
            Type = "table",
            Data = modulePerformance
        });
    }

    private async Task<List<Domain.Entities.Audit.AuditLog>> GetSecurityRelatedLogs(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var allLogs = await _unitOfWork.AuditLogs.GetAllAsync(cancellationToken);
        return allLogs
            .Where(l => l.CreatedAt >= startDate && l.CreatedAt <= endDate)
            .Where(l => l.Module == "Security" || l.Module == "Authentication" || 
                       l.Action.Contains("Login") || l.Action.Contains("Permission") ||
                       l.RiskLevel == "High" || l.RiskLevel == "Critical")
            .ToList();
    }

    private async Task<List<Domain.Entities.Audit.AuditLog>> GetComplianceRelatedLogs(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var allLogs = await _unitOfWork.AuditLogs.GetAllAsync(cancellationToken);
        return allLogs
            .Where(l => l.CreatedAt >= startDate && l.CreatedAt <= endDate)
            .Where(l => l.IsSensitive || l.Action.Contains("Export") || 
                       l.Action.Contains("Delete") || l.EntityType == "CustomerUser")
            .ToList();
    }

    private async Task<List<Domain.Entities.Audit.AuditLog>> GetPerformanceLogs(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var allLogs = await _unitOfWork.AuditLogs.GetAllAsync(cancellationToken);
        return allLogs
            .Where(l => l.CreatedAt >= startDate && l.CreatedAt <= endDate)
            .Where(l => l.ExecutionDuration.HasValue)
            .ToList();
    }

    private string GetActivityLevel(int actionCount)
    {
        return actionCount switch
        {
            < 10 => "低活跃",
            < 50 => "中等活跃",
            < 200 => "高活跃",
            _ => "超高活跃"
        };
    }

    private double CalculatePercentile(List<long> values, int percentile)
    {
        if (!values.Any()) return 0;
        
        var sortedValues = values.OrderBy(v => v).ToList();
        var index = (int)Math.Ceiling(percentile / 100.0 * sortedValues.Count) - 1;
        return sortedValues[Math.Max(0, Math.Min(index, sortedValues.Count - 1))];
    }

    #endregion
}