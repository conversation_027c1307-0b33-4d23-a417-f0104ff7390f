using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Audit;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos.Audit;

namespace WhimLabAI.Application.Services.Audit;

/// <summary>
/// 审计日志异常行为检测服务
/// </summary>
public class AuditAnomalyDetectionService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AuditAnomalyDetectionService> _logger;

    public AuditAnomalyDetectionService(
        IUnitOfWork unitOfWork,
        ILogger<AuditAnomalyDetectionService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<AnomalyDetectionResultDto> DetectAnomaliesAsync(
        AnomalyDetectionRequestDto request,
        CancellationToken cancellationToken = default)
    {
        var result = new AnomalyDetectionResultDto();
        
        // 获取时间范围内的审计日志
        var logs = await GetAuditLogsForAnalysis(request.StartDate, request.EndDate, cancellationToken);
        
        // 按用户分组
        var userLogs = logs.GroupBy(l => l.UserId).Where(g => g.Key.HasValue);
        
        foreach (var userGroup in userLogs)
        {
            var userId = userGroup.Key!.Value;
            var userLogsList = userGroup.OrderBy(l => l.CreatedAt).ToList();
            
            // 检测各种异常行为
            if (request.DetectionTypes.Contains("UnusualAccessTime"))
            {
                await DetectUnusualAccessTime(userId, userLogsList, request.SensitivityLevel, result);
            }
            
            if (request.DetectionTypes.Contains("HighFailureRate"))
            {
                await DetectHighFailureRate(userId, userLogsList, request.SensitivityLevel, result);
            }
            
            if (request.DetectionTypes.Contains("UnusualLocation"))
            {
                await DetectUnusualLocation(userId, userLogsList, request.SensitivityLevel, result);
            }
            
            if (request.DetectionTypes.Contains("RapidSequentialAccess"))
            {
                await DetectRapidSequentialAccess(userId, userLogsList, request.SensitivityLevel, result);
            }
            
            if (request.DetectionTypes.Contains("PrivilegeEscalation"))
            {
                await DetectPrivilegeEscalation(userId, userLogsList, request.SensitivityLevel, result);
            }
            
            if (request.DetectionTypes.Contains("DataExfiltration"))
            {
                await DetectDataExfiltration(userId, userLogsList, request.SensitivityLevel, result);
            }
        }
        
        // 汇总结果
        result.TotalAnomaliesDetected = result.Anomalies.Count;
        result.AnomaliesByType = result.Anomalies
            .GroupBy(a => a.AnomalyType)
            .ToDictionary(g => g.Key, g => g.Count());
        result.AnomaliesBySeverity = result.Anomalies
            .GroupBy(a => a.Severity.ToString())
            .ToDictionary(g => g.Key, g => g.Count());
        
        // 生成推荐操作
        GenerateRecommendedActions(result);
        
        return result;
    }

    private async Task<List<AuditLog>> GetAuditLogsForAnalysis(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        // 这里应该使用 Repository 的查询方法
        // 简化示例，实际应该通过规格模式查询
        var logs = await _unitOfWork.AuditLogs.GetAllAsync(cancellationToken);
        return logs.Where(l => l.CreatedAt >= startDate && l.CreatedAt <= endDate).ToList();
    }

    private async Task DetectUnusualAccessTime(
        Guid userId,
        List<AuditLog> userLogs,
        int sensitivityLevel,
        AnomalyDetectionResultDto result)
    {
        // 分析用户的正常访问时间模式
        var accessHours = userLogs.Select(l => l.CreatedAt.Hour).ToList();
        
        if (accessHours.Count < 10) return; // 数据不足
        
        // 计算正常访问时间范围（使用四分位数）
        var sortedHours = accessHours.OrderBy(h => h).ToList();
        var q1 = sortedHours[sortedHours.Count / 4];
        var q3 = sortedHours[3 * sortedHours.Count / 4];
        var iqr = q3 - q1;
        
        var lowerBound = q1 - 1.5 * iqr * (6 - sensitivityLevel) / 5.0;
        var upperBound = q3 + 1.5 * iqr * (6 - sensitivityLevel) / 5.0;
        
        // 检测异常访问时间
        var recentLogs = userLogs.Where(l => l.CreatedAt >= DateTime.UtcNow.AddDays(-7)).ToList();
        foreach (var log in recentLogs)
        {
            var hour = log.CreatedAt.Hour;
            if (hour < lowerBound || hour > upperBound)
            {
                result.Anomalies.Add(new AnomalousActivityDto
                {
                    UserId = userId,
                    UserName = log.UserName,
                    AnomalyType = "UnusualAccessTime",
                    Description = $"访问时间异常：{log.CreatedAt:HH:mm}，正常时间范围：{(int)lowerBound}:00-{(int)upperBound}:00",
                    Severity = 2,
                    DetectedAt = DateTime.UtcNow,
                    Details = new Dictionary<string, object>
                    {
                        ["AccessTime"] = log.CreatedAt,
                        ["NormalRange"] = $"{(int)lowerBound}:00-{(int)upperBound}:00",
                        ["Action"] = log.Action
                    }
                });
            }
        }
    }

    private async Task DetectHighFailureRate(
        Guid userId,
        List<AuditLog> userLogs,
        int sensitivityLevel,
        AnomalyDetectionResultDto result)
    {
        var recentLogs = userLogs.Where(l => l.CreatedAt >= DateTime.UtcNow.AddHours(-24)).ToList();
        if (recentLogs.Count < 5) return;
        
        var failureRate = recentLogs.Count(l => !l.IsSuccess) / (double)recentLogs.Count;
        var threshold = 0.3 * (6 - sensitivityLevel) / 5.0; // 敏感度越高，阈值越低
        
        if (failureRate > threshold)
        {
            result.Anomalies.Add(new AnomalousActivityDto
            {
                UserId = userId,
                UserName = recentLogs.First().UserName,
                AnomalyType = "HighFailureRate",
                Description = $"操作失败率异常高：{failureRate:P}（阈值：{threshold:P}）",
                Severity = failureRate > 0.5 ? 4 : 3,
                DetectedAt = DateTime.UtcNow,
                Details = new Dictionary<string, object>
                {
                    ["FailureRate"] = failureRate,
                    ["Threshold"] = threshold,
                    ["FailedOperations"] = recentLogs.Where(l => !l.IsSuccess).Count(),
                    ["TotalOperations"] = recentLogs.Count
                }
            });
        }
    }

    private async Task DetectUnusualLocation(
        Guid userId,
        List<AuditLog> userLogs,
        int sensitivityLevel,
        AnomalyDetectionResultDto result)
    {
        // 分析IP地址和地理位置
        var recentLocations = userLogs
            .Where(l => l.CreatedAt >= DateTime.UtcNow.AddDays(-30))
            .Select(l => l.IpAddress)
            .Where(ip => !string.IsNullOrEmpty(ip))
            .Distinct()
            .ToList();
        
        if (recentLocations.Count < 2) return;
        
        // 检测最近24小时内的新IP地址
        var last24HoursLogs = userLogs.Where(l => l.CreatedAt >= DateTime.UtcNow.AddHours(-24)).ToList();
        var historicalIps = userLogs
            .Where(l => l.CreatedAt < DateTime.UtcNow.AddHours(-24))
            .Select(l => l.IpAddress)
            .Distinct()
            .ToHashSet();
        
        foreach (var log in last24HoursLogs)
        {
            if (!string.IsNullOrEmpty(log.IpAddress) && !historicalIps.Contains(log.IpAddress))
            {
                result.Anomalies.Add(new AnomalousActivityDto
                {
                    UserId = userId,
                    UserName = log.UserName,
                    AnomalyType = "UnusualLocation",
                    Description = $"从新的IP地址访问：{log.IpAddress}",
                    Severity = 3,
                    DetectedAt = DateTime.UtcNow,
                    Details = new Dictionary<string, object>
                    {
                        ["NewIpAddress"] = log.IpAddress,
                        ["Action"] = log.Action,
                        ["AccessTime"] = log.CreatedAt
                    }
                });
            }
        }
    }

    private async Task DetectRapidSequentialAccess(
        Guid userId,
        List<AuditLog> userLogs,
        int sensitivityLevel,
        AnomalyDetectionResultDto result)
    {
        var recentLogs = userLogs
            .Where(l => l.CreatedAt >= DateTime.UtcNow.AddHours(-1))
            .OrderBy(l => l.CreatedAt)
            .ToList();
        
        if (recentLogs.Count < 10) return;
        
        // 检测短时间内的大量请求
        var threshold = 50 * (6 - sensitivityLevel) / 5.0; // 根据敏感度调整阈值
        
        if (recentLogs.Count > threshold)
        {
            result.Anomalies.Add(new AnomalousActivityDto
            {
                UserId = userId,
                UserName = recentLogs.First().UserName,
                AnomalyType = "RapidSequentialAccess",
                Description = $"短时间内大量访问：1小时内{recentLogs.Count}次操作",
                Severity = 4,
                DetectedAt = DateTime.UtcNow,
                Details = new Dictionary<string, object>
                {
                    ["RequestCount"] = recentLogs.Count,
                    ["TimeWindow"] = "1 hour",
                    ["Threshold"] = threshold,
                    ["Actions"] = recentLogs.GroupBy(l => l.Action)
                        .ToDictionary(g => g.Key, g => g.Count())
                }
            });
        }
    }

    private async Task DetectPrivilegeEscalation(
        Guid userId,
        List<AuditLog> userLogs,
        int sensitivityLevel,
        AnomalyDetectionResultDto result)
    {
        var privilegedActions = new[] { "Grant", "Revoke", "AdminAccess", "SystemConfig", "RoleAssign" };
        
        var recentPrivilegedActions = userLogs
            .Where(l => l.CreatedAt >= DateTime.UtcNow.AddDays(-1))
            .Where(l => privilegedActions.Any(pa => l.Action.Contains(pa, StringComparison.OrdinalIgnoreCase)))
            .ToList();
        
        if (recentPrivilegedActions.Any())
        {
            // 检查用户历史是否有类似权限操作
            var historicalPrivilegedActions = userLogs
                .Where(l => l.CreatedAt < DateTime.UtcNow.AddDays(-1))
                .Where(l => privilegedActions.Any(pa => l.Action.Contains(pa, StringComparison.OrdinalIgnoreCase)))
                .ToList();
            
            if (!historicalPrivilegedActions.Any())
            {
                result.Anomalies.Add(new AnomalousActivityDto
                {
                    UserId = userId,
                    UserName = recentPrivilegedActions.First().UserName,
                    AnomalyType = "PrivilegeEscalation",
                    Description = "检测到可能的权限提升行为",
                    Severity = 5,
                    DetectedAt = DateTime.UtcNow,
                    Details = new Dictionary<string, object>
                    {
                        ["PrivilegedActions"] = recentPrivilegedActions.Select(l => new
                        {
                            l.Action,
                            l.CreatedAt,
                            l.EntityType,
                            l.EntityId
                        }),
                        ["FirstTimePrivilegedAccess"] = true
                    }
                });
            }
        }
    }

    private async Task DetectDataExfiltration(
        Guid userId,
        List<AuditLog> userLogs,
        int sensitivityLevel,
        AnomalyDetectionResultDto result)
    {
        var exportActions = new[] { "Export", "Download", "Backup", "Transfer" };
        
        var recentExports = userLogs
            .Where(l => l.CreatedAt >= DateTime.UtcNow.AddHours(-24))
            .Where(l => exportActions.Any(ea => l.Action.Contains(ea, StringComparison.OrdinalIgnoreCase)))
            .ToList();
        
        var threshold = 10 * (6 - sensitivityLevel) / 5.0;
        
        if (recentExports.Count > threshold)
        {
            result.Anomalies.Add(new AnomalousActivityDto
            {
                UserId = userId,
                UserName = recentExports.First().UserName,
                AnomalyType = "DataExfiltration",
                Description = $"检测到大量数据导出操作：24小时内{recentExports.Count}次",
                Severity = 5,
                DetectedAt = DateTime.UtcNow,
                Details = new Dictionary<string, object>
                {
                    ["ExportCount"] = recentExports.Count,
                    ["TimeWindow"] = "24 hours",
                    ["Threshold"] = threshold,
                    ["ExportedEntities"] = recentExports
                        .Where(l => !string.IsNullOrEmpty(l.EntityType))
                        .GroupBy(l => l.EntityType)
                        .ToDictionary(g => g.Key!, g => g.Count())
                }
            });
        }
    }

    private void GenerateRecommendedActions(AnomalyDetectionResultDto result)
    {
        if (result.Anomalies.Any(a => a.Severity >= 4))
        {
            result.RecommendedActions.Add("立即审查高严重性异常活动");
            result.RecommendedActions.Add("考虑暂时限制相关用户的访问权限");
        }
        
        if (result.Anomalies.Any(a => a.AnomalyType == "PrivilegeEscalation"))
        {
            result.RecommendedActions.Add("审查最近的权限变更");
            result.RecommendedActions.Add("验证权限变更是否经过适当授权");
        }
        
        if (result.Anomalies.Any(a => a.AnomalyType == "DataExfiltration"))
        {
            result.RecommendedActions.Add("检查数据导出日志");
            result.RecommendedActions.Add("验证导出的数据是否包含敏感信息");
            result.RecommendedActions.Add("考虑实施数据导出限制策略");
        }
        
        if (result.Anomalies.Any(a => a.AnomalyType == "UnusualLocation"))
        {
            result.RecommendedActions.Add("验证新IP地址的合法性");
            result.RecommendedActions.Add("考虑启用基于地理位置的访问控制");
        }
        
        if (result.TotalAnomaliesDetected > 10)
        {
            result.RecommendedActions.Add("增强系统监控和警报机制");
            result.RecommendedActions.Add("考虑进行全面的安全审计");
        }
    }
}