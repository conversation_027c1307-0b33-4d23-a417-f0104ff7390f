using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Entities.Audit;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Specifications;
using WhimLabAI.Shared.Dtos.Audit;

namespace WhimLabAI.Application.Services.Audit;

/// <summary>
/// 审计日志应用层服务
/// 负责业务逻辑协调和DTO映射
/// </summary>
public class AuditLogApplicationService : IAuditLogApplicationService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AuditLogApplicationService> _logger;
    private readonly ILoggerFactory _loggerFactory;

    public AuditLogApplicationService(
        IUnitOfWork unitOfWork,
        ILogger<AuditLogApplicationService> logger,
        ILoggerFactory loggerFactory)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _loggerFactory = loggerFactory;
    }

    public async Task<AuditLogDto> CreateAsync(CreateAuditLogDto createDto, CancellationToken cancellationToken = default)
    {
        var auditLog = new AuditLog
        {
            UserId = createDto.UserId,
            UserName = createDto.UserName,
            UserType = createDto.UserType,
            Action = createDto.Action,
            Module = createDto.Module,
            Description = createDto.Description,
            EntityType = createDto.EntityType,
            EntityId = createDto.EntityId,
            OldValues = createDto.OldValues,
            NewValues = createDto.NewValues,
            ChangedProperties = createDto.ChangedProperties,
            IpAddress = createDto.IpAddress,
            UserAgent = createDto.UserAgent,
            RequestId = createDto.RequestId,
            CorrelationId = createDto.CorrelationId,
            JwtId = createDto.JwtId,
            HttpMethod = createDto.HttpMethod,
            RequestUrl = createDto.RequestUrl,
            RequestParameters = createDto.RequestParameters,
            ResponseStatusCode = createDto.ResponseStatusCode,
            ExecutionDuration = createDto.ExecutionDuration,
            IsSuccess = createDto.IsSuccess,
            ErrorMessage = createDto.ErrorMessage,
            RiskLevel = createDto.RiskLevel,
            IsSensitive = createDto.IsSensitive,
            ClientInfo = createDto.ClientInfo,
            GeoLocation = createDto.GeoLocation,
            AdditionalData = createDto.AdditionalData
        };

        await _unitOfWork.AuditLogs.AddAsync(auditLog, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return MapToDto(auditLog);
    }

    public async Task<AuditLogQueryResultDto> QueryAsync(AuditLogQueryDto queryDto, CancellationToken cancellationToken = default)
    {
        var specification = BuildSpecification(queryDto);
        
        var totalCount = await _unitOfWork.AuditLogs.CountAsync(specification, cancellationToken);
        var items = await _unitOfWork.AuditLogs.GetListAsync(
            specification,
            queryDto.PageNumber,
            queryDto.PageSize,
            "CreatedAt DESC",
            cancellationToken);

        return new AuditLogQueryResultDto
        {
            Items = items.Select(MapToDto).ToList(),
            Total = totalCount,
            PageNumber = queryDto.PageNumber,
            PageSize = queryDto.PageSize
        };
    }

    public async Task<AuditLogDto?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var auditLog = await _unitOfWork.AuditLogs.GetByIdAsync(id, cancellationToken);
        return auditLog == null ? null : MapToDto(auditLog);
    }

    public async Task<List<UserActivitySummaryDto>> GetUserActivitySummaryAsync(
        DateTime startDate,
        DateTime endDate,
        int topCount = 10,
        CancellationToken cancellationToken = default)
    {
        var summaries = await _unitOfWork.AuditLogs.GetUserActivitySummaryAsync(startDate, endDate, topCount, cancellationToken);
        
        return summaries.Select(s => new UserActivitySummaryDto
        {
            UserId = Guid.Parse(s.UserId),
            StartTime = startDate,
            EndTime = endDate,
            TotalActions = s.ActionCount,
            SuccessfulActions = s.ActionCount, // This would need proper implementation
            FailedActions = 0,
            LastActivityTime = s.LastActivityTime
        }).ToList();
    }

    public async Task<AuditLogStatsDto> GetStatsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var stats = await _unitOfWork.AuditLogs.GetStatsAsync(startDate, endDate, cancellationToken);
        
        return new AuditLogStatsDto
        {
            StatDate = DateTime.UtcNow,
            TotalLogs = stats.TotalActions,
            UniqueUsers = stats.UniqueUsers,
            SuccessfulActions = stats.TotalActions, // This would need proper implementation
            FailedActions = 0,
            SecurityEvents = 0,
            HighRiskEvents = 0,
            CriticalRiskEvents = 0,
            ActionsByModule = stats.TopActions,
            ActionsByType = new Dictionary<string, int>(),
            ActionsByRiskLevel = new Dictionary<string, int>(),
            AverageExecutionTime = 0
        };
    }

    public async Task<byte[]> ExportAsync(AuditLogExportRequestDto exportRequest, CancellationToken cancellationToken = default)
    {
        var queryDto = new AuditLogQueryDto
        {
            UserId = exportRequest.UserId,
            Action = exportRequest.Action,
            StartTime = exportRequest.StartTime,
            EndTime = exportRequest.EndTime,
            PageNumber = 1,
            PageSize = exportRequest.MaxRecords ?? 10000
        };

        var result = await QueryAsync(queryDto, cancellationToken);

        return exportRequest.Format.ToLower() switch
        {
            "csv" => ExportToCsv(result.Items),
            "json" => ExportToJson(result.Items),
            _ => throw new NotSupportedException($"Export format '{exportRequest.Format}' is not supported")
        };
    }

    public async Task CleanupOldLogsAsync(int retentionDays, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
        var deletedCount = await _unitOfWork.AuditLogs.DeleteOldLogsAsync(cutoffDate, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Cleaned up {Count} audit logs older than {Date}", deletedCount, cutoffDate);
    }
    
    public async Task<AnomalyDetectionResultDto> DetectAnomaliesAsync(
        AnomalyDetectionRequestDto request,
        CancellationToken cancellationToken = default)
    {
        // 这个方法应该委托给专门的异常检测服务
        // 这里提供一个简单的实现示例
        var logger = _loggerFactory.CreateLogger<AuditAnomalyDetectionService>();
        var anomalyService = new AuditAnomalyDetectionService(_unitOfWork, logger);
        return await anomalyService.DetectAnomaliesAsync(request, cancellationToken);
    }
    
    public async Task<AuditReportDto> GenerateReportAsync(
        string reportType,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        // 这个方法应该委托给专门的报表服务
        // 这里提供一个简单的实现示例
        var anomalyLogger = _loggerFactory.CreateLogger<AuditAnomalyDetectionService>();
        var anomalyService = new AuditAnomalyDetectionService(_unitOfWork, anomalyLogger);
        var reportLogger = _loggerFactory.CreateLogger<AuditReportService>();
        var reportService = new AuditReportService(_unitOfWork, reportLogger, anomalyService);
        
        return reportType.ToLower() switch
        {
            "comprehensive" => await reportService.GenerateComprehensiveReportAsync(startDate, endDate, cancellationToken),
            "security" => await reportService.GenerateSecurityReportAsync(startDate, endDate, cancellationToken),
            "compliance" => await reportService.GenerateComplianceReportAsync(startDate, endDate, cancellationToken),
            "performance" => await reportService.GeneratePerformanceReportAsync(startDate, endDate, cancellationToken),
            _ => throw new NotSupportedException($"Report type '{reportType}' is not supported")
        };
    }
    
    public async Task<AuditLogArchiveResultDto> ArchiveLogsAsync(
        AuditLogArchiveRequestDto request,
        CancellationToken cancellationToken = default)
    {
        // 这个方法应该委托给专门的归档服务
        // 这里提供一个简单的实现示例
        var archiveLogger = _loggerFactory.CreateLogger<AuditArchiveService>();
        var archiveService = new AuditArchiveService(_unitOfWork, archiveLogger, 
            Microsoft.Extensions.Options.Options.Create(new AuditArchiveOptions()));
        return await archiveService.ArchiveLogsAsync(request, cancellationToken);
    }
    
    public async Task<int> BulkCreateAsync(
        List<CreateAuditLogDto> auditLogs,
        CancellationToken cancellationToken = default)
    {
        var entities = auditLogs.Select(dto => 
        {
            var entity = AuditLog.Create(
                dto.Action,
                dto.Module,
                dto.Description,
                dto.UserId,
                dto.UserName,
                dto.UserType
            );
            
            entity.SetEntityInfo(dto.EntityType ?? string.Empty, dto.EntityId ?? string.Empty);
            entity.SetChangeInfo(dto.OldValues, dto.NewValues, dto.ChangedProperties);
            entity.SetRequestInfo(
                dto.HttpMethod ?? string.Empty,
                dto.RequestUrl ?? string.Empty,
                dto.RequestParameters,
                dto.IpAddress,
                dto.UserAgent,
                dto.RequestId,
                dto.CorrelationId,
                dto.JwtId
            );
            
            if (dto.ResponseStatusCode.HasValue && dto.ExecutionDuration.HasValue)
            {
                entity.SetResponseInfo(dto.ResponseStatusCode.Value, dto.ExecutionDuration.Value);
            }
            
            if (!dto.IsSuccess)
            {
                entity.MarkAsFailed(dto.ErrorMessage ?? "Unknown error");
            }
            
            entity.RiskLevel = dto.RiskLevel;
            entity.IsSensitive = dto.IsSensitive;
            entity.ClientInfo = dto.ClientInfo;
            entity.GeoLocation = dto.GeoLocation;
            entity.AdditionalData = dto.AdditionalData;
            
            return entity;
        }).ToList();
        
        await _unitOfWork.AuditLogs.BulkInsertAsync(entities, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return entities.Count;
    }
    
    public async Task<List<AuditLogDto>> GetHighRiskOperationsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var highRiskLogs = await _unitOfWork.AuditLogs.GetHighRiskOperationsAsync(
            startDate, endDate, cancellationToken);
            
        return highRiskLogs.Select(MapToDto).ToList();
    }
    
    public async Task<AuditLogPerformanceMetricsDto> GetPerformanceMetricsAsync(
        CancellationToken cancellationToken = default)
    {
        // 计算性能指标
        var now = DateTime.UtcNow;
        var startOfDay = now.Date;
        
        var todaysLogs = await _unitOfWork.AuditLogs.GetListAsync(
            new AuditLogQuerySpecification(new AuditLogQueryDto 
            { 
                StartTime = startOfDay,
                EndTime = now 
            }),
            1, int.MaxValue, "CreatedAt DESC", cancellationToken);
        
        var metrics = new AuditLogPerformanceMetricsDto
        {
            MetricDate = now,
            AverageWriteTime = 0, // 这需要从实际的写入操作中收集
            AverageQueryTime = 0, // 这需要从实际的查询操作中收集
            WriteQueueDepth = 0, // 这需要从后台服务中获取
            PendingBatches = 0, // 这需要从后台服务中获取
            TotalStorageSize = 0, // 这需要查询数据库表的大小
            WriteSuccessRate = 100.0 // 假设都成功
        };
        
        return metrics;
    }

    #region Private Methods

    private static AuditLogDto MapToDto(AuditLog auditLog)
    {
        return new AuditLogDto
        {
            Id = auditLog.Id,
            CreatedAt = auditLog.CreatedAt,
            UserId = auditLog.UserId,
            UserName = auditLog.UserName,
            UserType = auditLog.UserType,
            Action = auditLog.Action,
            Module = auditLog.Module,
            Description = auditLog.Description,
            EntityType = auditLog.EntityType,
            EntityId = auditLog.EntityId,
            OldValues = auditLog.OldValues,
            NewValues = auditLog.NewValues,
            ChangedProperties = auditLog.ChangedProperties,
            IpAddress = auditLog.IpAddress,
            UserAgent = auditLog.UserAgent,
            RequestId = auditLog.RequestId,
            CorrelationId = auditLog.CorrelationId,
            JwtId = auditLog.JwtId,
            HttpMethod = auditLog.HttpMethod,
            RequestUrl = auditLog.RequestUrl,
            RequestParameters = auditLog.RequestParameters,
            ResponseStatusCode = auditLog.ResponseStatusCode,
            ExecutionDuration = auditLog.ExecutionDuration,
            IsSuccess = auditLog.IsSuccess,
            ErrorMessage = auditLog.ErrorMessage,
            RiskLevel = auditLog.RiskLevel,
            IsSensitive = auditLog.IsSensitive,
            ClientInfo = auditLog.ClientInfo,
            GeoLocation = auditLog.GeoLocation,
            AdditionalData = auditLog.AdditionalData
        };
    }

    private Specification<AuditLog> BuildSpecification(AuditLogQueryDto queryDto)
    {
        var spec = new AuditLogQuerySpecification(queryDto);
        return spec;
    }

    private byte[] ExportToCsv(List<AuditLogDto> logs)
    {
        var csv = new System.Text.StringBuilder();
        csv.AppendLine("Id,UserId,UserName,Action,EntityType,EntityId,IpAddress,CreatedAt,Success");

        foreach (var log in logs)
        {
            csv.AppendLine($"{log.Id},{log.UserId},{log.UserName},{log.Action},{log.EntityType},{log.EntityId},{log.IpAddress},{log.CreatedAt:yyyy-MM-dd HH:mm:ss},{log.IsSuccess}");
        }

        return System.Text.Encoding.UTF8.GetBytes(csv.ToString());
    }

    private byte[] ExportToJson(List<AuditLogDto> logs)
    {
        var json = System.Text.Json.JsonSerializer.Serialize(logs, new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = true
        });
        return System.Text.Encoding.UTF8.GetBytes(json);
    }

    #endregion
}