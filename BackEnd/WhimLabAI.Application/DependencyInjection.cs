using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using FluentValidation;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Application.Services.CustomerAuth;
using WhimLabAI.Application.Services.AdminAuth;
using WhimLabAI.Application.Services.Agent;
using WhimLabAI.Application.Services.Conversation;
using WhimLabAI.Application.Services.Order;
using WhimLabAI.Application.Services.Payment;
using WhimLabAI.Application.Services.Subscription;
using WhimLabAI.Application.Services.CustomerUser;
using WhimLabAI.Application.Services.AdminUser;
using WhimLabAI.Application.Services.Verification;
using WhimLabAI.Application.Services.AI;
using WhimLabAI.Application.Services.KnowledgeBase;
using WhimLabAI.Application.Services.Coupon;
using WhimLabAI.Application.Services.Device;
using WhimLabAI.Application.Services.Audit;
using WhimLabAI.Application.Services.Notification;
using WhimLabAI.Application.Services.Analytics;
using WhimLabAI.Application.Services.Rbac;
using WhimLabAI.Application.Services.Finance;
using WhimLabAI.Abstractions.Application.Services;
// using WhimLabAI.Application.Caching.Decorators;

namespace WhimLabAI.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册认证服务
        services.AddScoped<ICustomerAuthService, CustomerAuthService>();
        services.AddScoped<IAdminAuthService, AdminAuthService>();
        
        // 注册用户服务
        services.AddScoped<ICustomerUserService, CustomerUserService>();
        services.AddScoped<IAdminUserService, AdminUserService>();
        
        // 注册管理员角色权限服务
        services.AddScoped<IRoleService, RoleService>();
        services.AddScoped<IPermissionService, PermissionService>();
        
        // TODO: 缓存装饰器需要根据实际接口重新实现
        // services.AddScoped<CustomerUserService>();
        // services.AddScoped<ICustomerUserService>(provider =>
        // {
        //     var innerService = provider.GetRequiredService<CustomerUserService>();
        //     var cacheService = provider.GetRequiredService<WhimLabAI.Abstractions.Infrastructure.ICacheService>();
        //     var logger = provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<CustomerUserServiceCacheDecorator>>();
        //     return new CustomerUserServiceCacheDecorator(innerService, cacheService, logger);
        // });
        
        // 注册验证服务和配置
        services.Configure<VerificationOptions>(configuration.GetSection("Verification"));
        services.Configure<VerificationCodeOptions>(configuration.GetSection("Verification:Code"));
        services.Configure<CaptchaOptions>(configuration.GetSection("Verification:Captcha"));
        services.AddScoped<IVerificationService, VerificationService>();
        
        // 注册AI Agent服务
        services.AddScoped<IAgentService, AgentService>();
        services.AddScoped<IAgentVersionService, AgentVersionService>();
        services.AddScoped<IAgentMarketplaceService, AgentMarketplaceService>();
        services.AddScoped<IAgentCategoryService, AgentCategoryService>();
        services.AddScoped<IAgentQueryService, AgentQueryService>();
        services.AddScoped<IAgentVersionUsageRecorder, AgentVersionUsageRecorder>();
        services.AddScoped<IAgentRatingService, AgentRatingService>();
        
        // 注册对话服务
        services.AddScoped<IConversationService, ConversationService>();
        services.AddScoped<IMessageSearchService, MessageSearchService>();
        
        // 注册订阅服务
        services.AddScoped<ISubscriptionService, SubscriptionService>();
        services.AddScoped<ISubscriptionPlanService, SubscriptionPlanService>();
        services.AddScoped<IUsageTrackingService, UsageTrackingService>();
        services.AddScoped<ISubscriptionRenewalService, SubscriptionRenewalService>();
        services.AddScoped<ITokenPackageService, TokenPackageService>();
        
        // 注册订单和支付服务
        services.AddScoped<IOrderService, OrderService>();
        services.AddScoped<IPaymentService, PaymentService>();
        
        // 注册发票服务
        services.AddScoped<IInvoiceService, InvoiceService>();
        
        // 注册AI服务
        services.AddScoped<IAIService, AIService>();
        services.AddScoped<IContextManagementService, ContextManagementService>();
        services.AddScoped<ITokenMeteringService, TokenMeteringService>();
        
        // 注册知识库服务
        services.AddScoped<IKnowledgeBaseService, KnowledgeBaseService>();
        services.AddScoped<IDocumentEmbeddingService, DocumentEmbeddingService>();
        services.AddScoped<IVectorSearchService, VectorSearchService>();
        services.AddScoped<IKnowledgeBaseContextService, KnowledgeBaseContextService>();
        
        // 注册优惠券服务
        services.AddScoped<ICouponService, CouponService>();
        
        // 注册设备管理服务
        services.AddScoped<IDeviceManagementService, DeviceManagementService>();
        
        // 注册审计日志服务
        services.AddScoped<IAuditLogApplicationService, AuditLogApplicationService>();
        
        // 注册通知服务
        services.AddScoped<INotificationService, NotificationService>();
        
        // 注册数据分析服务
        services.AddScoped<IDataAnalyticsService, DataAnalyticsService>();
        services.AddScoped<IPredictiveAnalyticsService, PredictiveAnalyticsService>();

        // 注册FluentValidation
        services.AddValidatorsFromAssemblyContaining<CustomerAuthService>();

        return services;
    }
}