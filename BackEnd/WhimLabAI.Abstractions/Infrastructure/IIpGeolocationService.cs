namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// IP地理位置解析服务
/// </summary>
public interface IIpGeolocationService
{
    /// <summary>
    /// 解析IP地址的地理位置信息
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>地理位置信息</returns>
    Task<IpGeolocationInfo?> GetLocationAsync(string ipAddress, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 计算两个地理位置之间的距离（公里）
    /// </summary>
    /// <param name="lat1">纬度1</param>
    /// <param name="lon1">经度1</param>
    /// <param name="lat2">纬度2</param>
    /// <param name="lon2">经度2</param>
    /// <returns>距离（公里）</returns>
    double CalculateDistance(double lat1, double lon1, double lat2, double lon2);
    
    /// <summary>
    /// 检测是否为异常位置
    /// </summary>
    /// <param name="currentLocation">当前位置</param>
    /// <param name="previousLocations">历史位置列表</param>
    /// <param name="thresholdKm">异常距离阈值（公里）</param>
    /// <returns>是否异常</returns>
    bool IsAnomalousLocation(IpGeolocationInfo currentLocation, IEnumerable<IpGeolocationInfo> previousLocations, double thresholdKm = 500);
}

/// <summary>
/// IP地理位置信息
/// </summary>
public class IpGeolocationInfo
{
    public string IpAddress { get; set; } = string.Empty;
    public string? City { get; set; }
    public string? Country { get; set; }
    public string? CountryCode { get; set; }
    public string? Region { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string? TimeZone { get; set; }
    public string? Isp { get; set; }
    public bool IsVpn { get; set; }
    public bool IsProxy { get; set; }
}