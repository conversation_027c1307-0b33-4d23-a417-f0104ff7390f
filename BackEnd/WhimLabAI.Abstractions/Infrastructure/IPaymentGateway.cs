using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 支付网关接口
/// </summary>
public interface IPaymentGateway
{
    /// <summary>
    /// 创建支付订单
    /// </summary>
    Task<Result<PaymentCreateResult>> CreatePaymentAsync(PaymentCreateRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// 查询支付状态
    /// </summary>
    Task<Result<PaymentQueryResult>> QueryPaymentAsync(string paymentNo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理支付回调
    /// </summary>
    Task<Result<PaymentCallbackResult>> ProcessCallbackAsync(Dictionary<string, string> parameters, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发起退款
    /// </summary>
    Task<Result<RefundCreateResult>> RefundAsync(RefundCreateRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// 查询退款状态
    /// </summary>
    Task<Result<RefundQueryResult>> QueryRefundAsync(string refundNo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 关闭交易
    /// </summary>
    Task<Result> ClosePaymentAsync(string paymentNo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取支付方式
    /// </summary>
    PaymentMethod PaymentMethod { get; }

    /// <summary>
    /// 是否支持退款
    /// </summary>
    bool SupportsRefund { get; }
}

/// <summary>
/// 支付创建请求
/// </summary>
public class PaymentCreateRequest
{
    public string OrderNo { get; set; } = string.Empty;
    public string PaymentNo { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "CNY";
    public string Subject { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? UserId { get; set; }
    public string? UserIp { get; set; }
    public string ReturnUrl { get; set; } = string.Empty;
    public string NotifyUrl { get; set; } = string.Empty;
    public Dictionary<string, string> ExtendParams { get; set; } = new();
    public DateTime ExpireTime { get; set; }
}

/// <summary>
/// 支付创建结果
/// </summary>
public class PaymentCreateResult
{
    public string PaymentNo { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public string? PaymentUrl { get; set; }
    public string? QrCode { get; set; }
    public Dictionary<string, string> FormData { get; set; } = new();
    public PaymentCreateType Type { get; set; }
}

public enum PaymentCreateType
{
    Redirect,    // 跳转支付
    QrCode,      // 二维码支付
    Form,        // 表单提交
    InApp        // APP内支付
}

/// <summary>
/// 支付查询结果
/// </summary>
public class PaymentQueryResult
{
    public string PaymentNo { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public TransactionStatus Status { get; set; }
    public decimal Amount { get; set; }
    public DateTime? PaidAt { get; set; }
    public string? BuyerId { get; set; }
    public string? BuyerAccount { get; set; }
    public Dictionary<string, string> ExtendInfo { get; set; } = new();
}

/// <summary>
/// 支付回调结果
/// </summary>
public class PaymentCallbackResult
{
    public string PaymentNo { get; set; } = string.Empty;
    public string TransactionId { get; set; } = string.Empty;
    public TransactionStatus Status { get; set; }
    public decimal Amount { get; set; }
    public DateTime? PaidAt { get; set; }
    public string ResponseMessage { get; set; } = string.Empty;
    public bool NeedResponse { get; set; } = true;
    public string? BuyerId { get; set; }
    public string? BuyerAccount { get; set; }
}

/// <summary>
/// 退款创建请求
/// </summary>
public class RefundCreateRequest
{
    public string RefundNo { get; set; } = string.Empty;
    public string PaymentNo { get; set; } = string.Empty;
    public string TransactionId { get; set; } = string.Empty;
    public decimal RefundAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string NotifyUrl { get; set; } = string.Empty;
    public Dictionary<string, string> ExtendParams { get; set; } = new();
}

/// <summary>
/// 退款创建结果
/// </summary>
public class RefundCreateResult
{
    public string RefundNo { get; set; } = string.Empty;
    public string? RefundTransactionId { get; set; }
    public RefundStatus Status { get; set; }
    public decimal RefundAmount { get; set; }
    public DateTime? RefundedAt { get; set; }
    public string? FailReason { get; set; }
}

/// <summary>
/// 退款查询结果
/// </summary>
public class RefundQueryResult : RefundCreateResult
{
    public string PaymentNo { get; set; } = string.Empty;
    public string PaymentTransactionId { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
}
