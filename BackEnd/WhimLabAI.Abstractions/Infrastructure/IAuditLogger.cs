namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 基础设施层审计日志记录器接口
/// 负责捕获和记录审计事件，不包含业务查询逻辑
/// </summary>
public interface IAuditLogger
{
    /// <summary>
    /// 记录审计日志
    /// </summary>
    Task LogAsync(object auditLog);
    
    /// <summary>
    /// 记录操作审计
    /// </summary>
    Task LogActionAsync(string action, string module, string description, object? additionalData = null);
    
    /// <summary>
    /// 记录实体变更审计
    /// </summary>
    Task LogEntityChangeAsync<TEntity>(string action, TEntity entity, TEntity? oldEntity = default, string? description = null) where TEntity : class;
    
    /// <summary>
    /// 记录安全事件
    /// </summary>
    Task LogSecurityEventAsync(string eventType, string description, string riskLevel = "High", object? details = null);
    
    /// <summary>
    /// 记录数据访问
    /// </summary>
    Task LogDataAccessAsync(string dataType, string operation, string? criteria = null, int? recordCount = null);
}