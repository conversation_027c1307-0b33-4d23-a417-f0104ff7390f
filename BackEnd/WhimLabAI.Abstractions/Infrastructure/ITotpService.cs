namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// Time-based One-Time Password (TOTP) service interface
/// </summary>
public interface ITotpService
{
    /// <summary>
    /// Generate a new secret key for TOTP
    /// </summary>
    /// <returns>Base32 encoded secret key</returns>
    string GenerateSecret();
    
    /// <summary>
    /// Generate TOTP URI for QR code
    /// </summary>
    /// <param name="issuer">Issuer name (e.g., "WhimLabAI")</param>
    /// <param name="accountName">User account identifier</param>
    /// <param name="secret">Base32 encoded secret</param>
    /// <returns>TOTP URI for QR code generation</returns>
    string GenerateTotpUri(string issuer, string accountName, string secret);
    
    /// <summary>
    /// Validate TOTP code
    /// </summary>
    /// <param name="secret">Base32 encoded secret</param>
    /// <param name="code">6-digit TOTP code</param>
    /// <param name="window">Time window tolerance (default: 1 = ±30 seconds)</param>
    /// <returns>True if valid, false otherwise</returns>
    bool ValidateCode(string secret, string code, int window = 1);
    
    /// <summary>
    /// Get current TOTP code for testing/debugging
    /// </summary>
    /// <param name="secret">Base32 encoded secret</param>
    /// <returns>Current 6-digit code</returns>
    string GetCurrentCode(string secret);
    
    /// <summary>
    /// Format secret for manual entry (with spaces for readability)
    /// </summary>
    /// <param name="secret">Base32 encoded secret</param>
    /// <returns>Formatted secret (e.g., "ABCD EFGH IJKL MNOP")</returns>
    string FormatSecretForManualEntry(string secret);
}