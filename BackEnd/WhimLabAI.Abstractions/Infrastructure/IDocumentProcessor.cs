using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 文档处理器接口
/// </summary>
public interface IDocumentProcessor
{
    /// <summary>
    /// 提取文档内容
    /// </summary>
    Task<Result<DocumentContent>> ExtractContentAsync(
        Stream fileStream,
        string fileName,
        DocumentType documentType,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 分块文档内容
    /// </summary>
    Task<Result<IEnumerable<DocumentChunkData>>> ChunkDocumentAsync(
        DocumentContent content,
        ChunkingStrategy strategy,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 计算内容哈希
    /// </summary>
    string ComputeHash(string content);
    
    /// <summary>
    /// 计算Token数量
    /// </summary>
    Task<int> CountTokensAsync(string text, string model, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查文档类型是否支持
    /// </summary>
    bool IsDocumentTypeSupported(DocumentType documentType);
}

/// <summary>
/// 文档内容
/// </summary>
public class DocumentContent
{
    public string Text { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<DocumentSection> Sections { get; set; } = new();
}

/// <summary>
/// 文档段落
/// </summary>
public class DocumentSection
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public int PageNumber { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 文档块数据
/// </summary>
public class DocumentChunkData
{
    public int Index { get; set; }
    public string Content { get; set; } = string.Empty;
    public string Hash { get; set; } = string.Empty;
    public int CharacterCount { get; set; }
    public int TokenCount { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 分块策略
/// </summary>
public class ChunkingStrategy
{
    public int MaxChunkSize { get; set; } = 1000;
    public int ChunkOverlap { get; set; } = 200;
    public bool PreserveWordBoundaries { get; set; } = true;
    public bool PreserveSentenceBoundaries { get; set; } = true;
    public string? Separator { get; set; }
    public Dictionary<string, object> AdditionalConfig { get; set; } = new();
}