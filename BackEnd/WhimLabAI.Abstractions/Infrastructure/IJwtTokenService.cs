using System.Security.Claims;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// JWT令牌服务接口
/// </summary>
public interface IJwtTokenService
{
    /// <summary>
    /// 生成访问令牌
    /// </summary>
    string GenerateAccessToken(Guid userId, string username, UserType userType, List<string>? roles = null, Guid? sessionId = null);
    
    /// <summary>
    /// 生成刷新令牌
    /// </summary>
    string GenerateRefreshToken();
    
    /// <summary>
    /// 生成Customer QR码登录令牌（5分钟有效期）
    /// </summary>
    string GenerateCustomerQRCodeToken(Dictionary<string, object> deviceInfo, int expirationMinutes = 5);
    
    /// <summary>
    /// 生成Admin双因素认证挑战令牌（5分钟有效期）
    /// </summary>
    string GenerateAdminTwoFactorChallengeToken(Guid adminId, int expirationMinutes = 5);
    
    /// <summary>
    /// 生成密码修改令牌（30分钟有效期）
    /// </summary>
    string GeneratePasswordChangeToken(Guid userId, int expirationMinutes = 30);
    
    /// <summary>
    /// 验证令牌
    /// </summary>
    ClaimsPrincipal ValidateToken(string token);
    
    /// <summary>
    /// 从令牌中获取用户ID
    /// </summary>
    Guid GetUserIdFromToken(ClaimsPrincipal principal);
    
    /// <summary>
    /// 从令牌中获取用户名
    /// </summary>
    string GetUsernameFromToken(ClaimsPrincipal principal);
    
    /// <summary>
    /// 从令牌中获取用户类型
    /// </summary>
    UserType GetUserTypeFromToken(ClaimsPrincipal principal);
    
    /// <summary>
    /// 从令牌中获取角色列表
    /// </summary>
    List<string> GetRolesFromToken(ClaimsPrincipal principal);
    
    /// <summary>
    /// 从令牌中获取令牌类型
    /// </summary>
    string GetTokenTypeFromToken(ClaimsPrincipal principal);
    
    /// <summary>
    /// 获取刷新令牌过期时间
    /// </summary>
    DateTime GetRefreshTokenExpiration();
}