namespace WhimLabAI.Abstractions.Infrastructure;

public interface IStorageService
{
    Task<UploadResult> UploadFileAsync(UploadFileRequest request, CancellationToken cancellationToken = default);
    Task<Stream?> DownloadFileAsync(string fileKey, CancellationToken cancellationToken = default);
    Task<bool> DeleteFileAsync(string fileKey, CancellationToken cancellationToken = default);
    Task<string> GetFileUrlAsync(string fileKey, int? expiryMinutes = null, CancellationToken cancellationToken = default);
    Task<FileInfo?> GetFileInfoAsync(string fileKey, CancellationToken cancellationToken = default);
    Task<bool> FileExistsAsync(string fileKey, CancellationToken cancellationToken = default);
    Task<List<FileInfo>> ListFilesAsync(string? prefix = null, int maxResults = 100, CancellationToken cancellationToken = default);
}

public class UploadFileRequest
{
    public Stream FileStream { get; set; } = Stream.Null;
    public string FileName { get; set; } = string.Empty;
    public string? ContentType { get; set; }
    public string? BucketName { get; set; }
    public string? ObjectPath { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
    public StorageClass StorageClass { get; set; } = StorageClass.Standard;
    public bool IsPublic { get; set; }
}

public class UploadResult
{
    public bool Success { get; set; }
    public string? FileKey { get; set; }
    public string? FileUrl { get; set; }
    public long FileSize { get; set; }
    public string? ETag { get; set; }
    public string? ErrorMessage { get; set; }
}

public class FileInfo
{
    public string FileKey { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string? ContentType { get; set; }
    public DateTime LastModified { get; set; }
    public string? ETag { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
    public StorageClass StorageClass { get; set; }
}

public enum StorageClass
{
    Standard,
    InfrequentAccess,
    Archive
}