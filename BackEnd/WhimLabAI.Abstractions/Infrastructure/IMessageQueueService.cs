namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 消息队列服务接口
/// </summary>
public interface IMessageQueueService
{
    /// <summary>
    /// 发布消息
    /// </summary>
    Task PublishAsync<T>(string topic, T message, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// 订阅消息
    /// </summary>
    Task SubscribeAsync<T>(string topic, Func<T, Task> handler, CancellationToken cancellationToken = default) where T : class;
}