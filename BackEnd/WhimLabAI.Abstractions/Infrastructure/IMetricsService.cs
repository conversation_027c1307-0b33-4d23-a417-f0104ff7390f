using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 指标服务接口，用于收集和导出应用程序指标
/// </summary>
public interface IMetricsService
{
    /// <summary>
    /// 增加计数器
    /// </summary>
    /// <param name="name">指标名称</param>
    /// <param name="value">增加的值，默认为1</param>
    /// <param name="labels">标签键值对</param>
    void IncrementCounter(string name, double value = 1, Dictionary<string, string>? labels = null);
    
    /// <summary>
    /// 设置仪表值
    /// </summary>
    /// <param name="name">指标名称</param>
    /// <param name="value">当前值</param>
    /// <param name="labels">标签键值对</param>
    void SetGauge(string name, double value, Dictionary<string, string>? labels = null);
    
    /// <summary>
    /// 记录直方图观测值
    /// </summary>
    /// <param name="name">指标名称</param>
    /// <param name="value">观测值</param>
    /// <param name="labels">标签键值对</param>
    void RecordHistogram(string name, double value, Dictionary<string, string>? labels = null);
    
    /// <summary>
    /// 记录摘要观测值
    /// </summary>
    /// <param name="name">指标名称</param>
    /// <param name="value">观测值</param>
    /// <param name="labels">标签键值对</param>
    void RecordSummary(string name, double value, Dictionary<string, string>? labels = null);
    
    /// <summary>
    /// 测量操作执行时间
    /// </summary>
    /// <param name="name">指标名称</param>
    /// <param name="labels">标签键值对</param>
    /// <returns>可释放的计时器，释放时记录时间</returns>
    IDisposable MeasureDuration(string name, Dictionary<string, string>? labels = null);
    
    /// <summary>
    /// 记录业务指标
    /// </summary>
    Task RecordBusinessMetricsAsync(BusinessMetrics metrics, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 记录技术指标
    /// </summary>
    Task RecordTechnicalMetricsAsync(TechnicalMetrics metrics, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取当前指标快照
    /// </summary>
    Task<MetricsSnapshot> GetMetricsSnapshotAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 业务指标
/// </summary>
public class BusinessMetrics
{
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    // 用户指标
    public int ActiveUsersDaily { get; set; }
    public int ActiveUsersMonthly { get; set; }
    public int NewRegistrations { get; set; }
    public double UserRetentionRate { get; set; }
    
    // AI代理指标
    public int TotalAgents { get; set; }
    public int ActiveAgents { get; set; }
    public int AgentInvocations { get; set; }
    public Dictionary<string, int> AgentUsageByType { get; set; } = new();
    
    // 对话指标
    public int TotalConversations { get; set; }
    public int ActiveConversations { get; set; }
    public double AverageMessagesPerConversation { get; set; }
    public double AverageConversationDuration { get; set; }
    
    // Token指标
    public long TotalTokensConsumed { get; set; }
    public Dictionary<string, long> TokensByModel { get; set; } = new();
    public Dictionary<string, long> TokensByUserTier { get; set; } = new();
    
    // 收入指标
    public decimal TotalRevenue { get; set; }
    public Dictionary<string, decimal> RevenueByTier { get; set; } = new();
    public double PaymentSuccessRate { get; set; }
    public decimal AverageRevenuePerUser { get; set; }
}

/// <summary>
/// 技术指标
/// </summary>
public class TechnicalMetrics
{
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    // API指标
    public double RequestsPerSecond { get; set; }
    public double ResponseTimeP50 { get; set; }
    public double ResponseTimeP95 { get; set; }
    public double ResponseTimeP99 { get; set; }
    public double ErrorRate { get; set; }
    public Dictionary<int, int> StatusCodeDistribution { get; set; } = new();
    
    // AI服务指标
    public Dictionary<string, double> ModelResponseTime { get; set; } = new();
    public double TokenGenerationSpeed { get; set; }
    public double StreamLatency { get; set; }
    public Dictionary<string, double> ModelAvailability { get; set; } = new();
    
    // 数据库指标
    public double AverageQueryTime { get; set; }
    public int ActiveConnections { get; set; }
    public double TransactionRate { get; set; }
    public int LockWaits { get; set; }
    
    // 缓存指标
    public double CacheHitRate { get; set; }
    public long CacheMemoryUsage { get; set; }
    public int CacheEvictions { get; set; }
    
    // 队列指标
    public Dictionary<string, int> QueueDepth { get; set; } = new();
    public Dictionary<string, double> QueueProcessingTime { get; set; } = new();
}

/// <summary>
/// 指标快照
/// </summary>
public class MetricsSnapshot
{
    public DateTime Timestamp { get; set; }
    public BusinessMetrics BusinessMetrics { get; set; } = new();
    public TechnicalMetrics TechnicalMetrics { get; set; } = new();
    public Dictionary<string, object> CustomMetrics { get; set; } = new();
}

/// <summary>
/// 计时器接口
/// </summary>
public interface IMetricTimer : IDisposable
{
    /// <summary>
    /// 停止计时并记录
    /// </summary>
    void Stop();
}