namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// QR码生成服务接口
/// </summary>
public interface IQRCodeGeneratorService
{
    /// <summary>
    /// 生成QR码图像
    /// </summary>
    /// <param name="content">QR码内容</param>
    /// <param name="pixelsPerModule">每个模块的像素数，默认20</param>
    /// <returns>Base64编码的PNG图像</returns>
    string GenerateQRCodeBase64(string content, int pixelsPerModule = 20);
    
    /// <summary>
    /// 生成QR码图像字节数组
    /// </summary>
    /// <param name="content">QR码内容</param>
    /// <param name="pixelsPerModule">每个模块的像素数，默认20</param>
    /// <returns>PNG图像字节数组</returns>
    byte[] GenerateQRCodeBytes(string content, int pixelsPerModule = 20);
    
    /// <summary>
    /// 生成带Logo的QR码图像
    /// </summary>
    /// <param name="content">QR码内容</param>
    /// <param name="logoBase64">Logo图像的Base64编码</param>
    /// <param name="pixelsPerModule">每个模块的像素数，默认20</param>
    /// <returns>Base64编码的PNG图像</returns>
    string GenerateQRCodeWithLogo(string content, string logoBase64, int pixelsPerModule = 20);
}