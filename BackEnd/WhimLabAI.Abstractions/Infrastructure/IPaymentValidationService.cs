using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 支付验证服务接口
/// </summary>
public interface IPaymentValidationService
{
    /// <summary>
    /// 验证支付回调数据
    /// </summary>
    Task<Result<PaymentValidationResult>> ValidatePaymentCallbackAsync(
        string paymentNo,
        decimal callbackAmount,
        string? transactionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证退款请求
    /// </summary>
    Task<Result<RefundValidationResult>> ValidateRefundRequestAsync(
        Guid orderId,
        Guid paymentId,
        decimal refundAmount,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 支付验证结果
/// </summary>
public class PaymentValidationResult
{
    public bool IsValid { get; set; }
    public Guid PaymentId { get; set; }
    public Guid OrderId { get; set; }
    public decimal Amount { get; set; }
    public bool IsAlreadyProcessed { get; set; }
    public WhimLabAI.Shared.Enums.TransactionStatus CurrentStatus { get; set; }
    public string? OrderNo { get; set; }
    public Guid? CustomerUserId { get; set; }
}

/// <summary>
/// 退款验证结果
/// </summary>
public class RefundValidationResult
{
    public bool IsValid { get; set; }
    public Guid PaymentId { get; set; }
    public string PaymentNo { get; set; } = string.Empty;
    public string TransactionId { get; set; } = string.Empty;
    public decimal TotalPaidAmount { get; set; }
    public decimal TotalRefundedAmount { get; set; }
    public decimal RemainingAmount { get; set; }
    public WhimLabAI.Shared.Enums.PaymentMethod PaymentMethod { get; set; }
}