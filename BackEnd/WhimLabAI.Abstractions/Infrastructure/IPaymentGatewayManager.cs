using System;
using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Shared.Dtos.Payment;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 支付网关管理器接口
/// </summary>
public interface IPaymentGatewayManager
{
    /// <summary>
    /// 获取支付网关
    /// </summary>
    IPaymentGateway GetGateway(PaymentMethod method);
    
    /// <summary>
    /// 获取所有支持的支付方式
    /// </summary>
    Task<PaymentMethod[]> GetSupportedMethodsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证网关配置
    /// </summary>
    Task<bool> ValidateGatewayConfigurationAsync(PaymentMethod paymentMethod, CancellationToken cancellationToken = default);
}