namespace WhimLabAI.Abstractions.Infrastructure;

public interface ICacheService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default);
    Task<string?> GetStringAsync(string key, CancellationToken cancellationToken = default);
    Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiry = null, CancellationToken cancellationToken = default);
    Task<bool> SetStringAsync(string key, string value, TimeSpan? expiry = null, CancellationToken cancellationToken = default);
    Task<bool> RemoveAsync(string key, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);
    Task<bool> ExpireAsync(string key, TimeSpan expiry, CancellationToken cancellationToken = default);
    Task<TimeSpan?> GetTimeToLiveAsync(string key, CancellationToken cancellationToken = default);
    
    // Hash operations
    Task<bool> HashSetAsync<T>(string key, string field, T value, CancellationToken cancellationToken = default);
    Task<T?> HashGetAsync<T>(string key, string field, CancellationToken cancellationToken = default);
    Task<Dictionary<string, T>> HashGetAllAsync<T>(string key, CancellationToken cancellationToken = default);
    Task<bool> HashDeleteAsync(string key, string field, CancellationToken cancellationToken = default);
    Task<bool> HashExistsAsync(string key, string field, CancellationToken cancellationToken = default);
    
    // List operations
    Task<long> ListPushAsync<T>(string key, T value, bool toEnd = true, CancellationToken cancellationToken = default);
    Task<T?> ListPopAsync<T>(string key, bool fromEnd = true, CancellationToken cancellationToken = default);
    Task<List<T>> ListRangeAsync<T>(string key, long start = 0, long stop = -1, CancellationToken cancellationToken = default);
    Task<long> ListLengthAsync(string key, CancellationToken cancellationToken = default);
    
    // Set operations
    Task<bool> SetAddAsync<T>(string key, T value, CancellationToken cancellationToken = default);
    Task<bool> SetRemoveAsync<T>(string key, T value, CancellationToken cancellationToken = default);
    Task<bool> SetContainsAsync<T>(string key, T value, CancellationToken cancellationToken = default);
    Task<HashSet<T>> SetMembersAsync<T>(string key, CancellationToken cancellationToken = default);
    
    // Atomic operations
    Task<long> IncrementAsync(string key, long value = 1, CancellationToken cancellationToken = default);
    Task<long> DecrementAsync(string key, long value = 1, CancellationToken cancellationToken = default);
    
    // Pattern operations
    Task<List<string>> GetKeysAsync(string pattern, CancellationToken cancellationToken = default);
    Task<bool> RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
    
    // Transaction support
    Task<bool> LockAsync(string key, string value, TimeSpan expiry, CancellationToken cancellationToken = default);
    Task<bool> UnlockAsync(string key, string value, CancellationToken cancellationToken = default);
    
    // Cache-Aside Pattern
    Task<T?> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null, CancellationToken cancellationToken = default);
}