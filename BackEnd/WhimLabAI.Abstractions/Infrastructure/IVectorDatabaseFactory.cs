using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 向量数据库工厂接口
/// </summary>
public interface IVectorDatabaseFactory
{
    /// <summary>
    /// 获取向量数据库实例
    /// </summary>
    /// <param name="type">向量数据库类型</param>
    /// <param name="config">配置信息</param>
    /// <returns>向量数据库实例</returns>
    IVectorDatabase GetVectorDatabase(VectorDatabaseType type, string config);
    
    /// <summary>
    /// 获取默认向量数据库实例
    /// </summary>
    /// <returns>默认向量数据库实例</returns>
    IVectorDatabase GetDefaultVectorDatabase();
    
    /// <summary>
    /// 检查向量数据库类型是否支持
    /// </summary>
    bool IsSupported(VectorDatabaseType type);
}