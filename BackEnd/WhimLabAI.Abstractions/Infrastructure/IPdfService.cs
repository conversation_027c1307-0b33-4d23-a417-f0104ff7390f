using WhimLabAI.Shared.Dtos.Invoice;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// PDF服务接口
/// </summary>
public interface IPdfService
{
    /// <summary>
    /// 生成发票PDF
    /// </summary>
    Task<byte[]> GenerateInvoicePdfAsync(InvoiceDto invoice, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 生成报告PDF
    /// </summary>
    Task<byte[]> GenerateReportPdfAsync(string reportContent, string title, CancellationToken cancellationToken = default);
}