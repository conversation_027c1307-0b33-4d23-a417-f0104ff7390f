using System;
using System.Threading;
using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 缓存失效服务接口，提供集中式的缓存失效管理
/// </summary>
public interface ICacheInvalidationService
{
    /// <summary>
    /// 使用户相关的所有缓存失效
    /// </summary>
    Task InvalidateUserCacheAsync(Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 使AI代理相关的所有缓存失效
    /// </summary>
    Task InvalidateAgentCacheAsync(Guid agentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 使订阅相关的所有缓存失效
    /// </summary>
    Task InvalidateSubscriptionCacheAsync(Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 使权限相关的所有缓存失效
    /// </summary>
    Task InvalidatePermissionCacheAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 使对话相关的缓存失效
    /// </summary>
    Task InvalidateConversationCacheAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 使统计数据缓存失效
    /// </summary>
    Task InvalidateStatisticsCacheAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 使搜索结果缓存失效
    /// </summary>
    Task InvalidateSearchCacheAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 使所有缓存失效（谨慎使用）
    /// </summary>
    Task InvalidateAllCacheAsync(CancellationToken cancellationToken = default);
}