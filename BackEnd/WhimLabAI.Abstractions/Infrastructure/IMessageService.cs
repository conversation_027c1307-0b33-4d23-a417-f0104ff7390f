namespace WhimLabAI.Abstractions.Infrastructure;

public interface IMessageService
{
    Task<SendResult> SendEmailAsync(EmailMessage message, CancellationToken cancellationToken = default);
    Task<SendResult> SendSmsAsync(SmsMessage message, CancellationToken cancellationToken = default);
    Task<SendResult> SendNotificationAsync(NotificationMessage message, CancellationToken cancellationToken = default);
    Task<List<MessageTemplate>> GetTemplatesAsync(string messageType, CancellationToken cancellationToken = default);
}

public class EmailMessage
{
    public List<string> To { get; set; } = new();
    public List<string>? Cc { get; set; }
    public List<string>? Bcc { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public bool IsHtml { get; set; } = true;
    public List<EmailAttachment>? Attachments { get; set; }
    public string? TemplateId { get; set; }
    public Dictionary<string, object>? TemplateData { get; set; }
    public Dictionary<string, string>? Headers { get; set; }
}

public class EmailAttachment
{
    public string FileName { get; set; } = string.Empty;
    public byte[] Content { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = string.Empty;
}

public class SmsMessage
{
    public string PhoneNumber { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string? TemplateId { get; set; }
    public Dictionary<string, string>? TemplateParams { get; set; }
    public string? SignName { get; set; }
}

public class NotificationMessage
{
    public Guid UserId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string? Action { get; set; }
    public Dictionary<string, object>? Data { get; set; }
    public bool SaveToDatabase { get; set; } = true;
}

public class SendResult
{
    public bool Success { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object>? Extra { get; set; }
}

public class MessageTemplate
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public List<string> Variables { get; set; } = new();
    public bool IsActive { get; set; }
}