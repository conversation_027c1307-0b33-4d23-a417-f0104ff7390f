using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Infrastructure;

public interface IAIProvider
{
    string ProviderName { get; }
    AIProviderType ProviderType { get; }
    IReadOnlyList<string> SupportedModels { get; }
    
    Task<Result<AIResponse>> SendMessageAsync(AIRequest request, CancellationToken cancellationToken = default);
    IAsyncEnumerable<Result<AIStreamChunk>> StreamMessageAsync(AIRequest request, CancellationToken cancellationToken = default);
    Task<Result<int>> GetTokenCountAsync(string text, string? model = null, CancellationToken cancellationToken = default);
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);
    Task<Result<AIModelInfo>> GetModelInfoAsync(string model, CancellationToken cancellationToken = default);
    Task<Result<AIEmbeddingResponse>> GetEmbeddingAsync(AIEmbeddingRequest request, CancellationToken cancellationToken = default);
}

public class AIRequest
{
    public string Model { get; set; } = string.Empty;
    public List<AIMessage> Messages { get; set; } = new();
    public double Temperature { get; set; } = 0.7;
    public int MaxTokens { get; set; } = 2048;
    public double? TopP { get; set; }
    public string? StopSequence { get; set; }
    public Dictionary<string, object>? AdditionalParameters { get; set; }
}

public class AIMessage
{
    public string Role { get; set; } = string.Empty; // system, user, assistant
    public string Content { get; set; } = string.Empty;
    public List<AIAttachment>? Attachments { get; set; }
}

public class AIAttachment
{
    public string Type { get; set; } = string.Empty; // image, file, etc.
    public string? Url { get; set; }
    public byte[]? Data { get; set; }
    public string? MimeType { get; set; }
}

public class AIResponse
{
    public string Content { get; set; } = string.Empty;
    public int TokensUsed { get; set; }
    public int PromptTokens { get; set; }
    public int CompletionTokens { get; set; }
    public string? FinishReason { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class AIStreamChunk
{
    public string? Content { get; set; }
    public bool IsComplete { get; set; }
    public int? TokenCount { get; set; }
    public string? Error { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class AIModelInfo
{
    public string ModelId { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public int MaxTokens { get; set; }
    public int ContextLength { get; set; }
    public bool SupportsStreaming { get; set; }
    public bool SupportsVision { get; set; }
    public bool SupportsEmbedding { get; set; }
    public List<string> SupportedLanguages { get; set; } = new();
    public Dictionary<string, object>? Capabilities { get; set; }
    public decimal PricePerMillionTokens { get; set; }
}

/// <summary>
/// 嵌入请求
/// </summary>
public class AIEmbeddingRequest
{
    public string Model { get; set; } = string.Empty;
    public List<string> Texts { get; set; } = new();
    public int? Dimensions { get; set; }
}

/// <summary>
/// 嵌入响应
/// </summary>
public class AIEmbeddingResponse
{
    public List<float[]> Embeddings { get; set; } = new();
    public int TokensUsed { get; set; }
    public string Model { get; set; } = string.Empty;
}

/// <summary>
/// AI提供商管理器接口
/// </summary>
public interface IAIProviderManager
{
    /// <summary>
    /// 获取指定类型的AI提供商
    /// </summary>
    IAIProvider GetProvider(AIProviderType type);
    
    /// <summary>
    /// 根据模型名称获取对应的提供商
    /// </summary>
    IAIProvider GetProviderByModel(string model);
    
    /// <summary>
    /// 获取所有可用的提供商
    /// </summary>
    IEnumerable<IAIProvider> GetAvailableProviders();
    
    /// <summary>
    /// 检查提供商是否可用
    /// </summary>
    Task<bool> IsProviderAvailableAsync(AIProviderType type, CancellationToken cancellationToken = default);
}