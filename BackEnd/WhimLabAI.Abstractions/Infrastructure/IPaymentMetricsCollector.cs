using System.Diagnostics;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 支付指标收集器接口
/// </summary>
public interface IPaymentMetricsCollector
{
    /// <summary>
    /// 记录支付指标
    /// </summary>
    void RecordPayment(
        PaymentMethod method,
        decimal amount,
        TransactionStatus status,
        TimeSpan duration);

    /// <summary>
    /// 记录退款指标
    /// </summary>
    void RecordRefund(
        PaymentMethod method,
        decimal amount,
        RefundStatus status,
        TimeSpan duration);

    /// <summary>
    /// 记录异常
    /// </summary>
    void RecordException(Exception ex, string operation, PaymentMethod? method = null);
    
    /// <summary>
    /// 开始支付活动跟踪
    /// </summary>
    Activity? StartPaymentActivity(
        string operationName,
        PaymentMethod method,
        string paymentNo);
    
    /// <summary>
    /// 开始退款活动跟踪
    /// </summary>
    Activity? StartRefundActivity(
        string operationName,
        PaymentMethod method,
        string refundNo);
}