using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 支付监控服务接口
/// </summary>
public interface IPaymentMonitoringService
{
    /// <summary>
    /// 监控支付操作
    /// </summary>
    Task<T> MonitorPaymentOperationAsync<T>(
        Func<Task<T>> operation,
        string operationName,
        PaymentMethod method,
        string referenceNo);

    /// <summary>
    /// 支付失败告警
    /// </summary>
    Task AlertOnPaymentFailureAsync(
        string paymentNo,
        PaymentMethod method,
        string failureReason,
        Exception? exception = null);
}