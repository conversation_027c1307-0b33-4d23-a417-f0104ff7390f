namespace WhimLabAI.Abstractions.Infrastructure;

public interface IDifyProvider : IAIProvider
{
    Task<DifyAppResponse> RunAppAsync(DifyAppRequest request, string apiKey, CancellationToken cancellationToken = default);
    Task<DifyWorkflowResponse> RunWorkflowAsync(DifyWorkflowRequest request, string apiKey, CancellationToken cancellationToken = default);
    Task<List<DifyAppInfo>> GetAppsAsync(string apiKey, CancellationToken cancellationToken = default);
    Task<DifyConversationHistory> GetConversationHistoryAsync(string conversationId, string apiKey, CancellationToken cancellationToken = default);
}

public class DifyAppRequest
{
    public string AppId { get; set; } = string.Empty;
    public string AppType { get; set; } = string.Empty; // "Chat", "Completion", "Workflow"
    public string Query { get; set; } = string.Empty;
    public string? ConversationId { get; set; }
    public Dictionary<string, object>? Inputs { get; set; }
    public bool ResponseMode { get; set; } = true; // true for streaming
    public string? User { get; set; }
}

public class DifyAppResponse
{
    public string Answer { get; set; } = string.Empty;
    public string ConversationId { get; set; } = string.Empty;
    public string MessageId { get; set; } = string.Empty;
    public int TotalTokens { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class DifyWorkflowRequest
{
    public string WorkflowId { get; set; } = string.Empty;
    public Dictionary<string, object> Inputs { get; set; } = new();
    public string? User { get; set; }
}

public class DifyWorkflowResponse
{
    public string RunId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> Outputs { get; set; } = new();
    public int TotalTokens { get; set; }
    public List<DifyWorkflowStep> Steps { get; set; } = new();
}

public class DifyWorkflowStep
{
    public string NodeId { get; set; } = string.Empty;
    public string NodeName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object>? Inputs { get; set; }
    public Dictionary<string, object>? Outputs { get; set; }
    public long ExecutionTime { get; set; }
}

public class DifyAppInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Mode { get; set; } = string.Empty; // chat, completion, workflow
    public string? Description { get; set; }
    public List<DifyAppParameter> Parameters { get; set; } = new();
}

public class DifyAppParameter
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public bool Required { get; set; }
    public string? Description { get; set; }
    public object? DefaultValue { get; set; }
}

public class DifyConversationHistory
{
    public string ConversationId { get; set; } = string.Empty;
    public List<DifyMessage> Messages { get; set; } = new();
    public bool HasMore { get; set; }
}

public class DifyMessage
{
    public string Id { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}