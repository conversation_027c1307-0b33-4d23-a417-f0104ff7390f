namespace WhimLabAI.Abstractions.Infrastructure;

public interface ISemanticKernelProvider : IAIProvider
{
    Task<SKKernelResponse> ExecuteSkillAsync(SKSkillRequest request, CancellationToken cancellationToken = default);
    Task<List<SKPluginInfo>> GetAvailablePluginsAsync(CancellationToken cancellationToken = default);
    Task<SKMemorySearchResult> SearchMemoryAsync(string collection, string query, int limit = 10, CancellationToken cancellationToken = default);
    Task SaveMemoryAsync(string collection, string id, string text, Dictionary<string, object>? metadata = null, CancellationToken cancellationToken = default);
}

public class SKSkillRequest
{
    public string SkillName { get; set; } = string.Empty;
    public string FunctionName { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public SKContextVariables? Context { get; set; }
}

public class SKKernelResponse
{
    public string Result { get; set; } = string.Empty;
    public Dictionary<string, object> Variables { get; set; } = new();
    public List<string> Logs { get; set; } = new();
    public bool Success { get; set; }
    public string? Error { get; set; }
}

public class SKContextVariables
{
    public Dictionary<string, string> Variables { get; set; } = new();
    public string Input { get; set; } = string.Empty;
}

public class SKPluginInfo
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<SKFunctionInfo> Functions { get; set; } = new();
}

public class SKFunctionInfo
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<SKParameterInfo> Parameters { get; set; } = new();
}

public class SKParameterInfo
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public object? DefaultValue { get; set; }
}

public class SKMemorySearchResult
{
    public List<SKMemoryRecord> Results { get; set; } = new();
    public int TotalCount { get; set; }
}

public class SKMemoryRecord
{
    public string Id { get; set; } = string.Empty;
    public string Text { get; set; } = string.Empty;
    public double Relevance { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public DateTime? Timestamp { get; set; }
}