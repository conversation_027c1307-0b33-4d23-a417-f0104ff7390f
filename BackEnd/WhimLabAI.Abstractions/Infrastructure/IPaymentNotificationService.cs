using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 支付通知服务接口
/// </summary>
public interface IPaymentNotificationService
{
    /// <summary>
    /// 发送支付通知
    /// </summary>
    Task SendPaymentNotificationAsync(
        Guid userId,
        string orderNo,
        decimal amount,
        TransactionStatus status,
        string? failureReason = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理支付回调
    /// </summary>
    Task ProcessPaymentCallbackAsync(
        string paymentNo,
        PaymentCallbackResult callback,
        int retryCount = 0,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送退款通知
    /// </summary>
    Task SendRefundNotificationAsync(
        Guid userId,
        string refundNo,
        decimal amount,
        RefundStatus status,
        string? failureReason = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查支付渠道健康状态
    /// </summary>
    Task<bool> CheckPaymentHealthAsync(
        PaymentMethod paymentMethod,
        CancellationToken cancellationToken = default);
}