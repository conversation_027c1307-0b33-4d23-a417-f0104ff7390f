using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 向量数据库接口
/// </summary>
public interface IVectorDatabase
{
    /// <summary>
    /// 初始化集合
    /// </summary>
    Task<Result> CreateCollectionAsync(
        string collectionName,
        int dimension,
        Dictionary<string, object>? config = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除集合
    /// </summary>
    Task<Result> DeleteCollectionAsync(
        string collectionName,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 插入向量
    /// </summary>
    Task<Result<string>> InsertVectorAsync(
        string collectionName,
        float[] vector,
        Dictionary<string, object> metadata,
        string? id = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量插入向量
    /// </summary>
    Task<Result<IEnumerable<string>>> InsertVectorsAsync(
        string collectionName,
        IEnumerable<VectorData> vectors,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 搜索相似向量
    /// </summary>
    Task<Result<IEnumerable<VectorSearchResult>>> SearchAsync(
        string collectionName,
        float[] queryVector,
        int topK,
        float? scoreThreshold = null,
        Dictionary<string, object>? filter = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新向量
    /// </summary>
    Task<Result> UpdateVectorAsync(
        string collectionName,
        string id,
        float[]? vector = null,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除向量
    /// </summary>
    Task<Result> DeleteVectorAsync(
        string collectionName,
        string id,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取向量
    /// </summary>
    Task<Result<float[]?>> GetVectorAsync(
        string collectionName,
        string id,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量删除向量
    /// </summary>
    Task<Result<int>> DeleteVectorsAsync(
        string collectionName,
        IEnumerable<string> ids,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取集合统计信息
    /// </summary>
    Task<Result<VectorCollectionStats>> GetCollectionStatsAsync(
        string collectionName,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查连接状态
    /// </summary>
    Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 向量数据
/// </summary>
public class VectorData
{
    public string? Id { get; set; }
    public float[] Vector { get; set; } = Array.Empty<float>();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 向量搜索结果
/// </summary>
public class VectorSearchResult
{
    public string Id { get; set; } = string.Empty;
    public float Score { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 向量集合统计
/// </summary>
public class VectorCollectionStats
{
    public long VectorCount { get; set; }
    public int Dimension { get; set; }
    public long IndexSize { get; set; }
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}