using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 邮件服务接口
/// </summary>
public interface IEmailService
{
    /// <summary>
    /// 发送邮件
    /// </summary>
    /// <param name="to">收件人邮箱地址</param>
    /// <param name="subject">邮件主题</param>
    /// <param name="body">邮件内容</param>
    /// <param name="isHtml">是否为HTML格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送邮件给多个收件人
    /// </summary>
    /// <param name="to">收件人邮箱地址列表</param>
    /// <param name="subject">邮件主题</param>
    /// <param name="body">邮件内容</param>
    /// <param name="isHtml">是否为HTML格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendEmailAsync(List<string> to, string subject, string body, bool isHtml = true, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送带附件的邮件
    /// </summary>
    /// <param name="to">收件人邮箱地址</param>
    /// <param name="subject">邮件主题</param>
    /// <param name="body">邮件内容</param>
    /// <param name="attachments">附件列表（文件路径）</param>
    /// <param name="isHtml">是否为HTML格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendEmailWithAttachmentsAsync(string to, string subject, string body, List<string> attachments, bool isHtml = true, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送模板邮件
    /// </summary>
    /// <param name="to">收件人邮箱地址</param>
    /// <param name="templateId">邮件模板ID</param>
    /// <param name="templateData">模板数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendTemplateEmailAsync(string to, string templateId, Dictionary<string, object> templateData, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送发票邮件
    /// </summary>
    /// <param name="to">收件人邮箱地址</param>
    /// <param name="invoiceNumber">发票号</param>
    /// <param name="pdfBytes">发票PDF字节数组</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendInvoiceEmailAsync(string to, string invoiceNumber, byte[] pdfBytes, CancellationToken cancellationToken = default);
}