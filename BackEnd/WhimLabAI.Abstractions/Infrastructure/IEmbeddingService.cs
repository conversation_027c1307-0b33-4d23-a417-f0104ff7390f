using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 嵌入服务接口
/// </summary>
public interface IEmbeddingService
{
    /// <summary>
    /// 生成文本嵌入向量
    /// </summary>
    /// <param name="text">要嵌入的文本</param>
    /// <param name="model">嵌入模型名称（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>嵌入向量</returns>
    Task<Result<float[]>> GenerateEmbeddingAsync(
        string text,
        string? model = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量生成文本嵌入向量
    /// </summary>
    /// <param name="texts">要嵌入的文本列表</param>
    /// <param name="model">嵌入模型名称（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>嵌入向量列表</returns>
    Task<Result<IList<float[]>>> GenerateEmbeddingsAsync(
        IList<string> texts,
        string? model = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取嵌入模型信息
    /// </summary>
    /// <param name="model">模型名称</param>
    /// <returns>模型信息</returns>
    Task<Result<EmbeddingModelInfo>> GetModelInfoAsync(string model);
    
    /// <summary>
    /// 获取支持的嵌入模型列表
    /// </summary>
    /// <returns>模型列表</returns>
    Task<Result<IList<EmbeddingModelInfo>>> GetSupportedModelsAsync();
}

/// <summary>
/// 嵌入模型信息
/// </summary>
public class EmbeddingModelInfo
{
    /// <summary>
    /// 模型名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 向量维度
    /// </summary>
    public int Dimension { get; set; }
    
    /// <summary>
    /// 最大输入长度（tokens）
    /// </summary>
    public int MaxInputLength { get; set; }
    
    /// <summary>
    /// 提供商
    /// </summary>
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsAvailable { get; set; }
    
    /// <summary>
    /// 额外信息
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}