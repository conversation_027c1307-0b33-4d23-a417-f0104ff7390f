using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface IOAuthService
{
    /// <summary>
    /// 获取OAuth授权URL
    /// </summary>
    /// <param name="provider">OAuth提供商（WeChat、Google、Microsoft等）</param>
    /// <param name="state">状态参数，用于防止CSRF攻击</param>
    /// <param name="redirectUri">回调URL</param>
    /// <returns>授权URL</returns>
    Task<Result<string>> GetAuthorizationUrlAsync(string provider, string state, string? redirectUri = null);
    
    /// <summary>
    /// 使用授权码交换访问令牌并获取用户信息
    /// </summary>
    /// <param name="provider">OAuth提供商</param>
    /// <param name="code">授权码</param>
    /// <param name="state">状态参数</param>
    /// <param name="redirectUri">回调URL</param>
    /// <returns>OAuth用户信息</returns>
    Task<Result<OAuthUserInfo>> ExchangeCodeForTokenAsync(string provider, string code, string state, string? redirectUri = null);
    
    /// <summary>
    /// OAuth登录或创建用户
    /// </summary>
    /// <param name="provider">OAuth提供商</param>
    /// <param name="userInfo">OAuth用户信息</param>
    /// <returns>认证响应</returns>
    Task<Result<OAuthLoginResult>> LoginOrCreateUserAsync(string provider, OAuthUserInfo userInfo);
    
    /// <summary>
    /// 刷新OAuth访问令牌
    /// </summary>
    /// <param name="provider">OAuth提供商</param>
    /// <param name="refreshToken">刷新令牌</param>
    /// <returns>新的令牌信息</returns>
    Task<Result<OAuthTokenInfo>> RefreshTokenAsync(string provider, string refreshToken);
    
    /// <summary>
    /// 验证state参数
    /// </summary>
    /// <param name="state">状态参数</param>
    /// <returns>验证结果</returns>
    Task<Result<bool>> ValidateStateAsync(string state);
}

/// <summary>
/// OAuth用户信息
/// </summary>
public class OAuthUserInfo
{
    public string ProviderId { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Avatar { get; set; } = string.Empty;
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime? ExpiresAt { get; set; }
    public Dictionary<string, object> ExtraData { get; set; } = new();
}

/// <summary>
/// OAuth令牌信息
/// </summary>
public class OAuthTokenInfo
{
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public int ExpiresIn { get; set; }
    public string TokenType { get; set; } = "Bearer";
}

/// <summary>
/// OAuth登录结果
/// </summary>
public class OAuthLoginResult
{
    public Guid UserId { get; set; }
    public string Username { get; set; } = string.Empty;
    public bool IsNewUser { get; set; }
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
}