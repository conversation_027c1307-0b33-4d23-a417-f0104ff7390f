using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Shared.DTOs.Analytics;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 数据分析服务接口
/// </summary>
public interface IDataAnalyticsService
{
    #region 用户分析
    
    /// <summary>
    /// 获取用户概览统计
    /// </summary>
    Task<UserOverviewDto> GetUserOverviewAsync(DateTimeRange? dateRange = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户增长趋势
    /// </summary>
    Task<UserGrowthTrendDto> GetUserGrowthTrendAsync(
        DateTimeRange dateRange, 
        TrendGranularity granularity = TrendGranularity.Daily,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户留存分析
    /// </summary>
    Task<UserRetentionDto> GetUserRetentionAsync(
        DateTime cohortDate,
        int daysToTrack = 30,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户活跃度分析
    /// </summary>
    Task<UserActivityDto> GetUserActivityAnalysisAsync(
        DateTimeRange dateRange,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户生命周期价值分析
    /// </summary>
    Task<UserLifetimeValueDto> GetUserLifetimeValueAsync(
        DateTime? startDate = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户流失预测分析
    /// </summary>
    Task<ChurnAnalysisDto> GetChurnAnalysisAsync(
        DateTimeRange? dateRange = null,
        CancellationToken cancellationToken = default);
    
    #endregion
    
    #region Agent分析
    
    /// <summary>
    /// 获取Agent使用统计
    /// </summary>
    Task<AgentUsageOverviewDto> GetAgentUsageOverviewAsync(
        DateTimeRange? dateRange = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取热门Agent排行
    /// </summary>
    Task<List<PopularAgentDto>> GetPopularAgentsAsync(
        DateTimeRange dateRange,
        int topN = 10,
        PopularityMetric metric = PopularityMetric.ConversationCount,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Agent评分分析
    /// </summary>
    Task<AgentRatingAnalysisDto> GetAgentRatingAnalysisAsync(
        Guid? agentId = null,
        DateTimeRange? dateRange = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Agent使用趋势
    /// </summary>
    Task<AgentUsageTrendDto> GetAgentUsageTrendAsync(
        Guid agentId,
        DateTimeRange dateRange,
        TrendGranularity granularity = TrendGranularity.Daily,
        CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 对话分析
    
    /// <summary>
    /// 获取对话统计概览
    /// </summary>
    Task<ConversationOverviewDto> GetConversationOverviewAsync(
        DateTimeRange? dateRange = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取对话质量分析
    /// </summary>
    Task<ConversationQualityDto> GetConversationQualityAnalysisAsync(
        DateTimeRange dateRange,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取对话时段分布
    /// </summary>
    Task<ConversationTimeDistributionDto> GetConversationTimeDistributionAsync(
        DateTimeRange dateRange,
        CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 财务分析
    
    /// <summary>
    /// 获取收入概览
    /// </summary>
    Task<RevenueOverviewDto> GetRevenueOverviewAsync(
        DateTimeRange? dateRange = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取收入趋势
    /// </summary>
    Task<RevenueTrendDto> GetRevenueTrendAsync(
        DateTimeRange dateRange,
        TrendGranularity granularity = TrendGranularity.Daily,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取订阅分析
    /// </summary>
    Task<SubscriptionAnalysisDto> GetSubscriptionAnalysisAsync(
        DateTimeRange? dateRange = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取支付方式分析
    /// </summary>
    Task<PaymentMethodAnalysisDto> GetPaymentMethodAnalysisAsync(
        DateTimeRange dateRange,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取退款分析
    /// </summary>
    Task<RefundAnalysisDto> GetRefundAnalysisAsync(
        DateTimeRange dateRange,
        CancellationToken cancellationToken = default);
    
    #endregion
    
    #region Token使用分析
    
    /// <summary>
    /// 获取Token使用概览
    /// </summary>
    Task<TokenUsageOverviewDto> GetTokenUsageOverviewAsync(
        DateTimeRange? dateRange = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Token使用趋势
    /// </summary>
    Task<TokenUsageTrendDto> GetTokenUsageTrendAsync(
        DateTimeRange dateRange,
        TrendGranularity granularity = TrendGranularity.Daily,
        GroupBy groupBy = GroupBy.Total,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取模型使用分布
    /// </summary>
    Task<ModelUsageDistributionDto> GetModelUsageDistributionAsync(
        DateTimeRange dateRange,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Token成本分析
    /// </summary>
    Task<TokenCostAnalysisDto> GetTokenCostAnalysisAsync(
        DateTimeRange dateRange,
        CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 综合报表
    
    /// <summary>
    /// 获取执行摘要报表
    /// </summary>
    Task<ExecutiveSummaryDto> GetExecutiveSummaryAsync(
        DateTimeRange dateRange,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取自定义报表
    /// </summary>
    Task<CustomReportDto> GetCustomReportAsync(
        CustomReportRequest request,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 导出报表
    /// </summary>
    Task<byte[]> ExportReportAsync(
        ReportType reportType,
        DateTimeRange dateRange,
        ExportFormat format = ExportFormat.Excel,
        CancellationToken cancellationToken = default);
    
    #endregion
}