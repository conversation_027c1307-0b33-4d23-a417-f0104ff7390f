using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface IUsageTrackingService
{
    Task<Result<bool>> ConsumeTokensAsync(Guid userId, Guid conversationId, int tokens, string model, CancellationToken cancellationToken = default);
    Task<Result<int>> GetRemainingTokensAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result<bool>> CheckQuotaAsync(Guid userId, int requiredTokens, CancellationToken cancellationToken = default);
    Task<Result> ResetMonthlyQuotaAsync(Guid userId, CancellationToken cancellationToken = default);
}