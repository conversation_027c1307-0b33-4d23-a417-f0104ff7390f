using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Whim<PERSON>abAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 缓存优化服务接口
/// </summary>
public interface ICacheOptimizationService
{
    /// <summary>
    /// 分析缓存性能
    /// </summary>
    Task<Result<CachePerformanceAnalysis>> AnalyzeCachePerformanceAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    Task<Result<CacheStatistics>> GetCacheStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取缓存键分析
    /// </summary>
    Task<Result<CacheKeyAnalysis>> AnalyzeCacheKeysAsync(
        string pattern = "*",
        int sampleSize = 1000,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取缓存内存分析
    /// </summary>
    Task<Result<CacheMemoryAnalysis>> AnalyzeCacheMemoryAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 优化缓存配置
    /// </summary>
    Task<Result<CacheOptimizationResult>> OptimizeCacheAsync(
        CacheOptimizationOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 实施缓存预热
    /// </summary>
    Task<Result<CacheWarmingResult>> WarmCacheAsync(
        CacheWarmingStrategy strategy,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期缓存
    /// </summary>
    Task<Result<CacheCleanupResult>> CleanupExpiredCacheAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证缓存一致性
    /// </summary>
    Task<Result<CacheConsistencyReport>> ValidateCacheConsistencyAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 监控缓存性能
    /// </summary>
    Task<Result<CacheMonitoringResult>> MonitorCachePerformanceAsync(
        TimeSpan duration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 生成缓存优化报告
    /// </summary>
    Task<Result<CacheOptimizationReport>> GenerateOptimizationReportAsync(CancellationToken cancellationToken = default);
}

#region DTOs

/// <summary>
/// 缓存性能分析
/// </summary>
public class CachePerformanceAnalysis
{
    public DateTime AnalyzedAt { get; set; }
    public CacheHitRateAnalysis HitRate { get; set; } = new();
    public CacheLatencyAnalysis Latency { get; set; } = new();
    public CacheThroughputAnalysis Throughput { get; set; } = new();
    public List<CacheHotKey> HotKeys { get; set; } = new();
    public List<CacheColdKey> ColdKeys { get; set; } = new();
    public List<CachePerformanceIssue> Issues { get; set; } = new();
    public CacheHealthScore HealthScore { get; set; } = new();
}

/// <summary>
/// 缓存命中率分析
/// </summary>
public class CacheHitRateAnalysis
{
    public double OverallHitRate { get; set; }
    public Dictionary<string, double> HitRateByPrefix { get; set; } = new();
    public Dictionary<string, double> HitRateByHour { get; set; } = new();
    public double AverageHitRate { get; set; }
    public double PeakHitRate { get; set; }
    public DateTime PeakTime { get; set; }
    public List<string> LowHitRateKeys { get; set; } = new();
}

/// <summary>
/// 缓存延迟分析
/// </summary>
public class CacheLatencyAnalysis
{
    public double AverageLatencyMs { get; set; }
    public double P50LatencyMs { get; set; }
    public double P95LatencyMs { get; set; }
    public double P99LatencyMs { get; set; }
    public double MaxLatencyMs { get; set; }
    public Dictionary<string, double> LatencyByOperation { get; set; } = new();
    public List<SlowCacheOperation> SlowOperations { get; set; } = new();
}

/// <summary>
/// 缓存吞吐量分析
/// </summary>
public class CacheThroughputAnalysis
{
    public long TotalOperations { get; set; }
    public double OperationsPerSecond { get; set; }
    public long TotalBytesTransferred { get; set; }
    public double BytesPerSecond { get; set; }
    public Dictionary<string, long> OperationsByType { get; set; } = new();
    public List<ThroughputSnapshot> Snapshots { get; set; } = new();
}

/// <summary>
/// 热键
/// </summary>
public class CacheHotKey
{
    public string Key { get; set; } = string.Empty;
    public long AccessCount { get; set; }
    public double AccessFrequency { get; set; }
    public long Size { get; set; }
    public TimeSpan TTL { get; set; }
    public string DataType { get; set; } = string.Empty;
}

/// <summary>
/// 冷键
/// </summary>
public class CacheColdKey
{
    public string Key { get; set; } = string.Empty;
    public DateTime LastAccessTime { get; set; }
    public int DaysSinceLastAccess { get; set; }
    public long Size { get; set; }
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// 缓存性能问题
/// </summary>
public class CachePerformanceIssue
{
    public string IssueType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
}

/// <summary>
/// 缓存健康评分
/// </summary>
public class CacheHealthScore
{
    public int OverallScore { get; set; }
    public string Grade { get; set; } = string.Empty;
    public Dictionary<string, int> CategoryScores { get; set; } = new();
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
}

/// <summary>
/// 慢缓存操作
/// </summary>
public class SlowCacheOperation
{
    public string Operation { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
    public double LatencyMs { get; set; }
    public DateTime Timestamp { get; set; }
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 吞吐量快照
/// </summary>
public class ThroughputSnapshot
{
    public DateTime Timestamp { get; set; }
    public double OperationsPerSecond { get; set; }
    public double BytesPerSecond { get; set; }
    public int ActiveConnections { get; set; }
}

/// <summary>
/// 缓存统计信息
/// </summary>
public class CacheStatistics
{
    public DateTime CollectedAt { get; set; }
    public long TotalKeys { get; set; }
    public long TotalMemoryUsed { get; set; }
    public long MaxMemory { get; set; }
    public double MemoryUsagePercent { get; set; }
    public long TotalHits { get; set; }
    public long TotalMisses { get; set; }
    public double HitRate { get; set; }
    public long EvictedKeys { get; set; }
    public long ExpiredKeys { get; set; }
    public Dictionary<string, long> KeysByType { get; set; } = new();
    public Dictionary<string, long> MemoryByType { get; set; } = new();
    public CacheServerInfo ServerInfo { get; set; } = new();
}

/// <summary>
/// 缓存服务器信息
/// </summary>
public class CacheServerInfo
{
    public string Version { get; set; } = string.Empty;
    public TimeSpan Uptime { get; set; }
    public int ConnectedClients { get; set; }
    public long TotalCommandsProcessed { get; set; }
    public double InstantaneousOpsPerSec { get; set; }
    public string EvictionPolicy { get; set; } = string.Empty;
    public bool PersistenceEnabled { get; set; }
}

/// <summary>
/// 缓存键分析
/// </summary>
public class CacheKeyAnalysis
{
    public DateTime AnalyzedAt { get; set; }
    public int TotalKeysAnalyzed { get; set; }
    public Dictionary<string, KeyPrefixStats> PrefixStats { get; set; } = new();
    public List<LargeKey> LargeKeys { get; set; } = new();
    public List<KeyPattern> CommonPatterns { get; set; } = new();
    public KeyDistribution Distribution { get; set; } = new();
    public List<KeyNamingIssue> NamingIssues { get; set; } = new();
}

/// <summary>
/// 键前缀统计
/// </summary>
public class KeyPrefixStats
{
    public string Prefix { get; set; } = string.Empty;
    public int Count { get; set; }
    public long TotalSize { get; set; }
    public double AverageSize { get; set; }
    public TimeSpan AverageTTL { get; set; }
    public string DataType { get; set; } = string.Empty;
}

/// <summary>
/// 大键
/// </summary>
public class LargeKey
{
    public string Key { get; set; } = string.Empty;
    public long Size { get; set; }
    public string DataType { get; set; } = string.Empty;
    public int ElementCount { get; set; }
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// 键模式
/// </summary>
public class KeyPattern
{
    public string Pattern { get; set; } = string.Empty;
    public int Count { get; set; }
    public double Percentage { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 键分布
/// </summary>
public class KeyDistribution
{
    public Dictionary<string, int> ByDataType { get; set; } = new();
    public Dictionary<string, int> ByTTLRange { get; set; } = new();
    public Dictionary<string, int> BySizeRange { get; set; } = new();
}

/// <summary>
/// 键命名问题
/// </summary>
public class KeyNamingIssue
{
    public string Key { get; set; } = string.Empty;
    public string Issue { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// 缓存内存分析
/// </summary>
public class CacheMemoryAnalysis
{
    public DateTime AnalyzedAt { get; set; }
    public MemoryUsage CurrentUsage { get; set; } = new();
    public MemoryFragmentation Fragmentation { get; set; } = new();
    public List<MemoryConsumer> TopConsumers { get; set; } = new();
    public EvictionAnalysis Eviction { get; set; } = new();
    public List<MemoryOptimizationSuggestion> Suggestions { get; set; } = new();
}

/// <summary>
/// 内存使用
/// </summary>
public class MemoryUsage
{
    public long UsedMemory { get; set; }
    public long UsedMemoryRss { get; set; }
    public long UsedMemoryPeak { get; set; }
    public long MaxMemory { get; set; }
    public double UsagePercent { get; set; }
    public Dictionary<string, long> ByDataType { get; set; } = new();
}

/// <summary>
/// 内存碎片
/// </summary>
public class MemoryFragmentation
{
    public double FragmentationRatio { get; set; }
    public long FragmentedBytes { get; set; }
    public string Severity { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// 内存消费者
/// </summary>
public class MemoryConsumer
{
    public string Key { get; set; } = string.Empty;
    public long MemoryUsage { get; set; }
    public double Percentage { get; set; }
    public string DataType { get; set; } = string.Empty;
    public TimeSpan TTL { get; set; }
}

/// <summary>
/// 逐出分析
/// </summary>
public class EvictionAnalysis
{
    public string Policy { get; set; } = string.Empty;
    public long EvictedKeys { get; set; }
    public double EvictionRate { get; set; }
    public List<EvictionEvent> RecentEvictions { get; set; } = new();
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// 逐出事件
/// </summary>
public class EvictionEvent
{
    public DateTime Timestamp { get; set; }
    public string Key { get; set; } = string.Empty;
    public long Size { get; set; }
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 内存优化建议
/// </summary>
public class MemoryOptimizationSuggestion
{
    public string Category { get; set; } = string.Empty;
    public string Issue { get; set; } = string.Empty;
    public string Suggestion { get; set; } = string.Empty;
    public long PotentialSavings { get; set; }
    public string Priority { get; set; } = string.Empty;
}

/// <summary>
/// 缓存优化选项
/// </summary>
public class CacheOptimizationOptions
{
    public bool OptimizeMemory { get; set; } = true;
    public bool OptimizeTTL { get; set; } = true;
    public bool CompressLargeValues { get; set; } = true;
    public bool RemoveDuplicates { get; set; } = true;
    public bool CleanupExpired { get; set; } = true;
    public bool DefragmentMemory { get; set; } = true;
    public double LargeKeyThreshold { get; set; } = 1048576; // 1MB
    public int MaxKeysToProcess { get; set; } = 10000;
}

/// <summary>
/// 缓存优化结果
/// </summary>
public class CacheOptimizationResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration => EndTime - StartTime;
    public List<CacheOptimizationAction> ActionsPerformed { get; set; } = new();
    public long MemoryReclaimed { get; set; }
    public int KeysOptimized { get; set; }
    public int KeysRemoved { get; set; }
    public double PerformanceImprovement { get; set; }
    public bool Success { get; set; }
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// 缓存优化动作
/// </summary>
public class CacheOptimizationAction
{
    public string ActionType { get; set; } = string.Empty;
    public string Target { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Result { get; set; } = string.Empty;
    public long MemorySaved { get; set; }
}

/// <summary>
/// 缓存预热策略
/// </summary>
public class CacheWarmingStrategy
{
    public string StrategyName { get; set; } = string.Empty;
    public List<WarmingTarget> Targets { get; set; } = new();
    public int Parallelism { get; set; } = 4;
    public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(10);
    public bool ValidateAfterWarming { get; set; } = true;
}

/// <summary>
/// 预热目标
/// </summary>
public class WarmingTarget
{
    public string TargetType { get; set; } = string.Empty; // "Query", "Entity", "Config"
    public string TargetName { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public TimeSpan TTL { get; set; } = TimeSpan.FromHours(1);
    public int Priority { get; set; } = 0;
}

/// <summary>
/// 缓存预热结果
/// </summary>
public class CacheWarmingResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public int TotalTargets { get; set; }
    public int SuccessfulTargets { get; set; }
    public int FailedTargets { get; set; }
    public long TotalDataLoaded { get; set; }
    public List<WarmingTargetResult> Results { get; set; } = new();
    public bool Success { get; set; }
}

/// <summary>
/// 预热目标结果
/// </summary>
public class WarmingTargetResult
{
    public string TargetName { get; set; } = string.Empty;
    public bool Success { get; set; }
    public TimeSpan Duration { get; set; }
    public long DataSize { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}

/// <summary>
/// 缓存清理结果
/// </summary>
public class CacheCleanupResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public int ExpiredKeysRemoved { get; set; }
    public int EmptyKeysRemoved { get; set; }
    public int DuplicateKeysRemoved { get; set; }
    public long MemoryReclaimed { get; set; }
    public List<string> CleanupActions { get; set; } = new();
}

/// <summary>
/// 缓存一致性报告
/// </summary>
public class CacheConsistencyReport
{
    public DateTime CheckedAt { get; set; }
    public int TotalKeysChecked { get; set; }
    public int InconsistentKeys { get; set; }
    public List<ConsistencyIssue> Issues { get; set; } = new();
    public double ConsistencyScore { get; set; }
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// 一致性问题
/// </summary>
public class ConsistencyIssue
{
    public string Key { get; set; } = string.Empty;
    public string IssueType { get; set; } = string.Empty;
    public string ExpectedValue { get; set; } = string.Empty;
    public string ActualValue { get; set; } = string.Empty;
    public string Resolution { get; set; } = string.Empty;
}

/// <summary>
/// 缓存监控结果
/// </summary>
public class CacheMonitoringResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public List<CacheMetricSnapshot> Snapshots { get; set; } = new();
    public CachePerformanceSummary Summary { get; set; } = new();
    public List<CacheAlert> Alerts { get; set; } = new();
}

/// <summary>
/// 缓存指标快照
/// </summary>
public class CacheMetricSnapshot
{
    public DateTime Timestamp { get; set; }
    public double HitRate { get; set; }
    public double OperationsPerSecond { get; set; }
    public long UsedMemory { get; set; }
    public int ConnectionCount { get; set; }
    public double AverageLatencyMs { get; set; }
    public long EvictedKeys { get; set; }
}

/// <summary>
/// 缓存性能摘要
/// </summary>
public class CachePerformanceSummary
{
    public double AverageHitRate { get; set; }
    public double MinHitRate { get; set; }
    public double MaxHitRate { get; set; }
    public double AverageOps { get; set; }
    public double PeakOps { get; set; }
    public double AverageLatency { get; set; }
    public long TotalEvictions { get; set; }
}

/// <summary>
/// 缓存警报
/// </summary>
public class CacheAlert
{
    public DateTime Timestamp { get; set; }
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double Value { get; set; }
    public double Threshold { get; set; }
}

/// <summary>
/// 缓存优化报告
/// </summary>
public class CacheOptimizationReport
{
    public string ReportId { get; set; } = Guid.NewGuid().ToString();
    public DateTime GeneratedAt { get; set; }
    public CacheStatistics Statistics { get; set; } = new();
    public CachePerformanceAnalysis Performance { get; set; } = new();
    public CacheKeyAnalysis KeyAnalysis { get; set; } = new();
    public CacheMemoryAnalysis MemoryAnalysis { get; set; } = new();
    public List<CacheOptimizationRecommendation> Recommendations { get; set; } = new();
    public CacheScore Score { get; set; } = new();
}

/// <summary>
/// 缓存优化建议
/// </summary>
public class CacheOptimizationRecommendation
{
    public string Category { get; set; } = string.Empty;
    public string Issue { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public double ExpectedImprovement { get; set; }
    public string Implementation { get; set; } = string.Empty;
}

/// <summary>
/// 缓存评分
/// </summary>
public class CacheScore
{
    public int OverallScore { get; set; }
    public string Grade { get; set; } = string.Empty;
    public Dictionary<string, int> CategoryScores { get; set; } = new();
    public List<string> TopIssues { get; set; } = new();
    public List<string> QuickWins { get; set; } = new();
}

#endregion