using WhimLabAI.Shared.Dtos.Audit;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 审计日志应用层服务接口
/// </summary>
public interface IAuditLogApplicationService
{
    /// <summary>
    /// 创建审计日志
    /// </summary>
    Task<AuditLogDto> CreateAsync(CreateAuditLogDto createDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// 查询审计日志
    /// </summary>
    Task<AuditLogQueryResultDto> QueryAsync(AuditLogQueryDto queryDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取审计日志
    /// </summary>
    Task<AuditLogDto?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户活动摘要
    /// </summary>
    Task<List<UserActivitySummaryDto>> GetUserActivitySummaryAsync(
        DateTime startDate,
        DateTime endDate,
        int topCount = 10,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取审计日志统计
    /// </summary>
    Task<AuditLogStatsDto> GetStatsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 导出审计日志
    /// </summary>
    Task<byte[]> ExportAsync(AuditLogExportRequestDto exportRequest, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理旧的审计日志
    /// </summary>
    Task CleanupOldLogsAsync(int retentionDays, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检测异常行为
    /// </summary>
    Task<AnomalyDetectionResultDto> DetectAnomaliesAsync(
        AnomalyDetectionRequestDto request,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 生成审计报表
    /// </summary>
    Task<AuditReportDto> GenerateReportAsync(
        string reportType,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 归档审计日志
    /// </summary>
    Task<AuditLogArchiveResultDto> ArchiveLogsAsync(
        AuditLogArchiveRequestDto request,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量创建审计日志
    /// </summary>
    Task<int> BulkCreateAsync(
        List<CreateAuditLogDto> auditLogs,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取高风险操作
    /// </summary>
    Task<List<AuditLogDto>> GetHighRiskOperationsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取性能指标
    /// </summary>
    Task<AuditLogPerformanceMetricsDto> GetPerformanceMetricsAsync(
        CancellationToken cancellationToken = default);
}