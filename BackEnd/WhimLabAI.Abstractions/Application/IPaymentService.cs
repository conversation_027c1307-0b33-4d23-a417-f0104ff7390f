using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Abstractions.Application;

public interface IPaymentService
{
    Task<Result<PaymentResponseDto>> ProcessPaymentAsync(ProcessPaymentDto request, CancellationToken cancellationToken = default);
    Task<Result<PaymentDto>> GetPaymentAsync(Guid paymentId, CancellationToken cancellationToken = default);
    Task<Result<PaymentDto>> GetPaymentByNoAsync(string paymentNo, CancellationToken cancellationToken = default);
    Task<Result<PagedResult<PaymentDto>>> GetOrderPaymentsAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<Result<PaymentCallbackResultDto>> ProcessCallbackAsync(PaymentMethod paymentMethod, Dictionary<string, string> parameters, CancellationToken cancellationToken = default);
    Task<Result<RefundResponseDto>> ProcessRefundAsync(ProcessRefundDto request, CancellationToken cancellationToken = default);
    Task<Result<RefundDto>> GetRefundAsync(Guid refundId, CancellationToken cancellationToken = default);
    Task<Result<PagedResult<RefundDto>>> GetOrderRefundsAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<Result> CheckPaymentStatusAsync(string paymentNo, CancellationToken cancellationToken = default);
    Task<Result> CheckRefundStatusAsync(string refundNo, CancellationToken cancellationToken = default);
    Task<Result<PaymentStatisticsDto>> GetPaymentStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}

public class ProcessPaymentDto
{
    public Guid OrderId { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public decimal? Amount { get; set; } // Optional amount for validation
    public string? ReturnUrl { get; set; }
    public string? ClientIp { get; set; }
}

public class PaymentResponseDto
{
    public Guid PaymentId { get; set; }
    public string PaymentNo { get; set; } = string.Empty;
    public PaymentMethod PaymentMethod { get; set; }
    public decimal Amount { get; set; }
    public string? PaymentUrl { get; set; }
    public string? QrCode { get; set; }
    public Dictionary<string, string> FormData { get; set; } = new();
    public PaymentCreateType PaymentType { get; set; }
    public DateTime ExpireAt { get; set; }
}

public class PaymentDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string OrderNo { get; set; } = string.Empty;
    public string PaymentNo { get; set; } = string.Empty;
    public PaymentMethod PaymentMethod { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "CNY";
    public TransactionStatus Status { get; set; }
    public string? TransactionId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? PaidAt { get; set; }
    public DateTime? FailedAt { get; set; }
    public DateTime ExpireAt { get; set; }
    public string? FailReason { get; set; }
    public string? PayerAccount { get; set; }
    public string? GatewayResponse { get; set; }
}

public class PaymentCallbackResultDto
{
    public bool Success { get; set; }
    public string PaymentNo { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public TransactionStatus Status { get; set; }
    public decimal Amount { get; set; }
    public DateTime? PaidAt { get; set; }
    public string ResponseMessage { get; set; } = string.Empty;
}

public class ProcessRefundDto
{
    public Guid OrderId { get; set; }
    public Guid PaymentId { get; set; }
    public decimal RefundAmount { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? OperatorId { get; set; }
}

public class RefundResponseDto
{
    public Guid RefundId { get; set; }
    public string RefundNo { get; set; } = string.Empty;
    public RefundStatus Status { get; set; }
    public decimal RefundAmount { get; set; }
    public DateTime? ExpectedArrivalTime { get; set; }
}

public class RefundDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string OrderNo { get; set; } = string.Empty;
    public Guid PaymentId { get; set; }
    public string PaymentNo { get; set; } = string.Empty;
    public string RefundNo { get; set; } = string.Empty;
    public decimal RefundAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public RefundStatus Status { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public DateTime? RefundedAt { get; set; }
    public DateTime? FailedAt { get; set; }
    public string? FailReason { get; set; }
    public string? OperatorId { get; set; }
}

public class PaymentStatisticsDto
{
    public int TotalPayments { get; set; }
    public int SuccessfulPayments { get; set; }
    public int FailedPayments { get; set; }
    public decimal TotalAmount { get; set; }
    public Dictionary<string, PaymentMethodStatDto> PaymentsByMethod { get; set; } = new();
    public Dictionary<DateTime, DailyPaymentStatDto> DailyPayments { get; set; } = new();
    public double SuccessRate { get; set; }
    public RefundStatisticsDto RefundStats { get; set; } = new();
}

public class PaymentMethodStatDto
{
    public int Count { get; set; }
    public decimal Amount { get; set; }
    public double SuccessRate { get; set; }
}

public class DailyPaymentStatDto
{
    public int Count { get; set; }
    public decimal Amount { get; set; }
    public int RefundCount { get; set; }
    public decimal RefundAmount { get; set; }
}

public class RefundStatisticsDto
{
    public int TotalRefunds { get; set; }
    public int SuccessfulRefunds { get; set; }
    public decimal TotalAmount { get; set; }
    public double AverageProcessingTime { get; set; }
}
