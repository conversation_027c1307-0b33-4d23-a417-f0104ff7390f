using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 通知服务接口
/// </summary>
public interface INotificationService
{
    /// <summary>
    /// 发送通知给用户
    /// </summary>
    Task<Result> SendNotificationAsync(SendNotificationDto notification, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量发送通知
    /// </summary>
    Task<Result> SendBatchNotificationsAsync(SendBatchNotificationDto batchNotification, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送系统广播
    /// </summary>
    Task<Result> SendSystemBroadcastAsync(SystemBroadcastDto broadcast, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户通知列表
    /// </summary>
    Task<Result<PagedResult<NotificationDto>>> GetUserNotificationsAsync(Guid userId, NotificationQueryDto query, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 标记通知为已读
    /// </summary>
    Task<Result> MarkAsReadAsync(Guid userId, List<Guid> notificationIds, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 标记所有通知为已读
    /// </summary>
    Task<Result> MarkAllAsReadAsync(Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除通知
    /// </summary>
    Task<Result> DeleteNotificationsAsync(Guid userId, List<Guid> notificationIds, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取未读通知数量
    /// </summary>
    Task<Result<int>> GetUnreadCountAsync(Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建订阅计划变更通知
    /// </summary>
    Task<Result> NotifySubscriptionChangeAsync(Guid userId, string planName, string changeType, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建Token使用提醒通知
    /// </summary>
    Task<Result> NotifyTokenUsageAsync(Guid userId, int remainingTokens, int totalTokens, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建Agent发布通知
    /// </summary>
    Task<Result> NotifyAgentPublishedAsync(Guid agentId, string agentName, Guid creatorId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建异地登录通知
    /// </summary>
    Task<Result> NotifyAnomalousLoginAsync(Guid adminId, string location, string ipAddress, DateTime loginTime, CancellationToken cancellationToken = default);
}