using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface IMessageSearchService
{
    Task<Result<PagedResult<MessageSearchResultDto>>> SearchMessagesAsync(MessageSearchDto query, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<List<MessageContextDto>>> GetMessageContextAsync(Guid messageId, int contextSize, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<Dictionary<string, int>>> GetKeywordFrequencyAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<ConversationSummaryDto>> GenerateConversationSummaryAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
}

public class MessageSearchDto
{
    public string Keyword { get; set; } = string.Empty;
    public Guid? ConversationId { get; set; }
    public Guid? AgentId { get; set; }
    public DateRange? DateRange { get; set; }
    public string? Role { get; set; }
    public bool IncludeSystemMessages { get; set; } = false;
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class MessageSearchResultDto
{
    public Guid MessageId { get; set; }
    public Guid ConversationId { get; set; }
    public string ConversationTitle { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string HighlightedContent { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public int TokenCount { get; set; }
}

public class ConversationSummaryDto
{
    public Guid ConversationId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public List<string> KeyPoints { get; set; } = new();
    public Dictionary<string, int> Keywords { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}