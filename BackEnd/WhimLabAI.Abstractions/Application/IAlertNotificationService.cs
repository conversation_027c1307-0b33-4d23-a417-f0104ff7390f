using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Shared.DTOs.Monitoring;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 告警通知服务接口
/// </summary>
public interface IAlertNotificationService
{
    /// <summary>
    /// 处理Prometheus Alertmanager的webhook通知
    /// </summary>
    /// <param name="notification">告警通知信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<bool> HandleAlertWebhookAsync(AlertmanagerWebhook notification, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送自定义告警通知
    /// </summary>
    /// <param name="alert">告警信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendCustomAlertAsync(CustomAlert alert, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取告警历史记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="severity">严重程度筛选</param>
    /// <param name="service">服务筛选</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警历史列表</returns>
    Task<AlertHistoryResponse> GetAlertHistoryAsync(
        DateTime? startTime,
        DateTime? endTime,
        string? severity,
        string? service,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取告警统计信息
    /// </summary>
    /// <param name="period">统计周期（hour, day, week, month）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警统计数据</returns>
    Task<AlertStatistics> GetAlertStatisticsAsync(string period = "day", CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 测试告警通知渠道
    /// </summary>
    /// <param name="channel">通知渠道（email, dingtalk, wechat, slack）</param>
    /// <param name="recipient">接收者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>测试结果</returns>
    Task<bool> TestNotificationChannelAsync(string channel, string recipient, CancellationToken cancellationToken = default);
}