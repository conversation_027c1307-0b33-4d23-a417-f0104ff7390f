namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// Agent API密钥管理服务接口
/// </summary>
public interface IAgentApiKeyService
{
    /// <summary>
    /// 为Dify类型的Agent设置API密钥
    /// </summary>
    Task SetDifyApiKeyAsync(Guid agentId, string apiKey, string difyAppId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 为使用系统供应商的Agent设置API密钥
    /// </summary>
    Task SetSystemProviderApiKeyAsync(Guid agentId, string providerName, string apiKey, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Agent的活跃API密钥
    /// </summary>
    Task<string?> GetActiveApiKeyAsync(Guid agentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Agent的Dify API密钥
    /// </summary>
    Task<(string? apiKey, string? difyAppId)> GetDifyApiKeyAsync(Guid agentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 移除Agent的API密钥
    /// </summary>
    Task RemoveApiKeyAsync(Guid agentId, Guid apiKeyId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证API密钥是否有效
    /// </summary>
    Task<bool> ValidateApiKeyAsync(Guid agentId, CancellationToken cancellationToken = default);
}