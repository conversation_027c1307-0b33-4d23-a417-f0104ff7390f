using WhimLabAI.Shared.Dtos.Compliance;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 合规性服务接口
/// </summary>
public interface IComplianceService
{
    // 用户同意管理
    Task<Result<UserConsentDto>> RecordConsentAsync(Guid userId, string consentType, bool isGranted, string ipAddress, CancellationToken cancellationToken = default);
    Task<Result<List<UserConsentDto>>> GetUserConsentsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result> RevokeConsentAsync(Guid userId, string consentType, string ipAddress, CancellationToken cancellationToken = default);
    Task<Result<bool>> CheckConsentAsync(Guid userId, string consentType, CancellationToken cancellationToken = default);
    
    // 隐私设置管理
    Task<Result<PrivacySettingsDto>> GetPrivacySettingsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result> UpdatePrivacySettingsAsync(Guid userId, PrivacySettingsUpdateRequest request, CancellationToken cancellationToken = default);
    Task<Result> EnableStrictPrivacyAsync(Guid userId, CancellationToken cancellationToken = default);
    
    // 数据导出
    Task<Result<DataExportRequestDto>> CreateDataExportRequestAsync(Guid userId, string exportFormat, List<string> dataTypes, string ipAddress, CancellationToken cancellationToken = default);
    Task<Result<DataExportRequestDto>> GetDataExportRequestAsync(Guid requestId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<List<DataExportRequestDto>>> GetUserDataExportRequestsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result<string>> DownloadDataExportAsync(Guid requestId, Guid userId, CancellationToken cancellationToken = default);
    
    // 数据删除（被遗忘权）
    Task<Result> DeleteUserDataAsync(Guid userId, string reason, CancellationToken cancellationToken = default);
    Task<Result> AnonymizeUserDataAsync(Guid userId, CancellationToken cancellationToken = default);
    
    // 数据保留策略
    Task<Result<List<DataRetentionPolicyDto>>> GetDataRetentionPoliciesAsync(CancellationToken cancellationToken = default);
    Task<Result> ApplyDataRetentionPoliciesAsync(CancellationToken cancellationToken = default);
    
    // 合规性报告
    Task<Result<ComplianceReportDto>> GenerateComplianceReportAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<Result<UserDataReportDto>> GenerateUserDataReportAsync(Guid userId, CancellationToken cancellationToken = default);
    
    // 安全合规检查
    Task<Result<SecurityComplianceCheckDto>> RunSecurityComplianceCheckAsync(CancellationToken cancellationToken = default);
    Task<Result<PrivacyComplianceCheckDto>> RunPrivacyComplianceCheckAsync(CancellationToken cancellationToken = default);
}