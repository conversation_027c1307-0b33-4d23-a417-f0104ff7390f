using System.Collections.Generic;
using System.Threading.Tasks;
using WhimLab<PERSON>I.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 安全验证服务接口
/// </summary>
public interface ISecurityValidationService
{
    /// <summary>
    /// 运行完整的安全扫描
    /// </summary>
    Task<Result<SecurityScanResult>> RunFullSecurityScanAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查SQL注入漏洞
    /// </summary>
    Task<Result<List<SqlInjectionVulnerability>>> CheckSqlInjectionVulnerabilitiesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查XSS漏洞
    /// </summary>
    Task<Result<List<XssVulnerability>>> CheckXssVulnerabilitiesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查CSRF保护
    /// </summary>
    Task<Result<CsrfProtectionStatus>> CheckCsrfProtectionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查安全头配置
    /// </summary>
    Task<Result<SecurityHeadersStatus>> CheckSecurityHeadersAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查认证配置
    /// </summary>
    Task<Result<AuthenticationConfigStatus>> CheckAuthenticationConfigAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查加密配置
    /// </summary>
    Task<Result<EncryptionConfigStatus>> CheckEncryptionConfigAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查API安全
    /// </summary>
    Task<Result<ApiSecurityStatus>> CheckApiSecurityAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查依赖项漏洞
    /// </summary>
    Task<Result<List<DependencyVulnerability>>> CheckDependencyVulnerabilitiesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证输入数据
    /// </summary>
    Result<bool> ValidateInput(string input, InputValidationType validationType);
    
    /// <summary>
    /// 清理HTML内容
    /// </summary>
    Result<string> SanitizeHtml(string html);
    
    /// <summary>
    /// 生成安全报告
    /// </summary>
    Task<Result<SecurityReport>> GenerateSecurityReportAsync(CancellationToken cancellationToken = default);
}

#region DTOs

/// <summary>
/// 安全扫描结果
/// </summary>
public class SecurityScanResult
{
    public DateTime ScanTime { get; set; }
    public bool IsSecure { get; set; }
    public int CriticalIssues { get; set; }
    public int HighIssues { get; set; }
    public int MediumIssues { get; set; }
    public int LowIssues { get; set; }
    public List<SecurityIssue> Issues { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 安全问题
/// </summary>
public class SecurityIssue
{
    public string Id { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public SecuritySeverity Severity { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
    public string AffectedComponent { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
    public bool IsFixed { get; set; }
}

/// <summary>
/// 安全严重性
/// </summary>
public enum SecuritySeverity
{
    Critical,
    High,
    Medium,
    Low,
    Info
}

/// <summary>
/// SQL注入漏洞
/// </summary>
public class SqlInjectionVulnerability
{
    public string Location { get; set; } = string.Empty;
    public string VulnerableCode { get; set; } = string.Empty;
    public string Risk { get; set; } = string.Empty;
    public string Mitigation { get; set; } = string.Empty;
}

/// <summary>
/// XSS漏洞
/// </summary>
public class XssVulnerability
{
    public string Location { get; set; } = string.Empty;
    public string VulnerableCode { get; set; } = string.Empty;
    public XssType Type { get; set; }
    public string Risk { get; set; } = string.Empty;
    public string Mitigation { get; set; } = string.Empty;
}

/// <summary>
/// XSS类型
/// </summary>
public enum XssType
{
    Reflected,
    Stored,
    Dom
}

/// <summary>
/// CSRF保护状态
/// </summary>
public class CsrfProtectionStatus
{
    public bool IsEnabled { get; set; }
    public bool TokenValidationEnabled { get; set; }
    public bool SameSiteConfigured { get; set; }
    public List<string> UnprotectedEndpoints { get; set; } = new();
}

/// <summary>
/// 安全头状态
/// </summary>
public class SecurityHeadersStatus
{
    public bool ContentSecurityPolicyEnabled { get; set; }
    public bool XFrameOptionsEnabled { get; set; }
    public bool XContentTypeOptionsEnabled { get; set; }
    public bool StrictTransportSecurityEnabled { get; set; }
    public bool ReferrerPolicyEnabled { get; set; }
    public bool PermissionsPolicyEnabled { get; set; }
    public List<string> MissingHeaders { get; set; } = new();
}

/// <summary>
/// 认证配置状态
/// </summary>
public class AuthenticationConfigStatus
{
    public bool JwtConfigured { get; set; }
    public bool PasswordPolicyEnforced { get; set; }
    public bool MultiFactorEnabled { get; set; }
    public bool AccountLockoutEnabled { get; set; }
    public int PasswordMinLength { get; set; }
    public bool RequireUppercase { get; set; }
    public bool RequireDigit { get; set; }
    public bool RequireSpecialChar { get; set; }
    public int SessionTimeout { get; set; }
}

/// <summary>
/// 加密配置状态
/// </summary>
public class EncryptionConfigStatus
{
    public bool DataEncryptionEnabled { get; set; }
    public string EncryptionAlgorithm { get; set; } = string.Empty;
    public int KeySize { get; set; }
    public bool KeyRotationEnabled { get; set; }
    public DateTime? LastKeyRotation { get; set; }
    public bool HttpsEnforced { get; set; }
    public bool CertificateValid { get; set; }
}

/// <summary>
/// API安全状态
/// </summary>
public class ApiSecurityStatus
{
    public bool ApiKeyAuthEnabled { get; set; }
    public bool RateLimitingEnabled { get; set; }
    public bool InputValidationEnabled { get; set; }
    public bool OutputEncodingEnabled { get; set; }
    public bool ApiVersioningEnabled { get; set; }
    public List<string> InsecureEndpoints { get; set; } = new();
}

/// <summary>
/// 依赖项漏洞
/// </summary>
public class DependencyVulnerability
{
    public string PackageName { get; set; } = string.Empty;
    public string CurrentVersion { get; set; } = string.Empty;
    public string VulnerableVersion { get; set; } = string.Empty;
    public string SafeVersion { get; set; } = string.Empty;
    public SecuritySeverity Severity { get; set; }
    public string CveId { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 输入验证类型
/// </summary>
public enum InputValidationType
{
    Email,
    Url,
    PhoneNumber,
    AlphaNumeric,
    SafeHtml,
    SqlSafe,
    NoScript,
    FilePath
}

/// <summary>
/// 安全报告
/// </summary>
public class SecurityReport
{
    public string ReportId { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public string GeneratedBy { get; set; } = string.Empty;
    public SecurityScanResult ScanResult { get; set; } = new();
    public Dictionary<string, SecurityCheckResult> DetailedResults { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public SecurityScore OverallScore { get; set; } = new();
}

/// <summary>
/// 安全检查结果
/// </summary>
public class SecurityCheckResult
{
    public string CheckName { get; set; } = string.Empty;
    public bool Passed { get; set; }
    public string Details { get; set; } = string.Empty;
    public DateTime CheckedAt { get; set; }
}

/// <summary>
/// 安全评分
/// </summary>
public class SecurityScore
{
    public int TotalScore { get; set; }
    public int MaxScore { get; set; }
    public string Grade { get; set; } = string.Empty; // A+, A, B, C, D, F
    public Dictionary<string, int> CategoryScores { get; set; } = new();
}

#endregion