using WhimLabAI.Shared.Dtos.Subscription;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface ISubscriptionService
{
    Task<Result<List<SubscriptionPlanDto>>> GetSubscriptionPlansAsync(CancellationToken cancellationToken = default);
    Task<Result<SubscriptionDto>> GetCurrentSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result<Guid>> SubscribeAsync(SubscribeRequestDto request, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> UpgradeSubscriptionAsync(UpgradeSubscriptionDto request, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> CancelSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result<QuotaUsageDto>> GetQuotaUsageAsync(Guid userId, CancellationToken cancellationToken = default);
}