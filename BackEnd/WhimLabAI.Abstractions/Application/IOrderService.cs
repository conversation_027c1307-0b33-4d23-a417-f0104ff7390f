using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Abstractions.Application;

public interface IOrderService
{
    Task<Result<OrderDto>> CreateOrderAsync(CreateOrderDto request, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<OrderDto>> GetOrderAsync(Guid orderId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<OrderDto>> GetOrderByNoAsync(string orderNo, CancellationToken cancellationToken = default);
    Task<Result<PagedResult<OrderDto>>> GetUserOrdersAsync(Guid userId, OrderQueryDto query, CancellationToken cancellationToken = default);
    Task<Result> CancelOrderAsync(Guid orderId, Guid userId, string reason, CancellationToken cancellationToken = default);
    Task<Result<PaymentRequestDto>> InitiatePaymentAsync(Guid orderId, PaymentMethod paymentMethod, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> UpdateOrderStatusAsync(Guid orderId, OrderStatus newStatus, CancellationToken cancellationToken = default);
    Task<Result<OrderStatisticsDto>> GetOrderStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}

public class CreateOrderDto
{
    public List<OrderItemDto> Items { get; set; } = new();
    public string? CouponCode { get; set; }
    public string? Remark { get; set; }
    public string? ClientIp { get; set; }
    public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Alipay;
}

public class OrderItemDto
{
    public ProductType ProductType { get; set; }
    public Guid ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
}

public class OrderDto
{
    public Guid Id { get; set; }
    public string OrderNo { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public OrderStatus Status { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal PayableAmount { get; set; }
    public string Currency { get; set; } = "CNY";
    public List<OrderItemDto> Items { get; set; } = new();
    public string? CouponCode { get; set; }
    public string? Remark { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? PaidAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime? CancelledAt { get; set; }
    public DateTime? RefundedAt { get; set; }
    public string? CancellationReason { get; set; }
    public DateTime ExpireAt { get; set; }
}

public class OrderQueryDto
{
    public OrderStatus? Status { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Keyword { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string OrderBy { get; set; } = "CreatedAt";
    public bool Descending { get; set; } = true;
}

public class PaymentRequestDto
{
    public Guid OrderId { get; set; }
    public string OrderNo { get; set; } = string.Empty;
    public string PaymentNo { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string? PaymentUrl { get; set; }
    public string? QrCode { get; set; }
    public Dictionary<string, string> FormData { get; set; } = new();
    public PaymentCreateType PaymentType { get; set; }
    public DateTime ExpireAt { get; set; }
}

public class OrderStatisticsDto
{
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public int CancelledOrders { get; set; }
    public int RefundedOrders { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal AverageOrderValue { get; set; }
    public Dictionary<string, int> OrdersByType { get; set; } = new();
    public Dictionary<DateTime, decimal> DailyRevenue { get; set; } = new();
    public List<TopProductDto> TopProducts { get; set; } = new();
}

public class TopProductDto
{
    public Guid ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public ProductType ProductType { get; set; }
    public int SalesCount { get; set; }
    public decimal Revenue { get; set; }
}
