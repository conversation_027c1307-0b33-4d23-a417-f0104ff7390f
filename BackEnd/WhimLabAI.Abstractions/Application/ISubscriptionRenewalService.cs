using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface ISubscriptionRenewalService
{
    Task<Result<int>> ProcessExpiringSubscriptionsAsync(CancellationToken cancellationToken = default);
    Task<Result<int>> ProcessMonthlyResetAsync(CancellationToken cancellationToken = default);
    Task<Result> SendRenewalRemindersAsync(CancellationToken cancellationToken = default);
    Task<Result> CleanupExpiredSubscriptionsAsync(int daysToKeep = 90, CancellationToken cancellationToken = default);
}