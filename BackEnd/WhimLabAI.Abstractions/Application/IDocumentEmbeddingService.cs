using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 文档嵌入服务接口
/// </summary>
public interface IDocumentEmbeddingService
{
    /// <summary>
    /// 为文档生成嵌入向量
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <param name="forceRegenerate">是否强制重新生成（即使已有嵌入）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<Result<DocumentEmbeddingResult>> EmbedDocumentAsync(
        Guid documentId,
        bool forceRegenerate = false,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量为文档生成嵌入向量
    /// </summary>
    /// <param name="documentIds">文档ID列表</param>
    /// <param name="forceRegenerate">是否强制重新生成</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果列表</returns>
    Task<Result<IList<DocumentEmbeddingResult>>> EmbedDocumentsAsync(
        IList<Guid> documentIds,
        bool forceRegenerate = false,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 为知识库中的所有文档生成嵌入向量
    /// </summary>
    /// <param name="knowledgeBaseId">知识库ID</param>
    /// <param name="forceRegenerate">是否强制重新生成</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<Result<KnowledgeBaseEmbeddingResult>> EmbedKnowledgeBaseAsync(
        Guid knowledgeBaseId,
        bool forceRegenerate = false,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取文档嵌入状态
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>嵌入状态</returns>
    Task<Result<DocumentEmbeddingStatus>> GetEmbeddingStatusAsync(
        Guid documentId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 文档嵌入结果
/// </summary>
public class DocumentEmbeddingResult
{
    public Guid DocumentId { get; set; }
    public bool Success { get; set; }
    public int TotalChunks { get; set; }
    public int EmbeddedChunks { get; set; }
    public int FailedChunks { get; set; }
    public TimeSpan ProcessingTime { get; set; }
    public string? ErrorMessage { get; set; }
    public List<ChunkEmbeddingError> Errors { get; set; } = new();
}

/// <summary>
/// 知识库嵌入结果
/// </summary>
public class KnowledgeBaseEmbeddingResult
{
    public Guid KnowledgeBaseId { get; set; }
    public int TotalDocuments { get; set; }
    public int ProcessedDocuments { get; set; }
    public int FailedDocuments { get; set; }
    public int TotalChunks { get; set; }
    public int EmbeddedChunks { get; set; }
    public TimeSpan ProcessingTime { get; set; }
    public List<DocumentEmbeddingResult> DocumentResults { get; set; } = new();
}

/// <summary>
/// 文档嵌入状态
/// </summary>
public class DocumentEmbeddingStatus
{
    public Guid DocumentId { get; set; }
    public int TotalChunks { get; set; }
    public int EmbeddedChunks { get; set; }
    public int PendingChunks { get; set; }
    public DateTime? LastEmbeddedAt { get; set; }
    public bool IsFullyEmbedded => TotalChunks > 0 && EmbeddedChunks == TotalChunks;
}

/// <summary>
/// 块嵌入错误
/// </summary>
public class ChunkEmbeddingError
{
    public Guid ChunkId { get; set; }
    public int ChunkIndex { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}