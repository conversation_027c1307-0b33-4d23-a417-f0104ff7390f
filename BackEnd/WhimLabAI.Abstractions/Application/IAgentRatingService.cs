using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// Agent评价服务接口
/// </summary>
public interface IAgentRatingService
{
    /// <summary>
    /// 提交或更新评价
    /// </summary>
    /// <param name="agentId">Agent ID</param>
    /// <param name="userId">用户 ID</param>
    /// <param name="score">评分（1-5）</param>
    /// <param name="feedback">评价内容</param>
    /// <returns>评价结果</returns>
    Task<Result<AgentRatingDto>> SubmitRatingAsync(Guid agentId, Guid userId, int score, string? feedback = null);
    
    /// <summary>
    /// 获取Agent的评价列表
    /// </summary>
    /// <param name="agentId">Agent ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>评价列表</returns>
    Task<Result<PagedResult<AgentRatingDto>>> GetAgentRatingsAsync(Guid agentId, int pageNumber = 1, int pageSize = 20);
    
    /// <summary>
    /// 获取用户对Agent的评价
    /// </summary>
    /// <param name="agentId">Agent ID</param>
    /// <param name="userId">用户 ID</param>
    /// <returns>评价信息</returns>
    Task<Result<AgentRatingDto?>> GetUserRatingAsync(Guid agentId, Guid userId);
    
    /// <summary>
    /// 获取用户的所有评价
    /// </summary>
    /// <param name="userId">用户 ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>评价列表</returns>
    Task<Result<PagedResult<AgentRatingDto>>> GetUserRatingsAsync(Guid userId, int pageNumber = 1, int pageSize = 20);
    
    /// <summary>
    /// 删除评价
    /// </summary>
    /// <param name="agentId">Agent ID</param>
    /// <param name="userId">用户 ID</param>
    /// <returns>删除结果</returns>
    Task<Result> DeleteRatingAsync(Guid agentId, Guid userId);
    
    /// <summary>
    /// 标记评价为有帮助
    /// </summary>
    /// <param name="ratingId">评价 ID</param>
    /// <param name="userId">用户 ID</param>
    /// <returns>操作结果</returns>
    Task<Result> MarkRatingAsHelpfulAsync(Guid ratingId, Guid userId);
    
    /// <summary>
    /// 标记评价为无帮助
    /// </summary>
    /// <param name="ratingId">评价 ID</param>
    /// <param name="userId">用户 ID</param>
    /// <returns>操作结果</returns>
    Task<Result> MarkRatingAsUnhelpfulAsync(Guid ratingId, Guid userId);
    
    /// <summary>
    /// 获取Agent的评价统计
    /// </summary>
    /// <param name="agentId">Agent ID</param>
    /// <returns>评价统计信息</returns>
    Task<Result<AgentRatingStatisticsDto>> GetRatingStatisticsAsync(Guid agentId);
}