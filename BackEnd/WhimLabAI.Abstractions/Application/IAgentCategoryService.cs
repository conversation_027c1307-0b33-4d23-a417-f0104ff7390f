using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface IAgentCategoryService
{
    Task<Result<List<AgentCategoryDto>>> GetCategoriesAsync(CancellationToken cancellationToken = default);
    Task<Result<AgentCategoryDto>> GetCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default);
    Task<Result<AgentCategoryDto>> CreateCategoryAsync(CreateAgentCategoryDto request, CancellationToken cancellationToken = default);
    Task<Result<AgentCategoryDto>> UpdateCategoryAsync(Guid categoryId, UpdateAgentCategoryDto request, CancellationToken cancellationToken = default);
    Task<Result> DeleteCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default);
}