using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Shared.Dtos.Admin.Rbac;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 管理员权限服务接口
/// </summary>
public interface IAdminPermissionService
{
    /// <summary>
    /// 获取所有权限列表
    /// </summary>
    Task<Result<List<PermissionDto>>> GetAllPermissionsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 按模块获取权限列表
    /// </summary>
    Task<Result<Dictionary<string, List<PermissionDto>>>> GetPermissionsGroupedByModuleAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取权限树形结构
    /// </summary>
    Task<Result<List<PermissionTreeDto>>> GetPermissionTreeAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据权限代码获取权限详情
    /// </summary>
    Task<Result<PermissionDto>> GetPermissionByCodeAsync(string code, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查用户是否有指定权限
    /// </summary>
    Task<Result<bool>> CheckUserPermissionAsync(Guid userId, string permissionCode, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量检查用户权限
    /// </summary>
    Task<Result<Dictionary<string, bool>>> CheckUserPermissionsAsync(Guid userId, List<string> permissionCodes, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的所有权限
    /// </summary>
    Task<Result<List<PermissionDto>>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取角色的权限
    /// </summary>
    Task<Result<List<PermissionDto>>> GetRolePermissionsAsync(Guid roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 刷新用户权限缓存
    /// </summary>
    Task<Result<bool>> RefreshUserPermissionsCacheAsync(Guid userId, CancellationToken cancellationToken = default);
}