using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 知识库服务接口
/// </summary>
public interface IKnowledgeBaseService
{
    /// <summary>
    /// 创建知识库
    /// </summary>
    Task<Result<KnowledgeBaseDto>> CreateKnowledgeBaseAsync(
        CreateKnowledgeBaseDto dto,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新知识库
    /// </summary>
    Task<Result<KnowledgeBaseDto>> UpdateKnowledgeBaseAsync(
        Guid knowledgeBaseId,
        UpdateKnowledgeBaseDto dto,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除知识库
    /// </summary>
    Task<Result> DeleteKnowledgeBaseAsync(
        Guid knowledgeBaseId,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取知识库详情
    /// </summary>
    Task<Result<KnowledgeBaseDto>> GetKnowledgeBaseAsync(
        Guid knowledgeBaseId,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的知识库列表
    /// </summary>
    Task<Result<IEnumerable<KnowledgeBaseDto>>> GetUserKnowledgeBasesAsync(
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 搜索知识库
    /// </summary>
    Task<Result<PagedResult<KnowledgeBaseDto>>> SearchKnowledgeBasesAsync(
        SearchKnowledgeBaseDto dto,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 上传文档到知识库
    /// </summary>
    Task<Result<DocumentDto>> UploadDocumentAsync(
        Guid knowledgeBaseId,
        UploadDocumentDto dto,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除文档
    /// </summary>
    Task<Result> DeleteDocumentAsync(
        Guid documentId,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取知识库的文档列表
    /// </summary>
    Task<Result<PagedResult<DocumentDto>>> GetDocumentsAsync(
        Guid knowledgeBaseId,
        GetDocumentsDto dto,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 处理文档（分块和嵌入）
    /// </summary>
    Task<Result> ProcessDocumentAsync(
        Guid documentId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 搜索知识库内容
    /// </summary>
    Task<Result<IEnumerable<SearchResultDto>>> SearchKnowledgeAsync(
        Guid knowledgeBaseId,
        string query,
        int topK,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量搜索多个知识库
    /// </summary>
    Task<Result<IEnumerable<SearchResultDto>>> SearchMultipleKnowledgeBasesAsync(
        IEnumerable<Guid> knowledgeBaseIds,
        string query,
        int topK,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取知识库统计信息
    /// </summary>
    Task<Result<KnowledgeBaseStatisticsDto>> GetStatisticsAsync(
        Guid knowledgeBaseId,
        Guid userId,
        CancellationToken cancellationToken = default);
}