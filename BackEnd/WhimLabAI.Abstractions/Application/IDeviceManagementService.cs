using WhimLabAI.Shared.Dtos.Device;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 设备管理服务接口
/// </summary>
public interface IDeviceManagementService
{
    /// <summary>
    /// 获取用户设备列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="currentDeviceId">当前设备ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备列表</returns>
    Task<Result<DeviceListDto>> GetUserDevicesAsync(Guid userId, string? currentDeviceId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 撤销设备授权
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<Result> RevokeDeviceAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新设备信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="updateDeviceDto">更新设备信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<Result> UpdateDeviceAsync(Guid userId, UpdateDeviceDto updateDeviceDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// 激活设备
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<Result> ActivateDeviceAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 停用设备
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<Result> DeactivateDeviceAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备统计信息</returns>
    Task<Result<DeviceStatsDto>> GetDeviceStatsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量撤销设备授权
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="deviceIds">设备ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<Result> RevokeMultipleDevicesAsync(Guid userId, List<string> deviceIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理非活跃设备
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="daysInactive">非活跃天数阈值</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    Task<Result<int>> CleanupInactiveDevicesAsync(Guid userId, int daysInactive = 90, CancellationToken cancellationToken = default);
}