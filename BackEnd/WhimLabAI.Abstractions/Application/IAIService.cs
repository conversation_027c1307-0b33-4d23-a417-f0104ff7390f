using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Conversation;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// AI服务接口
/// </summary>
public interface IAIService
{
    /// <summary>
    /// 处理对话消息
    /// </summary>
    Task<Result<ConversationResponseDto>> ProcessMessageAsync(
        ProcessMessageDto request,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 处理流式对话消息
    /// </summary>
    IAsyncEnumerable<Result<StreamMessageChunk>> ProcessMessageStreamAsync(
        ProcessMessageDto request,
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 计算消息的Token数量
    /// </summary>
    Task<Result<TokenCountDto>> CountTokensAsync(
        TokenCountRequestDto request,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取可用的AI模型列表
    /// </summary>
    Task<Result<List<AIModelDto>>> GetAvailableModelsAsync(
        AIProviderType? providerType = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证Agent的配置
    /// </summary>
    Task<Result<AgentValidationResult>> ValidateAgentConfigurationAsync(
        Guid agentId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 执行Agent特定功能
    /// </summary>
    Task<Result<AgentExecutionResult>> ExecuteAgentFunctionAsync(
        AgentFunctionRequest request,
        Guid userId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 处理消息请求
/// </summary>
public class ProcessMessageDto
{
    public Guid ConversationId { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<MessageAttachmentDto>? Attachments { get; set; }
    public Dictionary<string, object>? Parameters { get; set; }
    public bool EnableStreaming { get; set; } = true;
}

/// <summary>
/// 对话响应
/// </summary>
public class ConversationResponseDto
{
    public Guid MessageId { get; set; }
    public string Content { get; set; } = string.Empty;
    public TokenUsageDto TokenUsage { get; set; } = new();
    public long ResponseTimeMs { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Token使用情况
/// </summary>
public class TokenUsageDto
{
    public int PromptTokens { get; set; }
    public int CompletionTokens { get; set; }
    public int TotalTokens { get; set; }
}

/// <summary>
/// Token计数请求
/// </summary>
public class TokenCountRequestDto
{
    public string Text { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
}

/// <summary>
/// Token计数响应
/// </summary>
public class TokenCountDto
{
    public int Count { get; set; }
    public string Model { get; set; } = string.Empty;
}

/// <summary>
/// AI模型信息
/// </summary>
public class AIModelDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public AIProviderType Provider { get; set; }
    public int MaxTokens { get; set; }
    public int ContextLength { get; set; }
    public bool SupportsStreaming { get; set; }
    public bool SupportsVision { get; set; }
    public bool SupportsEmbedding { get; set; }
    public bool IsAvailable { get; set; }
    public decimal PricePerMillionTokens { get; set; }
}

/// <summary>
/// Agent验证结果
/// </summary>
public class AgentValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Agent功能请求
/// </summary>
public class AgentFunctionRequest
{
    public Guid AgentId { get; set; }
    public string FunctionName { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Guid? ConversationId { get; set; }
}

/// <summary>
/// Agent执行结果
/// </summary>
public class AgentExecutionResult
{
    public bool Success { get; set; }
    public object? Result { get; set; }
    public string? Error { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// 上下文管理服务接口
/// </summary>
public interface IContextManagementService
{
    /// <summary>
    /// 构建对话上下文
    /// </summary>
    Task<Result<ConversationContext>> BuildContextAsync(
        Guid conversationId,
        int maxMessages = 10,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 优化上下文（移除冗余信息）
    /// </summary>
    Task<Result<ConversationContext>> OptimizeContextAsync(
        ConversationContext context,
        int maxTokens,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 保存上下文快照
    /// </summary>
    Task<Result> SaveContextSnapshotAsync(
        Guid conversationId,
        ConversationContext context,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 对话上下文
/// </summary>
public class ConversationContext
{
    public Guid ConversationId { get; set; }
    public List<Infrastructure.AIMessage> Messages { get; set; } = new();
    public Dictionary<string, object> Variables { get; set; } = new();
    public int TotalTokens { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Token计量服务接口
/// </summary>
public interface ITokenMeteringService
{
    /// <summary>
    /// 记录Token使用
    /// </summary>
    Task<Result> RecordTokenUsageAsync(
        TokenUsageRecord record,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户Token使用统计
    /// </summary>
    Task<Result<TokenUsageStatistics>> GetUserTokenUsageAsync(
        Guid userId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查用户Token配额
    /// </summary>
    Task<Result<TokenQuotaStatus>> CheckTokenQuotaAsync(
        Guid userId,
        int requiredTokens,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Token使用记录
/// </summary>
public class TokenUsageRecord
{
    public Guid UserId { get; set; }
    public Guid ConversationId { get; set; }
    public Guid? MessageId { get; set; }
    public string Model { get; set; } = string.Empty;
    public int PromptTokens { get; set; }
    public int CompletionTokens { get; set; }
    public int TotalTokens { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Token使用统计
/// </summary>
public class TokenUsageStatistics
{
    public Guid UserId { get; set; }
    public int TotalTokensUsed { get; set; }
    public int TotalPromptTokens { get; set; }
    public int TotalCompletionTokens { get; set; }
    public Dictionary<string, int> TokensByModel { get; set; } = new();
    public Dictionary<DateTime, int> DailyUsage { get; set; } = new();
    public decimal EstimatedCost { get; set; }
}

/// <summary>
/// Token配额状态
/// </summary>
public class TokenQuotaStatus
{
    public bool HasSufficientTokens { get; set; }
    public int RemainingTokens { get; set; }
    public int MonthlyLimit { get; set; }
    public DateTime? ResetDate { get; set; }
    public string? Message { get; set; }
}