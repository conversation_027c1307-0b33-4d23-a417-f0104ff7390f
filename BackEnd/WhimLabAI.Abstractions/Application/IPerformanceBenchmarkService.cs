using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 性能基准测试服务接口
/// </summary>
public interface IPerformanceBenchmarkService
{
    /// <summary>
    /// 运行API端点基准测试
    /// </summary>
    Task<Result<ApiEndpointBenchmark>> BenchmarkApiEndpointAsync(
        string endpoint, 
        HttpMethod method, 
        object? requestBody = null,
        int iterations = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 运行数据库查询基准测试
    /// </summary>
    Task<Result<DatabaseBenchmark>> BenchmarkDatabaseOperationAsync(
        string operationName,
        Func<Task> operation,
        int iterations = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 运行缓存操作基准测试
    /// </summary>
    Task<Result<CacheBenchmark>> BenchmarkCacheOperationAsync(
        string operationName,
        Func<Task> writeOperation,
        Func<Task> readOperation,
        int iterations = 1000,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 运行并发负载测试
    /// </summary>
    Task<Result<ConcurrencyBenchmark>> BenchmarkConcurrencyAsync(
        string operationName,
        Func<Task> operation,
        int concurrentUsers = 10,
        int requestsPerUser = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 运行内存使用基准测试
    /// </summary>
    Task<Result<MemoryBenchmark>> BenchmarkMemoryUsageAsync(
        string operationName,
        Func<Task> operation,
        int iterations = 50,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 运行完整的性能测试套件
    /// </summary>
    Task<Result<PerformanceTestSuite>> RunFullPerformanceTestSuiteAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 比较两个性能测试结果
    /// </summary>
    Result<PerformanceComparison> ComparePerformanceResults(
        PerformanceTestSuite baseline,
        PerformanceTestSuite current);

    /// <summary>
    /// 生成性能报告
    /// </summary>
    Task<Result<PerformanceReport>> GeneratePerformanceReportAsync(
        PerformanceTestSuite testSuite,
        CancellationToken cancellationToken = default);
}

#region DTOs

/// <summary>
/// API端点基准测试结果
/// </summary>
public class ApiEndpointBenchmark
{
    public string Endpoint { get; set; } = string.Empty;
    public HttpMethod Method { get; set; }
    public int Iterations { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan TotalDuration => EndTime - StartTime;
    public double AverageResponseTime { get; set; }
    public double MinResponseTime { get; set; }
    public double MaxResponseTime { get; set; }
    public double MedianResponseTime { get; set; }
    public double P95ResponseTime { get; set; }
    public double P99ResponseTime { get; set; }
    public int SuccessfulRequests { get; set; }
    public int FailedRequests { get; set; }
    public double RequestsPerSecond { get; set; }
    public Dictionary<int, int> StatusCodeDistribution { get; set; } = new();
    public List<double> ResponseTimes { get; set; } = new();
}

/// <summary>
/// 数据库基准测试结果
/// </summary>
public class DatabaseBenchmark
{
    public string OperationName { get; set; } = string.Empty;
    public int Iterations { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan TotalDuration => EndTime - StartTime;
    public double AverageExecutionTime { get; set; }
    public double MinExecutionTime { get; set; }
    public double MaxExecutionTime { get; set; }
    public double MedianExecutionTime { get; set; }
    public double P95ExecutionTime { get; set; }
    public double P99ExecutionTime { get; set; }
    public int SuccessfulOperations { get; set; }
    public int FailedOperations { get; set; }
    public double OperationsPerSecond { get; set; }
    public long TotalRowsAffected { get; set; }
    public List<double> ExecutionTimes { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 缓存基准测试结果
/// </summary>
public class CacheBenchmark
{
    public string OperationName { get; set; } = string.Empty;
    public int Iterations { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan TotalDuration => EndTime - StartTime;
    
    // 写操作统计
    public double AverageWriteTime { get; set; }
    public double MinWriteTime { get; set; }
    public double MaxWriteTime { get; set; }
    public double MedianWriteTime { get; set; }
    public double WritesPerSecond { get; set; }
    
    // 读操作统计
    public double AverageReadTime { get; set; }
    public double MinReadTime { get; set; }
    public double MaxReadTime { get; set; }
    public double MedianReadTime { get; set; }
    public double ReadsPerSecond { get; set; }
    
    // 缓存命中率
    public int CacheHits { get; set; }
    public int CacheMisses { get; set; }
    public double CacheHitRate => CacheHits + CacheMisses > 0 ? (double)CacheHits / (CacheHits + CacheMisses) : 0;
    
    public List<double> WriteTimes { get; set; } = new();
    public List<double> ReadTimes { get; set; } = new();
}

/// <summary>
/// 并发基准测试结果
/// </summary>
public class ConcurrencyBenchmark
{
    public string OperationName { get; set; } = string.Empty;
    public int ConcurrentUsers { get; set; }
    public int RequestsPerUser { get; set; }
    public int TotalRequests => ConcurrentUsers * RequestsPerUser;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan TotalDuration => EndTime - StartTime;
    public double AverageResponseTime { get; set; }
    public double MinResponseTime { get; set; }
    public double MaxResponseTime { get; set; }
    public double MedianResponseTime { get; set; }
    public double P95ResponseTime { get; set; }
    public double P99ResponseTime { get; set; }
    public int SuccessfulRequests { get; set; }
    public int FailedRequests { get; set; }
    public double RequestsPerSecond { get; set; }
    public double ErrorRate => TotalRequests > 0 ? (double)FailedRequests / TotalRequests : 0;
    public List<ConcurrencyTimeSlot> TimeSlots { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 并发时间片
/// </summary>
public class ConcurrencyTimeSlot
{
    public DateTime Timestamp { get; set; }
    public int ActiveRequests { get; set; }
    public double RequestsPerSecond { get; set; }
    public double AverageResponseTime { get; set; }
    public int Errors { get; set; }
}

/// <summary>
/// 内存基准测试结果
/// </summary>
public class MemoryBenchmark
{
    public string OperationName { get; set; } = string.Empty;
    public int Iterations { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan TotalDuration => EndTime - StartTime;
    
    // 内存使用统计
    public long InitialMemoryUsage { get; set; }
    public long PeakMemoryUsage { get; set; }
    public long FinalMemoryUsage { get; set; }
    public long AverageMemoryUsage { get; set; }
    public long MemoryLeaked => FinalMemoryUsage - InitialMemoryUsage;
    
    // GC统计
    public int Gen0Collections { get; set; }
    public int Gen1Collections { get; set; }
    public int Gen2Collections { get; set; }
    public long TotalAllocatedBytes { get; set; }
    
    // 内存分配速率
    public double AllocationRatePerSecond { get; set; }
    
    public List<MemorySnapshot> Snapshots { get; set; } = new();
}

/// <summary>
/// 内存快照
/// </summary>
public class MemorySnapshot
{
    public DateTime Timestamp { get; set; }
    public long WorkingSet { get; set; }
    public long GCHeapSize { get; set; }
    public long Gen0Size { get; set; }
    public long Gen1Size { get; set; }
    public long Gen2Size { get; set; }
    public long LargeObjectHeapSize { get; set; }
}

/// <summary>
/// 性能测试套件
/// </summary>
public class PerformanceTestSuite
{
    public string TestSuiteId { get; set; } = Guid.NewGuid().ToString();
    public DateTime ExecutedAt { get; set; }
    public string Environment { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, ApiEndpointBenchmark> ApiEndpoints { get; set; } = new();
    public Dictionary<string, DatabaseBenchmark> DatabaseOperations { get; set; } = new();
    public Dictionary<string, CacheBenchmark> CacheOperations { get; set; } = new();
    public Dictionary<string, ConcurrencyBenchmark> ConcurrencyTests { get; set; } = new();
    public Dictionary<string, MemoryBenchmark> MemoryTests { get; set; } = new();
    public SystemMetrics SystemMetrics { get; set; } = new();
    public TimeSpan TotalExecutionTime { get; set; }
}

/// <summary>
/// 系统指标
/// </summary>
public class SystemMetrics
{
    public double CpuUsage { get; set; }
    public long MemoryUsage { get; set; }
    public long DiskIORead { get; set; }
    public long DiskIOWrite { get; set; }
    public long NetworkIn { get; set; }
    public long NetworkOut { get; set; }
    public int ActiveConnections { get; set; }
    public int ThreadCount { get; set; }
}

/// <summary>
/// 性能比较结果
/// </summary>
public class PerformanceComparison
{
    public string ComparisonId { get; set; } = Guid.NewGuid().ToString();
    public DateTime ComparedAt { get; set; }
    public string BaselineVersion { get; set; } = string.Empty;
    public string CurrentVersion { get; set; } = string.Empty;
    public List<PerformanceChange> Changes { get; set; } = new();
    public BenchmarkPerformanceSummary Summary { get; set; } = new();
}

/// <summary>
/// 性能变化
/// </summary>
public class PerformanceChange
{
    public string Category { get; set; } = string.Empty;
    public string Operation { get; set; } = string.Empty;
    public string Metric { get; set; } = string.Empty;
    public double BaselineValue { get; set; }
    public double CurrentValue { get; set; }
    public double ChangePercent => BaselineValue > 0 ? ((CurrentValue - BaselineValue) / BaselineValue) * 100 : 0;
    public ChangeType Type => ChangePercent > 5 ? ChangeType.Regression : 
                             ChangePercent < -5 ? ChangeType.Improvement : 
                             ChangeType.NoChange;
}

/// <summary>
/// 变化类型
/// </summary>
public enum ChangeType
{
    Improvement,
    NoChange,
    Regression
}

/// <summary>
/// 基准测试性能摘要
/// </summary>
public class BenchmarkPerformanceSummary
{
    public int TotalImprovements { get; set; }
    public int TotalRegressions { get; set; }
    public int TotalNoChange { get; set; }
    public double OverallPerformanceChange { get; set; }
    public List<string> CriticalRegressions { get; set; } = new();
    public List<string> SignificantImprovements { get; set; } = new();
}

/// <summary>
/// 性能报告
/// </summary>
public class PerformanceReport
{
    public string ReportId { get; set; } = Guid.NewGuid().ToString();
    public DateTime GeneratedAt { get; set; }
    public PerformanceTestSuite TestResults { get; set; } = new();
    public PerformanceAnalysis Analysis { get; set; } = new();
    public List<BenchmarkPerformanceRecommendation> Recommendations { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 性能分析
/// </summary>
public class PerformanceAnalysis
{
    public List<PerformanceBottleneck> Bottlenecks { get; set; } = new();
    public List<PerformanceTrend> Trends { get; set; } = new();
    public ResourceUtilization ResourceUtilization { get; set; } = new();
    public ScalabilityAnalysis Scalability { get; set; } = new();
}

/// <summary>
/// 性能瓶颈
/// </summary>
public class PerformanceBottleneck
{
    public string Component { get; set; } = string.Empty;
    public string Issue { get; set; } = string.Empty;
    public double Impact { get; set; }
    public string Severity { get; set; } = string.Empty;
}

/// <summary>
/// 性能趋势
/// </summary>
public class PerformanceTrend
{
    public string Metric { get; set; } = string.Empty;
    public string Direction { get; set; } = string.Empty;
    public double ChangeRate { get; set; }
}

/// <summary>
/// 资源利用率
/// </summary>
public class ResourceUtilization
{
    public double CpuUtilization { get; set; }
    public double MemoryUtilization { get; set; }
    public double DiskUtilization { get; set; }
    public double NetworkUtilization { get; set; }
}

/// <summary>
/// 可扩展性分析
/// </summary>
public class ScalabilityAnalysis
{
    public double MaxThroughput { get; set; }
    public int OptimalConcurrency { get; set; }
    public double BreakingPoint { get; set; }
    public string ScalabilityGrade { get; set; } = string.Empty;
}

/// <summary>
/// 基准测试性能建议
/// </summary>
public class BenchmarkPerformanceRecommendation
{
    public string Area { get; set; } = string.Empty;
    public string Issue { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public double ExpectedImprovement { get; set; }
}

#endregion