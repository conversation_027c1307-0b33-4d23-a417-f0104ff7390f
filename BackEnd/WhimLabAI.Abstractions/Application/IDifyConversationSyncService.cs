using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// Dify对话同步服务接口
/// </summary>
public interface IDifyConversationSyncService
{
    /// <summary>
    /// 创建或更新Dify对话映射
    /// </summary>
    Task<Result> CreateOrUpdateMappingAsync(
        Guid conversationId, 
        Guid agentId, 
        string difyConversationId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Dify对话ID
    /// </summary>
    Task<Result<string>> GetDifyConversationIdAsync(
        Guid conversationId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 同步对话历史
    /// </summary>
    Task<Result> SyncConversationHistoryAsync(
        Guid conversationId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 清理过期的映射
    /// </summary>
    Task<Result<int>> CleanupExpiredMappingsAsync(
        int daysToKeep = 30, 
        CancellationToken cancellationToken = default);
}