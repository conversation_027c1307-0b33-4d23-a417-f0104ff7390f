using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Shared.Dtos.Admin.Rbac;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 管理员角色服务接口
/// </summary>
public interface IAdminRoleService
{
    /// <summary>
    /// 获取角色列表
    /// </summary>
    Task<Result<PagedList<RoleDto>>> GetRolesAsync(RoleQueryDto query, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取角色详情
    /// </summary>
    Task<Result<RoleDto>> GetRoleByIdAsync(Guid roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取角色详情（通过名称）
    /// </summary>
    Task<Result<RoleDto>> GetRoleByNameAsync(string roleName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建角色
    /// </summary>
    Task<Result<RoleDto>> CreateRoleAsync(CreateRoleDto dto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新角色
    /// </summary>
    Task<Result<RoleDto>> UpdateRoleAsync(Guid roleId, UpdateRoleDto dto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除角色
    /// </summary>
    Task<Result<bool>> DeleteRoleAsync(Guid roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 启用/禁用角色
    /// </summary>
    Task<Result<bool>> SetRoleStatusAsync(Guid roleId, bool isActive, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取所有可用的权限列表
    /// </summary>
    Task<Result<List<RolePermissionDto>>> GetAllPermissionsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 为角色分配权限
    /// </summary>
    Task<Result<bool>> AssignPermissionsToRoleAsync(Guid roleId, List<string> permissions, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取角色的权限列表
    /// </summary>
    Task<Result<List<string>>> GetRolePermissionsAsync(Guid roleId, CancellationToken cancellationToken = default);
}