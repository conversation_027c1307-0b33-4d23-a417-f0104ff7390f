using WhimLabAI.Shared.Dtos.Auth;
using WhimLabAI.Shared.Dtos.Customer.Auth;
using WhimLabAI.Shared.Dtos.Customer.Security;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 客户认证服务接口
/// </summary>
public interface ICustomerAuthService
{
    /// <summary>
    /// 客户登录
    /// </summary>
    Task<Result<AuthResponseDto>> LoginAsync(CustomerLoginDto loginDto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证码登录
    /// </summary>
    Task<Result<AuthResponseDto>> LoginByVerificationCodeAsync(CustomerLoginByVerificationCodeDto loginDto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 客户注册
    /// </summary>
    Task<Result<AuthResponseDto>> RegisterAsync(CustomerRegisterDto registerDto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 通过用户名注册
    /// </summary>
    Task<Result<AuthResponseDto>> RegisterByUsernameAsync(CustomerRegisterByUsernameDto registerDto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 通过邮箱注册
    /// </summary>
    Task<Result<AuthResponseDto>> RegisterByEmailAsync(CustomerRegisterByEmailDto registerDto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 通过手机号注册
    /// </summary>
    Task<Result<AuthResponseDto>> RegisterByPhoneAsync(CustomerRegisterByPhoneDto registerDto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查账号是否存在
    /// </summary>
    Task<Result<bool>> CheckAccountExistsAsync(CheckAccountExistsDto checkDto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 刷新令牌
    /// </summary>
    Task<Result<AuthResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshDto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 登出
    /// </summary>
    Task<Result> LogoutAsync(Guid userId, string? deviceId = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 修改密码
    /// </summary>
    Task<Result> ChangePasswordAsync(Guid userId, string oldPassword, string newPassword, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 重置密码
    /// </summary>
    Task<Result> ResetPasswordAsync(string account, string newPassword, string verificationCode, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送验证码
    /// </summary>
    Task<Result<object>> SendVerificationCodeAsync(WhimLabAI.Shared.Dtos.Customer.Auth.SendVerificationCodeDto sendCodeDto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证验证码
    /// </summary>
    Task<Result<object>> VerifyCodeAsync(VerifyCodeDto verifyDto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 生成二维码登录会话
    /// </summary>
    Task<Result<QRCodeResponseDto>> GenerateQRCodeAsync(GenerateQRCodeDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取二维码状态
    /// </summary>
    Task<Result<QRCodeStatusDto>> GetQRCodeStatusAsync(string sessionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 扫描二维码
    /// </summary>
    Task<Result<QRCodeScanResponseDto>> ScanQRCodeAsync(ScanQRCodeDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 确认二维码登录
    /// </summary>
    Task<Result<AuthResponseDto>> ConfirmQRCodeLoginAsync(ConfirmQRCodeLoginDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 取消二维码登录
    /// </summary>
    Task<Result> CancelQRCodeAsync(string sessionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 启用双因素认证
    /// </summary>
    Task<Result<TwoFactorSetupDto>> EnableTwoFactorAsync(Guid userId, EnableTwoFactorDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 确认双因素认证设置
    /// </summary>
    Task<Result> ConfirmTwoFactorAsync(Guid userId, ConfirmTwoFactorDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 禁用双因素认证
    /// </summary>
    Task<Result> DisableTwoFactorAsync(Guid userId, DisableTwoFactorDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 生成恢复码
    /// </summary>
    Task<Result<GenerateRecoveryCodesResponseDto>> GenerateRecoveryCodesAsync(Guid userId, GenerateRecoveryCodesDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 使用恢复码登录
    /// </summary>
    Task<Result<AuthResponseDto>> LoginWithRecoveryCodeAsync(LoginWithRecoveryCodeDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取恢复码列表（仅显示状态，不显示实际码）
    /// </summary>
    Task<Result<List<RecoveryCodeDto>>> GetRecoveryCodesAsync(Guid userId, CancellationToken cancellationToken = default);
}