using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Admin;
using WhimLabAI.Shared.Dtos.Admin.User;
using WhimLabAI.Shared.Dtos.Admin.Rbac;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface IAdminUserService
{
    Task<Result<PagedResult<AdminListDto>>> GetAdminListAsync(AdminQueryDto query, CancellationToken cancellationToken = default);
    Task<Result<AdminDetailDto>> GetAdminDetailAsync(Guid adminId, CancellationToken cancellationToken = default);
    Task<Result<Guid>> CreateAdminAsync(CreateAdminDto request, CancellationToken cancellationToken = default);
    Task<Result> UpdateAdminAsync(Guid adminId, UpdateAdminDto request, CancellationToken cancellationToken = default);
    Task<Result> DeleteAdminAsync(Guid adminId, CancellationToken cancellationToken = default);
    
    // 角色管理
    Task<Result> AssignRolesAsync(Guid adminId, List<Guid> roleIds, Guid? assignedBy = null, CancellationToken cancellationToken = default);
    Task<Result> RemoveRoleAsync(Guid adminId, Guid roleId, CancellationToken cancellationToken = default);
    Task<Result<AdminUserPermissionsDto>> GetUserPermissionsAsync(Guid adminId, CancellationToken cancellationToken = default);
    Task<Result<PermissionCheckResultDto>> CheckPermissionAsync(Guid adminId, string permissionCode, CancellationToken cancellationToken = default);
    Task<Result<List<string>>> GetUserPermissionCodesAsync(Guid adminId, CancellationToken cancellationToken = default);
    
    // IP白名单管理
    Task<Result<IpWhitelistResponseDto>> GetIpWhitelistAsync(Guid adminId, string? currentIp = null, CancellationToken cancellationToken = default);
    Task<Result> UpdateIpWhitelistAsync(Guid adminId, UpdateIpWhitelistDto request, CancellationToken cancellationToken = default);
    Task<Result> AddIpToWhitelistAsync(Guid adminId, string ipAddress, CancellationToken cancellationToken = default);
    Task<Result> RemoveIpFromWhitelistAsync(Guid adminId, string ipAddress, CancellationToken cancellationToken = default);
    Task<Result> ToggleIpWhitelistAsync(Guid adminId, bool enable, CancellationToken cancellationToken = default);
}