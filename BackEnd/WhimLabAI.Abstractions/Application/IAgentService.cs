using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface IAgentService
{
    Task<Result<Guid>> CreateAgentAsync(CreateAgentDto request, Guid creatorId, CancellationToken cancellationToken = default);
    Task<Result> UpdateAgentAsync(Guid agentId, UpdateAgentDto request, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> PublishAgentAsync(Guid agentId, Guid versionId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> ArchiveAgentAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<PagedResult<AgentListDto>>> GetAgentListAsync(AgentQueryDto query, CancellationToken cancellationToken = default);
    Task<Result<AgentDetailDto>> GetAgentDetailAsync(Guid agentId, CancellationToken cancellationToken = default);
    Task<Result<List<AgentVersionDto>>> GetAgentVersionsAsync(Guid agentId, CancellationToken cancellationToken = default);
    Task<Result<AgentTestResponseDto>> TestAgentAsync(Guid agentId, AgentTestRequestDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取代理分类列表
    /// </summary>
    Task<Result<List<AgentCategoryDto>>> GetCategoriesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取评分最高的代理
    /// </summary>
    Task<Result<List<AgentListDto>>> GetTopRatedAsync(int count, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取已发布的代理（带分页）
    /// </summary>
    Task<Result<PagedResult<AgentListDto>>> GetPublishedAsync(int page, int pageSize, string? category, string? sortBy, CancellationToken cancellationToken = default);
}