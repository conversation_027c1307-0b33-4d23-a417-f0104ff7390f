using Microsoft.AspNetCore.Http;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Conversation;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface IConversationService
{
    Task<Result<Guid>> StartConversationAsync(StartConversationDto request, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<ConversationMessageDto>> SendMessageAsync(WhimLabAI.Shared.Dtos.SendMessageDto request, Guid userId, CancellationToken cancellationToken = default);
    Task<IAsyncEnumerable<StreamMessageChunk>> SendMessageStreamAsync(WhimLabAI.Shared.Dtos.SendMessageDto request, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<PagedResult<ConversationListDto>>> GetConversationHistoryAsync(Guid userId, ConversationQueryDto query, CancellationToken cancellationToken = default);
    Task<Result<ConversationDetailDto>> GetConversationDetailAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> DeleteConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<string>> ExportConversationAsync(Guid conversationId, Guid userId, string format, CancellationToken cancellationToken = default);
    Task<Result> RateResponseAsync(RateResponseDto request, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> ArchiveConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> UnarchiveConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> PinConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> UnpinConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> ClearConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> RenameConversationAsync(Guid conversationId, string newTitle, Guid userId, CancellationToken cancellationToken = default);
    
    // Advanced search functionality
    Task<Result<PagedResult<ConversationListDto>>> SearchConversationsAsync(ConversationSearchDto searchRequest, Guid userId, CancellationToken cancellationToken = default);
    
    // Batch export functionality
    Task<Result<byte[]>> ExportConversationsAsync(ConversationExportOptionsDto exportOptions, Guid userId, CancellationToken cancellationToken = default);
    
    // Cleanup functionality
    Task<Result<int>> CleanupOldConversationsAsync(ConversationCleanupPolicyDto policy, CancellationToken cancellationToken = default);
    Task<Result<int>> CleanupUserConversationsAsync(Guid userId, ConversationCleanupPolicyDto policy, CancellationToken cancellationToken = default);
    
    // Statistics functionality
    Task<Result<ConversationStatisticsDto>> GetUserConversationStatisticsAsync(Guid userId, DateRangeDto? dateRange = null, CancellationToken cancellationToken = default);
    Task<Result<Dictionary<string, object>>> GetConversationAnalyticsAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    
    // Sharing functionality
    Task<Result<string>> ShareConversationAsync(ConversationShareDto shareRequest, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> RevokeConversationShareAsync(string shareId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<ConversationDetailDto>> GetSharedConversationAsync(string shareId, string? password = null, CancellationToken cancellationToken = default);
    
    // Media attachment functionality
    Task<Result<MessageAttachmentDetailDto>> UploadAttachmentAsync(Guid conversationId, IFormFile file, Guid userId, CancellationToken cancellationToken = default);
    
    // Recovery functionality
    Task<Result<List<ConversationListDto>>> GetDeletedConversationsAsync(Guid userId, DateRangeDto? dateRange = null, CancellationToken cancellationToken = default);
    Task<Result<int>> RecoverDeletedConversationsAsync(ConversationRecoveryDto recoveryRequest, Guid userId, CancellationToken cancellationToken = default);
    
    // Bulk operations
    Task<Result<Dictionary<Guid, bool>>> BulkOperationAsync(BulkConversationOperationDto operation, Guid userId, CancellationToken cancellationToken = default);
}