using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos.Admin.Rbac;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 角色服务接口
/// </summary>
public interface IRoleService
{
    /// <summary>
    /// 获取所有角色
    /// </summary>
    Task<Result<List<RoleDto>>> GetRolesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取启用的角色
    /// </summary>
    Task<Result<List<RoleDto>>> GetEnabledRolesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取角色详情（包含权限）
    /// </summary>
    Task<Result<RoleDetailDto>> GetRoleDetailAsync(Guid roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建角色
    /// </summary>
    Task<Result<RoleDto>> CreateRoleAsync(CreateRoleDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新角色
    /// </summary>
    Task<Result> UpdateRoleAsync(Guid roleId, UpdateRoleDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新角色权限
    /// </summary>
    Task<Result> UpdateRolePermissionsAsync(Guid roleId, UpdateRolePermissionsDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 启用角色
    /// </summary>
    Task<Result> EnableRoleAsync(Guid roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 禁用角色
    /// </summary>
    Task<Result> DisableRoleAsync(Guid roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除角色
    /// </summary>
    Task<Result> DeleteRoleAsync(Guid roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 初始化系统角色
    /// </summary>
    Task<Result> InitializeSystemRolesAsync(CancellationToken cancellationToken = default);
}