using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 知识库上下文服务接口
/// </summary>
public interface IKnowledgeBaseContextService
{
    /// <summary>
    /// 获取与查询相关的知识库上下文
    /// </summary>
    /// <param name="agentId">智能体ID</param>
    /// <param name="query">用户查询</param>
    /// <param name="options">上下文选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>知识库上下文</returns>
    Task<Result<KnowledgeBaseContext>> GetContextAsync(
        Guid agentId,
        string query,
        KnowledgeBaseContextOptions? options = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取对话历史相关的上下文
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="query">用户查询</param>
    /// <param name="options">上下文选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>知识库上下文</returns>
    Task<Result<KnowledgeBaseContext>> GetConversationContextAsync(
        Guid conversationId,
        string query,
        KnowledgeBaseContextOptions? options = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 构建增强的提示词
    /// </summary>
    /// <param name="systemPrompt">系统提示词</param>
    /// <param name="userMessage">用户消息</param>
    /// <param name="context">知识库上下文</param>
    /// <returns>增强的提示词</returns>
    string BuildEnhancedPrompt(
        string systemPrompt,
        string userMessage,
        KnowledgeBaseContext context);

    /// <summary>
    /// 保存查询反馈
    /// </summary>
    /// <param name="contextId">上下文ID</param>
    /// <param name="feedback">反馈信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<Result> SaveFeedbackAsync(
        Guid contextId,
        ContextFeedback feedback,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 知识库上下文选项
/// </summary>
public class KnowledgeBaseContextOptions
{
    /// <summary>
    /// 最大上下文块数
    /// </summary>
    public int MaxChunks { get; set; } = 5;

    /// <summary>
    /// 最小相似度阈值
    /// </summary>
    public float MinSimilarity { get; set; } = 0.7f;

    /// <summary>
    /// 是否包含元数据
    /// </summary>
    public bool IncludeMetadata { get; set; } = true;

    /// <summary>
    /// 是否使用混合搜索
    /// </summary>
    public bool UseHybridSearch { get; set; } = true;

    /// <summary>
    /// 上下文窗口大小（字符数）
    /// </summary>
    public int ContextWindowSize { get; set; } = 4000;

    /// <summary>
    /// 是否包含相关文档信息
    /// </summary>
    public bool IncludeDocumentInfo { get; set; } = true;
}

/// <summary>
/// 知识库上下文
/// </summary>
public class KnowledgeBaseContext
{
    /// <summary>
    /// 上下文ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 相关的上下文块
    /// </summary>
    public List<ContextChunk> Chunks { get; set; } = new();

    /// <summary>
    /// 使用的知识库ID列表
    /// </summary>
    public List<Guid> KnowledgeBaseIds { get; set; } = new();

    /// <summary>
    /// 总字符数
    /// </summary>
    public int TotalCharacters { get; set; }

    /// <summary>
    /// 上下文生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 查询处理时间
    /// </summary>
    public TimeSpan ProcessingTime { get; set; }

    /// <summary>
    /// 元数据
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// 上下文块
/// </summary>
public class ContextChunk
{
    /// <summary>
    /// 块ID
    /// </summary>
    public Guid ChunkId { get; set; }

    /// <summary>
    /// 文档ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// 文档名称
    /// </summary>
    public string DocumentName { get; set; } = string.Empty;

    /// <summary>
    /// 知识库ID
    /// </summary>
    public Guid KnowledgeBaseId { get; set; }

    /// <summary>
    /// 知识库名称
    /// </summary>
    public string KnowledgeBaseName { get; set; } = string.Empty;

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 相似度分数
    /// </summary>
    public float SimilarityScore { get; set; }

    /// <summary>
    /// 块索引
    /// </summary>
    public int ChunkIndex { get; set; }

    /// <summary>
    /// 元数据
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// 是否被使用
    /// </summary>
    public bool IsUsed { get; set; } = true;
}

/// <summary>
/// 上下文反馈
/// </summary>
public class ContextFeedback
{
    /// <summary>
    /// 是否有帮助
    /// </summary>
    public bool IsHelpful { get; set; }

    /// <summary>
    /// 相关性评分 (1-5)
    /// </summary>
    public int? RelevanceScore { get; set; }

    /// <summary>
    /// 使用的块ID列表
    /// </summary>
    public List<Guid>? UsedChunkIds { get; set; }

    /// <summary>
    /// 反馈备注
    /// </summary>
    public string? Comments { get; set; }

    /// <summary>
    /// 反馈时间
    /// </summary>
    public DateTime FeedbackAt { get; set; } = DateTime.UtcNow;
}