using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface IAgentVersionService
{
    // Basic version operations
    Task<Result<AgentVersionDto>> CreateVersionAsync(Guid agentId, CreateAgentVersionDto request, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<AgentVersionDto>> GetVersionAsync(Guid agentId, Guid versionId, CancellationToken cancellationToken = default);
    Task<Result<List<AgentVersionDto>>> GetVersionsAsync(Guid agentId, CancellationToken cancellationToken = default);
    Task<Result> PublishVersionAsync(Guid agentId, Guid versionId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> RollbackToVersionAsync(Guid agentId, Guid versionId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result<AgentVersionComparisonDto>> CompareVersionsAsync(Guid agentId, Guid versionId1, Guid versionId2, CancellationToken cancellationToken = default);
    
    // Approval workflow
    Task<Result> SubmitForReviewAsync(Guid agentId, Guid versionId, Guid userId, CancellationToken cancellationToken = default);
    Task<Result> ReviewVersionAsync(Guid agentId, Guid versionId, ReviewVersionDto request, Guid reviewerId, CancellationToken cancellationToken = default);
    Task<Result<List<AgentVersionReviewDto>>> GetVersionReviewsAsync(Guid agentId, Guid versionId, CancellationToken cancellationToken = default);
    
    // Usage statistics
    Task<Result<AgentVersionUsageStatsDto>> GetVersionUsageStatsAsync(Guid agentId, Guid versionId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default);
    Task<Result> RecordVersionUsageAsync(Guid versionId, VersionUsageRecordDto usageRecord, CancellationToken cancellationToken = default);
    
    // Advanced comparison
    Task<Result<DetailedVersionComparisonDto>> GetDetailedComparisonAsync(Guid agentId, Guid versionId1, Guid versionId2, CancellationToken cancellationToken = default);
}