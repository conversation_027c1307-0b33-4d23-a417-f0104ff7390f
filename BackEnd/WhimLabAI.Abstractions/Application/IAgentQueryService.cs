using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface IAgentQueryService
{
    Task<PagedResult<AgentListDto>> QueryAgentsAsync(AgentQueryDto query, CancellationToken cancellationToken = default);
    Task<Result<List<AgentDto>>> GetAgentsByCreatorAsync(Guid creatorId, CancellationToken cancellationToken = default);
    Task<Result<List<AgentDto>>> GetPublishedAgentsAsync(int limit = 20, CancellationToken cancellationToken = default);
    Task<Result<List<AgentDto>>> SearchAgentsAsync(string keyword, CancellationToken cancellationToken = default);
}