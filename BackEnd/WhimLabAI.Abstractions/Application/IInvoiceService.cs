using WhimLabAI.Shared.Dtos.Invoice;
using Whim<PERSON>ab<PERSON><PERSON>.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 发票服务接口
/// </summary>
public interface IInvoiceService
{
    /// <summary>
    /// 创建发票
    /// </summary>
    Task<Result<InvoiceDto>> CreateInvoiceAsync(CreateInvoiceDto dto, Guid customerUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取发票详情
    /// </summary>
    Task<Result<InvoiceDto>> GetInvoiceAsync(Guid invoiceId, Guid customerUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取发票列表
    /// </summary>
    Task<Result<PagedResult<InvoiceDto>>> GetInvoicesAsync(
        Guid customerUserId,
        int page = 1,
        int pageSize = 20,
        string? status = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 下载发票
    /// </summary>
    Task<Result<byte[]>> DownloadInvoiceAsync(Guid invoiceId, Guid customerUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送发票邮件
    /// </summary>
    Task<Result> SendInvoiceEmailAsync(Guid invoiceId, Guid customerUserId, string email, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新发票信息（仅草稿状态）
    /// </summary>
    Task<Result<InvoiceDto>> UpdateInvoiceAsync(Guid invoiceId, UpdateInvoiceDto dto, Guid customerUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发出发票
    /// </summary>
    Task<Result<InvoiceDto>> IssueInvoiceAsync(Guid invoiceId, Guid customerUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 取消发票
    /// </summary>
    Task<Result> CancelInvoiceAsync(Guid invoiceId, string reason, Guid customerUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 标记发票已支付（管理员操作）
    /// </summary>
    Task<Result<InvoiceDto>> MarkAsPaidAsync(Guid invoiceId, MarkInvoicePaidDto dto, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 处理逾期发票（系统任务）
    /// </summary>
    Task<Result<int>> ProcessOverdueInvoicesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 为订阅生成发票
    /// </summary>
    Task<Result<InvoiceDto>> GenerateSubscriptionInvoiceAsync(Guid subscriptionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量生成月度发票（系统任务）
    /// </summary>
    Task<Result<int>> GenerateMonthlyInvoicesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取发票统计信息
    /// </summary>
    Task<Result<InvoiceStatisticsDto>> GetInvoiceStatisticsAsync(Guid customerUserId, CancellationToken cancellationToken = default);
}