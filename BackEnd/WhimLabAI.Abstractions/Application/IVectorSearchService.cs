using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 向量搜索服务接口
/// </summary>
public interface IVectorSearchService
{
    /// <summary>
    /// 在知识库中搜索相似文档
    /// </summary>
    /// <param name="knowledgeBaseId">知识库ID</param>
    /// <param name="query">查询文本</param>
    /// <param name="options">搜索选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    Task<Result<VectorSearchResponse>> SearchInKnowledgeBaseAsync(
        Guid knowledgeBaseId,
        string query,
        VectorSearchOptions? options = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 在多个知识库中搜索
    /// </summary>
    /// <param name="knowledgeBaseIds">知识库ID列表</param>
    /// <param name="query">查询文本</param>
    /// <param name="options">搜索选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    Task<Result<VectorSearchResponse>> SearchInMultipleKnowledgeBasesAsync(
        IList<Guid> knowledgeBaseIds,
        string query,
        VectorSearchOptions? options = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 混合搜索（向量搜索 + 关键词搜索）
    /// </summary>
    /// <param name="knowledgeBaseId">知识库ID</param>
    /// <param name="query">查询文本</param>
    /// <param name="options">搜索选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    Task<Result<VectorSearchResponse>> HybridSearchAsync(
        Guid knowledgeBaseId,
        string query,
        HybridSearchOptions? options = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取文档相似度
    /// </summary>
    /// <param name="documentId1">文档1 ID</param>
    /// <param name="documentId2">文档2 ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>相似度分数</returns>
    Task<Result<float>> GetDocumentSimilarityAsync(
        Guid documentId1,
        Guid documentId2,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 基于文档查找相似文档
    /// </summary>
    /// <param name="documentId">源文档ID</param>
    /// <param name="options">搜索选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>相似文档列表</returns>
    Task<Result<VectorSearchResponse>> FindSimilarDocumentsAsync(
        Guid documentId,
        VectorSearchOptions? options = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 向量搜索选项
/// </summary>
public class VectorSearchOptions
{
    /// <summary>
    /// 返回结果数量
    /// </summary>
    public int TopK { get; set; } = 10;

    /// <summary>
    /// 最小相似度阈值
    /// </summary>
    public float? MinSimilarity { get; set; }

    /// <summary>
    /// 元数据过滤器
    /// </summary>
    public Dictionary<string, object>? MetadataFilters { get; set; }

    /// <summary>
    /// 是否包含文档内容
    /// </summary>
    public bool IncludeContent { get; set; } = true;

    /// <summary>
    /// 是否包含元数据
    /// </summary>
    public bool IncludeMetadata { get; set; } = true;

    /// <summary>
    /// 内容最大长度
    /// </summary>
    public int? MaxContentLength { get; set; }
}

/// <summary>
/// 混合搜索选项
/// </summary>
public class HybridSearchOptions : VectorSearchOptions
{
    /// <summary>
    /// 向量搜索权重 (0-1)
    /// </summary>
    public float VectorWeight { get; set; } = 0.7f;

    /// <summary>
    /// 关键词搜索权重 (0-1)
    /// </summary>
    public float KeywordWeight { get; set; } = 0.3f;

    /// <summary>
    /// 是否使用模糊匹配
    /// </summary>
    public bool UseFuzzyMatching { get; set; } = true;

    /// <summary>
    /// 关键词搜索字段
    /// </summary>
    public List<string>? SearchFields { get; set; }
}

/// <summary>
/// 向量搜索响应
/// </summary>
public class VectorSearchResponse
{
    /// <summary>
    /// 搜索结果列表
    /// </summary>
    public List<VectorSearchResult> Results { get; set; } = new();

    /// <summary>
    /// 总结果数
    /// </summary>
    public int TotalResults { get; set; }

    /// <summary>
    /// 查询处理时间
    /// </summary>
    public TimeSpan QueryTime { get; set; }

    /// <summary>
    /// 查询向量维度
    /// </summary>
    public int? VectorDimension { get; set; }

    /// <summary>
    /// 搜索的知识库ID列表
    /// </summary>
    public List<Guid> SearchedKnowledgeBases { get; set; } = new();
}

/// <summary>
/// 向量搜索结果项
/// </summary>
public class VectorSearchResult
{
    /// <summary>
    /// 文档块ID
    /// </summary>
    public Guid ChunkId { get; set; }

    /// <summary>
    /// 文档ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// 知识库ID
    /// </summary>
    public Guid KnowledgeBaseId { get; set; }

    /// <summary>
    /// 文档名称
    /// </summary>
    public string DocumentName { get; set; } = string.Empty;

    /// <summary>
    /// 知识库名称
    /// </summary>
    public string KnowledgeBaseName { get; set; } = string.Empty;

    /// <summary>
    /// 相似度分数
    /// </summary>
    public float SimilarityScore { get; set; }

    /// <summary>
    /// 关键词匹配分数（混合搜索时）
    /// </summary>
    public float? KeywordScore { get; set; }

    /// <summary>
    /// 综合分数（混合搜索时）
    /// </summary>
    public float? CombinedScore { get; set; }

    /// <summary>
    /// 文档块内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 高亮内容（包含匹配关键词的高亮标记）
    /// </summary>
    public string? HighlightedContent { get; set; }

    /// <summary>
    /// 块索引
    /// </summary>
    public int ChunkIndex { get; set; }

    /// <summary>
    /// 元数据
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// 文档创建时间
    /// </summary>
    public DateTime DocumentCreatedAt { get; set; }

    /// <summary>
    /// 文档更新时间
    /// </summary>
    public DateTime DocumentUpdatedAt { get; set; }
}