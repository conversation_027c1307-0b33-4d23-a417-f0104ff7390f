using WhimLabAI.Shared.DTOs.Coupon;
using WhimLabAI.Shared.DTOs.Common;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application.Services;

public interface ICouponService
{
    Task<Result<CouponDto>> CreateAsync(CreateCouponDto dto, CancellationToken cancellationToken = default);
    Task<Result<CouponDto>> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Result<CouponDto>> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<Result<PagedResult<CouponDto>>> GetPagedAsync(PagedRequest request, CancellationToken cancellationToken = default);
    Task<Result<IEnumerable<CouponDto>>> GetActiveAsync(CancellationToken cancellationToken = default);
    Task<Result<IEnumerable<CouponDto>>> GetUserCouponsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result<CouponDto>> UpdateAsync(Guid id, UpdateCouponDto dto, CancellationToken cancellationToken = default);
    Task<Result<bool>> ActivateAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Result<bool>> DeactivateAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Result<bool>> ExtendValidityAsync(Guid id, DateTime newValidTo, CancellationToken cancellationToken = default);
    Task<Result<DiscountCalculationDto>> CalculateDiscountAsync(string code, decimal orderAmount, string currency, string? productId = null, CancellationToken cancellationToken = default);
    Task<Result<bool>> ApplyCouponAsync(string code, Guid userId, Guid orderId, decimal discountAmount, string currency, CancellationToken cancellationToken = default);
    Task<Result<bool>> ValidateCouponAsync(string code, Guid userId, CancellationToken cancellationToken = default);
}