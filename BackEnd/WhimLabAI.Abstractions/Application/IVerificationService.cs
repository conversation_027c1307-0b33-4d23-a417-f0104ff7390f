using System;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface IVerificationService
{
    /// <summary>
    /// 生成图形验证码
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证码图片数据和验证码ID</returns>
    Task<Result<CaptchaResult>> GenerateCaptchaAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证图形验证码
    /// </summary>
    /// <param name="captchaId">验证码ID</param>
    /// <param name="code">验证码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<Result<bool>> VerifyCaptchaAsync(string captchaId, string code, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送邮件验证码
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="type">验证码类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<Result<bool>> SendEmailCodeAsync(string email, VerificationType type, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送短信验证码
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <param name="type">验证码类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<Result<bool>> SendSmsCodeAsync(string phoneNumber, VerificationType type, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证邮件或短信验证码
    /// </summary>
    /// <param name="target">目标（邮箱或手机号）</param>
    /// <param name="code">验证码</param>
    /// <param name="type">验证码类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<Result<bool>> VerifyCodeAsync(string target, string code, VerificationType type, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查验证码是否已验证
    /// </summary>
    /// <param name="target">目标（邮箱或手机号）</param>
    /// <param name="type">验证码类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证状态</returns>
    Task<Result<bool>> IsVerifiedAsync(string target, VerificationType type, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 清除验证状态
    /// </summary>
    /// <param name="target">目标（邮箱或手机号）</param>
    /// <param name="type">验证码类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<Result<bool>> ClearVerificationAsync(string target, VerificationType type, CancellationToken cancellationToken = default);
}

/// <summary>
/// 验证码类型
/// </summary>
public enum VerificationType
{
    Register = 1,
    Login = 2,
    ResetPassword = 3,
    ChangeEmail = 4,
    ChangePhone = 5,
    DeleteAccount = 6,
    TwoFactorAuth = 7
}

/// <summary>
/// 图形验证码结果
/// </summary>
public class CaptchaResult
{
    public string CaptchaId { get; set; } = string.Empty;
    public byte[] ImageData { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = "image/svg+xml";
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// 验证码配置
/// </summary>
public class VerificationCodeOptions
{
    public int CodeLength { get; set; } = 6;
    public int ExpirationMinutes { get; set; } = 5;
    public int MaxAttempts { get; set; } = 5;
    public int ResendIntervalSeconds { get; set; } = 60;
    [Obsolete("使用AdminCaptchaPolicy和CustomerCaptchaPolicy替代")]
    public bool RequireCaptcha { get; set; } = true;
}

/// <summary>
/// 图形验证码配置
/// </summary>
public class CaptchaOptions
{
    public int Width { get; set; } = 120;
    public int Height { get; set; } = 40;
    public int CodeLength { get; set; } = 4;
    public int ExpirationMinutes { get; set; } = 5;
    public int NoiseLevel { get; set; } = 3;
    public string Characters { get; set; } = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    public string FontFamily { get; set; } = "Arial";
    public int FontSize { get; set; } = 16;
}

/// <summary>
/// 验证码策略选项
/// </summary>
public class CaptchaPolicyOptions
{
    /// <summary>
    /// 是否启用验证码
    /// </summary>
    public bool Enabled { get; set; } = true;
    
    /// <summary>
    /// 是否强制要求验证码（忽略其他条件）
    /// </summary>
    public bool AlwaysRequired { get; set; } = false;
    
    /// <summary>
    /// 登录失败多少次后需要验证码
    /// </summary>
    public int FailedAttemptsThreshold { get; set; } = 3;
    
    /// <summary>
    /// 一小时内登录尝试多少次后需要验证码
    /// </summary>
    public int HourlyAttemptsThreshold { get; set; } = 10;
    
    /// <summary>
    /// 在开发环境是否禁用验证码
    /// </summary>
    public bool DisableInDevelopment { get; set; } = false;
}

/// <summary>
/// 验证配置选项（包含Admin和Customer的独立配置）
/// </summary>
public class VerificationOptions
{
    /// <summary>
    /// 验证码配置
    /// </summary>
    public VerificationCodeOptions Code { get; set; } = new();
    
    /// <summary>
    /// 图形验证码基础配置
    /// </summary>
    public CaptchaOptions Captcha { get; set; } = new();
    
    /// <summary>
    /// Admin端验证码策略
    /// </summary>
    public CaptchaPolicyOptions AdminCaptchaPolicy { get; set; } = new()
    {
        Enabled = true,
        AlwaysRequired = true, // Admin端默认总是需要验证码
        DisableInDevelopment = false // Admin端即使在开发环境也需要验证码
    };
    
    /// <summary>
    /// Customer端验证码策略
    /// </summary>
    public CaptchaPolicyOptions CustomerCaptchaPolicy { get; set; } = new()
    {
        Enabled = true,
        AlwaysRequired = false, // Customer端根据条件判断
        FailedAttemptsThreshold = 3,
        HourlyAttemptsThreshold = 10,
        DisableInDevelopment = true // Customer端在开发环境可以禁用
    };
}