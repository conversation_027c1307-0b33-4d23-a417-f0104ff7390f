using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Shared.DTOs.Analytics;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 预测分析服务接口
/// </summary>
public interface IPredictiveAnalyticsService
{
    /// <summary>
    /// 预测用户生命周期价值
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="historicalData">历史数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>LTV预测结果</returns>
    Task<LTVPrediction> PredictUserLTVAsync(
        Guid userId, 
        UserLTVHistoricalData historicalData,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 预测收入
    /// </summary>
    /// <param name="historicalRevenue">历史收入数据</param>
    /// <param name="includeSeasonality">是否包含季节性因素</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>收入预测结果</returns>
    Task<RevenueForecast> PredictRevenueAsync(
        List<RevenueDataPoint> historicalRevenue,
        bool includeSeasonality = true,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 预测用户流失
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="userActivityData">用户活动数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>流失预测结果</returns>
    Task<ChurnPrediction> PredictUserChurnAsync(
        Guid userId,
        UserActivityData userActivityData,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 预测Token使用量
    /// </summary>
    /// <param name="userId">用户ID（可选，为空则预测总体）</param>
    /// <param name="historicalUsage">历史使用数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Token使用预测结果</returns>
    Task<TokenUsagePrediction> PredictTokenUsageAsync(
        Guid? userId,
        List<TokenUsageDataPoint> historicalUsage,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 分析时间序列趋势
    /// </summary>
    /// <param name="dataPoints">数据点</param>
    /// <param name="forecastPeriods">预测期数</param>
    /// <returns>趋势分析结果</returns>
    TimeSeriesTrendAnalysis AnalyzeTimeSeries(
        List<double> dataPoints,
        int forecastPeriods = 3);
    
    /// <summary>
    /// 检测季节性模式
    /// </summary>
    /// <param name="dataPoints">数据点</param>
    /// <param name="periodLength">周期长度（例如：7天、30天）</param>
    /// <returns>季节性分析结果</returns>
    SeasonalityAnalysis DetectSeasonality(
        List<double> dataPoints,
        int periodLength);
}

/// <summary>
/// 用户LTV历史数据
/// </summary>
public class UserLTVHistoricalData
{
    public List<OrderData> Orders { get; set; } = new();
    public List<SubscriptionData> Subscriptions { get; set; } = new();
    public DateTime FirstActivityDate { get; set; }
    public int TotalConversations { get; set; }
    public long TotalTokensUsed { get; set; }
}

/// <summary>
/// 订单数据
/// </summary>
public class OrderData
{
    public DateTime OrderDate { get; set; }
    public decimal Amount { get; set; }
    public string ProductType { get; set; } = string.Empty;
}

/// <summary>
/// 订阅数据
/// </summary>
public class SubscriptionData
{
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public decimal MonthlyAmount { get; set; }
    public string PlanTier { get; set; } = string.Empty;
}

/// <summary>
/// 收入数据点
/// </summary>
public class RevenueDataPoint
{
    public DateTime Date { get; set; }
    public decimal Revenue { get; set; }
    public int TransactionCount { get; set; }
}

/// <summary>
/// 用户活动数据
/// </summary>
public class UserActivityData
{
    public DateTime LastLoginDate { get; set; }
    public int LoginFrequencyLast30Days { get; set; }
    public int ConversationsLast30Days { get; set; }
    public long TokensUsedLast30Days { get; set; }
    public bool HasActiveSubscription { get; set; }
    public int DaysSinceRegistration { get; set; }
    public double AverageSessionDuration { get; set; }
}

/// <summary>
/// 流失预测结果
/// </summary>
public class ChurnPrediction
{
    public double ChurnProbability { get; set; }
    public string RiskLevel { get; set; } = string.Empty; // Low, Medium, High
    public List<string> RiskFactors { get; set; } = new();
    public DateTime PredictionDate { get; set; }
    public double ConfidenceLevel { get; set; }
}

/// <summary>
/// Token使用数据点
/// </summary>
public class TokenUsageDataPoint
{
    public DateTime Date { get; set; }
    public long TokensUsed { get; set; }
    public int ConversationCount { get; set; }
    public decimal Cost { get; set; }
}

/// <summary>
/// Token使用预测结果
/// </summary>
public class TokenUsagePrediction
{
    public long NextWeekPrediction { get; set; }
    public long NextMonthPrediction { get; set; }
    public long NextQuarterPrediction { get; set; }
    public decimal EstimatedCostNextMonth { get; set; }
    public decimal EstimatedCostNextQuarter { get; set; }
    public double GrowthRate { get; set; }
    public double ConfidenceLevel { get; set; }
    public List<string> Assumptions { get; set; } = new();
}

/// <summary>
/// 时间序列趋势分析结果
/// </summary>
public class TimeSeriesTrendAnalysis
{
    public double Trend { get; set; }
    public double Seasonality { get; set; }
    public double Noise { get; set; }
    public List<double> ForecastValues { get; set; } = new();
    public double MeanAbsoluteError { get; set; }
    public double RSquared { get; set; }
}

/// <summary>
/// 季节性分析结果
/// </summary>
public class SeasonalityAnalysis
{
    public bool HasSeasonality { get; set; }
    public double SeasonalityStrength { get; set; }
    public List<double> SeasonalFactors { get; set; } = new();
    public int DetectedPeriod { get; set; }
}