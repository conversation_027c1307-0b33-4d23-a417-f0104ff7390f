using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos.Auth;
using WhimLabAI.Shared.Dtos.Admin;
using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.Abstractions.Application;

public interface IAdminAuthService
{
    // 基础登录功能
    Task<Result<AdminAuthResponseDto>> LoginAsync(AdminLoginDto request, CancellationToken cancellationToken = default);
    Task<Result> LogoutAsync(Guid adminId, CancellationToken cancellationToken = default);
    Task<Result<AdminAuthResponseDto>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
    Task<Result> ChangePasswordAsync(Guid adminId, string oldPassword, string newPassword, CancellationToken cancellationToken = default);
    Task<Result<ForceChangePasswordResponseDto>> ForceChangePasswordAsync(ForceChangePasswordDto request, CancellationToken cancellationToken = default);
    Task<Result> ResetPasswordAsync(string account, string newPassword, string verificationCode, CancellationToken cancellationToken = default);
    Task<Result<object>> GetUserPermissionsAsync(Guid adminId, CancellationToken cancellationToken = default);
    
    // 验证码发送
    Task<Result> SendVerificationCodeAsync(SendAdminVerificationCodeDto request, CancellationToken cancellationToken = default);
    
    // 扫码登录
    Task<Result<AdminQrCodeResponseDto>> GenerateQrCodeAsync(GenerateAdminQrCodeDto request, CancellationToken cancellationToken = default);
    Task<Result<AdminQrStatusResponseDto>> CheckQrStatusAsync(CheckAdminQrStatusDto request, CancellationToken cancellationToken = default);
    Task<Result<AdminScannerInfoDto>> ScanQrCodeAsync(Guid adminId, ScanAdminQrCodeDto request, CancellationToken cancellationToken = default);
    Task<Result> ConfirmQrLoginAsync(Guid adminId, ConfirmAdminQrLoginDto request, CancellationToken cancellationToken = default);
    
    // Two-Factor Authentication
    Task<Result<object>> EnableTwoFactorAsync(Guid adminId, CancellationToken cancellationToken = default);
    Task<Result> ConfirmTwoFactorAsync(Guid adminId, string code, CancellationToken cancellationToken = default);
    Task<Result> DisableTwoFactorAsync(Guid adminId, string password, CancellationToken cancellationToken = default);
    
    // 登录历史查询
    Task<Result<PagedResult<AdminLoginHistoryDto>>> GetLoginHistoryAsync(AdminLoginHistoryQueryDto query, CancellationToken cancellationToken = default);
    Task<Result<AdminLoginStatisticsDto>> GetLoginStatisticsAsync(Guid adminId, CancellationToken cancellationToken = default);
    Task<Result> TerminateSessionAsync(Guid adminId, Guid sessionId, CancellationToken cancellationToken = default);
    Task<Result> TerminateAllSessionsAsync(Guid adminId, bool exceptCurrent = true, CancellationToken cancellationToken = default);
}