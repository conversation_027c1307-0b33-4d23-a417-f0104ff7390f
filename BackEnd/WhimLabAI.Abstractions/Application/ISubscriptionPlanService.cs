using WhimLabAI.Shared.Dtos.Subscription;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 订阅计划服务接口
/// </summary>
public interface ISubscriptionPlanService
{
    /// <summary>
    /// 获取所有订阅计划
    /// </summary>
    Task<Result<List<SubscriptionPlanDto>>> GetAllAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取活跃的订阅计划
    /// </summary>
    Task<Result<List<SubscriptionPlanDto>>> GetActiveAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取订阅计划详情
    /// </summary>
    Task<Result<SubscriptionPlanDto>> GetByIdAsync(Guid planId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据层级获取订阅计划
    /// </summary>
    Task<Result<SubscriptionPlanDto>> GetByTierAsync(string tier, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建订阅计划
    /// </summary>
    Task<Result<SubscriptionPlanDto>> CreateAsync(CreateSubscriptionPlanDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新订阅计划
    /// </summary>
    Task<Result> UpdateAsync(Guid planId, UpdateSubscriptionPlanDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 启用订阅计划
    /// </summary>
    Task<Result> EnableAsync(Guid planId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 禁用订阅计划
    /// </summary>
    Task<Result> DisableAsync(Guid planId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除订阅计划
    /// </summary>
    Task<Result> DeleteAsync(Guid planId, CancellationToken cancellationToken = default);
}