using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 负载测试服务接口
/// </summary>
public interface ILoadTestingService
{
    /// <summary>
    /// 运行负载测试场景
    /// </summary>
    Task<Result<LoadTestResult>> RunLoadTestScenarioAsync(
        LoadTestScenario scenario,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 运行压力测试
    /// </summary>
    Task<Result<StressTestResult>> RunStressTestAsync(
        StressTestConfiguration configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 运行尖峰测试
    /// </summary>
    Task<Result<SpikeTestResult>> RunSpikeTestAsync(
        SpikeTestConfiguration configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 运行浸泡测试
    /// </summary>
    Task<Result<SoakTestResult>> RunSoakTestAsync(
        SoakTestConfiguration configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 运行容量测试
    /// </summary>
    Task<Result<CapacityTestResult>> RunCapacityTestAsync(
        CapacityTestConfiguration configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取测试进度
    /// </summary>
    Task<Result<LoadTestProgress>> GetTestProgressAsync(
        string testId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止运行中的测试
    /// </summary>
    Task<Result> StopTestAsync(
        string testId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 分析负载测试结果
    /// </summary>
    Result<LoadTestAnalysis> AnalyzeTestResults(LoadTestResult result);

    /// <summary>
    /// 生成负载测试报告
    /// </summary>
    Task<Result<LoadTestReport>> GenerateLoadTestReportAsync(
        LoadTestResult result,
        CancellationToken cancellationToken = default);
}

#region Configuration DTOs

/// <summary>
/// 负载测试场景
/// </summary>
public class LoadTestScenario
{
    public string ScenarioId { get; set; } = Guid.NewGuid().ToString();
    public string ScenarioName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public LoadPattern LoadPattern { get; set; } = new();
    public List<UserBehavior> UserBehaviors { get; set; } = new();
    public TestDuration Duration { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// 负载模式
/// </summary>
public class LoadPattern
{
    public LoadPatternType Type { get; set; }
    public int InitialUsers { get; set; }
    public int TargetUsers { get; set; }
    public TimeSpan RampUpTime { get; set; }
    public TimeSpan RampDownTime { get; set; }
    public List<LoadStep> Steps { get; set; } = new();
}

/// <summary>
/// 负载模式类型
/// </summary>
public enum LoadPatternType
{
    Constant,    // 恒定负载
    Ramp,        // 渐进负载
    Step,        // 阶梯负载
    Wave,        // 波浪负载
    Custom       // 自定义负载
}

/// <summary>
/// 负载步骤
/// </summary>
public class LoadStep
{
    public int UserCount { get; set; }
    public TimeSpan Duration { get; set; }
    public TimeSpan TransitionTime { get; set; }
}

/// <summary>
/// 用户行为
/// </summary>
public class UserBehavior
{
    public string BehaviorName { get; set; } = string.Empty;
    public double Weight { get; set; } = 1.0;
    public List<UserAction> Actions { get; set; } = new();
    public TimeSpan ThinkTime { get; set; } = TimeSpan.FromSeconds(1);
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 用户动作
/// </summary>
public class UserAction
{
    public string ActionName { get; set; } = string.Empty;
    public ActionType Type { get; set; }
    public string Target { get; set; } = string.Empty; // URL或操作目标
    public HttpMethod HttpMethod { get; set; }
    public object? RequestBody { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);
    public List<ResponseValidation> Validations { get; set; } = new();
}

/// <summary>
/// 动作类型
/// </summary>
public enum ActionType
{
    HttpRequest,
    WebSocketConnect,
    WebSocketMessage,
    DatabaseQuery,
    CacheOperation,
    Custom
}

/// <summary>
/// 响应验证
/// </summary>
public class ResponseValidation
{
    public string ValidationName { get; set; } = string.Empty;
    public ValidationType Type { get; set; }
    public string ExpectedValue { get; set; } = string.Empty;
    public string ActualPath { get; set; } = string.Empty; // JSON路径或XPath
}

/// <summary>
/// 验证类型
/// </summary>
public enum ValidationType
{
    StatusCode,
    ResponseTime,
    Contains,
    JsonPath,
    Header,
    Custom
}

/// <summary>
/// 测试持续时间
/// </summary>
public class TestDuration
{
    public DurationType Type { get; set; }
    public TimeSpan? FixedDuration { get; set; }
    public int? IterationCount { get; set; }
    public string? StopCondition { get; set; }
}

/// <summary>
/// 持续时间类型
/// </summary>
public enum DurationType
{
    FixedTime,
    Iterations,
    UntilCondition
}

/// <summary>
/// 压力测试配置
/// </summary>
public class StressTestConfiguration
{
    public string TestId { get; set; } = Guid.NewGuid().ToString();
    public int StartingUsers { get; set; } = 10;
    public int UserIncrement { get; set; } = 10;
    public TimeSpan IncrementInterval { get; set; } = TimeSpan.FromMinutes(1);
    public double TargetErrorRate { get; set; } = 0.01;
    public double TargetResponseTime { get; set; } = 1000; // 毫秒
    public TimeSpan MaxDuration { get; set; } = TimeSpan.FromHours(1);
    public LoadTestScenario BaseScenario { get; set; } = new();
}

/// <summary>
/// 尖峰测试配置
/// </summary>
public class SpikeTestConfiguration
{
    public string TestId { get; set; } = Guid.NewGuid().ToString();
    public int BaselineUsers { get; set; } = 100;
    public int SpikeUsers { get; set; } = 1000;
    public TimeSpan SpikeRampTime { get; set; } = TimeSpan.FromSeconds(30);
    public TimeSpan SpikeDuration { get; set; } = TimeSpan.FromMinutes(5);
    public TimeSpan RecoveryTime { get; set; } = TimeSpan.FromMinutes(5);
    public LoadTestScenario BaseScenario { get; set; } = new();
}

/// <summary>
/// 浸泡测试配置
/// </summary>
public class SoakTestConfiguration
{
    public string TestId { get; set; } = Guid.NewGuid().ToString();
    public int TargetUsers { get; set; } = 500;
    public TimeSpan TestDuration { get; set; } = TimeSpan.FromHours(8);
    public TimeSpan RampUpTime { get; set; } = TimeSpan.FromMinutes(10);
    public double AcceptableMemoryGrowth { get; set; } = 0.1; // 10%
    public double AcceptableErrorRate { get; set; } = 0.001; // 0.1%
    public LoadTestScenario BaseScenario { get; set; } = new();
}

/// <summary>
/// 容量测试配置
/// </summary>
public class CapacityTestConfiguration
{
    public string TestId { get; set; } = Guid.NewGuid().ToString();
    public int MinUsers { get; set; } = 100;
    public int MaxUsers { get; set; } = 10000;
    public int UserStep { get; set; } = 100;
    public TimeSpan StepDuration { get; set; } = TimeSpan.FromMinutes(2);
    public double TargetResponseTime { get; set; } = 500; // 毫秒
    public double TargetThroughput { get; set; } = 1000; // 请求/秒
    public LoadTestScenario BaseScenario { get; set; } = new();
}

#endregion

#region Result DTOs

/// <summary>
/// 负载测试结果
/// </summary>
public class LoadTestResult
{
    public string TestId { get; set; } = string.Empty;
    public string ScenarioName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration => EndTime - StartTime;
    public TestStatus Status { get; set; }
    
    // 总体统计
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public double ErrorRate => TotalRequests > 0 ? (double)FailedRequests / TotalRequests : 0;
    
    // 性能指标
    public double AverageResponseTime { get; set; }
    public double MinResponseTime { get; set; }
    public double MaxResponseTime { get; set; }
    public double MedianResponseTime { get; set; }
    public double P90ResponseTime { get; set; }
    public double P95ResponseTime { get; set; }
    public double P99ResponseTime { get; set; }
    
    // 吞吐量
    public double RequestsPerSecond { get; set; }
    public double BytesPerSecond { get; set; }
    
    // 并发指标
    public int MaxConcurrentUsers { get; set; }
    public double AverageConcurrentUsers { get; set; }
    
    // 详细结果
    public List<TimeSeriesMetric> TimeSeriesMetrics { get; set; } = new();
    public Dictionary<string, ActionResult> ActionResults { get; set; } = new();
    public List<ErrorDetail> Errors { get; set; } = new();
    public Dictionary<int, int> StatusCodeDistribution { get; set; } = new();
}

/// <summary>
/// 测试状态
/// </summary>
public enum TestStatus
{
    Pending,
    Running,
    Completed,
    Failed,
    Stopped
}

/// <summary>
/// 时间序列指标
/// </summary>
public class TimeSeriesMetric
{
    public DateTime Timestamp { get; set; }
    public int ActiveUsers { get; set; }
    public double RequestsPerSecond { get; set; }
    public double AverageResponseTime { get; set; }
    public double ErrorRate { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
}

/// <summary>
/// 动作结果
/// </summary>
public class ActionResult
{
    public string ActionName { get; set; } = string.Empty;
    public long RequestCount { get; set; }
    public long SuccessCount { get; set; }
    public long FailureCount { get; set; }
    public double AverageResponseTime { get; set; }
    public double MinResponseTime { get; set; }
    public double MaxResponseTime { get; set; }
    public double P95ResponseTime { get; set; }
    public Dictionary<int, int> StatusCodes { get; set; } = new();
}

/// <summary>
/// 错误详情
/// </summary>
public class ErrorDetail
{
    public DateTime Timestamp { get; set; }
    public string ActionName { get; set; } = string.Empty;
    public string ErrorType { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public int StatusCode { get; set; }
    public int OccurrenceCount { get; set; }
}

/// <summary>
/// 压力测试结果
/// </summary>
public class StressTestResult : LoadTestResult
{
    public int BreakingPointUsers { get; set; }
    public double BreakingPointResponseTime { get; set; }
    public double BreakingPointErrorRate { get; set; }
    public string BreakingPointReason { get; set; } = string.Empty;
    public List<StressLevel> StressLevels { get; set; } = new();
}

/// <summary>
/// 压力级别
/// </summary>
public class StressLevel
{
    public int UserCount { get; set; }
    public double ResponseTime { get; set; }
    public double ErrorRate { get; set; }
    public double Throughput { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// 尖峰测试结果
/// </summary>
public class SpikeTestResult : LoadTestResult
{
    public double BaselineResponseTime { get; set; }
    public double SpikeResponseTime { get; set; }
    public double RecoveryResponseTime { get; set; }
    public TimeSpan RecoveryDuration { get; set; }
    public bool FullyRecovered { get; set; }
    public List<SpikeImpact> Impacts { get; set; } = new();
}

/// <summary>
/// 尖峰影响
/// </summary>
public class SpikeImpact
{
    public string Component { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public double Severity { get; set; }
}

/// <summary>
/// 浸泡测试结果
/// </summary>
public class SoakTestResult : LoadTestResult
{
    public long InitialMemoryUsage { get; set; }
    public long FinalMemoryUsage { get; set; }
    public double MemoryGrowthRate { get; set; }
    public bool MemoryLeakDetected { get; set; }
    public List<ResourceTrend> ResourceTrends { get; set; } = new();
    public List<DegradationPoint> DegradationPoints { get; set; } = new();
}

/// <summary>
/// 资源趋势
/// </summary>
public class ResourceTrend
{
    public string ResourceType { get; set; } = string.Empty;
    public double InitialValue { get; set; }
    public double FinalValue { get; set; }
    public double GrowthRate { get; set; }
    public string Trend { get; set; } = string.Empty;
}

/// <summary>
/// 性能退化点
/// </summary>
public class DegradationPoint
{
    public DateTime Timestamp { get; set; }
    public string Metric { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 容量测试结果
/// </summary>
public class CapacityTestResult : LoadTestResult
{
    public int MaxCapacityUsers { get; set; }
    public double MaxThroughput { get; set; }
    public int OptimalUsers { get; set; }
    public double OptimalThroughput { get; set; }
    public List<CapacityPoint> CapacityPoints { get; set; } = new();
    public CapacityRecommendation Recommendation { get; set; } = new();
}

/// <summary>
/// 容量点
/// </summary>
public class CapacityPoint
{
    public int UserCount { get; set; }
    public double Throughput { get; set; }
    public double ResponseTime { get; set; }
    public double ErrorRate { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public bool MeetsTargets { get; set; }
}

/// <summary>
/// 容量建议
/// </summary>
public class CapacityRecommendation
{
    public int RecommendedMaxUsers { get; set; }
    public int SafeOperatingUsers { get; set; }
    public string ScalingStrategy { get; set; } = string.Empty;
    public List<string> Bottlenecks { get; set; } = new();
}

/// <summary>
/// 负载测试进度
/// </summary>
public class LoadTestProgress
{
    public string TestId { get; set; } = string.Empty;
    public TestStatus Status { get; set; }
    public double ProgressPercent { get; set; }
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan EstimatedTimeRemaining { get; set; }
    public int CurrentUsers { get; set; }
    public long CompletedRequests { get; set; }
    public double CurrentErrorRate { get; set; }
    public double CurrentResponseTime { get; set; }
    public string CurrentPhase { get; set; } = string.Empty;
}

/// <summary>
/// 负载测试分析
/// </summary>
public class LoadTestAnalysis
{
    public string TestId { get; set; } = string.Empty;
    public DateTime AnalyzedAt { get; set; }
    public PerformanceGrade OverallGrade { get; set; }
    public List<PerformanceIssue> Issues { get; set; } = new();
    public List<PerformanceStrength> Strengths { get; set; } = new();
    public List<OptimizationOpportunity> Opportunities { get; set; } = new();
    public ScalabilityAssessment Scalability { get; set; } = new();
    public ReliabilityAssessment Reliability { get; set; } = new();
}

/// <summary>
/// 性能等级
/// </summary>
public enum PerformanceGrade
{
    Excellent,
    Good,
    Fair,
    Poor,
    Critical
}

/// <summary>
/// 性能问题
/// </summary>
public class PerformanceIssue
{
    public string Category { get; set; } = string.Empty;
    public string Issue { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public double Impact { get; set; }
    public string Evidence { get; set; } = string.Empty;
}

/// <summary>
/// 性能优势
/// </summary>
public class PerformanceStrength
{
    public string Area { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Metric { get; set; } = string.Empty;
}

/// <summary>
/// 优化机会
/// </summary>
public class OptimizationOpportunity
{
    public string Area { get; set; } = string.Empty;
    public string Opportunity { get; set; } = string.Empty;
    public double PotentialImprovement { get; set; }
    public string Implementation { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
}

/// <summary>
/// 可扩展性评估
/// </summary>
public class ScalabilityAssessment
{
    public bool IsLinearilyScalable { get; set; }
    public double ScalabilityFactor { get; set; }
    public int CurrentCapacity { get; set; }
    public int ProjectedCapacity { get; set; }
    public List<string> ScalingBottlenecks { get; set; } = new();
}

/// <summary>
/// 可靠性评估
/// </summary>
public class ReliabilityAssessment
{
    public double Availability { get; set; }
    public double MTBF { get; set; } // Mean Time Between Failures
    public double MTTR { get; set; } // Mean Time To Recovery
    public List<string> FailurePoints { get; set; } = new();
}

/// <summary>
/// 负载测试报告
/// </summary>
public class LoadTestReport
{
    public string ReportId { get; set; } = Guid.NewGuid().ToString();
    public DateTime GeneratedAt { get; set; }
    public LoadTestResult TestResult { get; set; } = new();
    public LoadTestAnalysis Analysis { get; set; } = new();
    public ExecutiveSummary Summary { get; set; } = new();
    public List<Chart> Charts { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 执行摘要
/// </summary>
public class ExecutiveSummary
{
    public string TestObjective { get; set; } = string.Empty;
    public string TestConclusion { get; set; } = string.Empty;
    public List<string> KeyFindings { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, string> KeyMetrics { get; set; } = new();
}

/// <summary>
/// 图表
/// </summary>
public class Chart
{
    public string ChartId { get; set; } = string.Empty;
    public string ChartType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public object Data { get; set; } = new { };
}

#endregion