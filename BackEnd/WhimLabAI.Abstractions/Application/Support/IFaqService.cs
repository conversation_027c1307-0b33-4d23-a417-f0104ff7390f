using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application.Support;

/// <summary>
/// FAQ服务接口
/// </summary>
public interface IFaqService
{
    /// <summary>
    /// 创建FAQ项
    /// </summary>
    Task<FaqItemDto> CreateFaqAsync(CreateFaqItemDto dto, Guid createdById);

    /// <summary>
    /// 更新FAQ项
    /// </summary>
    Task UpdateFaqAsync(Guid faqId, UpdateFaqItemDto dto, Guid updatedById);

    /// <summary>
    /// 删除FAQ项
    /// </summary>
    Task DeleteFaqAsync(Guid faqId);

    /// <summary>
    /// 获取FAQ详情
    /// </summary>
    Task<FaqItemDto> GetFaqAsync(Guid faqId);

    /// <summary>
    /// 获取已发布的FAQ列表
    /// </summary>
    Task<List<FaqItemDto>> GetPublishedFaqsAsync(string? category = null);

    /// <summary>
    /// 按类别分组获取FAQ
    /// </summary>
    Task<Dictionary<string, List<FaqItemDto>>> GetFaqsByCategoryAsync();

    /// <summary>
    /// 搜索FAQ
    /// </summary>
    Task<List<FaqItemDto>> SearchFaqsAsync(string keyword);

    /// <summary>
    /// 获取热门FAQ
    /// </summary>
    Task<List<FaqItemDto>> GetPopularFaqsAsync(int count = 10);

    /// <summary>
    /// 获取相关FAQ
    /// </summary>
    Task<List<FaqItemDto>> GetRelatedFaqsAsync(Guid faqId, int count = 5);

    /// <summary>
    /// 提交FAQ反馈
    /// </summary>
    Task SubmitFeedbackAsync(FaqFeedbackDto dto);

    /// <summary>
    /// 增加FAQ浏览次数
    /// </summary>
    Task IncrementViewCountAsync(Guid faqId);

    /// <summary>
    /// 发布FAQ
    /// </summary>
    Task PublishFaqAsync(Guid faqId);

    /// <summary>
    /// 取消发布FAQ
    /// </summary>
    Task UnpublishFaqAsync(Guid faqId);

    /// <summary>
    /// 获取FAQ类别列表
    /// </summary>
    Task<List<string>> GetFaqCategoriesAsync();

    /// <summary>
    /// 获取所有FAQ（管理用）
    /// </summary>
    Task<PagedResult<FaqItemDto>> GetAllFaqsAsync(int page = 1, int pageSize = 20, string? category = null, bool? isPublished = null);
}