using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application.Support;

/// <summary>
/// 支持工单服务接口
/// </summary>
public interface ISupportTicketService
{
    /// <summary>
    /// 创建工单
    /// </summary>
    Task<SupportTicketDto> CreateTicketAsync(CreateTicketDto dto, Guid customerId);

    /// <summary>
    /// 获取工单详情
    /// </summary>
    Task<SupportTicketDto> GetTicketAsync(Guid ticketId, Guid userId);

    /// <summary>
    /// 获取工单列表
    /// </summary>
    Task<PagedResult<SupportTicketDto>> GetTicketsAsync(SearchTicketsDto searchDto);

    /// <summary>
    /// 获取用户的工单列表
    /// </summary>
    Task<List<SupportTicketDto>> GetCustomerTicketsAsync(Guid customerId, string? status = null);

    /// <summary>
    /// 更新工单
    /// </summary>
    Task UpdateTicketAsync(Guid ticketId, UpdateTicketDto dto, Guid userId);

    /// <summary>
    /// 分配工单
    /// </summary>
    Task AssignTicketAsync(Guid ticketId, Guid assignToId, Guid assignedById);

    /// <summary>
    /// 添加工单消息
    /// </summary>
    Task<TicketMessageDto> AddMessageAsync(Guid ticketId, AddTicketMessageDto dto, Guid senderId, bool isSupport);

    /// <summary>
    /// 获取工单消息列表
    /// </summary>
    Task<List<TicketMessageDto>> GetMessagesAsync(Guid ticketId, Guid userId);

    /// <summary>
    /// 标记消息为已读
    /// </summary>
    Task MarkMessagesAsReadAsync(Guid ticketId, Guid userId);

    /// <summary>
    /// 添加工单附件
    /// </summary>
    Task<TicketAttachmentDto> AddAttachmentAsync(Guid ticketId, string fileName, long fileSize, string contentType, Stream fileStream, Guid uploadedById);

    /// <summary>
    /// 获取工单附件列表
    /// </summary>
    Task<List<TicketAttachmentDto>> GetAttachmentsAsync(Guid ticketId);

    /// <summary>
    /// 下载附件
    /// </summary>
    Task<(Stream Stream, string ContentType, string FileName)> DownloadAttachmentAsync(Guid attachmentId, Guid userId);

    /// <summary>
    /// 评价工单满意度
    /// </summary>
    Task RateTicketAsync(Guid ticketId, TicketSatisfactionDto dto, Guid customerId);

    /// <summary>
    /// 获取工单统计信息
    /// </summary>
    Task<TicketStatisticsDto> GetStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 关闭工单
    /// </summary>
    Task CloseTicketAsync(Guid ticketId, Guid userId);

    /// <summary>
    /// 重新打开工单
    /// </summary>
    Task ReopenTicketAsync(Guid ticketId, Guid customerId);
}