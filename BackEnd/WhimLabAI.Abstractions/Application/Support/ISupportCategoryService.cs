using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.Abstractions.Application.Support;

/// <summary>
/// 支持类别服务接口
/// </summary>
public interface ISupportCategoryService
{
    /// <summary>
    /// 创建类别
    /// </summary>
    Task<SupportCategoryDto> CreateCategoryAsync(CreateSupportCategoryDto dto);

    /// <summary>
    /// 更新类别
    /// </summary>
    Task UpdateCategoryAsync(Guid categoryId, CreateSupportCategoryDto dto);

    /// <summary>
    /// 删除类别
    /// </summary>
    Task DeleteCategoryAsync(Guid categoryId);

    /// <summary>
    /// 获取类别详情
    /// </summary>
    Task<SupportCategoryDto> GetCategoryAsync(Guid categoryId);

    /// <summary>
    /// 获取所有激活的类别
    /// </summary>
    Task<List<SupportCategoryDto>> GetActiveCategoriesAsync(string? type = null);

    /// <summary>
    /// 获取类别树结构
    /// </summary>
    Task<List<SupportCategoryDto>> GetCategoryTreeAsync(string? type = null);

    /// <summary>
    /// 激活类别
    /// </summary>
    Task ActivateCategoryAsync(Guid categoryId);

    /// <summary>
    /// 停用类别
    /// </summary>
    Task DeactivateCategoryAsync(Guid categoryId);

    /// <summary>
    /// 移动类别到新的父类别
    /// </summary>
    Task MoveCategoryAsync(Guid categoryId, Guid? newParentId);

    /// <summary>
    /// 重新排序类别
    /// </summary>
    Task ReorderCategoriesAsync(List<(Guid CategoryId, int DisplayOrder)> orders);
}