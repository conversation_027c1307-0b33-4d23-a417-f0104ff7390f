using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// Agent市场服务接口
/// </summary>
public interface IAgentMarketplaceService
{
    /// <summary>
    /// 获取Agent分类列表
    /// </summary>
    Task<Result<List<AgentCategoryDto>>> GetCategoriesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取热门标签
    /// </summary>
    Task<Result<List<AgentTagDto>>> GetPopularTagsAsync(int count = 20, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 点赞Agent
    /// </summary>
    Task<Result<AgentLikeResultDto>> LikeAgentAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 取消点赞Agent
    /// </summary>
    Task<Result> UnlikeAgentAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查用户是否已点赞
    /// </summary>
    Task<Result<bool>> IsLikedAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 评价Agent
    /// </summary>
    Task<Result<AgentRatingDto>> RateAgentAsync(Guid agentId, Guid userId, CreateRatingDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新评价
    /// </summary>
    Task<Result> UpdateRatingAsync(Guid ratingId, Guid userId, UpdateRatingDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Agent评价列表
    /// </summary>
    Task<Result<PagedResult<AgentRatingDto>>> GetAgentRatingsAsync(Guid agentId, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的Agent评价
    /// </summary>
    Task<Result<AgentRatingDto>> GetUserRatingAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 标记评价为有用
    /// </summary>
    Task<Result> MarkRatingAsHelpfulAsync(Guid ratingId, Guid userId, bool isHelpful, CancellationToken cancellationToken = default);
}