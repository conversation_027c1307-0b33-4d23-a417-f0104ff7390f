using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos.Admin.Rbac;
using WhimLabAI.Shared.Dtos.Admin.Permission;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 权限服务接口
/// </summary>
public interface IPermissionService
{
    /// <summary>
    /// 获取所有权限（树形结构）
    /// </summary>
    Task<Result<List<PermissionTreeDto>>> GetPermissionTreeAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取权限列表（平铺）
    /// </summary>
    Task<Result<List<PermissionDto>>> GetPermissionsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 按分类获取权限
    /// </summary>
    Task<Result<List<PermissionDto>>> GetPermissionsByCategoryAsync(string category, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取权限详情
    /// </summary>
    Task<Result<PermissionDto>> GetPermissionAsync(Guid permissionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建权限
    /// </summary>
    Task<Result<PermissionDto>> CreatePermissionAsync(CreatePermissionDto request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新权限
    /// </summary>
    Task<Result> UpdatePermissionAsync(Guid permissionId, UpdatePermissionDto request, Guid operatorId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 启用权限
    /// </summary>
    Task<Result> EnablePermissionAsync(Guid permissionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 禁用权限
    /// </summary>
    Task<Result> DisablePermissionAsync(Guid permissionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除权限
    /// </summary>
    Task<Result> DeletePermissionAsync(Guid permissionId, Guid operatorId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 初始化系统权限
    /// </summary>
    Task<Result> InitializeSystemPermissionsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取按模块分组的权限列表
    /// </summary>
    Task<Result<List<RolePermissionDto>>> GetGroupedPermissionsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取所有权限（简化接口，用于缓存预热）
    /// </summary>
    Task<Result<List<PermissionDto>>> GetAllAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 同步权限（从代码中扫描并更新数据库）
    /// </summary>
    Task<Result<PermissionSyncResultDto>> SyncPermissionsAsync(Guid operatorId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的权限
    /// </summary>
    Task<UserPermissionDto> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default);
}