using WhimLabAI.Shared.Dtos.Subscription;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// Token包服务接口，用于一次性购买额外Token
/// </summary>
public interface ITokenPackageService
{
    /// <summary>
    /// 获取可用的Token包列表
    /// </summary>
    /// <returns>Token包列表</returns>
    Task<Result<List<TokenPackageDto>>> GetAvailablePackagesAsync();
    
    /// <summary>
    /// 获取Token包详情
    /// </summary>
    /// <param name="packageId">Token包 ID</param>
    /// <returns>Token包详情</returns>
    Task<Result<TokenPackageDto>> GetPackageDetailsAsync(Guid packageId);
    
    /// <summary>
    /// 创建Token包购买订单
    /// </summary>
    /// <param name="userId">用户 ID</param>
    /// <param name="packageId">Token包 ID</param>
    /// <param name="paymentMethod">支付方式</param>
    /// <param name="couponCode">优惠券代码</param>
    /// <returns>订单信息</returns>
    Task<Result<TokenPackageOrderDto>> CreatePurchaseOrderAsync(Guid userId, Guid packageId, string paymentMethod, string? couponCode = null);
    
    /// <summary>
    /// 处理Token包购买成功
    /// </summary>
    /// <param name="orderId">订单 ID</param>
    /// <param name="paymentInfo">支付信息</param>
    /// <returns>处理结果</returns>
    Task<Result> ProcessPurchaseSuccessAsync(Guid orderId, PaymentInfo paymentInfo);
    
    /// <summary>
    /// 获取用户的Token包购买历史
    /// </summary>
    /// <param name="userId">用户 ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>购买历史</returns>
    Task<Result<PagedResult<TokenPackagePurchaseHistoryDto>>> GetUserPurchaseHistoryAsync(Guid userId, int pageNumber = 1, int pageSize = 20);
    
    /// <summary>
    /// 管理员创建Token包
    /// </summary>
    /// <param name="request">创建请求</param>
    /// <returns>创建的Token包</returns>
    Task<Result<TokenPackageDto>> CreatePackageAsync(CreateTokenPackageRequest request);
    
    /// <summary>
    /// 管理员更新Token包
    /// </summary>
    /// <param name="packageId">Token包 ID</param>
    /// <param name="request">更新请求</param>
    /// <returns>更新后的Token包</returns>
    Task<Result<TokenPackageDto>> UpdatePackageAsync(Guid packageId, UpdateTokenPackageRequest request);
    
    /// <summary>
    /// 管理员启用/禁用Token包
    /// </summary>
    /// <param name="packageId">Token包 ID</param>
    /// <param name="isActive">是否启用</param>
    /// <returns>操作结果</returns>
    Task<Result> SetPackageStatusAsync(Guid packageId, bool isActive);
    
    /// <summary>
    /// 获取Token包销售统计
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>销售统计</returns>
    Task<Result<TokenPackageSalesStatisticsDto>> GetSalesStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null);
}

/// <summary>
/// 支付信息
/// </summary>
public class PaymentInfo
{
    public string TransactionId { get; set; } = string.Empty;
    public string PaymentMethod { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public DateTime PaymentTime { get; set; }
    public Dictionary<string, string> ExtraData { get; set; } = new();
}

/// <summary>
/// 创建Token包请求
/// </summary>
public class CreateTokenPackageRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int TokenAmount { get; set; }
    public decimal Price { get; set; }
    public decimal? OriginalPrice { get; set; }
    public bool IsLimited { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public int? MaxPurchasePerUser { get; set; }
}

/// <summary>
/// 更新Token包请求
/// </summary>
public class UpdateTokenPackageRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public decimal? Price { get; set; }
    public decimal? OriginalPrice { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public int? MaxPurchasePerUser { get; set; }
}