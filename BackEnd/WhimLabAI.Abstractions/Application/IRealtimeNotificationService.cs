using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Auth;
using WhimLabAI.Shared.Dtos.Customer.Auth;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 实时通知推送服务接口
/// </summary>
public interface IRealtimeNotificationService
{
    /// <summary>
    /// 推送通知给特定用户
    /// </summary>
    Task PushNotificationToUserAsync(Guid userId, NotificationDto notification);
    
    /// <summary>
    /// 推送通知给多个用户
    /// </summary>
    Task PushNotificationToUsersAsync(List<Guid> userIds, NotificationDto notification);
    
    /// <summary>
    /// 推送系统通知给所有用户
    /// </summary>
    Task PushSystemNotificationAsync(object notification);
    
    /// <summary>
    /// 推送通知给特定类型的订阅者
    /// </summary>
    Task PushNotificationToSubscribersAsync(string notificationType, NotificationDto notification);
    
    /// <summary>
    /// 通知二维码已被扫描
    /// </summary>
    Task NotifyQRCodeScannedAsync(string sessionId, string scannerDeviceId, string scannerIp);
    
    /// <summary>
    /// 通知二维码登录已确认
    /// </summary>
    Task NotifyQRCodeAuthenticatedAsync(string sessionId, AuthResponseDto authResponse);
    
    /// <summary>
    /// 通知二维码登录被拒绝
    /// </summary>
    Task NotifyQRCodeRejectedAsync(string sessionId);
    
    /// <summary>
    /// 通知二维码已取消
    /// </summary>
    Task NotifyQRCodeCancelledAsync(string sessionId);
    
    /// <summary>
    /// 通知二维码已过期
    /// </summary>
    Task NotifyQRCodeExpiredAsync(string sessionId);
}