using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos.Performance;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 数据库性能服务接口
/// </summary>
public interface IDatabasePerformanceService
{
    /// <summary>
    /// 分析数据库索引
    /// </summary>
    Task<Result<IndexAnalysisResult>> AnalyzeIndexesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 分析慢查询
    /// </summary>
    Task<Result<SlowQueryAnalysisResult>> AnalyzeSlowQueriesAsync(
        TimeSpan threshold,
        int limit = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 分析查询执行计划
    /// </summary>
    Task<Result<QueryPlanAnalysis>> AnalyzeQueryPlanAsync(
        string query,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取数据库统计信息
    /// </summary>
    Task<Result<DatabaseStatistics>> GetDatabaseStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取表统计信息
    /// </summary>
    Task<Result<List<TableStatistics>>> GetTableStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 优化数据库
    /// </summary>
    Task<Result<DatabaseOptimizationResult>> OptimizeDatabaseAsync(
        DatabaseOptimizationOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 创建建议的索引
    /// </summary>
    Task<Result<IndexCreationResult>> CreateSuggestedIndexesAsync(
        List<IndexSuggestion> suggestions,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理数据库
    /// </summary>
    Task<Result<DatabaseCleanupResult>> CleanupDatabaseAsync(
        DatabaseCleanupOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 监控数据库性能
    /// </summary>
    Task<Result<DatabasePerformanceMetrics>> MonitorPerformanceAsync(
        TimeSpan duration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 生成数据库性能报告
    /// </summary>
    Task<Result<DatabasePerformanceReport>> GeneratePerformanceReportAsync(
        CancellationToken cancellationToken = default);
}

#region DTOs

/// <summary>
/// 索引分析结果
/// </summary>
public class IndexAnalysisResult
{
    public DateTime AnalyzedAt { get; set; }
    public List<IndexInfo> ExistingIndexes { get; set; } = new();
    public List<IndexSuggestion> SuggestedIndexes { get; set; } = new();
    public List<UnusedIndex> UnusedIndexes { get; set; } = new();
    public List<DuplicateIndex> DuplicateIndexes { get; set; } = new();
    public List<FragmentedIndex> FragmentedIndexes { get; set; } = new();
    public IndexHealthScore HealthScore { get; set; } = new();
}

/// <summary>
/// 索引信息
/// </summary>
public class IndexInfo
{
    public string IndexName { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
    public List<string> Columns { get; set; } = new();
    public bool IsUnique { get; set; }
    public bool IsPrimary { get; set; }
    public long IndexSize { get; set; }
    public long RowCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public long UsageCount { get; set; }
    public DateTime? LastUsedAt { get; set; }
}

/// <summary>
/// 索引建议
/// </summary>
public class IndexSuggestion
{
    public string SuggestionId { get; set; } = Guid.NewGuid().ToString();
    public string TableName { get; set; } = string.Empty;
    public List<string> Columns { get; set; } = new();
    public string IndexType { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public double ExpectedImprovement { get; set; }
    public string CreateStatement { get; set; } = string.Empty;
    public List<string> AffectedQueries { get; set; } = new();
}

/// <summary>
/// 未使用的索引
/// </summary>
public class UnusedIndex
{
    public string IndexName { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
    public long IndexSize { get; set; }
    public DateTime CreatedAt { get; set; }
    public int DaysSinceCreation { get; set; }
    public string DropStatement { get; set; } = string.Empty;
}

/// <summary>
/// 重复索引
/// </summary>
public class DuplicateIndex
{
    public string PrimaryIndex { get; set; } = string.Empty;
    public List<string> DuplicateIndexes { get; set; } = new();
    public string TableName { get; set; } = string.Empty;
    public long TotalWastedSpace { get; set; }
    public List<string> DropStatements { get; set; } = new();
}

/// <summary>
/// 碎片化索引
/// </summary>
public class FragmentedIndex
{
    public string IndexName { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
    public double FragmentationPercent { get; set; }
    public long PageCount { get; set; }
    public string RecommendedAction { get; set; } = string.Empty;
    public string RebuildStatement { get; set; } = string.Empty;
}

/// <summary>
/// 索引健康评分
/// </summary>
public class IndexHealthScore
{
    public int OverallScore { get; set; }
    public string Grade { get; set; } = string.Empty;
    public Dictionary<string, int> CategoryScores { get; set; } = new();
    public List<string> Issues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// 慢查询分析结果
/// </summary>
public class SlowQueryAnalysisResult
{
    public DateTime AnalyzedAt { get; set; }
    public TimeSpan AnalysisPeriod { get; set; }
    public int TotalSlowQueries { get; set; }
    public List<SlowQuery> SlowQueries { get; set; } = new();
    public List<QueryPattern> CommonPatterns { get; set; } = new();
    public Dictionary<string, int> SlowQueryByTable { get; set; } = new();
    public List<QueryOptimizationSuggestion> Suggestions { get; set; } = new();
}

/// <summary>
/// 慢查询
/// </summary>
public class SlowQuery
{
    public string QueryId { get; set; } = string.Empty;
    public string QueryText { get; set; } = string.Empty;
    public TimeSpan ExecutionTime { get; set; }
    public DateTime ExecutedAt { get; set; }
    public long RowsExamined { get; set; }
    public long RowsReturned { get; set; }
    public string ExecutionPlan { get; set; } = string.Empty;
    public List<string> Tables { get; set; } = new();
    public string QueryType { get; set; } = string.Empty;
    public int ExecutionCount { get; set; }
}

/// <summary>
/// 查询模式
/// </summary>
public class QueryPattern
{
    public string Pattern { get; set; } = string.Empty;
    public int Frequency { get; set; }
    public TimeSpan AverageExecutionTime { get; set; }
    public List<string> Examples { get; set; } = new();
    public string OptimizationHint { get; set; } = string.Empty;
}

/// <summary>
/// 查询优化建议
/// </summary>
public class QueryOptimizationSuggestion
{
    public string QueryPattern { get; set; } = string.Empty;
    public string Issue { get; set; } = string.Empty;
    public string Suggestion { get; set; } = string.Empty;
    public double ExpectedImprovement { get; set; }
    public string Priority { get; set; } = string.Empty;
}

/// <summary>
/// 查询计划分析
/// </summary>
public class QueryPlanAnalysis
{
    public string Query { get; set; } = string.Empty;
    public string ExecutionPlan { get; set; } = string.Empty;
    public List<QueryPlanNode> PlanNodes { get; set; } = new();
    public double EstimatedCost { get; set; }
    public long EstimatedRows { get; set; }
    public List<string> UsedIndexes { get; set; } = new();
    public List<string> MissingIndexes { get; set; } = new();
    public List<QueryPlanIssue> Issues { get; set; } = new();
    public List<string> Optimizations { get; set; } = new();
}

/// <summary>
/// 查询计划节点
/// </summary>
public class QueryPlanNode
{
    public string NodeType { get; set; } = string.Empty;
    public string Operation { get; set; } = string.Empty;
    public double Cost { get; set; }
    public long EstimatedRows { get; set; }
    public long ActualRows { get; set; }
    public string Table { get; set; } = string.Empty;
    public string Index { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 查询计划问题
/// </summary>
public class QueryPlanIssue
{
    public string IssueType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public string Resolution { get; set; } = string.Empty;
}

/// <summary>
/// 数据库统计信息
/// </summary>
public class DatabaseStatistics
{
    public string DatabaseName { get; set; } = string.Empty;
    public long DatabaseSize { get; set; }
    public long DataSize { get; set; }
    public long IndexSize { get; set; }
    public int TableCount { get; set; }
    public int IndexCount { get; set; }
    public long TotalRows { get; set; }
    public int ConnectionCount { get; set; }
    public int ActiveQueries { get; set; }
    public double CacheHitRatio { get; set; }
    public long BufferPoolSize { get; set; }
    public DateTime CollectedAt { get; set; }
}

/// <summary>
/// 表统计信息
/// </summary>
public class TableStatistics
{
    public string TableName { get; set; } = string.Empty;
    public long RowCount { get; set; }
    public long DataSize { get; set; }
    public long IndexSize { get; set; }
    public long TotalSize { get; set; }
    public int IndexCount { get; set; }
    public DateTime LastAnalyzed { get; set; }
    public DateTime LastModified { get; set; }
    public double FragmentationPercent { get; set; }
    public List<ColumnStatistics> Columns { get; set; } = new();
}

/// <summary>
/// 列统计信息
/// </summary>
public class ColumnStatistics
{
    public string ColumnName { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public int DistinctValues { get; set; }
    public double NullPercent { get; set; }
    public double AverageLength { get; set; }
    public bool IsIndexed { get; set; }
}

/// <summary>
/// 数据库优化选项
/// </summary>
public class DatabaseOptimizationOptions
{
    public bool AnalyzeTables { get; set; } = true;
    public bool OptimizeTables { get; set; } = true;
    public bool RebuildIndexes { get; set; } = true;
    public bool UpdateStatistics { get; set; } = true;
    public bool CleanupLogs { get; set; } = true;
    public bool VacuumDatabase { get; set; } = true;
    public double FragmentationThreshold { get; set; } = 30.0;
}

/// <summary>
/// 数据库优化结果
/// </summary>
public class DatabaseOptimizationResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration => EndTime - StartTime;
    public List<DatabaseOptimizationAction> ActionsPerformed { get; set; } = new();
    public long SpaceReclaimed { get; set; }
    public double PerformanceImprovement { get; set; }
    public List<string> Warnings { get; set; } = new();
    public bool Success { get; set; }
}

/// <summary>
/// 数据库优化动作
/// </summary>
public class DatabaseOptimizationAction
{
    public string ActionType { get; set; } = string.Empty;
    public string Target { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public string Result { get; set; } = string.Empty;
}

/// <summary>
/// 索引创建结果
/// </summary>
public class IndexCreationResult
{
    public int RequestedIndexes { get; set; }
    public int CreatedIndexes { get; set; }
    public int FailedIndexes { get; set; }
    public List<IndexCreationDetail> Details { get; set; } = new();
    public TimeSpan TotalDuration { get; set; }
}

/// <summary>
/// 索引创建详情
/// </summary>
public class IndexCreationDetail
{
    public string IndexName { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public TimeSpan CreationTime { get; set; }
}

/// <summary>
/// 数据库清理选项
/// </summary>
public class DatabaseCleanupOptions
{
    public bool DeleteOldLogs { get; set; } = true;
    public int LogRetentionDays { get; set; } = 30;
    public bool DeleteOrphanedRecords { get; set; } = true;
    public bool CompactTables { get; set; } = true;
    public bool ClearTempTables { get; set; } = true;
    public bool ArchiveOldData { get; set; } = false;
    public int ArchiveThresholdDays { get; set; } = 365;
}

/// <summary>
/// 数据库清理结果
/// </summary>
public class DatabaseCleanupResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public long SpaceReclaimed { get; set; }
    public int LogsDeleted { get; set; }
    public int OrphanedRecordsDeleted { get; set; }
    public int TablesCompacted { get; set; }
    public long RecordsArchived { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// 数据库性能指标
/// </summary>
public class DatabasePerformanceMetrics
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public List<PerformanceSnapshot> Snapshots { get; set; } = new();
    public PerformanceSummary Summary { get; set; } = new();
    public List<PerformanceAlert> Alerts { get; set; } = new();
}

/// <summary>
/// 性能快照
/// </summary>
public class PerformanceSnapshot
{
    public DateTime Timestamp { get; set; }
    public int QueriesPerSecond { get; set; }
    public double AverageQueryTime { get; set; }
    public int ConnectionCount { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskIORead { get; set; }
    public double DiskIOWrite { get; set; }
    public long BufferPoolHits { get; set; }
    public long BufferPoolMisses { get; set; }
    public int LockWaits { get; set; }
    public int Deadlocks { get; set; }
}

/// <summary>
/// 性能摘要
/// </summary>
public class PerformanceSummary
{
    public double AverageQPS { get; set; }
    public double PeakQPS { get; set; }
    public double AverageResponseTime { get; set; }
    public double P95ResponseTime { get; set; }
    public double P99ResponseTime { get; set; }
    public double CacheHitRatio { get; set; }
    public int TotalDeadlocks { get; set; }
    public int TotalLockWaits { get; set; }
}

/// <summary>
/// 数据库性能报告
/// </summary>
public class DatabasePerformanceReport
{
    public string ReportId { get; set; } = Guid.NewGuid().ToString();
    public DateTime GeneratedAt { get; set; }
    public TimeSpan ReportPeriod { get; set; }
    public DatabaseStatistics CurrentStatistics { get; set; } = new();
    public IndexAnalysisResult IndexAnalysis { get; set; } = new();
    public SlowQueryAnalysisResult SlowQueryAnalysis { get; set; } = new();
    public DatabasePerformanceMetrics PerformanceMetrics { get; set; } = new();
    public List<PerformanceRecommendation> Recommendations { get; set; } = new();
    public PerformanceScore Score { get; set; } = new();
}

/// <summary>
/// 性能建议
/// </summary>
public class PerformanceRecommendation
{
    public string Category { get; set; } = string.Empty;
    public string Issue { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public double ExpectedImprovement { get; set; }
    public string Implementation { get; set; } = string.Empty;
}

/// <summary>
/// 性能评分
/// </summary>
public class PerformanceScore
{
    public int OverallScore { get; set; }
    public string Grade { get; set; } = string.Empty;
    public Dictionary<string, int> CategoryScores { get; set; } = new();
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
}

#endregion