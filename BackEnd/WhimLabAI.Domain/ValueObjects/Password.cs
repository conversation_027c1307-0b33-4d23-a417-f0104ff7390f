using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Exceptions;
using WhimLabAI.Shared.Utilities;

namespace WhimLabAI.Domain.ValueObjects;

public class Password : ValueObject
{
    public string HashedValue { get; }
    public string Hash => HashedValue; // Alias for backward compatibility
    
    private Password(string hashedValue)
    {
        HashedValue = hashedValue;
    }
    
    public static Password CreateFromPlainText(string plainTextPassword)
    {
        ValidatePassword(plainTextPassword);
        var hashedPassword = PasswordHelper.HashPassword(plainTextPassword);
        return new Password(hashedPassword);
    }
    
    public static Password CreateFromHash(string hashedPassword)
    {
        if (string.IsNullOrWhiteSpace(hashedPassword))
            throw new ValidationException("Password", "密码哈希值不能为空");
            
        return new Password(hashedPassword);
    }
    
    public bool Verify(string plainTextPassword)
    {
        if (string.IsNullOrWhiteSpace(plainTextPassword))
            return false;
            
        return PasswordHelper.VerifyPassword(plainTextPassword, HashedValue);
    }
    
    private static void ValidatePassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            throw new ValidationException("Password", "密码不能为空");
            
        if (password.Length < 8 || password.Length > 20)
            throw new ValidationException("Password", "密码长度必须在8-20个字符之间");
            
        bool hasUpper = password.Any(char.IsUpper);
        bool hasLower = password.Any(char.IsLower);
        bool hasDigit = password.Any(char.IsDigit);
        bool hasSpecial = password.Any(c => !char.IsLetterOrDigit(c));
        
        if (!hasUpper || !hasLower || !hasDigit || !hasSpecial)
            throw new ValidationException("Password", "密码必须包含大写字母、小写字母、数字和特殊字符");
    }
    
    public static string GenerateRandomPassword(int length = 16)
    {
        return PasswordHelper.GenerateRandomPassword(length);
    }
    
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return HashedValue;
    }
    
    public override string ToString() => "[Protected]";
}