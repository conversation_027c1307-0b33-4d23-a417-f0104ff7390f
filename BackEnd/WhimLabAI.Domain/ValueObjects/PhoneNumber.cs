using System.Text.RegularExpressions;
using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.ValueObjects;

public class PhoneNumber : ValueObject
{
    private static readonly Regex ChinaMobileRegex = new(
        @"^1[3-9]\d{9}$",
        RegexOptions.Compiled);
    
    public string Value { get; }
    public string CountryCode { get; }
    
    private PhoneNumber(string value, string countryCode = "+86")
    {
        Value = value;
        CountryCode = countryCode;
    }
    
    public static PhoneNumber Create(string phoneNumber, string countryCode = "+86")
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            throw new ValidationException("PhoneNumber", "手机号码不能为空");
            
        // Remove common formatting characters
        phoneNumber = phoneNumber.Replace(" ", "")
                               .Replace("-", "")
                               .Replace("(", "")
                               .Replace(")", "");
        
        // For China mobile numbers
        if (countryCode == "+86")
        {
            if (!ChinaMobileRegex.IsMatch(phoneNumber))
                throw new ValidationException("PhoneNumber", "手机号码格式不正确");
        }
        
        return new PhoneNumber(phoneNumber, countryCode);
    }
    
    public static PhoneNumber? CreateOrNull(string? phoneNumber, string countryCode = "+86")
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return null;
            
        try
        {
            return Create(phoneNumber, countryCode);
        }
        catch
        {
            return null;
        }
    }
    
    public string GetFullNumber() => $"{CountryCode}{Value}";
    
    public string GetMaskedNumber()
    {
        if (Value.Length >= 11)
            return $"{Value.Substring(0, 3)}****{Value.Substring(7)}";
        return Value;
    }
    
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Value;
        yield return CountryCode;
    }
    
    public override string ToString() => GetFullNumber();
}