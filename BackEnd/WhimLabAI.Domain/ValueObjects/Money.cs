using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.ValueObjects;

public class Money : ValueObject
{
    public decimal Amount { get; }
    public string Currency { get; }
    
    private Money(decimal amount, string currency)
    {
        Amount = amount;
        Currency = currency;
    }
    
    public static Money Create(decimal amount, string currency = "CNY")
    {
        if (amount < 0)
            throw new ValidationException("Money", "金额不能为负数");
            
        if (string.IsNullOrWhiteSpace(currency))
            throw new ValidationException("Money", "货币类型不能为空");
            
        return new Money(Math.Round(amount, 2), currency.ToUpperInvariant());
    }
    
    public static Money Zero(string currency = "CNY") => new(0, currency);
    
    public Money Add(Money other)
    {
        if (Currency != other.Currency)
            throw new InvalidOperationException($"不同货币类型不能相加: {Currency} 和 {other.Currency}");
            
        return new Money(Amount + other.Amount, Currency);
    }
    
    public Money Subtract(Money other)
    {
        if (Currency != other.Currency)
            throw new InvalidOperationException($"不同货币类型不能相减: {Currency} 和 {other.Currency}");
            
        return new Money(Amount - other.Amount, Currency);
    }
    
    public Money Multiply(decimal factor)
    {
        return new Money(Math.Round(Amount * factor, 2), Currency);
    }
    
    public Money ApplyDiscount(decimal discountPercentage)
    {
        if (discountPercentage < 0 || discountPercentage > 100)
            throw new ValidationException("Discount", "折扣百分比必须在0-100之间");
            
        var discountFactor = 1 - (discountPercentage / 100);
        return Multiply(discountFactor);
    }
    
    public bool IsZero() => Amount == 0;
    
    public bool IsPositive() => Amount > 0;
    
    public bool IsNegative() => Amount < 0;
    
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Amount;
        yield return Currency;
    }
    
    public override string ToString() => $"{Currency} {Amount:F2}";
    
    public string ToChineseString()
    {
        return Currency switch
        {
            "CNY" => $"￥{Amount:F2}",
            "USD" => $"${Amount:F2}",
            _ => ToString()
        };
    }
    
    public static bool operator >(Money left, Money right)
    {
        if (left.Currency != right.Currency)
            throw new InvalidOperationException("不同货币类型不能比较");
        return left.Amount > right.Amount;
    }
    
    public static bool operator <(Money left, Money right)
    {
        if (left.Currency != right.Currency)
            throw new InvalidOperationException("不同货币类型不能比较");
        return left.Amount < right.Amount;
    }
    
    public static bool operator >=(Money left, Money right)
    {
        if (left.Currency != right.Currency)
            throw new InvalidOperationException("不同货币类型不能比较");
        return left.Amount >= right.Amount;
    }
    
    public static bool operator <=(Money left, Money right)
    {
        if (left.Currency != right.Currency)
            throw new InvalidOperationException("不同货币类型不能比较");
        return left.Amount <= right.Amount;
    }
    
    public static Money operator +(Money left, Money right) => left.Add(right);
    public static Money operator -(Money left, Money right) => left.Subtract(right);
    public static Money operator *(Money money, decimal factor) => money.Multiply(factor);
    public static Money operator *(decimal factor, Money money) => money.Multiply(factor);
}