using System.Text.RegularExpressions;
using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.ValueObjects;

public class Email : ValueObject
{
    private static readonly Regex EmailRegex = new(
        @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);
    
    public string Value { get; }
    
    private Email(string value)
    {
        Value = value.ToLowerInvariant();
    }
    
    public static Email Create(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            throw new ValidationException("Email", "邮箱地址不能为空");
            
        if (!EmailRegex.IsMatch(email))
            throw new ValidationException("Email", "邮箱地址格式不正确");
            
        return new Email(email);
    }
    
    public static Email? CreateOrNull(string? email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return null;
            
        return EmailRegex.IsMatch(email) ? new Email(email) : null;
    }
    
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Value;
    }
    
    public override string ToString() => Value;
    
    public static implicit operator string(Email email) => email.Value;
}