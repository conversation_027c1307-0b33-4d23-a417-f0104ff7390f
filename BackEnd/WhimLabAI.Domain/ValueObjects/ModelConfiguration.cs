using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.ValueObjects;

public class ModelConfiguration : ValueObject
{
    public string ModelType { get; }
    public string ModelName { get; }
    public double Temperature { get; }
    public int MaxTokens { get; }
    public double TopP { get; }
    public double FrequencyPenalty { get; }
    public double PresencePenalty { get; }
    public Dictionary<string, object> AdditionalSettings { get; }
    
    private ModelConfiguration(
        string modelType,
        string modelName,
        double temperature,
        int maxTokens,
        double topP,
        double frequencyPenalty,
        double presencePenalty,
        Dictionary<string, object>? additionalSettings)
    {
        ModelType = modelType;
        ModelName = modelName;
        Temperature = temperature;
        MaxTokens = maxTokens;
        TopP = topP;
        FrequencyPenalty = frequencyPenalty;
        PresencePenalty = presencePenalty;
        AdditionalSettings = additionalSettings ?? new Dictionary<string, object>();
    }
    
    public static ModelConfiguration Create(
        string modelType,
        string modelName,
        double temperature = 0.7,
        int maxTokens = 2048,
        double topP = 1.0,
        double frequencyPenalty = 0.0,
        double presencePenalty = 0.0,
        Dictionary<string, object>? additionalSettings = null)
    {
        if (string.IsNullOrWhiteSpace(modelType))
            throw new ValidationException("ModelType", "模型类型不能为空");
            
        if (string.IsNullOrWhiteSpace(modelName))
            throw new ValidationException("ModelName", "模型名称不能为空");
            
        if (temperature < 0 || temperature > 2)
            throw new ValidationException("Temperature", "Temperature必须在0-2之间");
            
        if (maxTokens < 1 || maxTokens > 32000)
            throw new ValidationException("MaxTokens", "MaxTokens必须在1-32000之间");
            
        if (topP < 0 || topP > 1)
            throw new ValidationException("TopP", "TopP必须在0-1之间");
            
        if (frequencyPenalty < -2 || frequencyPenalty > 2)
            throw new ValidationException("FrequencyPenalty", "FrequencyPenalty必须在-2到2之间");
            
        if (presencePenalty < -2 || presencePenalty > 2)
            throw new ValidationException("PresencePenalty", "PresencePenalty必须在-2到2之间");
            
        return new ModelConfiguration(
            modelType,
            modelName,
            temperature,
            maxTokens,
            topP,
            frequencyPenalty,
            presencePenalty,
            additionalSettings);
    }
    
    public static ModelConfiguration Default(string modelType = "OpenAI", string modelName = "gpt-3.5-turbo")
    {
        return Create(modelType, modelName);
    }
    
    public ModelConfiguration WithTemperature(double temperature)
    {
        return Create(
            ModelType,
            ModelName,
            temperature,
            MaxTokens,
            TopP,
            FrequencyPenalty,
            PresencePenalty,
            AdditionalSettings);
    }
    
    public ModelConfiguration WithMaxTokens(int maxTokens)
    {
        return Create(
            ModelType,
            ModelName,
            Temperature,
            maxTokens,
            TopP,
            FrequencyPenalty,
            PresencePenalty,
            AdditionalSettings);
    }
    
    public ModelConfiguration WithAdditionalSetting(string key, object value)
    {
        var newSettings = new Dictionary<string, object>(AdditionalSettings)
        {
            [key] = value
        };
        
        return Create(
            ModelType,
            ModelName,
            Temperature,
            MaxTokens,
            TopP,
            FrequencyPenalty,
            PresencePenalty,
            newSettings);
    }
    
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return ModelType;
        yield return ModelName;
        yield return Temperature;
        yield return MaxTokens;
        yield return TopP;
        yield return FrequencyPenalty;
        yield return PresencePenalty;
        foreach (var kvp in AdditionalSettings.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
    
    public override string ToString()
    {
        return $"{ModelType}:{ModelName} (T={Temperature}, Max={MaxTokens})";
    }
}