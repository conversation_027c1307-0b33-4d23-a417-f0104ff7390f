using System.Linq.Expressions;
using WhimLabAI.Domain.Entities.Audit;
using WhimLabAI.Shared.Dtos.Audit;

namespace WhimLabAI.Domain.Specifications;

/// <summary>
/// 审计日志查询规格
/// </summary>
public class AuditLogQuerySpecification : Specification<AuditLog>
{
    private readonly AuditLogQueryDto _queryDto;

    public AuditLogQuerySpecification(AuditLogQueryDto queryDto)
    {
        _queryDto = queryDto;
    }

    public override Expression<Func<AuditLog, bool>> ToExpression()
    {
        Expression<Func<AuditLog, bool>> expression = x => true;

        if (!string.IsNullOrEmpty(_queryDto.UserId))
        {
            Expression<Func<AuditLog, bool>> userIdExpr = x => x.UserId.ToString() == _queryDto.UserId;
            expression = CombineExpressions(expression, userIdExpr);
        }

        if (!string.IsNullOrEmpty(_queryDto.UserName))
        {
            Expression<Func<AuditLog, bool>> userNameExpr = x => x.UserName.Contains(_queryDto.UserName);
            expression = CombineExpressions(expression, userNameExpr);
        }

        if (!string.IsNullOrEmpty(_queryDto.Action))
        {
            Expression<Func<AuditLog, bool>> actionExpr = x => x.Action.Contains(_queryDto.Action);
            expression = CombineExpressions(expression, actionExpr);
        }

        if (!string.IsNullOrEmpty(_queryDto.Module))
        {
            Expression<Func<AuditLog, bool>> moduleExpr = x => x.Module == _queryDto.Module;
            expression = CombineExpressions(expression, moduleExpr);
        }

        if (!string.IsNullOrEmpty(_queryDto.EntityType))
        {
            Expression<Func<AuditLog, bool>> entityTypeExpr = x => x.EntityType == _queryDto.EntityType;
            expression = CombineExpressions(expression, entityTypeExpr);
        }

        if (!string.IsNullOrEmpty(_queryDto.EntityId))
        {
            Expression<Func<AuditLog, bool>> entityIdExpr = x => x.EntityId == _queryDto.EntityId;
            expression = CombineExpressions(expression, entityIdExpr);
        }

        if (_queryDto.StartTime.HasValue)
        {
            Expression<Func<AuditLog, bool>> startTimeExpr = x => x.CreatedAt >= _queryDto.StartTime.Value;
            expression = CombineExpressions(expression, startTimeExpr);
        }

        if (_queryDto.EndTime.HasValue)
        {
            Expression<Func<AuditLog, bool>> endTimeExpr = x => x.CreatedAt <= _queryDto.EndTime.Value;
            expression = CombineExpressions(expression, endTimeExpr);
        }

        if (_queryDto.IsSuccess.HasValue)
        {
            Expression<Func<AuditLog, bool>> successExpr = x => x.IsSuccess == _queryDto.IsSuccess.Value;
            expression = CombineExpressions(expression, successExpr);
        }

        if (!string.IsNullOrEmpty(_queryDto.RiskLevel))
        {
            Expression<Func<AuditLog, bool>> riskLevelExpr = x => x.RiskLevel == _queryDto.RiskLevel;
            expression = CombineExpressions(expression, riskLevelExpr);
        }

        return expression;
    }

    private Expression<Func<AuditLog, bool>> CombineExpressions(
        Expression<Func<AuditLog, bool>> expr1,
        Expression<Func<AuditLog, bool>> expr2)
    {
        var parameter = Expression.Parameter(typeof(AuditLog));
        var body1 = ReplaceParameter(expr1.Body, expr1.Parameters[0], parameter);
        var body2 = ReplaceParameter(expr2.Body, expr2.Parameters[0], parameter);
        var combined = Expression.AndAlso(body1, body2);
        return Expression.Lambda<Func<AuditLog, bool>>(combined, parameter);
    }

    private Expression ReplaceParameter(Expression expression, ParameterExpression oldParameter, ParameterExpression newParameter)
    {
        return new ParameterReplacer(oldParameter, newParameter).Visit(expression);
    }

    private class ParameterReplacer : ExpressionVisitor
    {
        private readonly ParameterExpression _oldParameter;
        private readonly ParameterExpression _newParameter;

        public ParameterReplacer(ParameterExpression oldParameter, ParameterExpression newParameter)
        {
            _oldParameter = oldParameter;
            _newParameter = newParameter;
        }

        protected override Expression VisitParameter(ParameterExpression node)
        {
            return node == _oldParameter ? _newParameter : base.VisitParameter(node);
        }
    }
}