using System.Linq.Expressions;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Specifications;

public class ValidSubscriptionSpecification : Specification<Subscription>
{
    public override Expression<Func<Subscription, bool>> ToExpression()
    {
        var now = DateTime.UtcNow;
        return subscription => subscription.Status == SubscriptionStatus.Active &&
                              subscription.StartDate <= now &&
                              subscription.EndDate >= now;
    }
}

public class ExpiringSubscriptionSpecification : Specification<Subscription>
{
    private readonly int _daysBeforeExpiry;
    
    public ExpiringSubscriptionSpecification(int daysBeforeExpiry = 7)
    {
        _daysBeforeExpiry = daysBeforeExpiry;
    }
    
    public override Expression<Func<Subscription, bool>> ToExpression()
    {
        var expiryDate = DateTime.UtcNow.AddDays(_daysBeforeExpiry);
        return subscription => subscription.Status == SubscriptionStatus.Active &&
                              subscription.EndDate <= expiryDate &&
                              subscription.EndDate >= DateTime.UtcNow;
    }
}

public class AutoRenewSubscriptionSpecification : Specification<Subscription>
{
    public override Expression<Func<Subscription, bool>> ToExpression()
    {
        return subscription => subscription.Status == SubscriptionStatus.Active &&
                              subscription.AutoRenew &&
                              subscription.NextBillingDate.HasValue;
    }
}

public class QuotaExhaustedSubscriptionSpecification : Specification<Subscription>
{
    public override Expression<Func<Subscription, bool>> ToExpression()
    {
        return subscription => subscription.Status == SubscriptionStatus.Active &&
                              subscription.TokenQuota.IsExhausted();
    }
}