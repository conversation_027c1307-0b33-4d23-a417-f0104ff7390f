using System.Linq.Expressions;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Specifications;

public class PaidOrderSpecification : Specification<Order>
{
    public override Expression<Func<Order, bool>> ToExpression()
    {
        return order => order.Status == OrderStatus.Paid;
    }
}

public class PendingOrderSpecification : Specification<Order>
{
    public override Expression<Func<Order, bool>> ToExpression()
    {
        return order => order.Status == OrderStatus.Pending;
    }
}

public class ExpiredOrderSpecification : Specification<Order>
{
    public override Expression<Func<Order, bool>> ToExpression()
    {
        var now = DateTime.UtcNow;
        return order => order.Status == OrderStatus.Pending &&
                       order.ExpireAt < now;
    }
}

public class RefundableOrderSpecification : Specification<Order>
{
    private readonly int _maxRefundDays;
    
    public RefundableOrderSpecification(int maxRefundDays = 7)
    {
        _maxRefundDays = maxRefundDays;
    }
    
    public override Expression<Func<Order, bool>> ToExpression()
    {
        var refundDeadline = DateTime.UtcNow.AddDays(-_maxRefundDays);
        return order => order.Status == OrderStatus.Paid &&
                       order.PaidAt.HasValue &&
                       order.PaidAt.Value >= refundDeadline &&
                       order.CanRefund();
    }
}

public class CustomerOrderSpecification : Specification<Order>
{
    private readonly Guid _customerUserId;
    
    public CustomerOrderSpecification(Guid customerUserId)
    {
        _customerUserId = customerUserId;
    }
    
    public override Expression<Func<Order, bool>> ToExpression()
    {
        return order => order.CustomerUserId == _customerUserId;
    }
}