using System.Linq.Expressions;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Specifications;

public class PublishedAgentSpecification : Specification<Agent>
{
    public override Expression<Func<Agent, bool>> ToExpression()
    {
        return agent => agent.Status == AgentStatus.Published;
    }
}

public class AgentByCategorySpecification : Specification<Agent>
{
    private readonly Guid _categoryId;
    
    public AgentByCategorySpecification(Guid categoryId)
    {
        _categoryId = categoryId;
    }
    
    public override Expression<Func<Agent, bool>> ToExpression()
    {
        return agent => agent.CategoryId == _categoryId;
    }
}

public class AgentByCreatorSpecification : Specification<Agent>
{
    private readonly Guid _creatorId;
    
    public AgentByCreatorSpecification(Guid creatorId)
    {
        _creatorId = creatorId;
    }
    
    public override Expression<Func<Agent, bool>> ToExpression()
    {
        return agent => agent.CreatorId == _creatorId;
    }
}

public class PopularAgentSpecification : Specification<Agent>
{
    private readonly int _minUsageCount;
    private readonly double _minRating;
    
    public PopularAgentSpecification(int minUsageCount = 100, double minRating = 4.0)
    {
        _minUsageCount = minUsageCount;
        _minRating = minRating;
    }
    
    public override Expression<Func<Agent, bool>> ToExpression()
    {
        return agent => agent.Status == AgentStatus.Published &&
                       agent.UsageCount >= _minUsageCount &&
                       agent.AverageRating >= _minRating;
    }
}