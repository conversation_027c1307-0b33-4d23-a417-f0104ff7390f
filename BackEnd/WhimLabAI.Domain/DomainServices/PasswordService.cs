using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Utilities;

namespace WhimLabAI.Domain.DomainServices;

public class PasswordService
{
    private readonly int _minLength;
    private readonly bool _requireUppercase;
    private readonly bool _requireLowercase;
    private readonly bool _requireDigit;
    private readonly bool _requireSpecialChar;
    
    public PasswordService(
        int minLength = 8,
        bool requireUppercase = true,
        bool requireLowercase = true,
        bool requireDigit = true,
        bool requireSpecialChar = false)
    {
        _minLength = minLength;
        _requireUppercase = requireUppercase;
        _requireLowercase = requireLowercase;
        _requireDigit = requireDigit;
        _requireSpecialChar = requireSpecialChar;
    }
    
    public Password HashPassword(string plainPassword)
    {
        ValidatePasswordStrength(plainPassword);
        return Password.CreateFromPlainText(plainPassword);
    }
    
    public bool VerifyPassword(string plainPassword, Password hashedPassword)
    {
        return hashedPassword.Verify(plainPassword);
    }
    
    public string GenerateRandomPassword(int length = 16)
    {
        if (length < _minLength)
            length = _minLength;
            
        return Password.GenerateRandomPassword(length);
    }
    
    public bool IsPasswordStrong(string password)
    {
        try
        {
            ValidatePasswordStrength(password);
            return true;
        }
        catch
        {
            return false;
        }
    }
    
    public List<string> GetPasswordStrengthIssues(string password)
    {
        var issues = new List<string>();
        
        if (string.IsNullOrEmpty(password))
        {
            issues.Add("密码不能为空");
            return issues;
        }
        
        if (password.Length < _minLength)
            issues.Add($"密码长度不能少于{_minLength}个字符");
            
        if (_requireUppercase && !password.Any(char.IsUpper))
            issues.Add("密码必须包含大写字母");
            
        if (_requireLowercase && !password.Any(char.IsLower))
            issues.Add("密码必须包含小写字母");
            
        if (_requireDigit && !password.Any(char.IsDigit))
            issues.Add("密码必须包含数字");
            
        if (_requireSpecialChar && !password.Any(c => !char.IsLetterOrDigit(c)))
            issues.Add("密码必须包含特殊字符");
            
        return issues;
    }
    
    private void ValidatePasswordStrength(string password)
    {
        var issues = GetPasswordStrengthIssues(password);
        if (issues.Any())
        {
            throw new ArgumentException(string.Join("; ", issues));
        }
    }
}