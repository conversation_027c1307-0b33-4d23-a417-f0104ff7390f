namespace WhimLabAI.Domain.DomainServices;

public class TokenCalculationService
{
    private readonly Dictionary<string, TokenPricing> _modelPricing;
    
    public TokenCalculationService()
    {
        _modelPricing = new Dictionary<string, TokenPricing>
        {
            // OpenAI Models
            ["gpt-3.5-turbo"] = new TokenPricing(0.0005m, 0.0015m),
            ["gpt-3.5-turbo-16k"] = new TokenPricing(0.003m, 0.004m),
            ["gpt-4"] = new TokenPricing(0.03m, 0.06m),
            ["gpt-4-32k"] = new TokenPricing(0.06m, 0.12m),
            ["gpt-4-turbo"] = new TokenPricing(0.01m, 0.03m),
            
            // Claude Models
            ["claude-3-opus"] = new TokenPricing(0.015m, 0.075m),
            ["claude-3-sonnet"] = new TokenPricing(0.003m, 0.015m),
            ["claude-3-haiku"] = new TokenPricing(0.00025m, 0.00125m),
            
            // Default pricing
            ["default"] = new TokenPricing(0.001m, 0.002m)
        };
    }
    
    public int CalculateTokens(string text, string? model = null)
    {
        if (string.IsNullOrEmpty(text))
            return 0;
            
        // Simple approximation: ~4 characters per token for English
        // This is a rough estimate and should be replaced with proper tokenization
        return (int)Math.Ceiling(text.Length / 4.0);
    }
    
    public int CalculateTokensForMessages(List<Message> messages, string? model = null)
    {
        if (messages == null || messages.Count == 0)
            return 0;
            
        int totalTokens = 0;
        
        foreach (var message in messages)
        {
            // Add tokens for role (system, user, assistant)
            totalTokens += 4; // Approximate tokens for message metadata
            
            // Add tokens for content
            totalTokens += CalculateTokens(message.Content, model);
            
            // Add tokens for attachments if any
            if (message.Attachments != null)
            {
                totalTokens += message.Attachments.Count * 10; // Approximate tokens for attachment metadata
            }
        }
        
        // Add tokens for conversation structure
        totalTokens += 3; // Start/end tokens
        
        return totalTokens;
    }
    
    public decimal EstimateTokenCost(int inputTokens, int outputTokens, string model)
    {
        var pricing = GetPricingForModel(model);
        
        var inputCost = (inputTokens / 1000m) * pricing.InputPricePerThousand;
        var outputCost = (outputTokens / 1000m) * pricing.OutputPricePerThousand;
        
        return inputCost + outputCost;
    }
    
    public TokenEstimate EstimateConversationCost(List<Message> messages, int expectedOutputTokens, string model)
    {
        var inputTokens = CalculateTokensForMessages(messages, model);
        var totalTokens = inputTokens + expectedOutputTokens;
        var cost = EstimateTokenCost(inputTokens, expectedOutputTokens, model);
        
        return new TokenEstimate
        {
            InputTokens = inputTokens,
            OutputTokens = expectedOutputTokens,
            TotalTokens = totalTokens,
            EstimatedCost = cost,
            Model = model
        };
    }
    
    public bool IsWithinTokenLimit(int tokens, string model)
    {
        var limits = GetTokenLimitsForModel(model);
        return tokens <= limits.MaxTokens;
    }
    
    private TokenPricing GetPricingForModel(string model)
    {
        model = model?.ToLowerInvariant() ?? "default";
        
        if (_modelPricing.TryGetValue(model, out var pricing))
            return pricing;
            
        // Check for partial matches (e.g., "gpt-4-0613" matches "gpt-4")
        foreach (var kvp in _modelPricing)
        {
            if (model.StartsWith(kvp.Key))
                return kvp.Value;
        }
        
        return _modelPricing["default"];
    }
    
    private TokenLimits GetTokenLimitsForModel(string model)
    {
        model = model?.ToLowerInvariant() ?? "default";
        
        return model switch
        {
            var m when m.Contains("gpt-3.5-turbo-16k") => new TokenLimits(16384, 4096),
            var m when m.Contains("gpt-3.5-turbo") => new TokenLimits(4096, 4096),
            var m when m.Contains("gpt-4-32k") => new TokenLimits(32768, 32768),
            var m when m.Contains("gpt-4-turbo") => new TokenLimits(128000, 4096),
            var m when m.Contains("gpt-4") => new TokenLimits(8192, 8192),
            var m when m.Contains("claude-3") => new TokenLimits(200000, 4096),
            _ => new TokenLimits(4096, 4096)
        };
    }
}

public class TokenPricing
{
    public decimal InputPricePerThousand { get; }
    public decimal OutputPricePerThousand { get; }
    
    public TokenPricing(decimal inputPrice, decimal outputPrice)
    {
        InputPricePerThousand = inputPrice;
        OutputPricePerThousand = outputPrice;
    }
}

public class TokenLimits
{
    public int MaxTokens { get; }
    public int MaxOutputTokens { get; }
    
    public TokenLimits(int maxTokens, int maxOutputTokens)
    {
        MaxTokens = maxTokens;
        MaxOutputTokens = maxOutputTokens;
    }
}

public class TokenEstimate
{
    public int InputTokens { get; set; }
    public int OutputTokens { get; set; }
    public int TotalTokens { get; set; }
    public decimal EstimatedCost { get; set; }
    public string Model { get; set; } = string.Empty;
}

public class Message
{
    public string Role { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public List<object>? Attachments { get; set; }
}