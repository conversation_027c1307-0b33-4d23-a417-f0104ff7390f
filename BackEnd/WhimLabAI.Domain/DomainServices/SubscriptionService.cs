using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.DomainServices;

public class SubscriptionService
{
    public Money CalculateUpgradePrice(
        Subscription currentSubscription,
        SubscriptionPlan newPlan,
        bool immediateUpgrade = true)
    {
        if (!currentSubscription.IsValid())
            throw new InvalidOperationException("当前订阅无效");
            
        if (newPlan.Tier <= currentSubscription.Plan.Tier)
            throw new InvalidOperationException("只能升级到更高级别的套餐");
            
        if (immediateUpgrade)
        {
            // Calculate prorated price
            var remainingDays = currentSubscription.GetDaysRemaining();
            var totalDays = currentSubscription.EndDate.HasValue 
                ? (currentSubscription.EndDate.Value - currentSubscription.StartDate).Days 
                : 30; // Default to 30 days for monthly billing
            
            if (remainingDays <= 0)
                return newPlan.Price;
                
            // Credit for unused portion of current subscription
            var unusedPercentage = (decimal)remainingDays / totalDays;
            var credit = currentSubscription.PaidAmount.Multiply(unusedPercentage);
            
            // Cost for remaining period on new plan
            var newCost = newPlan.Price.Multiply(unusedPercentage);
            
            // Upgrade price = new cost - credit
            var upgradePrice = newCost.Subtract(credit);
            
            // Ensure upgrade price is not negative
            return upgradePrice.IsNegative() ? Money.Zero(newPlan.Price.Currency) : upgradePrice;
        }
        else
        {
            // Upgrade at next billing cycle
            return newPlan.Price;
        }
    }
    
    public bool CheckQuotaAvailable(Subscription subscription, int requiredTokens)
    {
        if (!subscription.IsValid())
            return false;
            
        return subscription.TokenQuota.CanUse(requiredTokens);
    }
    
    public SubscriptionRecommendation RecommendPlan(
        int averageMonthlyTokens,
        int peakDailyTokens,
        bool needCustomAgents,
        bool needPlugins,
        bool needKnowledgeBase)
    {
        var recommendation = new SubscriptionRecommendation();
        
        // Estimate required monthly tokens with 20% buffer
        var requiredTokens = (int)(averageMonthlyTokens * 1.2);
        
        // Check Free tier
        if (requiredTokens <= 10000 && !needCustomAgents && !needPlugins && !needKnowledgeBase)
        {
            recommendation.RecommendedTier = SubscriptionTier.Free;
            recommendation.Reason = "免费版足以满足您的基本需求";
        }
        // Check Basic tier
        else if (requiredTokens <= 100000 && needCustomAgents && !needPlugins && !needKnowledgeBase)
        {
            recommendation.RecommendedTier = SubscriptionTier.Basic;
            recommendation.Reason = "基础版提供足够的Token配额和自定义智能体功能";
        }
        // Check Pro tier
        else if (requiredTokens <= 1000000 && (needPlugins || needKnowledgeBase))
        {
            recommendation.RecommendedTier = SubscriptionTier.Pro;
            recommendation.Reason = "专业版提供完整的功能和充足的Token配额";
        }
        // Ultra tier
        else
        {
            recommendation.RecommendedTier = SubscriptionTier.Ultra;
            recommendation.Reason = "旗舰版提供无限Token和所有高级功能";
        }
        
        recommendation.EstimatedMonthlyCost = GetEstimatedMonthlyCost(recommendation.RecommendedTier);
        recommendation.Features = GetTierFeatures(recommendation.RecommendedTier);
        
        return recommendation;
    }
    
    public bool CanDowngrade(Subscription currentSubscription, SubscriptionPlan newPlan)
    {
        // Can't downgrade if new plan's quota is less than already used tokens
        if (newPlan.TokenQuota != -1 && currentSubscription.TokenQuota.Used > newPlan.TokenQuota)
            return false;
            
        // Can only downgrade at the end of billing period
        return currentSubscription.GetDaysRemaining() <= 0;
    }
    
    public DateTime CalculateNextBillingDate(WhimLabAI.Shared.Enums.BillingCycle billingCycle, DateTime startDate)
    {
        return billingCycle switch
        {
            WhimLabAI.Shared.Enums.BillingCycle.Monthly => startDate.AddMonths(1),
            WhimLabAI.Shared.Enums.BillingCycle.Quarterly => startDate.AddMonths(3),
            WhimLabAI.Shared.Enums.BillingCycle.SemiAnnual => startDate.AddMonths(6),
            WhimLabAI.Shared.Enums.BillingCycle.Annual => startDate.AddYears(1),
            _ => startDate.AddMonths(1)
        };
    }
    
    private decimal GetEstimatedMonthlyCost(SubscriptionTier tier)
    {
        return tier switch
        {
            SubscriptionTier.Free => 0m,
            SubscriptionTier.Basic => 29.99m,
            SubscriptionTier.Pro => 99.99m,
            SubscriptionTier.Ultra => 299.99m,
            _ => 0m
        };
    }
    
    private List<string> GetTierFeatures(SubscriptionTier tier)
    {
        return tier switch
        {
            SubscriptionTier.Free => new List<string>
            {
                "10,000 Token/月",
                "使用预设智能体",
                "基本对话功能"
            },
            SubscriptionTier.Basic => new List<string>
            {
                "100,000 Token/月",
                "创建自定义智能体",
                "对话历史管理",
                "导出对话记录"
            },
            SubscriptionTier.Pro => new List<string>
            {
                "1,000,000 Token/月",
                "所有Basic功能",
                "使用插件系统",
                "知识库功能",
                "高级模型配置",
                "优先技术支持"
            },
            SubscriptionTier.Ultra => new List<string>
            {
                "无限Token",
                "所有Pro功能",
                "企业级SLA",
                "专属客户经理",
                "API访问权限",
                "定制开发支持"
            },
            _ => new List<string>()
        };
    }
}

public class SubscriptionRecommendation
{
    public SubscriptionTier RecommendedTier { get; set; }
    public string Reason { get; set; } = string.Empty;
    public decimal EstimatedMonthlyCost { get; set; }
    public List<string> Features { get; set; } = new();
}