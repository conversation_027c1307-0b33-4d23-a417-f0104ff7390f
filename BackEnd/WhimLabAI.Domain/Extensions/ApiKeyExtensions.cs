using System;
using WhimLabAI.Domain.Entities.ApiKey;

namespace WhimLabAI.Domain.Extensions;

/// <summary>
/// API密钥扩展方法
/// </summary>
public static class ApiKeyExtensions
{
    /// <summary>
    /// 创建带有增强安全性的客户用户API密钥
    /// </summary>
    public static (ApiKey apiKey, string rawKey) CreateSecureForCustomerUser(
        string name,
        Guid customerUserId,
        string keyType = "Development",
        DateTime? expiresAt = null)
    {
        var (apiKey, rawKey) = ApiKey.CreateForCustomerUser(name, customerUserId, keyType, expiresAt);
        
        // 注意：由于ApiKey的属性是私有set，我们暂时使用标准的SHA256
        // 在实际部署时，应该通过Infrastructure层的服务来处理PBKDF2加密
        
        return (apiKey, rawKey);
    }
    
    /// <summary>
    /// 创建带有增强安全性的管理员用户API密钥
    /// </summary>
    public static (ApiKey apiKey, string rawKey) CreateSecureForAdminUser(
        string name,
        Guid adminUserId,
        string keyType = "Development",
        DateTime? expiresAt = null)
    {
        var (apiKey, rawKey) = ApiKey.CreateForAdminUser(name, adminUserId, keyType, expiresAt);
        
        // 注意：由于ApiKey的属性是私有set，我们暂时使用标准的SHA256
        // 在实际部署时，应该通过Infrastructure层的服务来处理PBKDF2加密
        
        return (apiKey, rawKey);
    }
    
    /// <summary>
    /// 创建带有增强安全性的系统API密钥
    /// </summary>
    public static (ApiKey apiKey, string rawKey) CreateSecureForSystem(
        string name,
        string keyType = "Production",
        DateTime? expiresAt = null)
    {
        var (apiKey, rawKey) = ApiKey.CreateForSystem(name, keyType, expiresAt);
        
        // 注意：由于ApiKey的属性是私有set，我们暂时使用标准的SHA256
        // 在实际部署时，应该通过Infrastructure层的服务来处理PBKDF2加密
        
        return (apiKey, rawKey);
    }
}