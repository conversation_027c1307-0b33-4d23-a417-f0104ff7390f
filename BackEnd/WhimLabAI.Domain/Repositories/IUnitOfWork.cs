using System.Data;

namespace WhimLabAI.Domain.Repositories;

public interface IUnitOfWork : IDisposable
{
    Task<IDbTransaction> BeginTransactionAsync(IsolationLevel isolationLevel = IsolationLevel.ReadCommitted, CancellationToken cancellationToken = default);
    Task CommitAsync(CancellationToken cancellationToken = default);
    Task RollbackAsync(CancellationToken cancellationToken = default);
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    
    IRepository<T> Repository<T>() where T : Common.Entity;
    
    // Specific repository properties
    ICustomerUserRepository CustomerUsers { get; }
    IAdminUserRepository AdminUsers { get; }
    IAgentRepository Agents { get; }
    IConversationRepository Conversations { get; }
    ISubscriptionRepository Subscriptions { get; }
    ISubscriptionPlanRepository SubscriptionPlans { get; }
    IUsageRecordRepository UsageRecords { get; }
    IOrderRepository Orders { get; }
    IPaymentRepository Payments { get; }
    IRefundRepository Refunds { get; }
    IRefundRecordRepository RefundRecords { get; }
    IRoleRepository Roles { get; }
    IPermissionRepository Permissions { get; }
    ITokenUsageRepository TokenUsages { get; }
    IDifyConversationMappingRepository DifyConversationMappings { get; }
    IKnowledgeBaseRepository KnowledgeBases { get; }
    IDocumentRepository Documents { get; }
    IDocumentChunkRepository DocumentChunks { get; }
    IAuditLogRepository AuditLogs { get; }
    INotificationRepository Notifications { get; }
    ISystemEventRepository SystemEvents { get; }
    ICustomerSessionRepository CustomerSessions { get; }
    IAdminSessionRepository AdminSessions { get; }
    IFileRepository Files { get; }
    IVerificationCodeRepository VerificationCodes { get; }
    IAgentLikeRepository AgentLikes { get; }
    IAgentCategoryRepository AgentCategories { get; }
    IAgentRatingRepository AgentRatings { get; }
    IRatingHelpfulnessRepository RatingHelpfulnesses { get; }
    IInvoiceRepository Invoices { get; }
    ICouponRepository Coupons { get; }
    IQRCodeSessionRepository QRCodeSessions { get; }
}