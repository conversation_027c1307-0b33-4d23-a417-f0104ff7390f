using WhimLabAI.Domain.Entities.System;

namespace WhimLabAI.Domain.Repositories;

public interface ICustomerSessionRepository : IRepository<CustomerUserSession>
{
    Task<CustomerUserSession?> GetByTokenAsync(string token, CancellationToken cancellationToken = default);
    Task<IEnumerable<CustomerUserSession>> GetActiveSessionsAsync(Guid customerUserId, CancellationToken cancellationToken = default);
    Task<IEnumerable<CustomerUserSession>> GetExpiredSessionsAsync(int days, CancellationToken cancellationToken = default);
    Task<bool> InvalidateSessionAsync(string token, CancellationToken cancellationToken = default);
    Task<bool> InvalidateAllCustomerSessionsAsync(Guid customerUserId, CancellationToken cancellationToken = default);
}