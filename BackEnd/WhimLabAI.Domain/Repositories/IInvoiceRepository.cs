using WhimLabAI.Domain.Entities.Finance;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 发票仓储接口
/// </summary>
public interface IInvoiceRepository : IRepository<Invoice>
{
    /// <summary>
    /// 根据发票号获取发票
    /// </summary>
    Task<Invoice?> GetByInvoiceNumberAsync(string invoiceNumber, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的发票列表
    /// </summary>
    Task<List<Invoice>> GetByCustomerUserIdAsync(Guid customerUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取订阅相关的发票
    /// </summary>
    Task<List<Invoice>> GetBySubscriptionIdAsync(Guid subscriptionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取订单相关的发票
    /// </summary>
    Task<Invoice?> GetByOrderIdAsync(Guid orderId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取逾期发票
    /// </summary>
    Task<List<Invoice>> GetOverdueInvoicesAsync(DateTime currentDate, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 生成新的发票号
    /// </summary>
    Task<string> GenerateInvoiceNumberAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户在指定时间范围内的发票
    /// </summary>
    Task<List<Invoice>> GetByCustomerUserIdAndDateRangeAsync(
        Guid customerUserId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查发票号是否已存在
    /// </summary>
    Task<bool> InvoiceNumberExistsAsync(string invoiceNumber, CancellationToken cancellationToken = default);
}