using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.Notification;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 通知仓储接口
/// </summary>
public interface INotificationRepository : IRepository<Notification>
{
    /// <summary>
    /// 获取用户未读通知数量
    /// </summary>
    Task<int> GetUnreadCountAsync(Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量标记为已读
    /// </summary>
    Task<int> MarkAsReadAsync(Guid userId, List<Guid> notificationIds, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 标记用户所有通知为已读
    /// </summary>
    Task<int> MarkAllAsReadAsync(Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量软删除
    /// </summary>
    Task<int> SoftDeleteAsync(Guid userId, List<Guid> notificationIds, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 清理过期通知
    /// </summary>
    Task<int> CleanupExpiredAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取旧的已读通知
    /// </summary>
    Task<IEnumerable<Notification>> GetOldReadNotificationsAsync(int days, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户通知（支持分页和筛选）
    /// </summary>
    Task<(List<Notification> Items, int TotalCount)> GetUserNotificationsAsync(
        Guid userId,
        string? type = null,
        string? level = null,
        bool? isRead = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        string? keyword = null,
        int pageIndex = 0,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
}