using WhimLabAI.Domain.Entities.User;
using System.Linq;

namespace WhimLabAI.Domain.Repositories;

public interface ICustomerUserRepository : IRepository<CustomerUser>
{
    new IQueryable<CustomerUser> GetQueryable();
    Task<CustomerUser?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);
    Task<CustomerUser?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<CustomerUser?> GetByPhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default);
    Task<bool> IsUsernameExistsAsync(string username, CancellationToken cancellationToken = default);
    Task<bool> IsEmailExistsAsync(string email, CancellationToken cancellationToken = default);
    Task<bool> IsPhoneNumberExistsAsync(string phoneNumber, CancellationToken cancellationToken = default);
    Task<CustomerUser?> GetWithLoginHistoryAsync(Guid userId, int historyCount = 10, CancellationToken cancellationToken = default);
    Task<CustomerUser?> GetWithOAuthBindingsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<CustomerUser?> GetByOAuthBindingAsync(string provider, string providerUserId, CancellationToken cancellationToken = default);
    Task<CustomerUser?> GetWithDevicesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<CustomerUser>> GetActiveUsersAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据ID列表批量获取用户
    /// </summary>
    Task<List<CustomerUser>> GetByIdsAsync(List<Guid> userIds, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据刷新令牌获取用户
    /// </summary>
    Task<CustomerUser?> GetByRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
}