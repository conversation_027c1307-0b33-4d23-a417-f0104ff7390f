using WhimLabAI.Domain.Entities.Payment;

namespace WhimLabAI.Domain.Repositories;

public interface IOrderRepository : IRepository<Order>
{
    Task<Order?> GetByOrderNoAsync(string orderNo, CancellationToken cancellationToken = default);
    Task<IEnumerable<Order>> GetUserOrdersAsync(Guid userId, string? status = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<Order>> GetPendingOrdersAsync(DateTime expiryTime, CancellationToken cancellationToken = default);
    Task<decimal> GetUserTotalPaymentAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> UserOwnsOrderAsync(Guid userId, Guid orderId, CancellationToken cancellationToken = default);
    Task<string> GenerateOrderNoAsync(CancellationToken cancellationToken = default);
    Task<bool> HasPendingRenewalOrderAsync(Guid userId, Guid planId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Order>> GetExpiredPendingOrdersAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Order>> GetOldCancelledOrdersAsync(int days, CancellationToken cancellationToken = default);
}