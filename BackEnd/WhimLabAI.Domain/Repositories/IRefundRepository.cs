using WhimLabAI.Domain.Entities.Payment;

namespace WhimLabAI.Domain.Repositories;

public interface IRefundRepository : IRepository<RefundRecord>
{
    Task<RefundRecord?> GetByRefundNoAsync(string refundNo, CancellationToken cancellationToken = default);
    Task<IEnumerable<RefundRecord>> GetOrderRefundsAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<IEnumerable<RefundRecord>> GetPendingRefundsAsync(CancellationToken cancellationToken = default);
    Task<decimal> GetOrderRefundedAmountAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<bool> UpdateRefundStatusAsync(Guid refundId, WhimLabAI.Shared.Enums.RefundStatus status, string? refundTransactionId = null, string? failureReason = null, CancellationToken cancellationToken = default);
    Task<bool> CanRefundOrderAsync(Guid orderId, decimal refundAmount, CancellationToken cancellationToken = default);
}
