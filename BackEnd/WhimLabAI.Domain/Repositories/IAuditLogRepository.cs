using WhimLabAI.Domain.Entities.Audit;
using WhimLabAI.Domain.Specifications;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 审计日志仓储接口
/// </summary>
public interface IAuditLogRepository : IRepository<AuditLog>
{
    /// <summary>
    /// 获取用户活动摘要
    /// </summary>
    Task<List<UserActivitySummary>> GetUserActivitySummaryAsync(
        DateTime startDate,
        DateTime endDate,
        int topCount,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取审计日志统计
    /// </summary>
    Task<AuditLogStats> GetStatsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除旧的审计日志
    /// </summary>
    Task<int> DeleteOldLogsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据规格查询获取列表
    /// </summary>
    Task<List<AuditLog>> GetListAsync(
        Specification<AuditLog> specification,
        int pageIndex,
        int pageSize,
        string orderBy,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据规格查询获取数量
    /// </summary>
    Task<int> CountAsync(Specification<AuditLog> specification, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取旧的非敏感日志
    /// </summary>
    /// <param name="daysOld">天数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>旧的非敏感日志列表</returns>
    Task<IEnumerable<AuditLog>> GetOldNonSensitiveLogsAsync(int daysOld, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量插入审计日志
    /// </summary>
    Task BulkInsertAsync(IEnumerable<AuditLog> auditLogs, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取高风险操作日志
    /// </summary>
    Task<List<AuditLog>> GetHighRiskOperationsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取失败操作统计
    /// </summary>
    Task<Dictionary<string, int>> GetFailureStatsByModuleAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户操作时间分布
    /// </summary>
    Task<Dictionary<int, int>> GetUserActivityTimeDistributionAsync(
        Guid userId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 用户活动摘要
/// </summary>
public class UserActivitySummary
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public int ActionCount { get; set; }
    public DateTime LastActivityTime { get; set; }
}

/// <summary>
/// 审计日志统计
/// </summary>
public class AuditLogStats
{
    public int TotalActions { get; set; }
    public int UniqueUsers { get; set; }
    public Dictionary<string, int> TopActions { get; set; } = new();
    public Dictionary<DateTime, int> DailyActivityCounts { get; set; } = new();
}