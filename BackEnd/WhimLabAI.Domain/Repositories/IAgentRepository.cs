using WhimLabAI.Domain.Entities.Agent;

namespace WhimLabAI.Domain.Repositories;

public interface IAgentRepository : IRepository<Agent>
{
    Task<IEnumerable<Agent>> GetPublishedAgentsAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    Task<IEnumerable<Agent>> GetAgentsByStatusAsync(string status, CancellationToken cancellationToken = default);
    Task<IEnumerable<Agent>> GetAgentsByCategoryAsync(string category, CancellationToken cancellationToken = default);
    Task<IEnumerable<Agent>> GetAgentsByCreatorAsync(Guid creatorId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AgentVersion>> GetAgentVersionsAsync(Guid agentId, CancellationToken cancellationToken = default);
    Task<Agent?> GetAgentByUniqueKeyAsync(string uniqueKey, CancellationToken cancellationToken = default);
    Task<bool> IsUniqueKeyAvailableAsync(string uniqueKey, Guid? excludeAgentId = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据分类统计Agent数量
    /// </summary>
    Task<int> CountByCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取热门标签
    /// </summary>
    Task<List<(string Tag, int Count)>> GetPopularTagsAsync(int count, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据评价ID获取Agent
    /// </summary>
    Task<Agent?> GetAgentByRatingIdAsync(Guid ratingId, CancellationToken cancellationToken = default);
}