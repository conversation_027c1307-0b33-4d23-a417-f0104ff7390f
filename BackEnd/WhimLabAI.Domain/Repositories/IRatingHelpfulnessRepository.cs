using WhimLabAI.Domain.Entities.Agent;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 评价有用性标记仓储接口
/// </summary>
public interface IRatingHelpfulnessRepository : IRepository<RatingHelpfulness>
{
    /// <summary>
    /// 根据评价和用户获取标记
    /// </summary>
    Task<RatingHelpfulness?> GetByRatingAndUserAsync(Guid ratingId, Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量获取用户对评价的标记
    /// </summary>
    Task<Dictionary<Guid, bool?>> GetUserMarksForRatingsAsync(Guid userId, List<Guid> ratingIds, CancellationToken cancellationToken = default);
}