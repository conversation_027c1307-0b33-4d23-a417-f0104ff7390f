using WhimLabAI.Domain.Entities.Conversation;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// Dify对话映射仓储接口
/// </summary>
public interface IDifyConversationMappingRepository
{
    /// <summary>
    /// 根据系统对话ID获取Dify对话映射
    /// </summary>
    Task<DifyConversationMapping?> GetByConversationIdAsync(Guid conversationId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据Dify对话ID获取映射
    /// </summary>
    Task<DifyConversationMapping?> GetByDifyConversationIdAsync(string difyConversationId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Agent的所有对话映射
    /// </summary>
    Task<IEnumerable<DifyConversationMapping>> GetByAgentIdAsync(Guid agentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查对话是否已有映射
    /// </summary>
    Task<bool> ExistsAsync(Guid conversationId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 添加映射
    /// </summary>
    Task AddAsync(DifyConversationMapping mapping, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新映射
    /// </summary>
    Task UpdateAsync(DifyConversationMapping mapping, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除映射
    /// </summary>
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}