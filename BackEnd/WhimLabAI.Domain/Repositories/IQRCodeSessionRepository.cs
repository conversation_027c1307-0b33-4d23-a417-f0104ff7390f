using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Domain.Repositories;

public interface IQRCodeSessionRepository : IRepository<QRCodeSession>
{
    Task<QRCodeSession?> GetBySessionIdAsync(string sessionId, CancellationToken cancellationToken = default);
    Task<QRCodeSession?> GetActiveSessionBySessionIdAsync(string sessionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<QRCodeSession>> GetExpiredSessionsAsync(CancellationToken cancellationToken = default);
    Task<int> DeleteExpiredSessionsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<QRCodeSession>> GetUserSessionsAsync(Guid userId, int limit = 10, CancellationToken cancellationToken = default);
}