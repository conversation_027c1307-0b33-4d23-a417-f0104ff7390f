using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.KnowledgeBase;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 知识库仓储接口
/// </summary>
public interface IKnowledgeBaseRepository : IRepository<KnowledgeBase>
{
    /// <summary>
    /// 根据所有者获取知识库列表
    /// </summary>
    Task<IEnumerable<KnowledgeBase>> GetByOwnerAsync(
        Guid ownerId, 
        OwnerType ownerType,
        bool includeDeleted = false,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据名称和所有者查找知识库
    /// </summary>
    Task<KnowledgeBase?> GetByNameAndOwnerAsync(
        string name,
        Guid ownerId,
        OwnerType ownerType,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取公开的知识库列表
    /// </summary>
    Task<IEnumerable<KnowledgeBase>> GetPublicKnowledgeBasesAsync(
        int pageIndex,
        int pageSize,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 搜索知识库
    /// </summary>
    Task<(IEnumerable<KnowledgeBase> Items, int TotalCount)> SearchAsync(
        string? keyword,
        Guid? ownerId,
        OwnerType? ownerType,
        VectorDatabaseType? vectorDbType,
        KnowledgeBaseStatus? status,
        bool? isPublic,
        int pageIndex,
        int pageSize,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取知识库统计信息
    /// </summary>
    Task<Dictionary<string, object>> GetStatisticsAsync(
        Guid knowledgeBaseId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量更新知识库状态
    /// </summary>
    Task<int> UpdateStatusBatchAsync(
        IEnumerable<Guid> knowledgeBaseIds,
        KnowledgeBaseStatus status,
        CancellationToken cancellationToken = default);
}