using WhimLabAI.Domain.Entities.Auth;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 角色仓储接口
/// </summary>
public interface IRoleRepository : IRepository<Role>
{
    /// <summary>
    /// 根据角色代码获取角色
    /// </summary>
    Task<Role?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取角色（包含权限信息）
    /// </summary>
    Task<Role?> GetWithPermissionsAsync(Guid roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量获取角色（包含权限信息）
    /// </summary>
    Task<IReadOnlyList<Role>> GetByIdsWithPermissionsAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取所有启用的角色
    /// </summary>
    Task<IReadOnlyList<Role>> GetEnabledRolesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取所有角色（包含权限信息）
    /// </summary>
    Task<IReadOnlyList<Role>> GetAllWithPermissionsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查角色代码是否存在
    /// </summary>
    Task<bool> ExistsAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查角色名称是否存在
    /// </summary>
    Task<bool> NameExistsAsync(string name, Guid? excludeId = null, CancellationToken cancellationToken = default);
}