using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.Support;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 支持工单仓储接口
/// </summary>
public interface ISupportTicketRepository : IRepository<SupportTicket>
{
    /// <summary>
    /// 根据工单编号获取工单
    /// </summary>
    Task<SupportTicket?> GetByTicketNumberAsync(string ticketNumber);

    /// <summary>
    /// 获取用户的工单列表
    /// </summary>
    Task<List<SupportTicket>> GetByCustomerIdAsync(Guid customerId, TicketStatus? status = null);

    /// <summary>
    /// 获取分配给客服的工单列表
    /// </summary>
    Task<List<SupportTicket>> GetByAssignedToIdAsync(Guid assignedToId, TicketStatus? status = null);

    /// <summary>
    /// 获取未分配的工单列表
    /// </summary>
    Task<List<SupportTicket>> GetUnassignedTicketsAsync();

    /// <summary>
    /// 获取工单统计信息
    /// </summary>
    Task<TicketStatistics> GetStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 搜索工单
    /// </summary>
    Task<(List<SupportTicket> Items, int TotalCount)> SearchAsync(
        string? keyword,
        TicketStatus? status,
        TicketCategory? category,
        TicketPriority? priority,
        Guid? customerId,
        Guid? assignedToId,
        int page,
        int pageSize);
}

/// <summary>
/// 工单统计信息
/// </summary>
public class TicketStatistics
{
    public int TotalTickets { get; set; }
    public int OpenTickets { get; set; }
    public int InProgressTickets { get; set; }
    public int ResolvedTickets { get; set; }
    public int ClosedTickets { get; set; }
    public double AverageResolutionTime { get; set; }
    public double AverageSatisfactionRating { get; set; }
    public Dictionary<TicketCategory, int> TicketsByCategory { get; set; } = new();
    public Dictionary<TicketPriority, int> TicketsByPriority { get; set; } = new();
}