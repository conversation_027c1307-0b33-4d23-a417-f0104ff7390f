using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Domain.Repositories;

public interface ITokenUsageRepository
{
    Task<TokenUsage?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<TokenUsage> RecordUsageAsync(Guid subscriptionId, Guid? conversationId, int tokensUsed, string? model, UsageType usageType, CancellationToken cancellationToken = default);
    Task<IEnumerable<TokenUsage>> GetUsageByDateRangeAsync(Guid subscriptionId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<Dictionary<DateTime, int>> GetDailyUsageAsync(Guid subscriptionId, int days, CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetUsageByModelAsync(Guid subscriptionId, DateTime? startDate = null, CancellationToken cancellationToken = default);
    Task<int> GetTotalUsageAsync(Guid subscriptionId, DateTime? startDate = null, CancellationToken cancellationToken = default);
    Task<UsageStatistics> GetUsageStatisticsAsync(Guid subscriptionId, CancellationToken cancellationToken = default);
    Task<bool> DeleteOldRecordsAsync(int daysToKeep, CancellationToken cancellationToken = default);
    Task<IEnumerable<TopUserUsage>> GetTopUsersAsync(DateTime startDate, DateTime endDate, int topCount = 10, CancellationToken cancellationToken = default);
    Task AddAsync(TokenUsage usage, CancellationToken cancellationToken = default);
    Task UpdateAsync(TokenUsage usage, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

public class UsageStatistics
{
    public int TotalTokens { get; set; }
    public int TotalRequests { get; set; }
    public double AverageTokensPerRequest { get; set; }
    public Dictionary<string, int> TokensByModel { get; set; } = new();
    public Dictionary<UsageType, int> TokensByType { get; set; } = new();
    public DateTime? FirstUsageDate { get; set; }
    public DateTime? LastUsageDate { get; set; }
}

public class TopUserUsage
{
    public Guid UserId { get; set; }
    public string? UserName { get; set; }
    public int TotalTokens { get; set; }
    public int RequestCount { get; set; }
}