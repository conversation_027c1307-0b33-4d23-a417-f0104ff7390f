using WhimLabAI.Domain.Entities.Auth;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 权限仓储接口
/// </summary>
public interface IPermissionRepository : IRepository<Permission>
{
    /// <summary>
    /// 根据权限代码获取权限
    /// </summary>
    Task<Permission?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量获取权限
    /// </summary>
    Task<IReadOnlyList<Permission>> GetByIdsAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据权限代码批量获取权限
    /// </summary>
    Task<IReadOnlyList<Permission>> GetByCodesAsync(IEnumerable<string> codes, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取所有权限（带层级结构）
    /// </summary>
    Task<IReadOnlyList<Permission>> GetAllWithHierarchyAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 按分类获取权限
    /// </summary>
    Task<IReadOnlyList<Permission>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查权限代码是否存在
    /// </summary>
    Task<bool> ExistsAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取所有启用的权限
    /// </summary>
    Task<IReadOnlyList<Permission>> GetEnabledPermissionsAsync(CancellationToken cancellationToken = default);
}