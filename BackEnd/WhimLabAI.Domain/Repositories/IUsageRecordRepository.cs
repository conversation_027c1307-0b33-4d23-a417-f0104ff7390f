using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Domain.Repositories;

public interface IUsageRecordRepository : IRepository<UsageRecord>
{
    Task<int> GetUserMonthlyUsageAsync(Guid userId, DateTime month, CancellationToken cancellationToken = default);
    Task<IEnumerable<UsageRecord>> GetUserUsageHistoryAsync(Guid userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<int> GetConversationTokenUsageAsync(Guid conversationId, CancellationToken cancellationToken = default);
    Task RecordUsageAsync(Guid subscriptionId, Guid conversationId, int tokens, string model, CancellationToken cancellationToken = default);
}
