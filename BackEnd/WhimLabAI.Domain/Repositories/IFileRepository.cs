using WhimLabAI.Domain.Entities.Storage;

namespace WhimLabAI.Domain.Repositories;

public interface IFileRepository : IRepository<FileEntity>
{
    Task<FileEntity?> GetByFileKeyAsync(string fileKey, CancellationToken cancellationToken = default);
    Task<IEnumerable<FileEntity>> GetUserFilesAsync(Guid userId, string? fileType = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<FileEntity>> GetExpiredTemporaryFilesAsync(int hours, CancellationToken cancellationToken = default);
    Task<long> GetUserStorageUsageAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> FileExistsAsync(string fileKey, CancellationToken cancellationToken = default);
    Task<int> DeleteExpiredFilesAsync(CancellationToken cancellationToken = default);
}