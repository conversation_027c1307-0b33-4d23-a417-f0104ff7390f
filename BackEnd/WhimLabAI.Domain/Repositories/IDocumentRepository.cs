using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.KnowledgeBase;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 文档仓储接口
/// </summary>
public interface IDocumentRepository : IRepository<Document>
{
    /// <summary>
    /// 根据知识库ID获取文档列表
    /// </summary>
    Task<IEnumerable<Document>> GetByKnowledgeBaseIdAsync(
        Guid knowledgeBaseId,
        bool includeDeleted = false,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据文件哈希查找文档
    /// </summary>
    Task<Document?> GetByFileHashAsync(
        string fileHash,
        Guid knowledgeBaseId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取待处理的文档
    /// </summary>
    Task<IEnumerable<Document>> GetPendingDocumentsAsync(
        int batchSize,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 搜索文档
    /// </summary>
    Task<(IEnumerable<Document> Items, int TotalCount)> SearchAsync(
        Guid knowledgeBaseId,
        string? keyword,
        DocumentType? documentType,
        DocumentStatus? status,
        DateTime? startDate,
        DateTime? endDate,
        int pageIndex,
        int pageSize,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取文档统计信息
    /// </summary>
    Task<Dictionary<string, object>> GetStatisticsAsync(
        Guid knowledgeBaseId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量删除文档
    /// </summary>
    Task<int> DeleteBatchAsync(
        IEnumerable<Guid> documentIds,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取文档块
    /// </summary>
    Task<IEnumerable<DocumentChunk>> GetDocumentChunksAsync(
        Guid documentId,
        CancellationToken cancellationToken = default);
}