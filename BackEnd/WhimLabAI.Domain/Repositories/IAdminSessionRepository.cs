using WhimLabAI.Domain.Entities.System;
using System.Linq.Expressions;

namespace WhimLabAI.Domain.Repositories;

public interface IAdminSessionRepository : IRepository<AdminUserSession>
{
    Task<AdminUserSession?> GetByTokenAsync(string token, CancellationToken cancellationToken = default);
    Task<IEnumerable<AdminUserSession>> GetActiveSessionsAsync(Guid adminUserId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AdminUserSession>> GetExpiredSessionsAsync(int days, CancellationToken cancellationToken = default);
    Task<bool> InvalidateSessionAsync(string token, CancellationToken cancellationToken = default);
    Task<bool> InvalidateAllAdminSessionsAsync(Guid adminUserId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AdminUserSession>> GetSessionsRequiringMfaAsync(CancellationToken cancellationToken = default);
    
    // 登录历史查询
    Task<(IEnumerable<AdminUserSession> items, int totalCount)> GetLoginHistoryAsync(
        Expression<Func<AdminUserSession, bool>>? predicate,
        int pageNumber,
        int pageSize,
        string sortBy,
        bool isDescending,
        CancellationToken cancellationToken = default);
    
    Task<Dictionary<string, int>> GetLoginLocationStatisticsAsync(Guid adminUserId, int days = 30, CancellationToken cancellationToken = default);
    Task<bool> InvalidateSessionByIdAsync(Guid sessionId, CancellationToken cancellationToken = default);
    Task<bool> InvalidateAllAdminSessionsExceptAsync(Guid adminUserId, Guid exceptSessionId, CancellationToken cancellationToken = default);
}