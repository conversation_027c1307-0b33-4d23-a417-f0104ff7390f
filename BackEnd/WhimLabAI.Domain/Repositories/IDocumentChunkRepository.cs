using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.KnowledgeBase;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 文档块仓储接口
/// </summary>
public interface IDocumentChunkRepository : IRepository<DocumentChunk>
{
    /// <summary>
    /// 根据文档ID获取块列表
    /// </summary>
    Task<IEnumerable<DocumentChunk>> GetByDocumentIdAsync(
        Guid documentId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据知识库ID获取块列表
    /// </summary>
    Task<IEnumerable<DocumentChunk>> GetByKnowledgeBaseIdAsync(
        Guid knowledgeBaseId,
        int skip,
        int take,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取未嵌入的块
    /// </summary>
    Task<IEnumerable<DocumentChunk>> GetUnembeddedChunksAsync(
        Guid knowledgeBaseId,
        int batchSize,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据向量ID获取块
    /// </summary>
    Task<DocumentChunk?> GetByVectorIdAsync(
        string vectorId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量获取块
    /// </summary>
    Task<IEnumerable<DocumentChunk>> GetByVectorIdsAsync(
        IEnumerable<string> vectorIds,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量更新嵌入状态
    /// </summary>
    Task<int> UpdateEmbeddingStatusBatchAsync(
        IEnumerable<(Guid ChunkId, string VectorId)> updates,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除文档的所有块
    /// </summary>
    Task<int> DeleteByDocumentIdAsync(
        Guid documentId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据内容哈希查找重复块
    /// </summary>
    Task<IEnumerable<DocumentChunk>> FindDuplicatesByHashAsync(
        IEnumerable<string> contentHashes,
        Guid knowledgeBaseId,
        CancellationToken cancellationToken = default);
}