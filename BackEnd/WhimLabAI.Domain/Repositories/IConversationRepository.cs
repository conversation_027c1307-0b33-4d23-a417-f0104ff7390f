using WhimLabAI.Domain.Entities.Conversation;

namespace WhimLabAI.Domain.Repositories;

public interface IConversationRepository : IRepository<Conversation>
{
    Task<IEnumerable<Conversation>> GetUserConversationsAsync(Guid userId, bool includeArchived = false, CancellationToken cancellationToken = default);
    Task<IEnumerable<ConversationMessage>> GetConversationMessagesAsync(Guid conversationId, int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    Task<ConversationMessage?> GetLastMessageAsync(Guid conversationId, CancellationToken cancellationToken = default);
    Task<int> GetUserConversationCountAsync(Guid userId, Guid? agentId = null, CancellationToken cancellationToken = default);
    Task<int> GetUserTokenUsageAsync(Guid userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<bool> UserOwnsConversationAsync(Guid userId, Guid conversationId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Conversation>> GetOldConversationsAsync(int days, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查用户是否使用过指定的Agent
    /// </summary>
    Task<bool> HasUserUsedAgentAsync(Guid userId, Guid agentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户置顶对话的数量
    /// </summary>
    Task<int> GetPinnedConversationCountAsync(Guid userId, CancellationToken cancellationToken = default);
    
    // Advanced search functionality
    Task<(IEnumerable<Conversation> conversations, int totalCount)> SearchConversationsAsync(
        Guid userId, 
        string? keyword = null,
        List<Guid>? agentIds = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        bool includeArchived = false,
        bool onlyPinned = false,
        bool searchInContent = false,
        string sortBy = "lastMessage",
        bool sortDescending = true,
        int skip = 0,
        int take = 20,
        CancellationToken cancellationToken = default);
    
    // Statistics functionality
    Task<Dictionary<string, object>> GetConversationStatisticsAsync(Guid conversationId, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> GetUserStatisticsAsync(Guid userId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default);
    
    // Cleanup functionality
    Task<IEnumerable<Conversation>> GetConversationsForCleanupAsync(
        int daysOld,
        bool excludePinned = true,
        bool excludeWithAttachments = true,
        int? limit = null,
        CancellationToken cancellationToken = default);
    
    Task<int> GetUserStorageUsageAsync(Guid userId, CancellationToken cancellationToken = default);
    
    // Deleted conversations tracking
    Task<IEnumerable<Conversation>> GetDeletedConversationsAsync(Guid userId, DateTime? since = null, CancellationToken cancellationToken = default);
    
    // Batch operations
    Task<Dictionary<Guid, bool>> BatchUpdateMetadataAsync(List<Guid> conversationIds, string key, object value, CancellationToken cancellationToken = default);
    Task<int> BatchArchiveAsync(List<Guid> conversationIds, Guid userId, CancellationToken cancellationToken = default);
    Task<int> BatchDeleteAsync(List<Guid> conversationIds, Guid userId, CancellationToken cancellationToken = default);
}