using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.Support;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 支持类别仓储接口
/// </summary>
public interface ISupportCategoryRepository : IRepository<SupportCategory>
{
    /// <summary>
    /// 获取所有激活的类别
    /// </summary>
    Task<List<SupportCategory>> GetActiveAsync(CategoryType? type = null);

    /// <summary>
    /// 获取类别树
    /// </summary>
    Task<List<SupportCategory>> GetTreeAsync(CategoryType? type = null);

    /// <summary>
    /// 获取子类别
    /// </summary>
    Task<List<SupportCategory>> GetChildrenAsync(Guid parentId);

    /// <summary>
    /// 检查类别名称是否存在
    /// </summary>
    Task<bool> ExistsAsync(string name, CategoryType type, Guid? excludeId = null);
}