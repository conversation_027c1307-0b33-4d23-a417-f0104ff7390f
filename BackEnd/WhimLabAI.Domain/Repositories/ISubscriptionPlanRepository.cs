using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Domain.Repositories;

public interface ISubscriptionPlanRepository : IRepository<SubscriptionPlan>
{
    Task<SubscriptionPlan?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<SubscriptionPlan>> GetActivePlansAsync(CancellationToken cancellationToken = default);
    Task<SubscriptionPlan?> GetDefaultPlanAsync(CancellationToken cancellationToken = default);
}
