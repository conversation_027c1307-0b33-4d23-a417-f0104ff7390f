using WhimLabAI.Domain.Entities.System;

namespace WhimLabAI.Domain.Repositories;

public interface IVerificationCodeRepository : IRepository<VerificationCode>
{
    Task<VerificationCode?> GetLatestCodeAsync(string recipient, string type, CancellationToken cancellationToken = default);
    Task<bool> VerifyCodeAsync(string recipient, string type, string code, CancellationToken cancellationToken = default);
    Task<IEnumerable<VerificationCode>> GetExpiredCodesAsync(CancellationToken cancellationToken = default);
    Task<int> GetRecentCodeCountAsync(string recipient, TimeSpan timeWindow, CancellationToken cancellationToken = default);
    Task<bool> InvalidateCodesAsync(string recipient, string type, CancellationToken cancellationToken = default);
}