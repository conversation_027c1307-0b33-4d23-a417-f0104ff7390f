using WhimLabAI.Domain.Entities.System;

namespace WhimLabAI.Domain.Repositories;

public interface ISystemEventRepository : IRepository<SystemEvent>
{
    Task<IEnumerable<SystemEvent>> GetRecentEventsAsync(string eventType, int count = 10, CancellationToken cancellationToken = default);
    Task<IEnumerable<SystemEvent>> GetEventsByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}