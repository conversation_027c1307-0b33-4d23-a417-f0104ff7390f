using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Domain.Repositories;

public interface IRecoveryCodeRepository : IRepository<RecoveryCode>
{
    /// <summary>
    /// Get all valid recovery codes for a user
    /// </summary>
    Task<IEnumerable<RecoveryCode>> GetValidCodesAsync(Guid userId, string userType, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get a recovery code by code value
    /// </summary>
    Task<RecoveryCode?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Delete all recovery codes for a user
    /// </summary>
    Task DeleteUserCodesAsync(Guid userId, string userType, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Count valid recovery codes for a user
    /// </summary>
    Task<int> CountValidCodesAsync(Guid userId, string userType, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Check if a recovery code exists and is valid
    /// </summary>
    Task<bool> IsCodeValidAsync(string code, Guid userId, string userType, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get all recovery codes for a user (including used ones)
    /// </summary>
    Task<IEnumerable<RecoveryCode>> GetAllUserCodesAsync(Guid userId, string userType, CancellationToken cancellationToken = default);
}