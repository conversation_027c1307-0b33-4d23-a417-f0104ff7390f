using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.EventSourcing;

namespace WhimLabAI.Domain.Repositories;

public interface IEventStoreRepository
{
    Task SaveEventAsync(EventStore @event, CancellationToken cancellationToken = default);
    Task SaveEventsAsync(IEnumerable<EventStore> events, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<EventStore>> GetEventsAsync(Guid aggregateId, int? fromVersion = null, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<EventStore>> GetEventsAsync(Guid aggregateId, DateTime from, DateTime to, CancellationToken cancellationToken = default);
    Task<Snapshot?> GetLatestSnapshotAsync(Guid aggregateId, CancellationToken cancellationToken = default);
    Task SaveSnapshotAsync(Snapshot snapshot, CancellationToken cancellationToken = default);
    Task<int> GetLatestVersionAsync(Guid aggregateId, CancellationToken cancellationToken = default);
}