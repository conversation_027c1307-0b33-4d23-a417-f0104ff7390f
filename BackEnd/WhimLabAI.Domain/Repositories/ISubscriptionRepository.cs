using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Domain.Repositories;

public interface ISubscriptionRepository : IRepository<Subscription>
{
    Task<Subscription?> GetActiveSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Subscription?> GetActiveSubscriptionByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Subscription>> GetExpiringSubscriptionsAsync(int days, CancellationToken cancellationToken = default);
    Task<IEnumerable<Subscription>> GetExpiredSubscriptionsAsync(CancellationToken cancellationToken = default);
    Task<bool> HasActiveSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> ConsumeTokensAsync(Guid subscriptionId, int tokens, CancellationToken cancellationToken = default);
    Task<int> GetRemainingTokensAsync(Guid subscriptionId, CancellationToken cancellationToken = default);
    Task<bool> ResetMonthlyTokensAsync(Guid subscriptionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Subscription>> GetActiveSubscriptionsAsync(CancellationToken cancellationToken = default);
}
