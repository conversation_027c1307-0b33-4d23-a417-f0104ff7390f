using WhimLabAI.Domain.Entities.Agent;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// Agent评价仓储接口
/// </summary>
public interface IAgentRatingRepository : IRepository<AgentRating>
{
    /// <summary>
    /// 根据Agent和用户获取评价
    /// </summary>
    Task<AgentRating?> GetByAgentAndUserAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Agent的评价列表
    /// </summary>
    Task<List<AgentRating>> GetByAgentIdAsync(Guid agentId, int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Agent的评价数量
    /// </summary>
    Task<int> CountByAgentIdAsync(Guid agentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的评价列表
    /// </summary>
    Task<List<AgentRating>> GetByUserIdAsync(Guid userId, int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 计算Agent的平均评分
    /// </summary>
    Task<double> CalculateAverageRatingAsync(Guid agentId, CancellationToken cancellationToken = default);
}