using WhimLabAI.Domain.Entities.Agent;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// Agent分类仓储接口
/// </summary>
public interface IAgentCategoryRepository : IRepository<AgentCategory>
{
    /// <summary>
    /// 根据名称获取分类
    /// </summary>
    Task<AgentCategory?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取所有启用的分类
    /// </summary>
    Task<List<AgentCategory>> GetEnabledCategoriesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查分类名称是否存在
    /// </summary>
    Task<bool> IsNameExistsAsync(string name, Guid? excludeId = null, CancellationToken cancellationToken = default);
}