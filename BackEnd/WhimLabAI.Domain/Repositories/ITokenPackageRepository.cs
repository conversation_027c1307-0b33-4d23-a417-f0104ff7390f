using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// Token包仓储接口
/// </summary>
public interface ITokenPackageRepository : IRepository<TokenPackage>
{
    /// <summary>
    /// 获取所有可用的Token包
    /// </summary>
    Task<List<TokenPackage>> GetAvailablePackagesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的购买数量
    /// </summary>
    Task<int> GetUserPurchaseCountAsync(Guid userId, Guid packageId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 按排序获取活动的Token包
    /// </summary>
    Task<List<TokenPackage>> GetActivePackagesOrderedAsync(CancellationToken cancellationToken = default);
}