using WhimLabAI.Domain.Entities.Agent;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// Agent点赞仓储接口
/// </summary>
public interface IAgentLikeRepository : IRepository<AgentLike>
{
    /// <summary>
    /// 根据Agent和用户获取点赞记录
    /// </summary>
    Task<AgentLike?> GetByAgentAndUserAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取Agent的点赞用户ID列表
    /// </summary>
    Task<List<Guid>> GetLikedUserIdsAsync(Guid agentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户点赞的Agent ID列表
    /// </summary>
    Task<List<Guid>> GetUserLikedAgentIdsAsync(Guid userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量检查用户是否点赞了指定的Agents
    /// </summary>
    Task<Dictionary<Guid, bool>> CheckUserLikesAsync(Guid userId, List<Guid> agentIds, CancellationToken cancellationToken = default);
}