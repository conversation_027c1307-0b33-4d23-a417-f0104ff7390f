using WhimLabAI.Domain.Entities.Payment;

namespace WhimLabAI.Domain.Repositories;

public interface ICouponRepository : IRepository<Coupon>
{
    Task<Coupon?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<IEnumerable<Coupon>> GetActiveAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Coupon>> GetByUserAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<int> GetUserUsageCountAsync(Guid couponId, Guid userId, CancellationToken cancellationToken = default);
    Task<bool> IsCodeExistsAsync(string code, CancellationToken cancellationToken = default);
}