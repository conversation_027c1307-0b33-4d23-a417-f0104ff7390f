using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Domain.Repositories;

public interface IAdminUserRepository : IRepository<AdminUser>
{
    /// <summary>
    /// 获取可查询对象
    /// </summary>
    new IQueryable<AdminUser> GetQueryable();
    
    Task<AdminUser?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);
    Task<AdminUser?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<AdminUser?> GetByPhoneAsync(string phone, CancellationToken cancellationToken = default);
    Task<bool> IsUsernameExistsAsync(string username, CancellationToken cancellationToken = default);
    Task<bool> IsEmailExistsAsync(string email, CancellationToken cancellationToken = default);
    Task<AdminUser?> GetWithRolesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<AdminUser>> GetActiveUsersAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    Task<List<WhimLabAI.Domain.Entities.Auth.Permission>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> HasPermissionAsync(Guid userId, string permissionCode, CancellationToken cancellationToken = default);
}