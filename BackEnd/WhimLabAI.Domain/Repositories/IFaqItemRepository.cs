using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.Support;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// FAQ仓储接口
/// </summary>
public interface IFaqItemRepository : IRepository<FaqItem>
{
    /// <summary>
    /// 获取已发布的FAQ列表
    /// </summary>
    Task<List<FaqItem>> GetPublishedAsync(string? category = null);

    /// <summary>
    /// 按类别获取FAQ
    /// </summary>
    Task<Dictionary<string, List<FaqItem>>> GetGroupedByCategoryAsync();

    /// <summary>
    /// 获取热门FAQ
    /// </summary>
    Task<List<FaqItem>> GetPopularAsync(int count = 10);

    /// <summary>
    /// 搜索FAQ
    /// </summary>
    Task<List<FaqItem>> SearchAsync(string keyword);

    /// <summary>
    /// 获取相关FAQ
    /// </summary>
    Task<List<FaqItem>> GetRelatedAsync(Guid faqId, int count = 5);

    /// <summary>
    /// 获取所有类别
    /// </summary>
    Task<List<string>> GetCategoriesAsync();
}