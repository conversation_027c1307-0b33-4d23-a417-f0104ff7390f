using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Repositories;

public interface IPaymentRepository : IRepository<PaymentTransaction>
{
    Task<PaymentTransaction?> GetByTransactionIdAsync(string transactionId, CancellationToken cancellationToken = default);
    Task<PaymentTransaction?> GetByPaymentNoAsync(string paymentNo, CancellationToken cancellationToken = default);
    Task<PaymentTransaction?> GetByOrderIdAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<IEnumerable<PaymentTransaction>> GetOrderPaymentsAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<IEnumerable<PaymentTransaction>> GetPendingPaymentsAsync(TimeSpan timeout, CancellationToken cancellationToken = default);
    Task<bool> UpdatePaymentStatusAsync(Guid paymentId, WhimLabAI.Shared.Enums.TransactionStatus newStatus, string? transactionId = null, CancellationToken cancellationToken = default);
    Task<object> GetPaymentStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<bool> HasDuplicatePaymentAsync(Guid orderId, string paymentMethod, decimal amount, TimeSpan window, CancellationToken cancellationToken = default);
    Task<decimal> GetUserTotalPaymentsAsync(Guid userId, WhimLabAI.Shared.Enums.TransactionStatus? status = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<PaymentTransaction>> GetPaymentsByDateRangeAsync(DateTime startDate, DateTime endDate, PaymentMethod? method = null, CancellationToken cancellationToken = default);
    Task<PaymentTransaction?> GetLatestPaymentForOrderAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<bool> SavePaymentCallbackAsync(Guid paymentId, string callbackData, CancellationToken cancellationToken = default);
    Task<IEnumerable<PaymentTransaction>> GetExpiredPendingPaymentsAsync(CancellationToken cancellationToken = default);
}