using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class AdminUserRegisteredEvent : DomainEvent
{
    public string Username { get; }
    public string? Email { get; }
    public string? Phone { get; }
    public string[] Roles { get; }
    public DateTime RegisteredAt { get; }
    
    public AdminUserRegisteredEvent(
        Guid adminUserId,
        string username,
        string? email,
        string? phone,
        string[] roles) : base(adminUserId)
    {
        Username = username;
        Email = email;
        Phone = phone;
        Roles = roles;
        RegisteredAt = OccurredOn;
    }
}