using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class ConversationStartedEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public Guid AgentId { get; }
    public DateTime StartedAt { get; }
    
    public ConversationStartedEvent(
        Guid conversationId,
        Guid customerUserId,
        Guid agentId) : base(conversationId)
    {
        CustomerUserId = customerUserId;
        AgentId = agentId;
        StartedAt = OccurredOn;
    }
}