using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;

namespace WhimLabAI.Domain.DomainEvents;

public class OrderPaidEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public Money PaidAmount { get; }
    public DateTime PaidAt { get; }
    
    public OrderPaidEvent(
        Guid orderId,
        Guid customerUserId,
        Money paidAmount) : base(orderId)
    {
        CustomerUserId = customerUserId;
        PaidAmount = paidAmount;
        PaidAt = OccurredOn;
    }
}