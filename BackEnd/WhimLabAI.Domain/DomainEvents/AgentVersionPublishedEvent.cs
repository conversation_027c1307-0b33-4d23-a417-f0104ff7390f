using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

/// <summary>
/// AI代理版本发布事件
/// </summary>
public class AgentVersionPublishedEvent : DomainEvent
{
    public Guid VersionId { get; private set; }
    public int VersionNumber { get; private set; }
    public string PublishedBy { get; private set; }
    
    public AgentVersionPublishedEvent(
        Guid agentId,
        Guid versionId,
        int versionNumber,
        string publishedBy) : base(agentId)
    {
        VersionId = versionId;
        VersionNumber = versionNumber;
        PublishedBy = publishedBy;
    }
}