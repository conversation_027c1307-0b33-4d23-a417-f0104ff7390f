using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class CustomerUserRegisteredEvent : DomainEvent
{
    public string Username { get; }
    public string? Email { get; }
    public string? Phone { get; }
    public DateTime RegisteredAt { get; }
    
    public CustomerUserRegisteredEvent(
        Guid customerUserId,
        string username,
        string? email,
        string? phone) : base(customerUserId)
    {
        Username = username;
        Email = email;
        Phone = phone;
        RegisteredAt = OccurredOn;
    }
}