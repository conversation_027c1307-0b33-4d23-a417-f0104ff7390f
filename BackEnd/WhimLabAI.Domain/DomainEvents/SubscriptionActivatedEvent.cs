using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class SubscriptionActivatedEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public Guid PlanId { get; }
    public DateTime ActivatedAt { get; }
    
    public SubscriptionActivatedEvent(
        Guid subscriptionId,
        Guid customerUserId,
        Guid planId) : base(subscriptionId)
    {
        CustomerUserId = customerUserId;
        PlanId = planId;
        ActivatedAt = OccurredOn;
    }
}