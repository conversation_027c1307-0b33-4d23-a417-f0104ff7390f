using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class AgentPublishedEvent : DomainEvent
{
    public string AgentName { get; }
    public Guid CreatorId { get; }
    public DateTime PublishedAt { get; }
    
    public AgentPublishedEvent(
        Guid agentId,
        string agentName,
        Guid creatorId) : base(agentId)
    {
        AgentName = agentName;
        CreatorId = creatorId;
        PublishedAt = OccurredOn;
    }
}