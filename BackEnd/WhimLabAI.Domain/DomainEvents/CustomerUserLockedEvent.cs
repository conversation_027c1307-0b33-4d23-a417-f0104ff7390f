using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class CustomerUserLockedEvent : DomainEvent
{
    public string Username { get; }
    public DateTime LockedUntil { get; }
    
    public CustomerUserLockedEvent(Guid customerUserId, string username, DateTime lockedUntil) : base(customerUserId)
    {
        Username = username;
        LockedUntil = lockedUntil;
    }
}