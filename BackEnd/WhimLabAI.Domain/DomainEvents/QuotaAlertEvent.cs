using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class QuotaAlertEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public double UsagePercentage { get; }
    public DateTime AlertedAt { get; }
    
    public QuotaAlertEvent(
        Guid subscriptionId,
        Guid customerUserId,
        double usagePercentage) : base(subscriptionId)
    {
        CustomerUserId = customerUserId;
        UsagePercentage = usagePercentage;
        AlertedAt = OccurredOn;
    }
}