using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class CustomerUserLoggedInEvent : DomainEvent
{
    public string Username { get; }
    public string IpAddress { get; }
    public DateTime LoginAt { get; }
    
    public CustomerUserLoggedInEvent(
        Guid customerUserId,
        string username,
        string ipAddress) : base(customerUserId)
    {
        Username = username;
        IpAddress = ipAddress;
        LoginAt = OccurredOn;
    }
}