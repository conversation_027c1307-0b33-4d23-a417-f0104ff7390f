using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

/// <summary>
/// 角色创建事件
/// </summary>
public class RoleCreatedEvent : DomainEvent
{
    public Guid RoleId { get; }
    public string Code { get; }
    public string Name { get; }
    
    public RoleCreatedEvent(Guid roleId, string code, string name) : base(roleId)
    {
        RoleId = roleId;
        Code = code;
        Name = name;
    }
}

/// <summary>
/// 权限分配给角色事件
/// </summary>
public class PermissionAssignedToRoleEvent : DomainEvent
{
    public Guid RoleId { get; }
    public Guid PermissionId { get; }
    
    public PermissionAssignedToRoleEvent(Guid roleId, Guid permissionId) : base(roleId)
    {
        RoleId = roleId;
        PermissionId = permissionId;
    }
}

/// <summary>
/// 从角色移除权限事件
/// </summary>
public class PermissionRemovedFromRoleEvent : DomainEvent
{
    public Guid RoleId { get; }
    public Guid PermissionId { get; }
    
    public PermissionRemovedFromRoleEvent(Guid roleId, Guid permissionId) : base(roleId)
    {
        RoleId = roleId;
        PermissionId = permissionId;
    }
}

/// <summary>
/// 角色分配给管理员用户事件
/// </summary>
public class RoleAssignedToAdminUserEvent : DomainEvent
{
    public Guid AdminUserId { get; }
    public Guid RoleId { get; }
    public Guid? AssignedBy { get; }
    
    public RoleAssignedToAdminUserEvent(Guid adminUserId, Guid roleId, Guid? assignedBy = null) : base(adminUserId)
    {
        AdminUserId = adminUserId;
        RoleId = roleId;
        AssignedBy = assignedBy;
    }
}

/// <summary>
/// 从管理员用户移除角色事件
/// </summary>
public class RoleRemovedFromAdminUserEvent : DomainEvent
{
    public Guid AdminUserId { get; }
    public Guid RoleId { get; }
    
    public RoleRemovedFromAdminUserEvent(Guid adminUserId, Guid roleId) : base(adminUserId)
    {
        AdminUserId = adminUserId;
        RoleId = roleId;
    }
}