using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class AdminUserLoggedInEvent : DomainEvent
{
    public string Username { get; }
    public string IpAddress { get; }
    public DateTime LoginAt { get; }
    public bool RequiredMfa { get; }
    
    public AdminUserLoggedInEvent(
        Guid adminUserId,
        string username,
        string ipAddress,
        bool requiredMfa = false) : base(adminUserId)
    {
        Username = username;
        IpAddress = ipAddress;
        RequiredMfa = requiredMfa;
        LoginAt = OccurredOn;
    }
}