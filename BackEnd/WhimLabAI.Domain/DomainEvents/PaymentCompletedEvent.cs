using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;

namespace WhimLabAI.Domain.DomainEvents;

public class PaymentCompletedEvent : DomainEvent
{
    public Guid OrderId { get; }
    public Guid CustomerUserId { get; }
    public Money Amount { get; }
    public string PaymentMethod { get; }
    public string TransactionId { get; }
    public DateTime CompletedAt { get; }
    
    public PaymentCompletedEvent(
        Guid paymentId,
        Guid orderId,
        Guid customerUserId,
        Money amount,
        string paymentMethod,
        string transactionId) : base(paymentId)
    {
        OrderId = orderId;
        CustomerUserId = customerUserId;
        Amount = amount;
        PaymentMethod = paymentMethod;
        TransactionId = transactionId;
        CompletedAt = OccurredOn;
    }
}