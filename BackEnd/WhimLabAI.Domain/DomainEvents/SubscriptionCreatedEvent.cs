using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;

namespace WhimLabAI.Domain.DomainEvents;

public class SubscriptionCreatedEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public Guid PlanId { get; }
    public string PlanName { get; }
    public DateTime StartDate { get; }
    public DateTime? EndDate { get; }
    public TokenQuota TokenQuota { get; }
    public Money Price { get; }
    
    public SubscriptionCreatedEvent(
        Guid subscriptionId,
        Guid customerUserId,
        Guid planId,
        string planName,
        DateTime startDate,
        DateTime? endDate,
        TokenQuota tokenQuota,
        Money price) : base(subscriptionId)
    {
        CustomerUserId = customerUserId;
        PlanId = planId;
        PlanName = planName;
        StartDate = startDate;
        EndDate = endDate;
        TokenQuota = tokenQuota;
        Price = price;
    }
}