using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class QuotaExhaustedEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public DateTime ExhaustedAt { get; }
    
    public QuotaExhaustedEvent(
        Guid subscriptionId,
        Guid customerUserId) : base(subscriptionId)
    {
        CustomerUserId = customerUserId;
        ExhaustedAt = OccurredOn;
    }
}