using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

public class UserRegisteredEvent : DomainEvent
{
    public string UserType { get; }
    public string Username { get; }
    public string? Email { get; }
    public string? Phone { get; }
    public DateTime RegisteredAt { get; }
    public string? ReferralSource { get; }
    
    public UserRegisteredEvent(
        Guid userId,
        string userType,
        string username,
        string? email,
        string? phone,
        string? referralSource = null) : base(userId)
    {
        UserType = userType;
        Username = username;
        Email = email;
        Phone = phone;
        ReferralSource = referralSource;
        RegisteredAt = OccurredOn;
    }
}