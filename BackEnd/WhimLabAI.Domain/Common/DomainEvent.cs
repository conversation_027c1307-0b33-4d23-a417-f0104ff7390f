namespace WhimLabAI.Domain.Common;

public abstract class DomainEvent : IDomainEvent
{
    public DateTime OccurredOn { get; }
    public Guid AggregateId { get; }
    public string EventType { get; }
    public Guid EventId { get; }
    public int Version { get; set; }
    
    protected DomainEvent(Guid aggregateId)
    {
        EventId = Guid.NewGuid();
        OccurredOn = DateTime.UtcNow;
        AggregateId = aggregateId;
        EventType = GetType().Name;
        Version = 1;
    }
}