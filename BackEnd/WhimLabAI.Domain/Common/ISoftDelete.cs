namespace WhimLabAI.Domain.Common;

/// <summary>
/// 软删除接口
/// </summary>
public interface ISoftDelete
{
    /// <summary>
    /// 是否已删除
    /// </summary>
    bool IsDeleted { get; }
    
    /// <summary>
    /// 删除时间
    /// </summary>
    DateTime? DeletedAt { get; }
    
    /// <summary>
    /// 删除
    /// </summary>
    void Delete();
    
    /// <summary>
    /// 恢复
    /// </summary>
    void Restore();
}