using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.EventSourcing;

/// <summary>
/// 聚合根快照
/// </summary>
public class Snapshot : Entity
{
    public Guid AggregateId { get; private set; }
    public string AggregateType { get; private set; }
    public string SnapshotData { get; private set; }
    public int Version { get; private set; }
    public DateTime CreatedAt { get; private set; }

    protected Snapshot() { }

    public Snapshot(
        Guid aggregateId,
        string aggregateType,
        string snapshotData,
        int version)
    {
        AggregateId = aggregateId;
        AggregateType = aggregateType;
        SnapshotData = snapshotData;
        Version = version;
        CreatedAt = DateTime.UtcNow;
    }
}