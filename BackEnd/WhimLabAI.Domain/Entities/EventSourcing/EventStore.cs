using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.EventSourcing;

/// <summary>
/// 事件存储实体
/// </summary>
public class EventStore : Entity
{
    public Guid AggregateId { get; private set; }
    public string AggregateType { get; private set; }
    public string EventType { get; private set; }
    public string EventData { get; private set; }
    public string? Metadata { get; private set; }
    public int Version { get; private set; }
    public DateTime OccurredOn { get; private set; }
    public string? UserId { get; private set; }
    public string? CorrelationId { get; private set; }

    protected EventStore() { }

    public EventStore(
        Guid aggregateId,
        string aggregateType,
        string eventType,
        string eventData,
        int version,
        DateTime occurredOn,
        string? userId = null,
        string? correlationId = null,
        string? metadata = null)
    {
        AggregateId = aggregateId;
        AggregateType = aggregateType;
        EventType = eventType;
        EventData = eventData;
        Version = version;
        OccurredOn = occurredOn;
        UserId = userId;
        CorrelationId = correlationId;
        Metadata = metadata;
    }
}