using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Support;

/// <summary>
/// 工单消息实体
/// </summary>
public class TicketMessage : Entity
{
    /// <summary>
    /// 工单ID
    /// </summary>
    public Guid TicketId { get; private set; }

    /// <summary>
    /// 工单
    /// </summary>
    public SupportTicket Ticket { get; private set; } = null!;

    /// <summary>
    /// 发送者ID
    /// </summary>
    public Guid SenderId { get; private set; }

    /// <summary>
    /// 发送者类型
    /// </summary>
    public SenderType SenderType { get; private set; }

    /// <summary>
    /// 发送者名称
    /// </summary>
    public string SenderName { get; private set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; private set; } = string.Empty;

    /// <summary>
    /// 是否为内部备注
    /// </summary>
    public bool IsInternal { get; private set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime SentAt { get; private set; }

    /// <summary>
    /// 是否已读
    /// </summary>
    public bool IsRead { get; private set; }

    /// <summary>
    /// 读取时间
    /// </summary>
    public DateTime? ReadAt { get; private set; }

    private TicketMessage() { }

    public static TicketMessage Create(
        Guid ticketId,
        Guid senderId,
        SenderType senderType,
        string senderName,
        string content,
        bool isInternal = false)
    {
        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        if (string.IsNullOrWhiteSpace(senderName))
            throw new ArgumentException("Sender name cannot be empty", nameof(senderName));

        return new TicketMessage
        {
            Id = Guid.NewGuid(),
            TicketId = ticketId,
            SenderId = senderId,
            SenderType = senderType,
            SenderName = senderName,
            Content = content,
            IsInternal = isInternal,
            SentAt = DateTime.UtcNow,
            IsRead = false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    public void MarkAsRead()
    {
        if (!IsRead)
        {
            IsRead = true;
            ReadAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}

/// <summary>
/// 发送者类型
/// </summary>
public enum SenderType
{
    /// <summary>
    /// 客户
    /// </summary>
    Customer,
    
    /// <summary>
    /// 客服
    /// </summary>
    Support,
    
    /// <summary>
    /// 系统
    /// </summary>
    System
}