using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Support;

/// <summary>
/// 工单附件实体
/// </summary>
public class TicketAttachment : Entity
{
    /// <summary>
    /// 工单ID
    /// </summary>
    public Guid TicketId { get; private set; }

    /// <summary>
    /// 工单
    /// </summary>
    public SupportTicket Ticket { get; private set; } = null!;

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; private set; } = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; private set; }

    /// <summary>
    /// 文件类型
    /// </summary>
    public string ContentType { get; private set; } = string.Empty;

    /// <summary>
    /// 存储路径
    /// </summary>
    public string StoragePath { get; private set; } = string.Empty;

    /// <summary>
    /// 上传者ID
    /// </summary>
    public Guid UploadedById { get; private set; }

    /// <summary>
    /// 上传时间
    /// </summary>
    public DateTime UploadedAt { get; private set; }

    private TicketAttachment() { }

    public static TicketAttachment Create(
        Guid ticketId,
        string fileName,
        long fileSize,
        string contentType,
        string storagePath,
        Guid uploadedById)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty", nameof(fileName));

        if (fileSize <= 0)
            throw new ArgumentException("File size must be greater than zero", nameof(fileSize));

        if (string.IsNullOrWhiteSpace(contentType))
            throw new ArgumentException("Content type cannot be empty", nameof(contentType));

        if (string.IsNullOrWhiteSpace(storagePath))
            throw new ArgumentException("Storage path cannot be empty", nameof(storagePath));

        return new TicketAttachment
        {
            Id = Guid.NewGuid(),
            TicketId = ticketId,
            FileName = fileName,
            FileSize = fileSize,
            ContentType = contentType,
            StoragePath = storagePath,
            UploadedById = uploadedById,
            UploadedAt = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }
}