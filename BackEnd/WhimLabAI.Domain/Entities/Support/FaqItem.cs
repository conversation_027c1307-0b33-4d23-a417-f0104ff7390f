using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Support;

/// <summary>
/// 常见问题实体
/// </summary>
public class FaqItem : AggregateRoot
{
    /// <summary>
    /// 问题
    /// </summary>
    public string Question { get; private set; } = string.Empty;

    /// <summary>
    /// 答案
    /// </summary>
    public string Answer { get; private set; } = string.Empty;

    /// <summary>
    /// 类别
    /// </summary>
    public string Category { get; private set; } = string.Empty;

    /// <summary>
    /// 标签
    /// </summary>
    private readonly List<string> _tags = new();
    public IReadOnlyList<string> Tags => _tags.AsReadOnly();

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int DisplayOrder { get; private set; }

    /// <summary>
    /// 是否发布
    /// </summary>
    public bool IsPublished { get; private set; }

    /// <summary>
    /// 浏览次数
    /// </summary>
    public int ViewCount { get; private set; }

    /// <summary>
    /// 有用次数
    /// </summary>
    public int HelpfulCount { get; private set; }

    /// <summary>
    /// 无用次数
    /// </summary>
    public int NotHelpfulCount { get; private set; }

    /// <summary>
    /// 最后更新者ID
    /// </summary>
    public Guid? LastUpdatedById { get; private set; }

    /// <summary>
    /// 发布时间
    /// </summary>
    public DateTime? PublishedAt { get; private set; }

    private FaqItem() { }

    public static FaqItem Create(
        string question,
        string answer,
        string category,
        int displayOrder = 0)
    {
        if (string.IsNullOrWhiteSpace(question))
            throw new ArgumentException("Question cannot be empty", nameof(question));

        if (string.IsNullOrWhiteSpace(answer))
            throw new ArgumentException("Answer cannot be empty", nameof(answer));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Category cannot be empty", nameof(category));

        return new FaqItem
        {
            Id = Guid.NewGuid(),
            Question = question,
            Answer = answer,
            Category = category,
            DisplayOrder = displayOrder,
            IsPublished = false,
            ViewCount = 0,
            HelpfulCount = 0,
            NotHelpfulCount = 0,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    public void Update(string question, string answer, string category, int displayOrder, Guid updatedById)
    {
        Question = question ?? throw new ArgumentNullException(nameof(question));
        Answer = answer ?? throw new ArgumentNullException(nameof(answer));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        DisplayOrder = displayOrder;
        LastUpdatedById = updatedById;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Publish()
    {
        if (!IsPublished)
        {
            IsPublished = true;
            PublishedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public void Unpublish()
    {
        if (IsPublished)
        {
            IsPublished = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public void IncrementViewCount()
    {
        ViewCount++;
    }

    public void MarkAsHelpful()
    {
        HelpfulCount++;
        UpdatedAt = DateTime.UtcNow;
    }

    public void MarkAsNotHelpful()
    {
        NotHelpfulCount++;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddTags(params string[] tags)
    {
        foreach (var tag in tags.Where(t => !string.IsNullOrWhiteSpace(t)))
        {
            if (!_tags.Contains(tag, StringComparer.OrdinalIgnoreCase))
                _tags.Add(tag);
        }
        UpdatedAt = DateTime.UtcNow;
    }

    public void RemoveTag(string tag)
    {
        _tags.RemoveAll(t => t.Equals(tag, StringComparison.OrdinalIgnoreCase));
        UpdatedAt = DateTime.UtcNow;
    }

    public void ClearTags()
    {
        _tags.Clear();
        UpdatedAt = DateTime.UtcNow;
    }
}