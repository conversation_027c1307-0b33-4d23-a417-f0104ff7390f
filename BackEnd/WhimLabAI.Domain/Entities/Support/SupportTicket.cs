using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Domain.Entities.Support;

/// <summary>
/// 支持工单实体
/// </summary>
public class SupportTicket : AggregateRoot
{
    /// <summary>
    /// 工单编号
    /// </summary>
    public string TicketNumber { get; private set; } = string.Empty;

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; private set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; private set; } = string.Empty;

    /// <summary>
    /// 类别
    /// </summary>
    public TicketCategory Category { get; private set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public TicketPriority Priority { get; private set; }

    /// <summary>
    /// 状态
    /// </summary>
    public TicketStatus Status { get; private set; }

    /// <summary>
    /// 创建用户ID
    /// </summary>
    public Guid CustomerId { get; private set; }

    /// <summary>
    /// 创建用户
    /// </summary>
    public CustomerUser? Customer { get; private set; }

    /// <summary>
    /// 分配的客服ID
    /// </summary>
    public Guid? AssignedToId { get; private set; }

    /// <summary>
    /// 分配的客服
    /// </summary>
    public AdminUser? AssignedTo { get; private set; }

    /// <summary>
    /// 解决时间
    /// </summary>
    public DateTime? ResolvedAt { get; private set; }

    /// <summary>
    /// 关闭时间
    /// </summary>
    public DateTime? ClosedAt { get; private set; }

    /// <summary>
    /// 用户满意度评分
    /// </summary>
    public int? SatisfactionRating { get; private set; }

    /// <summary>
    /// 用户反馈
    /// </summary>
    public string? Feedback { get; private set; }

    /// <summary>
    /// 标签
    /// </summary>
    private readonly List<string> _tags = new();
    public IReadOnlyList<string> Tags => _tags.AsReadOnly();

    /// <summary>
    /// 消息列表
    /// </summary>
    private readonly List<TicketMessage> _messages = new();
    public IReadOnlyList<TicketMessage> Messages => _messages.AsReadOnly();

    /// <summary>
    /// 附件列表
    /// </summary>
    private readonly List<TicketAttachment> _attachments = new();
    public IReadOnlyList<TicketAttachment> Attachments => _attachments.AsReadOnly();

    private SupportTicket() { }

    public static SupportTicket Create(
        string title,
        string description,
        TicketCategory category,
        TicketPriority priority,
        Guid customerId)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));
        
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));

        var ticket = new SupportTicket
        {
            Id = Guid.NewGuid(),
            TicketNumber = GenerateTicketNumber(),
            Title = title,
            Description = description,
            Category = category,
            Priority = priority,
            Status = TicketStatus.Open,
            CustomerId = customerId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        return ticket;
    }

    public void AssignTo(Guid adminUserId)
    {
        AssignedToId = adminUserId;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateStatus(TicketStatus newStatus)
    {
        if (Status == TicketStatus.Closed && newStatus != TicketStatus.Reopened)
            throw new InvalidOperationException("Cannot change status of a closed ticket");

        Status = newStatus;
        UpdatedAt = DateTime.UtcNow;

        if (newStatus == TicketStatus.Resolved)
            ResolvedAt = DateTime.UtcNow;
        else if (newStatus == TicketStatus.Closed)
            ClosedAt = DateTime.UtcNow;
    }

    public void AddMessage(TicketMessage message)
    {
        if (Status == TicketStatus.Closed)
            throw new InvalidOperationException("Cannot add message to a closed ticket");

        _messages.Add(message);
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddAttachment(TicketAttachment attachment)
    {
        if (Status == TicketStatus.Closed)
            throw new InvalidOperationException("Cannot add attachment to a closed ticket");

        _attachments.Add(attachment);
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddTags(params string[] tags)
    {
        foreach (var tag in tags.Where(t => !string.IsNullOrWhiteSpace(t)))
        {
            if (!_tags.Contains(tag, StringComparer.OrdinalIgnoreCase))
                _tags.Add(tag);
        }
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetSatisfactionRating(int rating, string? feedback = null)
    {
        if (rating < 1 || rating > 5)
            throw new ArgumentException("Rating must be between 1 and 5", nameof(rating));

        if (Status != TicketStatus.Resolved && Status != TicketStatus.Closed)
            throw new InvalidOperationException("Can only rate resolved or closed tickets");

        SatisfactionRating = rating;
        Feedback = feedback;
        UpdatedAt = DateTime.UtcNow;
    }

    private static string GenerateTicketNumber()
    {
        var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
        var random = new Random().Next(1000, 9999);
        return $"TKT-{timestamp}-{random}";
    }
}

/// <summary>
/// 工单类别
/// </summary>
public enum TicketCategory
{
    /// <summary>
    /// 技术问题
    /// </summary>
    Technical,
    
    /// <summary>
    /// 账户问题
    /// </summary>
    Account,
    
    /// <summary>
    /// 计费问题
    /// </summary>
    Billing,
    
    /// <summary>
    /// 功能请求
    /// </summary>
    FeatureRequest,
    
    /// <summary>
    /// Bug报告
    /// </summary>
    BugReport,
    
    /// <summary>
    /// 其他
    /// </summary>
    Other
}

/// <summary>
/// 工单优先级
/// </summary>
public enum TicketPriority
{
    /// <summary>
    /// 低
    /// </summary>
    Low,
    
    /// <summary>
    /// 中
    /// </summary>
    Medium,
    
    /// <summary>
    /// 高
    /// </summary>
    High,
    
    /// <summary>
    /// 紧急
    /// </summary>
    Urgent
}

/// <summary>
/// 工单状态
/// </summary>
public enum TicketStatus
{
    /// <summary>
    /// 开放
    /// </summary>
    Open,
    
    /// <summary>
    /// 处理中
    /// </summary>
    InProgress,
    
    /// <summary>
    /// 等待用户回复
    /// </summary>
    WaitingForCustomer,
    
    /// <summary>
    /// 已解决
    /// </summary>
    Resolved,
    
    /// <summary>
    /// 已关闭
    /// </summary>
    Closed,
    
    /// <summary>
    /// 重新开放
    /// </summary>
    Reopened
}