using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Support;

/// <summary>
/// 支持类别实体
/// </summary>
public class SupportCategory : AggregateRoot
{
    /// <summary>
    /// 类别名称
    /// </summary>
    public string Name { get; private set; } = string.Empty;

    /// <summary>
    /// 类别描述
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// 图标
    /// </summary>
    public string? Icon { get; private set; }

    /// <summary>
    /// 颜色
    /// </summary>
    public string? Color { get; private set; }

    /// <summary>
    /// 父类别ID
    /// </summary>
    public Guid? ParentId { get; private set; }

    /// <summary>
    /// 父类别
    /// </summary>
    public SupportCategory? Parent { get; private set; }

    /// <summary>
    /// 子类别
    /// </summary>
    private readonly List<SupportCategory> _children = new();
    public IReadOnlyList<SupportCategory> Children => _children.AsReadOnly();

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int DisplayOrder { get; private set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// 类别类型
    /// </summary>
    public CategoryType Type { get; private set; }

    private SupportCategory() { }

    public static SupportCategory Create(
        string name,
        CategoryType type,
        string? description = null,
        string? icon = null,
        string? color = null,
        Guid? parentId = null,
        int displayOrder = 0)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name cannot be empty", nameof(name));

        return new SupportCategory
        {
            Id = Guid.NewGuid(),
            Name = name,
            Type = type,
            Description = description,
            Icon = icon,
            Color = color,
            ParentId = parentId,
            DisplayOrder = displayOrder,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    public void Update(
        string name,
        string? description,
        string? icon,
        string? color,
        int displayOrder)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description;
        Icon = icon;
        Color = color;
        DisplayOrder = displayOrder;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        if (!IsActive)
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public void Deactivate()
    {
        if (IsActive)
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public void SetParent(Guid? parentId)
    {
        if (parentId == Id)
            throw new InvalidOperationException("Category cannot be its own parent");

        ParentId = parentId;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddChild(SupportCategory child)
    {
        if (child.Id == Id)
            throw new InvalidOperationException("Cannot add self as child");

        if (!_children.Any(c => c.Id == child.Id))
        {
            _children.Add(child);
            child.SetParent(Id);
        }
    }

    public void RemoveChild(SupportCategory child)
    {
        _children.RemoveAll(c => c.Id == child.Id);
        child.SetParent(null);
    }
}

/// <summary>
/// 类别类型
/// </summary>
public enum CategoryType
{
    /// <summary>
    /// 工单类别
    /// </summary>
    Ticket,
    
    /// <summary>
    /// FAQ类别
    /// </summary>
    Faq,
    
    /// <summary>
    /// 通用类别
    /// </summary>
    General
}