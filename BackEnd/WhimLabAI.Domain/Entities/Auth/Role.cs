using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.DomainEvents;

namespace WhimLabAI.Domain.Entities.Auth;

/// <summary>
/// 角色实体 - 权限的集合
/// </summary>
public class Role : AggregateRoot
{
    private readonly List<RolePermission> _permissions = new();

    /// <summary>
    /// 角色名称
    /// </summary>
    public string Name { get; private set; }

    /// <summary>
    /// 角色代码（唯一标识，如 super_admin）
    /// </summary>
    public string Code { get; private set; }

    /// <summary>
    /// 角色描述
    /// </summary>
    public string? Description { get; private set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; private set; }

    /// <summary>
    /// 是否为系统预设角色（不可删除）
    /// </summary>
    public bool IsSystem { get; private set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int DisplayOrder { get; private set; }

    public byte[] RowVersion { get; set; }  // 乐观并发控制

    /// <summary>
    /// 角色权限关联
    /// </summary>
    public IReadOnlyCollection<RolePermission> Permissions => _permissions.AsReadOnly();

    private Role() : base()
    {
        // Required for EF Core
    }

    public Role(
        string name,
        string code,
        string? description = null,
        bool isSystem = false) : base()
    {
        SetName(name);
        SetCode(code);
        Description = description;
        IsEnabled = true;
        IsSystem = isSystem;
        DisplayOrder = 0;

        RaiseDomainEvent(new RoleCreatedEvent(Id, code, name));
    }

    public void Update(
        string name,
        string? description,
        int displayOrder)
    {
        SetName(name);
        Description = description;
        DisplayOrder = displayOrder;
        UpdateTimestamp();
    }

    public void Enable()
    {
        IsEnabled = true;
        UpdateTimestamp();
    }

    public void Disable()
    {
        if (IsSystem)
            throw new InvalidOperationException("系统预设角色不能禁用");

        IsEnabled = false;
        UpdateTimestamp();
    }

    public void AssignPermission(Guid permissionId)
    {
        if (_permissions.Any(p => p.PermissionId == permissionId))
            return; // 权限已存在，忽略

        var rolePermission = new RolePermission(Id, permissionId);
        _permissions.Add(rolePermission);
        UpdateTimestamp();

        RaiseDomainEvent(new PermissionAssignedToRoleEvent(Id, permissionId));
    }

    public void RemovePermission(Guid permissionId)
    {
        var permission = _permissions.FirstOrDefault(p => p.PermissionId == permissionId);
        if (permission == null)
            return; // 权限不存在，忽略

        _permissions.Remove(permission);
        UpdateTimestamp();

        RaiseDomainEvent(new PermissionRemovedFromRoleEvent(Id, permissionId));
    }

    public void UpdatePermissions(IEnumerable<Guid> permissionIds)
    {
        // 移除不在新列表中的权限
        var toRemove = _permissions
            .Where(p => !permissionIds.Contains(p.PermissionId))
            .ToList();

        foreach (var permission in toRemove)
        {
            _permissions.Remove(permission);
        }

        // 添加新权限
        foreach (var permissionId in permissionIds)
        {
            if (!_permissions.Any(p => p.PermissionId == permissionId))
            {
                _permissions.Add(new RolePermission(Id, permissionId));
            }
        }

        UpdateTimestamp();
    }

    private void SetName(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentNullException(nameof(name), "角色名称不能为空");

        Name = name;
    }

    private void SetCode(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentNullException(nameof(code), "角色代码不能为空");

        if (!global::System.Text.RegularExpressions.Regex.IsMatch(code, @"^[a-z_]+$"))
            throw new ArgumentException("角色代码格式不正确，应为小写字母和下划线", nameof(code));

        Code = code;
    }
}

/// <summary>
/// 角色权限关联实体
/// </summary>
public class RolePermission : Entity
{
    public Guid RoleId { get; private set; }
    public Guid PermissionId { get; private set; }

    // 导航属性
    public Role Role { get; private set; } = null!;
    public Permission Permission { get; private set; } = null!;

    private RolePermission() : base()
    {
        // Required for EF Core
    }

    public RolePermission(Guid roleId, Guid permissionId) : base()
    {
        RoleId = roleId;
        PermissionId = permissionId;
    }
}
