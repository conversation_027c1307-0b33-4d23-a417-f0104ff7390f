using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.Payment;

public class PaymentTransaction : Entity
{
    public Guid OrderId { get; private set; }
    public string? TransactionId { get; private set; }
    public string? PaymentNo { get; private set; }
    public Money Amount { get; private set; }
    public PaymentMethod PaymentMethod { get; private set; }
    public TransactionStatus Status { get; private set; }
    public DateTime? ExpireAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public DateTime? FailedAt { get; private set; }
    public string? FailReason { get; private set; }
    public string? PayerAccount { get; private set; }
    public string? PayerName { get; private set; }
    public Dictionary<string, string> RawData { get; private set; }
    
    public Order Order { get; private set; } = null!;
    
    private PaymentTransaction() : base()
    {
        RawData = new Dictionary<string, string>();
    }
    
    public PaymentTransaction(
        Guid orderId,
        Money amount,
        PaymentMethod paymentMethod,
        string? transactionId = null,
        string? paymentNo = null,
        DateTime? expireAt = null) : base()
    {
        OrderId = orderId;
        Amount = amount ?? throw new ArgumentNullException(nameof(amount));
        PaymentMethod = paymentMethod;
        TransactionId = transactionId;
        PaymentNo = paymentNo;
        ExpireAt = expireAt;
        Status = TransactionStatus.Pending;
        RawData = new Dictionary<string, string>();
    }
    
    public void SetTransactionId(string transactionId)
    {
        TransactionId = transactionId;
        UpdateTimestamp();
    }
    
    public void SetPaymentNo(string paymentNo)
    {
        PaymentNo = paymentNo;
        UpdateTimestamp();
    }
    
    public void MarkAsSuccess(
        string? payerAccount = null,
        string? payerName = null,
        DateTime? completedAt = null)
    {
        Status = TransactionStatus.Success;
        CompletedAt = completedAt ?? DateTime.UtcNow;
        PayerAccount = payerAccount;
        PayerName = payerName;
        UpdateTimestamp();
    }
    
    public void MarkAsFailed(string? reason = null, DateTime? failedAt = null)
    {
        Status = TransactionStatus.Failed;
        FailReason = reason;
        FailedAt = failedAt ?? DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void MarkAsCancelled()
    {
        Status = TransactionStatus.Cancelled;
        UpdateTimestamp();
    }
    
    public void AddRawData(string key, string value)
    {
        RawData[key] = value;
        UpdateTimestamp();
    }
    
    public void AddRawData(Dictionary<string, string> data)
    {
        foreach (var kvp in data)
        {
            RawData[kvp.Key] = kvp.Value;
        }
        UpdateTimestamp();
    }
}