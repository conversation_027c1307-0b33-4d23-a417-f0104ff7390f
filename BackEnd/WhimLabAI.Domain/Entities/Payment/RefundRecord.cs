using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.Payment;

public class RefundRecord : Entity
{
    public Guid OrderId { get; private set; }
    public string RefundNo { get; private set; }
    public string? RefundTransactionId { get; private set; }
    public Money RefundAmount { get; private set; }
    public string Reason { get; private set; }
    public string? Remark { get; private set; }
    public RefundStatus Status { get; private set; }
    public DateTime? ProcessedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public string? FailureReason { get; private set; }
    public string? ProcessedBy { get; private set; }
    
    public Order Order { get; private set; } = null!;
    
    private RefundRecord() : base()
    {
    }
    
    public RefundRecord(
        Guid orderId,
        Money refundAmount,
        string reason,
        string? refundNo = null,
        string? remark = null) : base()
    {
        OrderId = orderId;
        RefundNo = refundNo ?? GenerateRefundNo();
        RefundAmount = refundAmount ?? throw new ArgumentNullException(nameof(refundAmount));
        Reason = reason ?? throw new ArgumentNullException(nameof(reason));
        Remark = remark;
        Status = RefundStatus.Pending;
    }
    
    public void StartProcessing(string? processedBy = null)
    {
        if (Status != RefundStatus.Pending)
            throw new InvalidOperationException("只有待处理的退款才能开始处理");
            
        Status = RefundStatus.Processing;
        ProcessedAt = DateTime.UtcNow;
        ProcessedBy = processedBy;
        UpdateTimestamp();
    }
    
    public void MarkAsCompleted(string? refundTransactionId = null)
    {
        if (Status != RefundStatus.Processing && Status != RefundStatus.Pending)
            throw new InvalidOperationException("只有处理中或待处理的退款才能标记为完成");
            
        Status = RefundStatus.Completed;
        CompletedAt = DateTime.UtcNow;
        RefundTransactionId = refundTransactionId;
        UpdateTimestamp();
    }
    
    public void MarkAsFailed(string reason)
    {
        if (Status == RefundStatus.Completed)
            throw new InvalidOperationException("已完成的退款不能标记为失败");
            
        Status = RefundStatus.Failed;
        FailureReason = reason;
        UpdateTimestamp();
    }
    
    public void Cancel(string? reason = null)
    {
        if (Status != RefundStatus.Pending)
            throw new InvalidOperationException("只有待处理的退款才能取消");
            
        Status = RefundStatus.Cancelled;
        FailureReason = reason ?? "用户取消";
        UpdateTimestamp();
    }
    
    public void UpdateRemark(string remark)
    {
        Remark = remark;
        UpdateTimestamp();
    }
    
    private string GenerateRefundNo()
    {
        return $"R{DateTime.UtcNow:yyyyMMddHHmmss}{Guid.NewGuid().ToString("N").Substring(0, 8).ToUpper()}";
    }
}