using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;

namespace WhimLabAI.Domain.Entities.Payment;

public class Coupon : Entity
{
    private readonly List<CouponUsage> _usages = new();
    
    public string Code { get; private set; }
    public string Name { get; private set; }
    public string? Description { get; private set; }
    public CouponType Type { get; private set; }
    public Money? DiscountAmount { get; private set; }
    public decimal? DiscountPercentage { get; private set; }
    public Money? MinimumAmount { get; private set; }
    public Money? MaximumDiscount { get; private set; }
    public DateTime ValidFrom { get; private set; }
    public DateTime ValidTo { get; private set; }
    public int TotalQuota { get; private set; }
    public int UsedCount { get; private set; }
    public int? UsagePerUser { get; private set; }
    public bool IsActive { get; private set; }
    public ApplicableScope Scope { get; private set; }
    public List<string>? ApplicableProducts { get; private set; }
    public Dictionary<string, object> Rules { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    
    public IReadOnlyCollection<CouponUsage> Usages => _usages.AsReadOnly();
    
    private Coupon() : base()
    {
        ApplicableProducts = new List<string>();
        Rules = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
    }
    
    public Coupon(
        string code,
        string name,
        CouponType type,
        DateTime validFrom,
        DateTime validTo,
        int totalQuota = -1,
        int? usagePerUser = null,
        string? description = null) : base()
    {
        Code = code ?? throw new ArgumentNullException(nameof(code));
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type;
        ValidFrom = validFrom;
        ValidTo = validTo;
        TotalQuota = totalQuota; // -1 means unlimited
        UsagePerUser = usagePerUser;
        Description = description;
        IsActive = true;
        UsedCount = 0;
        Scope = ApplicableScope.All;
        ApplicableProducts = new List<string>();
        Rules = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
    }
    
    public void SetFixedDiscount(Money discountAmount, Money? minimumAmount = null)
    {
        if (Type != CouponType.FixedAmount)
            throw new InvalidOperationException("只有固定金额优惠券才能设置折扣金额");
            
        DiscountAmount = discountAmount ?? throw new ArgumentNullException(nameof(discountAmount));
        MinimumAmount = minimumAmount;
        DiscountPercentage = null;
        MaximumDiscount = null;
        UpdateTimestamp();
    }
    
    public void SetPercentageDiscount(
        decimal discountPercentage,
        Money? minimumAmount = null,
        Money? maximumDiscount = null)
    {
        if (Type != CouponType.Percentage)
            throw new InvalidOperationException("只有百分比优惠券才能设置折扣百分比");
            
        if (discountPercentage <= 0 || discountPercentage > 100)
            throw new ArgumentException("折扣百分比必须在0-100之间", nameof(discountPercentage));
            
        DiscountPercentage = discountPercentage;
        MinimumAmount = minimumAmount;
        MaximumDiscount = maximumDiscount;
        DiscountAmount = null;
        UpdateTimestamp();
    }
    
    public void SetApplicableScope(ApplicableScope scope, List<string>? applicableProducts = null)
    {
        Scope = scope;
        ApplicableProducts = scope == ApplicableScope.Specific 
            ? applicableProducts ?? new List<string>() 
            : new List<string>();
        UpdateTimestamp();
    }
    
    public Money CalculateDiscount(Money orderAmount, string? productId = null)
    {
        if (!IsValid())
            throw new InvalidOperationException("优惠券无效");
            
        if (MinimumAmount != null && orderAmount < MinimumAmount)
            throw new InvalidOperationException($"订单金额不满足最低消费金额{MinimumAmount}");
            
        if (Scope == ApplicableScope.Specific && 
            (productId == null || !ApplicableProducts!.Contains(productId)))
            throw new InvalidOperationException("该优惠券不适用于此产品");
            
        Money discount;
        
        if (Type == CouponType.FixedAmount)
        {
            discount = DiscountAmount!;
            if (discount > orderAmount)
                discount = orderAmount; // Can't discount more than order amount
        }
        else // Percentage
        {
            discount = orderAmount.Multiply(DiscountPercentage!.Value / 100);
            if (MaximumDiscount != null && discount > MaximumDiscount)
                discount = MaximumDiscount;
        }
        
        return discount;
    }
    
    public void Use(Guid userId, Guid orderId, Money discountAmount)
    {
        if (!IsValid())
            throw new InvalidOperationException("优惠券无效");
            
        if (TotalQuota != -1 && UsedCount >= TotalQuota)
            throw new InvalidOperationException("优惠券已用完");
            
        if (UsagePerUser.HasValue)
        {
            var userUsageCount = _usages.Count(u => u.UserId == userId);
            if (userUsageCount >= UsagePerUser.Value)
                throw new InvalidOperationException($"每个用户最多只能使用{UsagePerUser.Value}次");
        }
        
        var usage = new CouponUsage(userId, orderId, discountAmount);
        _usages.Add(usage);
        UsedCount++;
        
        UpdateTimestamp();
    }
    
    public void Activate()
    {
        IsActive = true;
        UpdateTimestamp();
    }
    
    public void Deactivate()
    {
        IsActive = false;
        UpdateTimestamp();
    }
    
    public void ExtendValidity(DateTime newValidTo)
    {
        if (newValidTo <= ValidTo)
            throw new ArgumentException("新的有效期必须晚于当前有效期");
            
        ValidTo = newValidTo;
        UpdateTimestamp();
    }
    
    public bool IsValid()
    {
        if (!IsActive)
            return false;
            
        var now = DateTime.UtcNow;
        if (now < ValidFrom || now > ValidTo)
            return false;
            
        if (TotalQuota != -1 && UsedCount >= TotalQuota)
            return false;
            
        return true;
    }
    
    public bool CanUseBy(Guid userId)
    {
        if (!IsValid())
            return false;
            
        if (!UsagePerUser.HasValue)
            return true;
            
        var userUsageCount = _usages.Count(u => u.UserId == userId);
        return userUsageCount < UsagePerUser.Value;
    }
}

public enum CouponType
{
    FixedAmount,
    Percentage
}

public enum ApplicableScope
{
    All,
    Subscription,
    TokenPackage,
    Specific
}