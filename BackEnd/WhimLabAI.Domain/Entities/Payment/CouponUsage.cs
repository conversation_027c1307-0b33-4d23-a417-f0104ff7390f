using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;

namespace WhimLabAI.Domain.Entities.Payment;

public class CouponUsage : Entity
{
    public Guid UserId { get; private set; }
    public Guid OrderId { get; private set; }
    public Money DiscountAmount { get; private set; }
    public DateTime UsedAt { get; private set; }
    
    private CouponUsage() : base()
    {
    }
    
    public CouponUsage(
        Guid userId,
        Guid orderId,
        Money discountAmount) : base()
    {
        UserId = userId;
        OrderId = orderId;
        DiscountAmount = discountAmount ?? throw new ArgumentNullException(nameof(discountAmount));
        UsedAt = DateTime.UtcNow;
    }
}