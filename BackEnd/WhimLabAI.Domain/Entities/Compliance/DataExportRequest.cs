using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Compliance;

/// <summary>
/// 数据导出请求
/// </summary>
public class DataExportRequest : Entity
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; private set; }
    
    /// <summary>
    /// 请求类型
    /// </summary>
    public string RequestType { get; private set; } = string.Empty;
    
    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; private set; } = string.Empty;
    
    /// <summary>
    /// 导出格式
    /// </summary>
    public string ExportFormat { get; private set; } = string.Empty;
    
    /// <summary>
    /// 包含的数据类型
    /// </summary>
    public List<string> IncludedDataTypes { get; private set; } = new();
    
    /// <summary>
    /// 请求时间
    /// </summary>
    public DateTime RequestedAt { get; private set; }
    
    /// <summary>
    /// 开始处理时间
    /// </summary>
    public DateTime? ProcessingStartedAt { get; private set; }
    
    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; private set; }
    
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; private set; }
    
    /// <summary>
    /// 文件URL
    /// </summary>
    public string? FileUrl { get; private set; }
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long? FileSize { get; private set; }
    
    /// <summary>
    /// 文件哈希
    /// </summary>
    public string? FileHash { get; private set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; private set; }
    
    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress { get; private set; } = string.Empty;
    
    /// <summary>
    /// 用户代理
    /// </summary>
    public string? UserAgent { get; private set; }
    
    /// <summary>
    /// 处理进度（百分比）
    /// </summary>
    public int Progress { get; private set; }
    
    /// <summary>
    /// 是否已下载
    /// </summary>
    public bool IsDownloaded { get; private set; }
    
    /// <summary>
    /// 下载次数
    /// </summary>
    public int DownloadCount { get; private set; }
    
    /// <summary>
    /// 最后下载时间
    /// </summary>
    public DateTime? LastDownloadedAt { get; private set; }

    /// <summary>
    /// 创建数据导出请求
    /// </summary>
    public static DataExportRequest Create(
        Guid userId,
        string requestType,
        string exportFormat,
        List<string> includedDataTypes,
        string ipAddress)
    {
        return new DataExportRequest
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            RequestType = requestType,
            Status = ExportStatus.Pending,
            ExportFormat = exportFormat,
            IncludedDataTypes = includedDataTypes,
            RequestedAt = DateTime.UtcNow,
            IpAddress = ipAddress,
            Progress = 0
        };
    }

    /// <summary>
    /// 开始处理
    /// </summary>
    public void StartProcessing()
    {
        Status = ExportStatus.Processing;
        ProcessingStartedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }

    /// <summary>
    /// 更新进度
    /// </summary>
    public void UpdateProgress(int progress)
    {
        if (progress >= 0 && progress <= 100)
        {
            Progress = progress;
            UpdateTimestamp();
        }
    }

    /// <summary>
    /// 完成处理
    /// </summary>
    public void Complete(string fileUrl, long fileSize, string fileHash, int expirationDays = 7)
    {
        Status = ExportStatus.Completed;
        CompletedAt = DateTime.UtcNow;
        FileUrl = fileUrl;
        FileSize = fileSize;
        FileHash = fileHash;
        ExpiresAt = DateTime.UtcNow.AddDays(expirationDays);
        Progress = 100;
        UpdateTimestamp();
    }

    /// <summary>
    /// 标记失败
    /// </summary>
    public void MarkAsFailed(string errorMessage)
    {
        Status = ExportStatus.Failed;
        ErrorMessage = errorMessage;
        UpdateTimestamp();
    }

    /// <summary>
    /// 记录下载
    /// </summary>
    public void RecordDownload()
    {
        IsDownloaded = true;
        DownloadCount++;
        LastDownloadedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
    }

    /// <summary>
    /// 取消请求
    /// </summary>
    public void Cancel()
    {
        if (Status == ExportStatus.Pending || Status == ExportStatus.Processing)
        {
            Status = ExportStatus.Cancelled;
            UpdateTimestamp();
        }
    }
}

/// <summary>
/// 导出状态
/// </summary>
public static class ExportStatus
{
    public const string Pending = "pending";
    public const string Processing = "processing";
    public const string Completed = "completed";
    public const string Failed = "failed";
    public const string Cancelled = "cancelled";
    public const string Expired = "expired";
}

/// <summary>
/// 导出请求类型
/// </summary>
public static class ExportRequestTypes
{
    public const string PersonalData = "personal_data";
    public const string ConversationHistory = "conversation_history";
    public const string FullExport = "full_export";
    public const string CustomExport = "custom_export";
}

/// <summary>
/// 导出格式
/// </summary>
public static class ExportFormats
{
    public const string Json = "json";
    public const string Csv = "csv";
    public const string Xml = "xml";
    public const string Zip = "zip";
    public const string Pdf = "pdf";
}