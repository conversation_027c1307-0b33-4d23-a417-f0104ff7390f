using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Compliance;

/// <summary>
/// 客户用户同意记录
/// </summary>
public class CustomerUserConsent : Entity
{
    /// <summary>
    /// 客户用户ID
    /// </summary>
    public Guid CustomerUserId { get; private set; }
    
    /// <summary>
    /// 同意类型
    /// </summary>
    public string ConsentType { get; private set; } = string.Empty;
    
    /// <summary>
    /// 同意版本
    /// </summary>
    public string Version { get; private set; } = string.Empty;
    
    /// <summary>
    /// 是否同意
    /// </summary>
    public bool IsGranted { get; private set; }
    
    /// <summary>
    /// 同意时间
    /// </summary>
    public DateTime? GrantedAt { get; private set; }
    
    /// <summary>
    /// 撤回时间
    /// </summary>
    public DateTime? RevokedAt { get; private set; }
    
    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress { get; private set; } = string.Empty;
    
    /// <summary>
    /// 用户代理
    /// </summary>
    public string? UserAgent { get; private set; }
    
    /// <summary>
    /// 同意文本
    /// </summary>
    public string ConsentText { get; private set; } = string.Empty;
    
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; private set; }
    
    /// <summary>
    /// 元数据
    /// </summary>
    public string? Metadata { get; private set; }

    /// <summary>
    /// 创建客户用户同意
    /// </summary>
    public static CustomerUserConsent Create(
        Guid customerUserId,
        string consentType,
        string version,
        bool isGranted,
        string ipAddress,
        string consentText,
        DateTime? expiresAt = null)
    {
        return new CustomerUserConsent
        {
            Id = Guid.NewGuid(),
            CustomerUserId = customerUserId,
            ConsentType = consentType,
            Version = version,
            IsGranted = isGranted,
            GrantedAt = isGranted ? DateTime.UtcNow : null,
            IpAddress = ipAddress,
            ConsentText = consentText,
            ExpiresAt = expiresAt
        };
    }

    /// <summary>
    /// 撤回同意
    /// </summary>
    public void Revoke(string ipAddress)
    {
        IsGranted = false;
        RevokedAt = DateTime.UtcNow;
        IpAddress = ipAddress;
        UpdateTimestamp();
    }

    /// <summary>
    /// 重新授予同意
    /// </summary>
    public void Grant(string ipAddress, string? userAgent = null)
    {
        IsGranted = true;
        GrantedAt = DateTime.UtcNow;
        RevokedAt = null;
        IpAddress = ipAddress;
        UserAgent = userAgent;
        UpdateTimestamp();
    }

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
    }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid()
    {
        return IsGranted && !IsExpired();
    }
}

/// <summary>
/// 客户同意类型常量
/// </summary>
public static class CustomerConsentTypes
{
    public const string TermsOfService = "terms_of_service";
    public const string PrivacyPolicy = "privacy_policy";
    public const string DataProcessing = "data_processing";
    public const string Marketing = "marketing";
    public const string Analytics = "analytics";
    public const string ThirdPartySharing = "third_party_sharing";
    public const string Cookies = "cookies";
    public const string PersonalizedAds = "personalized_ads";
    public const string DataRetention = "data_retention";
    public const string ProfilePublic = "profile_public";
}