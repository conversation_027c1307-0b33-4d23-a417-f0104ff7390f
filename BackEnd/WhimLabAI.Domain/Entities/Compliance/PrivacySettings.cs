using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Compliance;

/// <summary>
/// 用户隐私设置
/// </summary>
public class PrivacySettings : Entity
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; private set; }
    
    /// <summary>
    /// 是否允许公开个人资料
    /// </summary>
    public bool AllowPublicProfile { get; private set; }
    
    /// <summary>
    /// 是否允许数据分析
    /// </summary>
    public bool AllowAnalytics { get; private set; }
    
    /// <summary>
    /// 是否允许营销通信
    /// </summary>
    public bool AllowMarketing { get; private set; }
    
    /// <summary>
    /// 是否允许个性化推荐
    /// </summary>
    public bool AllowPersonalization { get; private set; }
    
    /// <summary>
    /// 是否允许第三方共享
    /// </summary>
    public bool AllowThirdPartySharing { get; private set; }
    
    /// <summary>
    /// 是否允许搜索引擎索引
    /// </summary>
    public bool AllowSearchEngineIndexing { get; private set; }
    
    /// <summary>
    /// 是否允许位置访问
    /// </summary>
    public bool AllowLocationAccess { get; private set; }
    
    /// <summary>
    /// 是否允许对话历史保存
    /// </summary>
    public bool AllowConversationHistory { get; private set; }
    
    /// <summary>
    /// 数据保留期限（天）
    /// </summary>
    public int DataRetentionDays { get; private set; }
    
    /// <summary>
    /// 是否启用两步验证
    /// </summary>
    public bool TwoFactorEnabled { get; private set; }
    
    /// <summary>
    /// 是否启用登录通知
    /// </summary>
    public bool LoginNotificationEnabled { get; private set; }
    
    /// <summary>
    /// 隐私模式
    /// </summary>
    public string PrivacyMode { get; private set; } = "Standard";
    
    /// <summary>
    /// 上次更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; private set; }

    /// <summary>
    /// 创建默认隐私设置
    /// </summary>
    public static PrivacySettings CreateDefault(Guid userId)
    {
        return new PrivacySettings
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            AllowPublicProfile = false,
            AllowAnalytics = true,
            AllowMarketing = false,
            AllowPersonalization = true,
            AllowThirdPartySharing = false,
            AllowSearchEngineIndexing = false,
            AllowLocationAccess = false,
            AllowConversationHistory = true,
            DataRetentionDays = 365,
            TwoFactorEnabled = false,
            LoginNotificationEnabled = true,
            PrivacyMode = "Standard",
            LastUpdatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 更新隐私设置
    /// </summary>
    public void UpdateSettings(PrivacySettingsUpdateRequest request)
    {
        if (request.AllowPublicProfile.HasValue)
            AllowPublicProfile = request.AllowPublicProfile.Value;
        
        if (request.AllowAnalytics.HasValue)
            AllowAnalytics = request.AllowAnalytics.Value;
        
        if (request.AllowMarketing.HasValue)
            AllowMarketing = request.AllowMarketing.Value;
        
        if (request.AllowPersonalization.HasValue)
            AllowPersonalization = request.AllowPersonalization.Value;
        
        if (request.AllowThirdPartySharing.HasValue)
            AllowThirdPartySharing = request.AllowThirdPartySharing.Value;
        
        if (request.AllowSearchEngineIndexing.HasValue)
            AllowSearchEngineIndexing = request.AllowSearchEngineIndexing.Value;
        
        if (request.AllowLocationAccess.HasValue)
            AllowLocationAccess = request.AllowLocationAccess.Value;
        
        if (request.AllowConversationHistory.HasValue)
            AllowConversationHistory = request.AllowConversationHistory.Value;
        
        if (request.DataRetentionDays.HasValue && request.DataRetentionDays.Value >= 30)
            DataRetentionDays = request.DataRetentionDays.Value;
        
        if (request.TwoFactorEnabled.HasValue)
            TwoFactorEnabled = request.TwoFactorEnabled.Value;
        
        if (request.LoginNotificationEnabled.HasValue)
            LoginNotificationEnabled = request.LoginNotificationEnabled.Value;
        
        if (!string.IsNullOrWhiteSpace(request.PrivacyMode))
            PrivacyMode = request.PrivacyMode;
        
        LastUpdatedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }

    /// <summary>
    /// 启用严格隐私模式
    /// </summary>
    public void EnableStrictPrivacy()
    {
        AllowPublicProfile = false;
        AllowAnalytics = false;
        AllowMarketing = false;
        AllowPersonalization = false;
        AllowThirdPartySharing = false;
        AllowSearchEngineIndexing = false;
        AllowLocationAccess = false;
        DataRetentionDays = 30;
        PrivacyMode = "Strict";
        LastUpdatedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
}

/// <summary>
/// 隐私设置更新请求
/// </summary>
public class PrivacySettingsUpdateRequest
{
    public bool? AllowPublicProfile { get; set; }
    public bool? AllowAnalytics { get; set; }
    public bool? AllowMarketing { get; set; }
    public bool? AllowPersonalization { get; set; }
    public bool? AllowThirdPartySharing { get; set; }
    public bool? AllowSearchEngineIndexing { get; set; }
    public bool? AllowLocationAccess { get; set; }
    public bool? AllowConversationHistory { get; set; }
    public int? DataRetentionDays { get; set; }
    public bool? TwoFactorEnabled { get; set; }
    public bool? LoginNotificationEnabled { get; set; }
    public string? PrivacyMode { get; set; }
}

/// <summary>
/// 隐私模式
/// </summary>
public static class PrivacyModes
{
    public const string Standard = "Standard";
    public const string Enhanced = "Enhanced";
    public const string Strict = "Strict";
    public const string Custom = "Custom";
}