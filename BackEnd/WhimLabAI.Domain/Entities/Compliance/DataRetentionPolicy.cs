using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Compliance;

/// <summary>
/// 数据保留策略
/// </summary>
public class DataRetentionPolicy : Entity
{
    /// <summary>
    /// 策略名称
    /// </summary>
    public string PolicyName { get; private set; } = string.Empty;
    
    /// <summary>
    /// 数据类型
    /// </summary>
    public string DataType { get; private set; } = string.Empty;
    
    /// <summary>
    /// 保留期限（天）
    /// </summary>
    public int RetentionDays { get; private set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; private set; }
    
    /// <summary>
    /// 删除方式
    /// </summary>
    public string DeletionMethod { get; private set; } = string.Empty;
    
    /// <summary>
    /// 是否自动删除
    /// </summary>
    public bool AutoDelete { get; private set; }
    
    /// <summary>
    /// 是否允许用户覆盖
    /// </summary>
    public bool AllowUserOverride { get; private set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; private set; }
    
    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; private set; }
    
    /// <summary>
    /// 最后执行时间
    /// </summary>
    public DateTime? LastExecutedAt { get; private set; }
    
    /// <summary>
    /// 下次执行时间
    /// </summary>
    public DateTime? NextExecutionAt { get; private set; }

    /// <summary>
    /// 创建数据保留策略
    /// </summary>
    public static DataRetentionPolicy Create(
        string policyName,
        string dataType,
        int retentionDays,
        string deletionMethod,
        bool autoDelete = true,
        bool allowUserOverride = false)
    {
        var policy = new DataRetentionPolicy
        {
            Id = Guid.NewGuid(),
            PolicyName = policyName,
            DataType = dataType,
            RetentionDays = retentionDays,
            DeletionMethod = deletionMethod,
            AutoDelete = autoDelete,
            AllowUserOverride = allowUserOverride,
            IsEnabled = true,
            Priority = 100
        };

        if (autoDelete)
        {
            policy.NextExecutionAt = DateTime.UtcNow.AddDays(1);
        }

        return policy;
    }

    /// <summary>
    /// 更新策略
    /// </summary>
    public void UpdatePolicy(
        int? retentionDays = null,
        bool? autoDelete = null,
        bool? allowUserOverride = null,
        string? description = null)
    {
        if (retentionDays.HasValue && retentionDays.Value > 0)
            RetentionDays = retentionDays.Value;

        if (autoDelete.HasValue)
        {
            AutoDelete = autoDelete.Value;
            if (autoDelete.Value && !NextExecutionAt.HasValue)
            {
                NextExecutionAt = DateTime.UtcNow.AddDays(1);
            }
        }

        if (allowUserOverride.HasValue)
            AllowUserOverride = allowUserOverride.Value;

        if (description != null)
            Description = description;

        UpdateTimestamp();
    }

    /// <summary>
    /// 启用策略
    /// </summary>
    public void Enable()
    {
        IsEnabled = true;
        if (AutoDelete && !NextExecutionAt.HasValue)
        {
            NextExecutionAt = DateTime.UtcNow.AddDays(1);
        }
        UpdateTimestamp();
    }

    /// <summary>
    /// 禁用策略
    /// </summary>
    public void Disable()
    {
        IsEnabled = false;
        NextExecutionAt = null;
        UpdateTimestamp();
    }

    /// <summary>
    /// 记录执行
    /// </summary>
    public void RecordExecution()
    {
        LastExecutedAt = DateTime.UtcNow;
        if (AutoDelete && IsEnabled)
        {
            NextExecutionAt = DateTime.UtcNow.AddDays(1);
        }
        UpdateTimestamp();
    }

    /// <summary>
    /// 是否需要执行
    /// </summary>
    public bool ShouldExecute()
    {
        return IsEnabled && 
               AutoDelete && 
               NextExecutionAt.HasValue && 
               NextExecutionAt.Value <= DateTime.UtcNow;
    }
}

/// <summary>
/// 数据类型常量
/// </summary>
public static class DataTypes
{
    public const string UserProfile = "user_profile";
    public const string LoginHistory = "login_history";
    public const string ConversationHistory = "conversation_history";
    public const string MessageAttachments = "message_attachments";
    public const string AuditLogs = "audit_logs";
    public const string ApiKeyUsage = "api_key_usage";
    public const string TokenUsage = "token_usage";
    public const string PaymentHistory = "payment_history";
    public const string DeviceAuthorization = "device_authorization";
    public const string TempData = "temp_data";
    public const string CachedData = "cached_data";
    public const string InactiveUsers = "inactive_users";
}

/// <summary>
/// 删除方法常量
/// </summary>
public static class DeletionMethods
{
    public const string HardDelete = "hard_delete";
    public const string SoftDelete = "soft_delete";
    public const string Anonymize = "anonymize";
    public const string Archive = "archive";
    public const string Export = "export";
}