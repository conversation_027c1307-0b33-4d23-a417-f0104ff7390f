using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Notification;

/// <summary>
/// 通知实体
/// </summary>
public class Notification : Entity
{
    /// <summary>
    /// 接收用户ID
    /// </summary>
    public Guid UserId { get; private set; }
    
    /// <summary>
    /// 通知标题
    /// </summary>
    public string Title { get; private set; }
    
    /// <summary>
    /// 通知内容
    /// </summary>
    public string Content { get; private set; }
    
    /// <summary>
    /// 通知类型
    /// </summary>
    public string Type { get; private set; }
    
    /// <summary>
    /// 通知级别
    /// </summary>
    public string Level { get; private set; }
    
    /// <summary>
    /// 是否已读
    /// </summary>
    public bool IsRead { get; private set; }
    
    /// <summary>
    /// 阅读时间
    /// </summary>
    public DateTime? ReadAt { get; private set; }
    
    /// <summary>
    /// 元数据（JSON）
    /// </summary>
    public string? MetadataJson { get; private set; }
    
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; private set; }
    
    /// <summary>
    /// 是否已删除
    /// </summary>
    public bool IsDeleted { get; private set; }
    
    /// <summary>
    /// 删除时间
    /// </summary>
    public DateTime? DeletedAt { get; private set; }

    private Notification() { }

    public Notification(
        Guid userId,
        string title,
        string content,
        string type,
        string level = "info",
        string? metadataJson = null,
        DateTime? expiresAt = null)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("UserId cannot be empty", nameof(userId));
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));
        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));
        if (string.IsNullOrWhiteSpace(type))
            throw new ArgumentException("Type cannot be empty", nameof(type));
        
        UserId = userId;
        Title = title.Trim();
        Content = content.Trim();
        Type = type.Trim().ToLower();
        Level = (level ?? "info").Trim().ToLower();
        MetadataJson = metadataJson;
        ExpiresAt = expiresAt;
        IsRead = false;
        IsDeleted = false;
    }

    /// <summary>
    /// 标记为已读
    /// </summary>
    public void MarkAsRead()
    {
        if (!IsRead)
        {
            IsRead = true;
            ReadAt = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 标记为未读
    /// </summary>
    public void MarkAsUnread()
    {
        IsRead = false;
        ReadAt = null;
    }

    /// <summary>
    /// 软删除
    /// </summary>
    public void Delete()
    {
        if (!IsDeleted)
        {
            IsDeleted = true;
            DeletedAt = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 恢复删除
    /// </summary>
    public void Restore()
    {
        IsDeleted = false;
        DeletedAt = null;
    }

    /// <summary>
    /// 更新元数据
    /// </summary>
    public void UpdateMetadata(string? metadataJson)
    {
        MetadataJson = metadataJson;
    }

    /// <summary>
    /// 检查是否已过期
    /// </summary>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow;
    }
}