using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Conversation;

public class MessageAttachment : Entity
{
    public string FileName { get; private set; }
    public string FileUrl { get; private set; }
    public string ContentType { get; private set; }
    public long FileSize { get; private set; }
    public string? ThumbnailUrl { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    
    private MessageAttachment() : base()
    {
        Metadata = new Dictionary<string, object>();
    }
    
    public MessageAttachment(
        string fileName,
        string fileUrl,
        string contentType,
        long fileSize,
        string? thumbnailUrl = null,
        Dictionary<string, object>? metadata = null) : base()
    {
        FileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
        FileUrl = fileUrl ?? throw new ArgumentNullException(nameof(fileUrl));
        ContentType = contentType ?? throw new ArgumentNullException(nameof(contentType));
        FileSize = fileSize;
        ThumbnailUrl = thumbnailUrl;
        Metadata = metadata ?? new Dictionary<string, object>();
    }
    
    public void UpdateThumbnail(string thumbnailUrl)
    {
        ThumbnailUrl = thumbnailUrl;
        UpdateTimestamp();
    }
    
    public void SetMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdateTimestamp();
    }
    
    public void RemoveMetadata(string key)
    {
        if (Metadata.Remove(key))
        {
            UpdateTimestamp();
        }
    }
    
    public bool IsImage()
    {
        return ContentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
    }
    
    public bool IsVideo()
    {
        return ContentType.StartsWith("video/", StringComparison.OrdinalIgnoreCase);
    }
    
    public bool IsAudio()
    {
        return ContentType.StartsWith("audio/", StringComparison.OrdinalIgnoreCase);
    }
    
    public bool IsDocument()
    {
        var documentTypes = new[] 
        { 
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/plain",
            "text/markdown"
        };
        
        return documentTypes.Contains(ContentType.ToLowerInvariant());
    }
}