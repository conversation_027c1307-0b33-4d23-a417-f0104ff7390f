using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Conversation;

public class ConversationMessage : Entity
{
    private readonly List<MessageAttachment> _attachments = new();
    
    public Guid ConversationId { get; private set; }
    public string Role { get; private set; } // User or Assistant
    public string Content { get; private set; }
    public int SequenceNumber { get; private set; }
    public int TokenCount { get; private set; }
    public bool IsDeleted { get; private set; }
    public DateTime? DeletedAt { get; private set; }
    public MessageRating? Rating { get; private set; }
    
    public Conversation Conversation { get; private set; } = null!;
    public IReadOnlyCollection<MessageAttachment> Attachments => _attachments.AsReadOnly();
    
    private ConversationMessage() : base()
    {
    }
    
    public ConversationMessage(
        Guid conversationId,
        string role,
        string content,
        int sequenceNumber,
        int tokenCount = 0,
        List<MessageAttachment>? attachments = null) : base()
    {
        ConversationId = conversationId;
        Role = ValidateRole(role);
        Content = content ?? string.Empty;
        SequenceNumber = sequenceNumber;
        TokenCount = tokenCount;
        IsDeleted = false;
        
        if (attachments != null)
        {
            _attachments.AddRange(attachments);
        }
    }
    
    public void UpdateContent(string content)
    {
        if (IsDeleted)
            throw new InvalidOperationException("已删除的消息不能修改");
            
        Content = content ?? string.Empty;
        UpdateTimestamp();
    }
    
    public void UpdateTokenCount(int tokenCount)
    {
        TokenCount = Math.Max(0, tokenCount);
        UpdateTimestamp();
    }
    
    public void Delete()
    {
        if (IsDeleted)
            return;
            
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void Restore()
    {
        if (!IsDeleted)
            return;
            
        IsDeleted = false;
        DeletedAt = null;
        UpdateTimestamp();
    }
    
    public void AddAttachment(MessageAttachment attachment)
    {
        if (attachment == null)
            throw new ArgumentNullException(nameof(attachment));
            
        _attachments.Add(attachment);
        UpdateTimestamp();
    }
    
    public void RemoveAttachment(Guid attachmentId)
    {
        var attachment = _attachments.FirstOrDefault(a => a.Id == attachmentId);
        if (attachment != null)
        {
            _attachments.Remove(attachment);
            UpdateTimestamp();
        }
    }
    
    public void AddRating(int score, string? feedback = null)
    {
        if (Role.ToLower() != "assistant")
            throw new InvalidOperationException("只能评价助手的回复");
            
        if (Rating != null)
        {
            Rating.Update(score, feedback);
        }
        else
        {
            Rating = new MessageRating(score, feedback);
        }
        
        UpdateTimestamp();
    }
    
    public void RemoveRating()
    {
        Rating = null;
        UpdateTimestamp();
    }
    
    private string ValidateRole(string role)
    {
        var normalizedRole = role?.ToLower() ?? string.Empty;
        
        if (normalizedRole != "user" && normalizedRole != "assistant" && normalizedRole != "system")
            throw new ArgumentException("角色必须是user、assistant或system", nameof(role));
            
        return normalizedRole;
    }
}