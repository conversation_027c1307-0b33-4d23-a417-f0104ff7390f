using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.Entities.Conversation;

/// <summary>
/// Dify对话映射实体
/// 维护系统对话ID与Dify conversation_id的映射关系
/// </summary>
public class DifyConversationMapping : Entity
{
    public Guid ConversationId { get; private set; }
    public Guid AgentId { get; private set; }
    public string DifyConversationId { get; private set; }
    public DateTime LastSyncedAt { get; private set; }
    
    private DifyConversationMapping() : base() { }
    
    /// <summary>
    /// 创建Dify对话映射
    /// </summary>
    public static DifyConversationMapping Create(
        Guid conversationId,
        Guid agentId,
        string difyConversationId)
    {
        if (conversationId == Guid.Empty)
            throw new ValidationException("ConversationId", "对话ID不能为空");
            
        if (agentId == Guid.Empty)
            throw new ValidationException("AgentId", "AgentID不能为空");
            
        if (string.IsNullOrWhiteSpace(difyConversationId))
            throw new ValidationException("DifyConversationId", "Dify对话ID不能为空");
            
        return new DifyConversationMapping
        {
            Id = Guid.NewGuid(),
            ConversationId = conversationId,
            AgentId = agentId,
            DifyConversationId = difyConversationId,
            LastSyncedAt = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow
        };
    }
    
    /// <summary>
    /// 更新最后同步时间
    /// </summary>
    public void UpdateLastSynced()
    {
        LastSyncedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 更新Dify对话ID
    /// </summary>
    public void UpdateDifyConversationId(string difyConversationId)
    {
        if (string.IsNullOrWhiteSpace(difyConversationId))
            throw new ValidationException("DifyConversationId", "Dify对话ID不能为空");
            
        DifyConversationId = difyConversationId;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 更新同步时间（UpdateLastSynced的别名）
    /// </summary>
    public void UpdateSyncTime() => UpdateLastSynced();
}