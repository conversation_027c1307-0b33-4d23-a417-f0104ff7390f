using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Conversation;

public class MessageRating : ValueObject
{
    public int Score { get; private set; }
    public string? Feedback { get; private set; }
    public DateTime RatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    
    public MessageRating(int score, string? feedback = null)
    {
        SetScore(score);
        Feedback = feedback;
        RatedAt = DateTime.UtcNow;
    }
    
    public void Update(int score, string? feedback = null)
    {
        SetScore(score);
        Feedback = feedback;
        UpdatedAt = DateTime.UtcNow;
    }
    
    private void SetScore(int score)
    {
        if (score < 1 || score > 5)
            throw new ArgumentOutOfRangeException(nameof(score), "评分必须在1-5之间");
            
        Score = score;
    }
    
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Score;
        yield return Feedback;
        yield return RatedAt;
    }
    
    public bool IsPositive() => Score >= 4;
    public bool IsNegative() => Score <= 2;
    public bool IsNeutral() => Score == 3;
}