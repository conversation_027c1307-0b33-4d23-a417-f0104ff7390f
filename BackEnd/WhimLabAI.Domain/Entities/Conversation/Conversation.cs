using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.DomainEvents;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.Entities.Conversation;

public class Conversation : AggregateRoot, ISoftDelete
{
    private readonly List<ConversationMessage> _messages = new();
    
    public Guid CustomerUserId { get; private set; }
    public Guid AgentId { get; private set; }
    public string Title { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime LastMessageAt { get; private set; }
    public bool IsArchived { get; private set; }
    public int MessageCount { get; private set; }
    public int TotalTokens { get; private set; }
    public int InputTokens { get; private set; }
    public int OutputTokens { get; private set; }
    public string? Model { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public bool IsDeleted { get; private set; }
    public DateTime? DeletedAt { get; private set; }
    
    public IReadOnlyCollection<ConversationMessage> Messages => _messages.AsReadOnly();
    
    private Conversation() : base()
    {
        Metadata = new Dictionary<string, object>();
    }
    
    public Conversation(
        Guid customerUserId,
        Guid agentId,
        string? title = null,
        string? model = null,
        Dictionary<string, object>? metadata = null) : base()
    {
        CustomerUserId = customerUserId;
        AgentId = agentId;
        Title = title ?? "新对话";
        Model = model;
        StartedAt = DateTime.UtcNow;
        LastMessageAt = DateTime.UtcNow;
        IsArchived = false;
        MessageCount = 0;
        TotalTokens = 0;
        InputTokens = 0;
        OutputTokens = 0;
        Metadata = metadata ?? new Dictionary<string, object>();
        IsDeleted = false;
        DeletedAt = null;
        
        RaiseDomainEvent(new ConversationStartedEvent(Id, customerUserId, agentId));
    }
    
    public ConversationMessage AddMessage(
        string role,
        string content,
        int tokenCount = 0,
        List<MessageAttachment>? attachments = null)
    {
        if (IsArchived)
            throw new BusinessException("归档的对话不能添加消息");
            
        if (string.IsNullOrWhiteSpace(content) && (attachments == null || attachments.Count == 0))
            throw new ValidationException("Message", "消息内容不能为空");
            
        var message = new ConversationMessage(
            Id,
            role,
            content,
            MessageCount + 1,
            tokenCount,
            attachments);
            
        _messages.Add(message);
        MessageCount++;
        TotalTokens += tokenCount;
        
        // Update input/output tokens based on role
        if (role.ToLower() == "user")
        {
            InputTokens += tokenCount;
        }
        else if (role.ToLower() == "assistant")
        {
            OutputTokens += tokenCount;
        }
        
        LastMessageAt = DateTime.UtcNow;
        
        // Auto-generate title from first user message if not set
        if (Title == "新对话" && role.ToLower() == "user" && !string.IsNullOrWhiteSpace(content))
        {
            UpdateTitle(GenerateTitleFromContent(content));
        }
        
        UpdateTimestamp();
        return message;
    }
    
    public void DeleteMessage(Guid messageId)
    {
        var message = _messages.FirstOrDefault(m => m.Id == messageId);
        if (message == null)
            throw new NotFoundException("Message", messageId);
            
        message.Delete();
        UpdateTimestamp();
    }
    
    public void UpdateTitle(string title)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ValidationException("Title", "标题不能为空");
            
        if (title.Length > 200)
            throw new ValidationException("Title", "标题不能超过200个字符");
            
        Title = title;
        UpdateTimestamp();
    }
    
    public void SetModel(string model)
    {
        if (string.IsNullOrWhiteSpace(model))
            throw new ValidationException("Model", "模型名称不能为空");
            
        Model = model;
        UpdateTimestamp();
    }
    
    public void Archive()
    {
        if (IsArchived)
            throw new BusinessException("对话已经归档");
            
        IsArchived = true;
        UpdateTimestamp();
    }
    
    public void Unarchive()
    {
        if (!IsArchived)
            throw new BusinessException("对话未归档");
            
        IsArchived = false;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 归档消息（压缩存储）
    /// </summary>
    public void ArchiveMessages()
    {
        // 标记对话为已归档
        if (!IsArchived)
        {
            Archive();
        }
        
        // 在实际实现中，这里会将消息移动到压缩存储
        // 例如：将消息序列化后压缩，存储到单独的表或对象存储中
        // 然后清空 _messages 集合以节省内存
        
        // 添加元数据标记
        SetMetadata("MessagesArchived", true);
        SetMetadata("MessagesArchivedAt", DateTime.UtcNow);
        
        UpdateTimestamp();
    }
    
    public void SetMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdateTimestamp();
    }
    
    public void RemoveMetadata(string key)
    {
        if (Metadata.Remove(key))
        {
            UpdateTimestamp();
        }
    }
    
    public void RateMessage(Guid messageId, int score, string? feedback = null)
    {
        var message = _messages.FirstOrDefault(m => m.Id == messageId);
        if (message == null)
            throw new NotFoundException("Message", messageId);
            
        if (message.Role.ToLower() != "assistant")
            throw new BusinessException("只能评价助手的回复");
            
        message.AddRating(score, feedback);
        UpdateTimestamp();
    }
    
    public ConversationSummary GetSummary()
    {
        return new ConversationSummary
        {
            Id = Id,
            Title = Title,
            MessageCount = MessageCount,
            TotalTokens = TotalTokens,
            StartedAt = StartedAt,
            LastMessageAt = LastMessageAt,
            IsArchived = IsArchived,
            LastMessage = _messages.LastOrDefault(m => !m.IsDeleted)?.Content
        };
    }
    
    private string GenerateTitleFromContent(string content)
    {
        // Simple title generation - take first 50 chars
        var title = content.Length > 50 ? content.Substring(0, 47) + "..." : content;
        
        // Remove newlines and extra spaces
        title = global::System.Text.RegularExpressions.Regex.Replace(title, @"\s+", " ").Trim();
        
        return title;
    }
    
    public void Delete()
    {
        if (IsDeleted)
            return;
            
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void Restore()
    {
        if (!IsDeleted)
            throw new BusinessException("对话未被删除");
            
        IsDeleted = false;
        DeletedAt = null;
        UpdateTimestamp();
    }
}

public class ConversationSummary
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public int MessageCount { get; set; }
    public int TotalTokens { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime LastMessageAt { get; set; }
    public bool IsArchived { get; set; }
    public string? LastMessage { get; set; }
}