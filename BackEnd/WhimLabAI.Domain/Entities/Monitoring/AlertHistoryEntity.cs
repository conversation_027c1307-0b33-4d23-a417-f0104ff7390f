using System;
using System.Collections.Generic;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Monitoring;

/// <summary>
/// 告警历史记录实体
/// </summary>
public class AlertHistoryEntity : Entity
{
    /// <summary>
    /// 告警名称
    /// </summary>
    public string AlertName { get; set; } = string.Empty;
    
    /// <summary>
    /// 告警指纹（用于关联同一告警的firing和resolved状态）
    /// </summary>
    public string Fingerprint { get; set; } = string.Empty;
    
    /// <summary>
    /// 严重程度
    /// </summary>
    public string Severity { get; set; } = string.Empty;
    
    /// <summary>
    /// 服务名称
    /// </summary>
    public string Service { get; set; } = string.Empty;
    
    /// <summary>
    /// 告警状态
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 告警摘要
    /// </summary>
    public string Summary { get; set; } = string.Empty;
    
    /// <summary>
    /// 详细描述
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartsAt { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndsAt { get; set; }
    
    /// <summary>
    /// 持续时间（秒）
    /// </summary>
    public int? DurationSeconds { get; set; }
    
    /// <summary>
    /// 标签（JSON存储）
    /// </summary>
    public string LabelsJson { get; set; } = "{}";
    
    /// <summary>
    /// 注解（JSON存储）
    /// </summary>
    public string AnnotationsJson { get; set; } = "{}";
    
    /// <summary>
    /// Prometheus查询URL
    /// </summary>
    public string? GeneratorUrl { get; set; }
    
    /// <summary>
    /// 接收者
    /// </summary>
    public string? Receiver { get; set; }
    
    /// <summary>
    /// 通知渠道（逗号分隔）
    /// </summary>
    public string? NotificationChannels { get; set; }
    
    /// <summary>
    /// 是否已通知
    /// </summary>
    public bool IsNotified { get; set; }
    
    /// <summary>
    /// 通知时间
    /// </summary>
    public DateTime? NotifiedAt { get; set; }
    
    /// <summary>
    /// 解决者
    /// </summary>
    public string? ResolvedBy { get; set; }
    
    /// <summary>
    /// 解决方案描述
    /// </summary>
    public string? Resolution { get; set; }
    
    /// <summary>
    /// 是否为误报
    /// </summary>
    public bool IsFalsePositive { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }
}