using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.System;

/// <summary>
/// 客户用户会话
/// </summary>
public class CustomerUserSession : Entity
{
    public Guid CustomerUserId { get; private set; }
    public string SessionToken { get; private set; }
    public string? RefreshToken { get; private set; }
    public DateTime ExpiresAt { get; private set; }
    public DateTime? RefreshTokenExpiresAt { get; private set; }
    public string IpAddress { get; private set; }
    public string UserAgent { get; private set; }
    public string? DeviceId { get; private set; }
    public string? DeviceName { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime LastActivityAt { get; private set; }
    
    private CustomerUserSession() : base() { }
    
    public CustomerUserSession(
        Guid customerUserId,
        string sessionToken,
        string ipAddress,
        string userAgent,
        DateTime expiresAt,
        string? refreshToken = null,
        DateTime? refreshTokenExpiresAt = null,
        string? deviceId = null,
        string? deviceName = null) : base()
    {
        CustomerUserId = customerUserId;
        SessionToken = sessionToken;
        RefreshToken = refreshToken;
        ExpiresAt = expiresAt;
        RefreshTokenExpiresAt = refreshTokenExpiresAt;
        IpAddress = ipAddress;
        UserAgent = userAgent;
        DeviceId = deviceId;
        DeviceName = deviceName;
        IsActive = true;
        LastActivityAt = DateTime.UtcNow;
    }
    
    public void UpdateActivity()
    {
        LastActivityAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void RefreshSession(string newSessionToken, DateTime newExpiresAt)
    {
        SessionToken = newSessionToken;
        ExpiresAt = newExpiresAt;
        LastActivityAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void InvalidateSession()
    {
        IsActive = false;
        UpdateTimestamp();
    }
    
    public bool IsExpired()
    {
        return DateTime.UtcNow > ExpiresAt || !IsActive;
    }
}