using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.System;

/// <summary>
/// 管理员用户会话
/// </summary>
public class AdminUserSession : Entity
{
    public Guid AdminUserId { get; private set; }
    public string SessionToken { get; private set; }
    public string? RefreshToken { get; private set; }
    public DateTime ExpiresAt { get; private set; }
    public DateTime? RefreshTokenExpiresAt { get; private set; }
    public string IpAddress { get; private set; }
    public string UserAgent { get; private set; }
    public string? DeviceId { get; private set; }
    public string? DeviceName { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime LastActivityAt { get; private set; }
    public bool RequiresMfa { get; private set; }
    public bool MfaVerified { get; private set; }
    public DateTime? MfaVerifiedAt { get; private set; }
    
    // 地理位置信息
    public string? City { get; private set; }
    public string? Country { get; private set; }
    public string? Region { get; private set; }
    public double? Latitude { get; private set; }
    public double? Longitude { get; private set; }
    public bool IsAnomalousLocation { get; private set; }
    
    private AdminUserSession() : base() { }
    
    public AdminUserSession(
        Guid adminUserId,
        string sessionToken,
        string ipAddress,
        string userAgent,
        DateTime expiresAt,
        bool requiresMfa = false,
        string? refreshToken = null,
        DateTime? refreshTokenExpiresAt = null,
        string? deviceId = null,
        string? deviceName = null) : base()
    {
        AdminUserId = adminUserId;
        SessionToken = sessionToken;
        RefreshToken = refreshToken;
        ExpiresAt = expiresAt;
        RefreshTokenExpiresAt = refreshTokenExpiresAt;
        IpAddress = ipAddress;
        UserAgent = userAgent;
        DeviceId = deviceId;
        DeviceName = deviceName;
        IsActive = true;
        LastActivityAt = DateTime.UtcNow;
        RequiresMfa = requiresMfa;
        MfaVerified = false;
    }
    
    public void UpdateActivity()
    {
        LastActivityAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void RefreshSession(string newSessionToken, DateTime newExpiresAt)
    {
        SessionToken = newSessionToken;
        ExpiresAt = newExpiresAt;
        LastActivityAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void VerifyMfa()
    {
        MfaVerified = true;
        MfaVerifiedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void InvalidateSession()
    {
        IsActive = false;
        UpdateTimestamp();
    }
    
    public bool IsExpired()
    {
        return DateTime.UtcNow > ExpiresAt || !IsActive;
    }
    
    public bool IsFullyAuthenticated()
    {
        return IsActive && !IsExpired() && (!RequiresMfa || MfaVerified);
    }
    
    public void UpdateLocation(string? city, string? country, string? region, double? latitude, double? longitude, bool isAnomalous = false)
    {
        City = city;
        Country = country;
        Region = region;
        Latitude = latitude;
        Longitude = longitude;
        IsAnomalousLocation = isAnomalous;
        UpdateTimestamp();
    }
}