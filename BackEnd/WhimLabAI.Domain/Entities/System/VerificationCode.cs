using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.System;

/// <summary>
/// 验证码
/// </summary>
public class VerificationCode : Entity
{
    public string Recipient { get; private set; } // Email or Phone
    public string Code { get; private set; }
    public string Type { get; private set; } // Login, Register, ResetPassword, etc.
    public DateTime ExpiresAt { get; private set; }
    public bool IsUsed { get; private set; }
    public DateTime? UsedAt { get; private set; }
    public int AttemptCount { get; private set; }
    public string? IpAddress { get; private set; }
    public string? UserAgent { get; private set; }
    
    private VerificationCode() : base() { }
    
    public VerificationCode(
        string recipient,
        string code,
        string type,
        int expirationMinutes = 10,
        string? ipAddress = null,
        string? userAgent = null) : base()
    {
        Recipient = recipient;
        Code = code;
        Type = type;
        ExpiresAt = DateTime.UtcNow.AddMinutes(expirationMinutes);
        IsUsed = false;
        AttemptCount = 0;
        IpAddress = ipAddress;
        UserAgent = userAgent;
    }
    
    public bool Verify(string inputCode)
    {
        AttemptCount++;
        UpdateTimestamp();
        
        if (IsUsed || IsExpired() || AttemptCount > 5)
        {
            return false;
        }
        
        if (Code == inputCode)
        {
            IsUsed = true;
            UsedAt = DateTime.UtcNow;
            return true;
        }
        
        return false;
    }
    
    public bool IsExpired()
    {
        return DateTime.UtcNow > ExpiresAt;
    }
    
    public void Invalidate()
    {
        IsUsed = true;
        UpdateTimestamp();
    }
}