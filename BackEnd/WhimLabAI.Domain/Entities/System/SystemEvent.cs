using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.System;

/// <summary>
/// 系统事件
/// </summary>
public class SystemEvent : Entity
{
    public string EventType { get; private set; }
    public string EventName { get; private set; }
    public Dictionary<string, object> EventData { get; private set; }
    public DateTime OccurredAt { get; private set; }
    public string? SourceSystem { get; private set; }
    public string? CorrelationId { get; private set; }
    
    private SystemEvent() : base() 
    {
        EventData = new Dictionary<string, object>();
    }
    
    public SystemEvent(
        string eventType,
        string eventName,
        Dictionary<string, object>? eventData = null,
        string? sourceSystem = null,
        string? correlationId = null) : base()
    {
        EventType = eventType;
        EventName = eventName;
        EventData = eventData ?? new Dictionary<string, object>();
        OccurredAt = DateTime.UtcNow;
        SourceSystem = sourceSystem ?? "WhimLabAI";
        CorrelationId = correlationId;
    }
    
    public void AddEventData(string key, object value)
    {
        EventData[key] = value;
        UpdateTimestamp();
    }
}