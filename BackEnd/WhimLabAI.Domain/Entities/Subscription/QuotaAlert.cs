using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Subscription;

public class QuotaAlert : Entity
{
    public Guid SubscriptionId { get; private set; }
    public Guid UserId { get; private set; }
    public AlertType Type { get; private set; }
    public double UsagePercentage { get; private set; }
    public int UsedTokens { get; private set; }
    public int TotalTokens { get; private set; }
    public DateTime AlertedAt { get; private set; }
    public bool IsNotified { get; private set; }
    public DateTime? NotifiedAt { get; private set; }
    public string? NotificationMethod { get; private set; }
    
    public Subscription Subscription { get; private set; } = null!;
    
    private QuotaAlert() : base()
    {
    }
    
    public QuotaAlert(
        Guid subscriptionId,
        Guid userId,
        AlertType type,
        double usagePercentage,
        int usedTokens,
        int totalTokens) : base()
    {
        SubscriptionId = subscriptionId;
        UserId = userId;
        Type = type;
        UsagePercentage = usagePercentage;
        UsedTokens = usedTokens;
        TotalTokens = totalTokens;
        AlertedAt = DateTime.UtcNow;
        IsNotified = false;
    }
    
    public void MarkAsNotified(string notificationMethod)
    {
        IsNotified = true;
        NotifiedAt = DateTime.UtcNow;
        NotificationMethod = notificationMethod;
        UpdateTimestamp();
    }
    
    public bool ShouldNotify(double currentPercentage)
    {
        // Don't notify if already notified
        if (IsNotified)
            return false;
            
        // Don't notify if usage has decreased
        if (currentPercentage < UsagePercentage)
            return false;
            
        // Check if enough time has passed since alert
        var timeSinceAlert = DateTime.UtcNow - AlertedAt;
        
        return Type switch
        {
            AlertType.QuotaLow => timeSinceAlert.TotalHours >= 24, // Once per day
            AlertType.QuotaExhausted => timeSinceAlert.TotalMinutes >= 1, // Immediate
            AlertType.QuotaCritical => timeSinceAlert.TotalHours >= 6, // Every 6 hours
            _ => false
        };
    }
}

public enum AlertType
{
    QuotaLow,       // 80% used
    QuotaCritical,  // 90% used
    QuotaExhausted  // 100% used
}