using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.Subscription;

public class SubscriptionPlan : Entity
{
    private readonly List<string> _features = new();
    private readonly Dictionary<string, object> _limits = new();
    
    public string Name { get; private set; }
    public SubscriptionTier Tier { get; private set; }
    public string? Description { get; private set; }
    public Money Price { get; private set; }
    public BillingCycle BillingCycle { get; private set; }
    public int TokenQuota { get; private set; }
    public int MonthlyTokens => TokenQuota; // Alias for TokenQuota
    public bool IsActive { get; private set; }
    public bool IsPopular { get; private set; }
    public int SortOrder { get; private set; }
    public DateTime? ValidFrom { get; private set; }
    public DateTime? ValidTo { get; private set; }
    public int MaxAgents { get; private set; }
    public int MaxConversationsPerDay { get; private set; }
    public bool AllowCustomAgents { get; private set; }
    public bool AllowCustomAgent => AllowCustomAgents; // Alias for compatibility
    public bool AllowPlugins { get; private set; }
    public bool AllowKnowledgeBase { get; private set; }
    public int DurationDays => BillingCycle switch
    {
        BillingCycle.Monthly => 30,
        BillingCycle.Quarterly => 90,
        BillingCycle.SemiAnnual => 180,
        BillingCycle.Annual => 365,
        _ => 30
    };
    
    public IReadOnlyCollection<string> Features => _features.AsReadOnly();
    public IReadOnlyDictionary<string, object> Limits => _limits;
    
    private SubscriptionPlan() : base()
    {
    }
    
    public SubscriptionPlan(
        string name,
        SubscriptionTier tier,
        Money price,
        int tokenQuota,
        BillingCycle billingCycle = BillingCycle.Monthly,
        string? description = null) : base()
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Tier = tier;
        Price = price ?? throw new ArgumentNullException(nameof(price));
        TokenQuota = tokenQuota;
        BillingCycle = billingCycle;
        Description = description;
        IsActive = true;
        IsPopular = false;
        SortOrder = (int)tier;
        
        // Set default limits based on tier
        SetDefaultLimits();
    }
    
    public void Update(
        string name,
        string? description,
        Money price,
        int tokenQuota)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description;
        Price = price ?? throw new ArgumentNullException(nameof(price));
        TokenQuota = tokenQuota;
        UpdateTimestamp();
    }
    
    public void SetFeatures(List<string> features)
    {
        _features.Clear();
        if (features != null)
        {
            _features.AddRange(features.Where(f => !string.IsNullOrWhiteSpace(f)).Distinct());
        }
        UpdateTimestamp();
    }
    
    public void AddFeature(string feature)
    {
        if (!string.IsNullOrWhiteSpace(feature) && !_features.Contains(feature))
        {
            _features.Add(feature);
            UpdateTimestamp();
        }
    }
    
    public void RemoveFeature(string feature)
    {
        if (_features.Remove(feature))
        {
            UpdateTimestamp();
        }
    }
    
    public void SetLimit(string key, object value)
    {
        _limits[key] = value;
        UpdateTimestamp();
    }
    
    public void RemoveLimit(string key)
    {
        if (_limits.Remove(key))
        {
            UpdateTimestamp();
        }
    }
    
    public void SetAdvancedLimits(
        int maxAgents,
        int maxConversationsPerDay,
        bool allowCustomAgents,
        bool allowPlugins,
        bool allowKnowledgeBase)
    {
        MaxAgents = maxAgents;
        MaxConversationsPerDay = maxConversationsPerDay;
        AllowCustomAgents = allowCustomAgents;
        AllowPlugins = allowPlugins;
        AllowKnowledgeBase = allowKnowledgeBase;
        UpdateTimestamp();
    }
    
    public void Activate()
    {
        IsActive = true;
        UpdateTimestamp();
    }
    
    public void Deactivate()
    {
        IsActive = false;
        IsPopular = false;
        UpdateTimestamp();
    }
    
    public void MarkAsPopular()
    {
        IsPopular = true;
        UpdateTimestamp();
    }
    
    public void UnmarkAsPopular()
    {
        IsPopular = false;
        UpdateTimestamp();
    }
    
    public void SetSortOrder(int sortOrder)
    {
        SortOrder = sortOrder;
        UpdateTimestamp();
    }
    
    public void SetValidityPeriod(DateTime? validFrom, DateTime? validTo)
    {
        if (validFrom.HasValue && validTo.HasValue && validFrom.Value > validTo.Value)
            throw new ArgumentException("有效期开始时间不能晚于结束时间");
            
        ValidFrom = validFrom;
        ValidTo = validTo;
        UpdateTimestamp();
    }
    
    public bool IsValidNow()
    {
        var now = DateTime.UtcNow;
        
        if (ValidFrom.HasValue && now < ValidFrom.Value)
            return false;
            
        if (ValidTo.HasValue && now > ValidTo.Value)
            return false;
            
        return IsActive;
    }
    
    public Money CalculatePrice(int months)
    {
        if (months < 1)
            throw new ArgumentException("月数必须大于0", nameof(months));
            
        // Apply discounts for longer subscriptions
        var discount = months switch
        {
            >= 12 => 0.15m, // 15% discount for yearly
            >= 6 => 0.10m,  // 10% discount for half-yearly
            >= 3 => 0.05m,  // 5% discount for quarterly
            _ => 0m
        };
        
        var totalPrice = Price.Multiply(months);
        return totalPrice.ApplyDiscount(discount * 100);
    }
    
    private void SetDefaultLimits()
    {
        switch (Tier)
        {
            case SubscriptionTier.Free:
                MaxAgents = 3;
                MaxConversationsPerDay = 10;
                AllowCustomAgents = false;
                AllowPlugins = false;
                AllowKnowledgeBase = false;
                break;
                
            case SubscriptionTier.Basic:
                MaxAgents = 10;
                MaxConversationsPerDay = 50;
                AllowCustomAgents = true;
                AllowPlugins = false;
                AllowKnowledgeBase = false;
                break;
                
            case SubscriptionTier.Pro:
                MaxAgents = 50;
                MaxConversationsPerDay = 200;
                AllowCustomAgents = true;
                AllowPlugins = true;
                AllowKnowledgeBase = true;
                break;
                
            case SubscriptionTier.Ultra:
                MaxAgents = -1; // Unlimited
                MaxConversationsPerDay = -1; // Unlimited
                AllowCustomAgents = true;
                AllowPlugins = true;
                AllowKnowledgeBase = true;
                break;
        }
        
        _limits["MaxAgents"] = MaxAgents;
        _limits["MaxConversationsPerDay"] = MaxConversationsPerDay;
        _limits["AllowCustomAgents"] = AllowCustomAgents;
        _limits["AllowPlugins"] = AllowPlugins;
        _limits["AllowKnowledgeBase"] = AllowKnowledgeBase;
    }
}