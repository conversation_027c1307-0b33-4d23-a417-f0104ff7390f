using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Subscription;

public class TokenUsage : Entity
{
    public Guid SubscriptionId { get; private set; }
    public int Tokens { get; private set; }
    public DateTime UsedAt { get; private set; }
    public Guid? AgentId { get; private set; }
    public Guid? ConversationId { get; private set; }
    public string? MessageId { get; private set; }
    public string? Model { get; private set; }
    public UsageType Type { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    
    public Subscription Subscription { get; private set; } = null!;
    
    private TokenUsage() : base()
    {
        Metadata = new Dictionary<string, object>();
    }
    
    public TokenUsage(
        Guid subscriptionId,
        int tokens,
        Guid? agentId = null,
        Guid? conversationId = null,
        string? messageId = null,
        string? model = null,
        UsageType type = UsageType.Conversation,
        Dictionary<string, object>? metadata = null) : base()
    {
        SubscriptionId = subscriptionId;
        Tokens = tokens;
        UsedAt = DateTime.UtcNow;
        AgentId = agentId;
        ConversationId = conversationId;
        MessageId = messageId;
        Model = model;
        Type = type;
        Metadata = metadata ?? new Dictionary<string, object>();
    }
    
    public void SetMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdateTimestamp();
    }
    
    public void RemoveMetadata(string key)
    {
        if (Metadata.Remove(key))
        {
            UpdateTimestamp();
        }
    }
}

public enum UsageType
{
    Conversation,
    AgentTest,
    KnowledgeBaseIndexing,
    ImageGeneration,
    AudioTranscription,
    Other
}