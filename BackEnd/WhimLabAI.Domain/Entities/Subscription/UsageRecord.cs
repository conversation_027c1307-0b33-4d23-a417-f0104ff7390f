using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Subscription;

public class UsageRecord : Entity
{
    public Guid SubscriptionId { get; private set; }
    public Guid UserId { get; private set; }
    public Guid ConversationId { get; private set; }
    public Guid AgentId { get; private set; }
    public string ModelName { get; private set; }
    public string ModelProvider { get; private set; }
    public int TokensUsed { get; private set; }
    public decimal CostAmount { get; private set; }
    public DateTime UsageTime { get; private set; }
    public string? RequestId { get; private set; }
    public string? SessionId { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    
    public Subscription? Subscription { get; private set; }
    
    private UsageRecord() : base()
    {
        ModelName = string.Empty;
        ModelProvider = string.Empty;
        Metadata = new Dictionary<string, object>();
    }
    
    public UsageRecord(
        Guid subscriptionId,
        Guid userId,
        Guid conversationId,
        Guid agentId,
        string modelName,
        string modelProvider,
        int tokensUsed,
        decimal costAmount = 0) : base()
    {
        SubscriptionId = subscriptionId;
        UserId = userId;
        ConversationId = conversationId;
        AgentId = agentId;
        ModelName = modelName ?? throw new ArgumentNullException(nameof(modelName));
        ModelProvider = modelProvider ?? throw new ArgumentNullException(nameof(modelProvider));
        TokensUsed = tokensUsed;
        CostAmount = costAmount;
        UsageTime = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();
    }
    
    public void SetRequestId(string requestId)
    {
        RequestId = requestId;
        UpdateTimestamp();
    }
    
    public void SetSessionId(string sessionId)
    {
        SessionId = sessionId;
        UpdateTimestamp();
    }
    
    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdateTimestamp();
    }
}