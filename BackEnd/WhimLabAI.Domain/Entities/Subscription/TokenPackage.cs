using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Subscription;

/// <summary>
/// Token包实体 - 用于一次性购买额外Token
/// </summary>
public class TokenPackage : AggregateRoot
{
    public string Name { get; private set; }
    public string? Description { get; private set; }
    public int TokenAmount { get; private set; }
    public decimal Price { get; private set; }
    public decimal? OriginalPrice { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsLimited { get; private set; }
    public DateTime? ValidFrom { get; private set; }
    public DateTime? ValidTo { get; private set; }
    public int? MaxPurchasePerUser { get; private set; }
    public int? TotalStock { get; private set; }
    public int SoldCount { get; private set; }
    public int SortOrder { get; private set; }
    public string Features { get; private set; }
    
    private TokenPackage() : base()
    {
        Name = string.Empty;
        Features = string.Empty;
    }
    
    public TokenPackage(
        string name,
        int tokenAmount,
        decimal price,
        string? description = null,
        decimal? originalPrice = null,
        bool isLimited = false,
        int sortOrder = 0) : base()
    {
        SetName(name);
        SetTokenAmount(tokenAmount);
        SetPrice(price);
        Description = description;
        OriginalPrice = originalPrice;
        IsActive = true;
        IsLimited = isLimited;
        SoldCount = 0;
        SortOrder = sortOrder;
        Features = string.Empty;
    }
    
    public void Update(
        string name,
        string? description,
        decimal price,
        decimal? originalPrice,
        int sortOrder)
    {
        SetName(name);
        Description = description;
        SetPrice(price);
        OriginalPrice = originalPrice;
        SortOrder = sortOrder;
        UpdateTimestamp();
    }
    
    public void SetLimitedTimeOffer(DateTime validFrom, DateTime validTo)
    {
        if (validFrom >= validTo)
            throw new ArgumentException("有效开始时间必须早于结束时间");
            
        IsLimited = true;
        ValidFrom = validFrom;
        ValidTo = validTo;
        UpdateTimestamp();
    }
    
    public void SetStockLimit(int totalStock)
    {
        if (totalStock <= 0)
            throw new ArgumentException("库存数量必须大于0");
            
        if (totalStock < SoldCount)
            throw new ArgumentException("库存数量不能小于已售出数量");
            
        TotalStock = totalStock;
        UpdateTimestamp();
    }
    
    public void SetPurchaseLimit(int maxPurchasePerUser)
    {
        if (maxPurchasePerUser <= 0)
            throw new ArgumentException("每用户购买限制必须大于0");
            
        MaxPurchasePerUser = maxPurchasePerUser;
        UpdateTimestamp();
    }
    
    public void SetFeatures(List<string> features)
    {
        Features = string.Join(",", features);
        UpdateTimestamp();
    }
    
    public List<string> GetFeatures()
    {
        return string.IsNullOrEmpty(Features) 
            ? new List<string>() 
            : Features.Split(',').ToList();
    }
    
    public void Activate()
    {
        IsActive = true;
        UpdateTimestamp();
    }
    
    public void Deactivate()
    {
        IsActive = false;
        UpdateTimestamp();
    }
    
    public bool IsAvailable()
    {
        if (!IsActive)
            return false;
            
        var now = DateTime.UtcNow;
        
        if (IsLimited)
        {
            if (ValidFrom.HasValue && now < ValidFrom.Value)
                return false;
                
            if (ValidTo.HasValue && now > ValidTo.Value)
                return false;
        }
        
        if (TotalStock.HasValue && SoldCount >= TotalStock.Value)
            return false;
            
        return true;
    }
    
    public void IncrementSoldCount()
    {
        SoldCount++;
        UpdateTimestamp();
    }
    
    public int? GetRemainingStock()
    {
        if (!TotalStock.HasValue)
            return null;
            
        return Math.Max(0, TotalStock.Value - SoldCount);
    }
    
    public decimal GetPricePerThousandTokens()
    {
        if (TokenAmount == 0)
            return 0;
            
        return Math.Round((Price * 1000m) / TokenAmount, 2);
    }
    
    private void SetName(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("包名称不能为空");
            
        Name = name;
    }
    
    private void SetTokenAmount(int tokenAmount)
    {
        if (tokenAmount <= 0)
            throw new ArgumentException("Token数量必须大于0");
            
        TokenAmount = tokenAmount;
    }
    
    private void SetPrice(decimal price)
    {
        if (price < 0)
            throw new ArgumentException("价格不能为负数");
            
        Price = price;
    }
}