using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.ApiKey;

/// <summary>
/// API密钥使用记录
/// </summary>
public class ApiKeyUsage : Entity
{
    /// <summary>
    /// API密钥ID
    /// </summary>
    public Guid ApiKeyId { get; private set; }
    
    /// <summary>
    /// 关联的API密钥
    /// </summary>
    public ApiKey ApiKey { get; private set; } = null!;
    
    /// <summary>
    /// 请求路径
    /// </summary>
    public string RequestPath { get; private set; } = string.Empty;
    
    /// <summary>
    /// 请求方法
    /// </summary>
    public string RequestMethod { get; private set; } = string.Empty;
    
    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress { get; private set; } = string.Empty;
    
    /// <summary>
    /// 用户代理
    /// </summary>
    public string? UserAgent { get; private set; }
    
    /// <summary>
    /// 来源域名
    /// </summary>
    public string? Origin { get; private set; }
    
    /// <summary>
    /// 响应状态码
    /// </summary>
    public int ResponseStatusCode { get; private set; }
    
    /// <summary>
    /// 响应时间（毫秒）
    /// </summary>
    public long ResponseTime { get; private set; }
    
    /// <summary>
    /// 请求大小（字节）
    /// </summary>
    public long? RequestSize { get; private set; }
    
    /// <summary>
    /// 响应大小（字节）
    /// </summary>
    public long? ResponseSize { get; private set; }
    
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; private set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; private set; }
    
    /// <summary>
    /// 使用时间（用于统计）
    /// </summary>
    public DateTime UsedAt { get; private set; }

    /// <summary>
    /// 创建使用记录
    /// </summary>
    public static ApiKeyUsage Create(
        Guid apiKeyId,
        string requestPath,
        string requestMethod,
        string ipAddress,
        int statusCode,
        long responseTime)
    {
        return new ApiKeyUsage
        {
            Id = Guid.NewGuid(),
            ApiKeyId = apiKeyId,
            RequestPath = requestPath,
            RequestMethod = requestMethod,
            IpAddress = ipAddress,
            ResponseStatusCode = statusCode,
            ResponseTime = responseTime,
            IsSuccess = statusCode >= 200 && statusCode < 300,
            UsedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 设置请求详情
    /// </summary>
    public void SetRequestDetails(string? userAgent, string? origin, long? requestSize)
    {
        UserAgent = userAgent;
        Origin = origin;
        RequestSize = requestSize;
    }

    /// <summary>
    /// 设置响应详情
    /// </summary>
    public void SetResponseDetails(long? responseSize, string? errorMessage = null)
    {
        ResponseSize = responseSize;
        ErrorMessage = errorMessage;
    }
}