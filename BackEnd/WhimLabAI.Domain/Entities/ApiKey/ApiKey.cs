using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.ApiKey;

/// <summary>
/// API密钥实体
/// </summary>
public class ApiKey : Entity
{
    /// <summary>
    /// 密钥名称
    /// </summary>
    public string Name { get; private set; } = string.Empty;
    
    /// <summary>
    /// 密钥值（哈希存储）
    /// </summary>
    public string KeyHash { get; private set; } = string.Empty;
    
    /// <summary>
    /// 密钥盐值（用于PBKDF2算法）
    /// </summary>
    public string? KeySalt { get; private set; }
    
    /// <summary>
    /// 密钥前缀（用于识别，如: wak_）
    /// </summary>
    public string KeyPrefix { get; private set; } = string.Empty;
    
    /// <summary>
    /// 密钥后4位（用于显示）
    /// </summary>
    public string KeySuffix { get; private set; } = string.Empty;
    
    /// <summary>
    /// 所属客户用户ID（当UserType为Customer时）
    /// </summary>
    public Guid? CustomerUserId { get; private set; }
    
    /// <summary>
    /// 所属管理员用户ID（当UserType为Admin时）
    /// </summary>
    public Guid? AdminUserId { get; private set; }
    
    /// <summary>
    /// 用户类型 (Customer/Admin/System)
    /// </summary>
    public string UserType { get; private set; } = string.Empty;
    
    /// <summary>
    /// 密钥类型 (Development/Production)
    /// </summary>
    public string KeyType { get; private set; } = "Development";
    
    /// <summary>
    /// 密钥作用域
    /// </summary>
    public List<string> Scopes { get; private set; } = new();
    
    /// <summary>
    /// IP白名单（空表示不限制）
    /// </summary>
    public List<string> IpWhitelist { get; private set; } = new();
    
    /// <summary>
    /// 允许的域名（空表示不限制）
    /// </summary>
    public List<string> AllowedDomains { get; private set; } = new();
    
    /// <summary>
    /// 速率限制（每分钟请求数）
    /// </summary>
    public int RateLimit { get; private set; } = 100;
    
    /// <summary>
    /// 每日请求配额
    /// </summary>
    public int? DailyQuota { get; private set; }
    
    /// <summary>
    /// 每月请求配额
    /// </summary>
    public int? MonthlyQuota { get; private set; }
    
    /// <summary>
    /// 是否活跃
    /// </summary>
    public bool IsActive { get; private set; } = true;
    
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; private set; }
    
    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; private set; }
    
    /// <summary>
    /// 最后使用的IP
    /// </summary>
    public string? LastUsedIp { get; private set; }
    
    /// <summary>
    /// 总使用次数
    /// </summary>
    public long TotalUsageCount { get; private set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; private set; }
    
    /// <summary>
    /// 元数据（JSON格式）
    /// </summary>
    public string? Metadata { get; private set; }

    /// <summary>
    /// 创建客户用户的API密钥
    /// </summary>
    public static (ApiKey apiKey, string rawKey) CreateForCustomerUser(
        string name,
        Guid customerUserId,
        string keyType = "Development",
        DateTime? expiresAt = null)
    {
        // 生成随机密钥
        var rawKey = GenerateApiKey();
        var keyHash = HashApiKey(rawKey);
        
        var apiKey = new ApiKey
        {
            Id = Guid.NewGuid(),
            Name = name,
            KeyHash = keyHash,
            KeyPrefix = "wak_",
            KeySuffix = rawKey.Substring(rawKey.Length - 4),
            CustomerUserId = customerUserId,
            UserType = "Customer",
            KeyType = keyType,
            ExpiresAt = expiresAt,
            IsActive = true
        };

        return (apiKey, rawKey);
    }
    
    /// <summary>
    /// 创建管理员用户的API密钥
    /// </summary>
    public static (ApiKey apiKey, string rawKey) CreateForAdminUser(
        string name,
        Guid adminUserId,
        string keyType = "Development",
        DateTime? expiresAt = null)
    {
        // 生成随机密钥
        var rawKey = GenerateApiKey();
        var keyHash = HashApiKey(rawKey);
        
        var apiKey = new ApiKey
        {
            Id = Guid.NewGuid(),
            Name = name,
            KeyHash = keyHash,
            KeyPrefix = "wak_",
            KeySuffix = rawKey.Substring(rawKey.Length - 4),
            AdminUserId = adminUserId,
            UserType = "Admin",
            KeyType = keyType,
            ExpiresAt = expiresAt,
            IsActive = true
        };

        return (apiKey, rawKey);
    }
    
    /// <summary>
    /// 创建系统API密钥
    /// </summary>
    public static (ApiKey apiKey, string rawKey) CreateForSystem(
        string name,
        string keyType = "Production",
        DateTime? expiresAt = null)
    {
        // 生成随机密钥
        var rawKey = GenerateApiKey();
        var keyHash = HashApiKey(rawKey);
        
        var apiKey = new ApiKey
        {
            Id = Guid.NewGuid(),
            Name = name,
            KeyHash = keyHash,
            KeyPrefix = "wak_",
            KeySuffix = rawKey.Substring(rawKey.Length - 4),
            UserType = "System",
            KeyType = keyType,
            ExpiresAt = expiresAt,
            IsActive = true
        };

        return (apiKey, rawKey);
    }

    /// <summary>
    /// 设置作用域
    /// </summary>
    public void SetScopes(params string[] scopes)
    {
        Scopes.Clear();
        Scopes.AddRange(scopes);
        UpdateTimestamp();
    }

    /// <summary>
    /// 设置IP白名单
    /// </summary>
    public void SetIpWhitelist(params string[] ips)
    {
        IpWhitelist.Clear();
        IpWhitelist.AddRange(ips);
        UpdateTimestamp();
    }

    /// <summary>
    /// 设置允许的域名
    /// </summary>
    public void SetAllowedDomains(params string[] domains)
    {
        AllowedDomains.Clear();
        AllowedDomains.AddRange(domains);
        UpdateTimestamp();
    }

    /// <summary>
    /// 设置速率限制
    /// </summary>
    public void SetRateLimit(int rateLimit, int? dailyQuota = null, int? monthlyQuota = null)
    {
        if (rateLimit <= 0)
            throw new ArgumentException("Rate limit must be greater than 0");

        RateLimit = rateLimit;
        DailyQuota = dailyQuota;
        MonthlyQuota = monthlyQuota;
        UpdateTimestamp();
    }

    /// <summary>
    /// 记录使用
    /// </summary>
    public void RecordUsage(string ipAddress)
    {
        LastUsedAt = DateTime.UtcNow;
        LastUsedIp = ipAddress;
        TotalUsageCount++;
        UpdateTimestamp();
    }

    /// <summary>
    /// 停用密钥
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
        UpdateTimestamp();
    }

    /// <summary>
    /// 激活密钥
    /// </summary>
    public void Activate()
    {
        IsActive = true;
        UpdateTimestamp();
    }

    /// <summary>
    /// 更新过期时间
    /// </summary>
    public void UpdateExpiration(DateTime? expiresAt)
    {
        ExpiresAt = expiresAt;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 更新基本信息
    /// </summary>
    public void UpdateInfo(string? name = null, string? description = null)
    {
        if (!string.IsNullOrWhiteSpace(name))
            Name = name;
            
        if (description != null)
            Description = description;
            
        UpdateTimestamp();
    }

    /// <summary>
    /// 是否过期
    /// </summary>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
    }

    /// <summary>
    /// 验证IP是否允许
    /// </summary>
    public bool IsIpAllowed(string ipAddress)
    {
        if (!IpWhitelist.Any())
            return true;

        return IpWhitelist.Contains(ipAddress);
    }

    /// <summary>
    /// 验证域名是否允许
    /// </summary>
    public bool IsDomainAllowed(string domain)
    {
        if (!AllowedDomains.Any())
            return true;

        return AllowedDomains.Any(d => 
            domain.Equals(d, StringComparison.OrdinalIgnoreCase) ||
            domain.EndsWith($".{d}", StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 验证作用域
    /// </summary>
    public bool HasScope(string scope)
    {
        if (!Scopes.Any())
            return true;

        return Scopes.Contains(scope, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 生成API密钥
    /// </summary>
    private static string GenerateApiKey()
    {
        // 使用加密安全的随机数生成器
        using var rng = global::System.Security.Cryptography.RandomNumberGenerator.Create();
        var keyBytes = new byte[32];
        rng.GetBytes(keyBytes);
        
        // 使用URL安全的Base64编码
        var key = Convert.ToBase64String(keyBytes)
            .Replace('+', '-')
            .Replace('/', '_')
            .Replace("=", "");
        
        return "wak_" + key;
    }

    /// <summary>
    /// 哈希API密钥
    /// </summary>
    /// <remarks>
    /// 注意：由于需要在Domain层保持简单，这里仍使用SHA256。
    /// 在实际使用时，建议通过Infrastructure层的ApiKeyCryptoHelper使用PBKDF2算法。
    /// </remarks>
    private static string HashApiKey(string rawKey)
    {
        using var sha256 = global::System.Security.Cryptography.SHA256.Create();
        var bytes = global::System.Text.Encoding.UTF8.GetBytes(rawKey);
        var hash = sha256.ComputeHash(bytes);
        return Convert.ToBase64String(hash);
    }

    /// <summary>
    /// 验证API密钥
    /// </summary>
    /// <remarks>
    /// 注意：这是简化版本。在生产环境中，应使用Infrastructure层的ApiKeyCryptoHelper.VerifyApiKey方法。
    /// </remarks>
    public static bool VerifyApiKey(string rawKey, string keyHash)
    {
        var computedHash = HashApiKey(rawKey);
        // 使用固定时间比较防止时序攻击
        return global::System.Security.Cryptography.CryptographicOperations.FixedTimeEquals(
            Convert.FromBase64String(computedHash),
            Convert.FromBase64String(keyHash)
        );
    }
}