using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;

namespace WhimLabAI.Domain.Entities.Finance;

/// <summary>
/// 发票明细项
/// </summary>
public class InvoiceItem : Entity
{
    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; private set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public decimal Quantity { get; private set; }
    
    /// <summary>
    /// 单价
    /// </summary>
    public Money UnitPrice { get; private set; }
    
    /// <summary>
    /// 税率（例如：0.06 表示 6%）
    /// </summary>
    public decimal TaxRate { get; private set; }
    
    /// <summary>
    /// 金额（数量 × 单价）
    /// </summary>
    public Money Amount { get; private set; }
    
    /// <summary>
    /// 税额
    /// </summary>
    public Money TaxAmount { get; private set; }
    
    /// <summary>
    /// 总金额（金额 + 税额）
    /// </summary>
    public Money TotalAmount { get; private set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; private set; }
    
    private InvoiceItem() { }
    
    public InvoiceItem(
        string description,
        decimal quantity,
        Money unitPrice,
        decimal taxRate = 0.06m)
    {
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("描述不能为空", nameof(description));
            
        if (quantity <= 0)
            throw new ArgumentException("数量必须大于0", nameof(quantity));
            
        if (unitPrice.Amount < 0)
            throw new ArgumentException("单价不能为负数", nameof(unitPrice));
            
        if (taxRate < 0 || taxRate > 1)
            throw new ArgumentException("税率必须在0-1之间", nameof(taxRate));
        
        Description = description;
        Quantity = quantity;
        UnitPrice = unitPrice;
        TaxRate = taxRate;
        
        // 计算金额
        var amount = quantity * unitPrice.Amount;
        Amount = Money.Create(amount, unitPrice.Currency);
        
        // 计算税额
        var tax = amount * taxRate;
        TaxAmount = Money.Create(tax, unitPrice.Currency);
        
        // 计算总金额
        TotalAmount = Money.Create(amount + tax, unitPrice.Currency);
        
        CreatedAt = DateTime.UtcNow;
    }
    
    /// <summary>
    /// 更新数量
    /// </summary>
    public void UpdateQuantity(decimal quantity)
    {
        if (quantity <= 0)
            throw new ArgumentException("数量必须大于0", nameof(quantity));
            
        Quantity = quantity;
        
        // 重新计算金额
        var amount = quantity * UnitPrice.Amount;
        Amount = Money.Create(amount, UnitPrice.Currency);
        
        // 重新计算税额
        var tax = amount * TaxRate;
        TaxAmount = Money.Create(tax, UnitPrice.Currency);
        
        // 重新计算总金额
        TotalAmount = Money.Create(amount + tax, UnitPrice.Currency);
    }
}