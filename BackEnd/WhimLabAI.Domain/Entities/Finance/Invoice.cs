using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Domain.Events;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.Finance;

/// <summary>
/// 发票实体
/// </summary>
public class Invoice : AggregateRoot
{
    private readonly List<InvoiceItem> _items = new();
    
    /// <summary>
    /// 发票号
    /// </summary>
    public string InvoiceNumber { get; private set; }
    
    /// <summary>
    /// 客户用户ID
    /// </summary>
    public Guid CustomerUserId { get; private set; }
    
    /// <summary>
    /// 关联的订阅ID（如果有）
    /// </summary>
    public Guid? SubscriptionId { get; private set; }
    
    /// <summary>
    /// 关联的订单ID（如果有）
    /// </summary>
    public Guid? OrderId { get; private set; }
    
    /// <summary>
    /// 开票日期
    /// </summary>
    public DateTime IssueDate { get; private set; }
    
    /// <summary>
    /// 到期日期
    /// </summary>
    public DateTime DueDate { get; private set; }
    
    /// <summary>
    /// 支付日期
    /// </summary>
    public DateTime? PaidDate { get; private set; }
    
    /// <summary>
    /// 小计金额
    /// </summary>
    public Money SubtotalAmount { get; private set; }
    
    /// <summary>
    /// 折扣金额
    /// </summary>
    public Money? DiscountAmount { get; private set; }
    
    /// <summary>
    /// 税额
    /// </summary>
    public Money TaxAmount { get; private set; }
    
    /// <summary>
    /// 总金额
    /// </summary>
    public Money TotalAmount { get; private set; }
    
    /// <summary>
    /// 发票状态
    /// </summary>
    public InvoiceStatus Status { get; private set; }
    
    /// <summary>
    /// 支付方式
    /// </summary>
    public PaymentMethod? PaymentMethod { get; private set; }
    
    /// <summary>
    /// 支付交易ID
    /// </summary>
    public string? PaymentTransactionId { get; private set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; private set; }
    
    /// <summary>
    /// 公司信息
    /// </summary>
    public CompanyInfo? CompanyInfo { get; private set; }
    
    /// <summary>
    /// 发票明细项
    /// </summary>
    public IReadOnlyCollection<InvoiceItem> Items => _items.AsReadOnly();
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; private set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; private set; }
    
    /// <summary>
    /// 取消时间
    /// </summary>
    public DateTime? CancelledAt { get; private set; }
    
    /// <summary>
    /// 取消原因
    /// </summary>
    public string? CancellationReason { get; private set; }
    
    private Invoice() { }
    
    public Invoice(
        string invoiceNumber,
        Guid customerUserId,
        DateTime issueDate,
        DateTime dueDate,
        Money taxRate,
        Guid? subscriptionId = null,
        Guid? orderId = null,
        string? notes = null,
        CompanyInfo? companyInfo = null)
    {
        if (string.IsNullOrWhiteSpace(invoiceNumber))
            throw new ArgumentException("发票号不能为空", nameof(invoiceNumber));
            
        if (dueDate < issueDate)
            throw new ArgumentException("到期日期不能早于开票日期");
        
        InvoiceNumber = invoiceNumber;
        CustomerUserId = customerUserId;
        SubscriptionId = subscriptionId;
        OrderId = orderId;
        IssueDate = issueDate;
        DueDate = dueDate;
        Status = InvoiceStatus.Draft;
        Notes = notes;
        CompanyInfo = companyInfo;
        CreatedAt = DateTime.UtcNow;
        
        // 初始化金额
        SubtotalAmount = Money.Create(0, taxRate.Currency);
        TaxAmount = Money.Create(0, taxRate.Currency);
        TotalAmount = Money.Create(0, taxRate.Currency);
    }
    
    /// <summary>
    /// 添加发票项
    /// </summary>
    public void AddItem(
        string description,
        decimal quantity,
        Money unitPrice,
        decimal taxRate = 0.06m)
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException("只能向草稿状态的发票添加项目");
            
        var item = new InvoiceItem(
            description,
            quantity,
            unitPrice,
            taxRate);
            
        _items.Add(item);
        RecalculateAmounts();
        UpdatedAt = DateTime.UtcNow;
    }
    
    /// <summary>
    /// 移除发票项
    /// </summary>
    public void RemoveItem(Guid itemId)
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException("只能从草稿状态的发票移除项目");
            
        var item = _items.FirstOrDefault(i => i.Id == itemId);
        if (item != null)
        {
            _items.Remove(item);
            RecalculateAmounts();
            UpdatedAt = DateTime.UtcNow;
        }
    }
    
    /// <summary>
    /// 应用折扣
    /// </summary>
    public void ApplyDiscount(Money discountAmount)
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException("只能向草稿状态的发票应用折扣");
            
        if (discountAmount.Currency != SubtotalAmount.Currency)
            throw new ArgumentException("折扣货币必须与发票货币一致");
            
        if (discountAmount.Amount > SubtotalAmount.Amount)
            throw new ArgumentException("折扣金额不能超过小计金额");
            
        DiscountAmount = discountAmount;
        RecalculateAmounts();
        UpdatedAt = DateTime.UtcNow;
    }
    
    /// <summary>
    /// 发出发票
    /// </summary>
    public void Issue()
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException("只有草稿状态的发票可以发出");
            
        if (!_items.Any())
            throw new InvalidOperationException("发票必须至少包含一个项目");
            
        Status = InvoiceStatus.Issued;
        UpdatedAt = DateTime.UtcNow;
        
        RaiseDomainEvent(new InvoiceIssuedEvent(Id, CustomerUserId, TotalAmount));
    }
    
    /// <summary>
    /// 标记为已支付
    /// </summary>
    public void MarkAsPaid(PaymentMethod paymentMethod, string transactionId)
    {
        if (Status != InvoiceStatus.Issued && Status != InvoiceStatus.Overdue)
            throw new InvalidOperationException("只有已发出或逾期的发票可以标记为已支付");
            
        Status = InvoiceStatus.Paid;
        PaymentMethod = paymentMethod;
        PaymentTransactionId = transactionId;
        PaidDate = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        
        RaiseDomainEvent(new InvoicePaidEvent(Id, CustomerUserId, TotalAmount, paymentMethod.ToString()));
    }
    
    /// <summary>
    /// 标记为逾期
    /// </summary>
    public void MarkAsOverdue()
    {
        if (Status != InvoiceStatus.Issued)
            throw new InvalidOperationException("只有已发出的发票可以标记为逾期");
            
        if (DateTime.UtcNow <= DueDate)
            throw new InvalidOperationException("发票尚未到期");
            
        Status = InvoiceStatus.Overdue;
        UpdatedAt = DateTime.UtcNow;
        
        RaiseDomainEvent(new InvoiceOverdueEvent(Id, CustomerUserId, TotalAmount));
    }
    
    /// <summary>
    /// 取消发票
    /// </summary>
    public void Cancel(string reason)
    {
        if (Status == InvoiceStatus.Paid)
            throw new InvalidOperationException("已支付的发票不能取消");
            
        if (Status == InvoiceStatus.Cancelled)
            throw new InvalidOperationException("发票已经被取消");
            
        Status = InvoiceStatus.Cancelled;
        CancelledAt = DateTime.UtcNow;
        CancellationReason = reason;
        UpdatedAt = DateTime.UtcNow;
        
        RaiseDomainEvent(new InvoiceCancelledEvent(Id, CustomerUserId, reason));
    }
    
    /// <summary>
    /// 退款
    /// </summary>
    public void Refund(string reason)
    {
        if (Status != InvoiceStatus.Paid)
            throw new InvalidOperationException("只有已支付的发票可以退款");
            
        Status = InvoiceStatus.Refunded;
        UpdatedAt = DateTime.UtcNow;
        
        if (!string.IsNullOrWhiteSpace(reason))
        {
            Notes = string.IsNullOrWhiteSpace(Notes) 
                ? $"退款原因：{reason}" 
                : $"{Notes}; 退款原因：{reason}";
        }
        
        RaiseDomainEvent(new InvoiceRefundedEvent(Id, CustomerUserId, TotalAmount, reason));
    }
    
    /// <summary>
    /// 更新公司信息
    /// </summary>
    public void UpdateCompanyInfo(CompanyInfo companyInfo)
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException("只能更新草稿状态发票的公司信息");
            
        CompanyInfo = companyInfo;
        UpdatedAt = DateTime.UtcNow;
    }
    
    /// <summary>
    /// 重新计算金额
    /// </summary>
    private void RecalculateAmounts()
    {
        if (!_items.Any())
        {
            SubtotalAmount = Money.Create(0, SubtotalAmount.Currency);
            TaxAmount = Money.Create(0, SubtotalAmount.Currency);
            TotalAmount = Money.Create(0, SubtotalAmount.Currency);
            return;
        }
        
        // 计算小计
        var subtotal = _items.Sum(i => i.Amount.Amount);
        SubtotalAmount = Money.Create(subtotal, _items.First().Amount.Currency);
        
        // 应用折扣
        var afterDiscount = subtotal;
        if (DiscountAmount != null)
        {
            afterDiscount = subtotal - DiscountAmount.Amount;
        }
        
        // 计算税额
        var tax = _items.Sum(i => i.TaxAmount.Amount);
        TaxAmount = Money.Create(tax, _items.First().Amount.Currency);
        
        // 计算总额
        TotalAmount = Money.Create(afterDiscount + tax, _items.First().Amount.Currency);
    }
}

/// <summary>
/// 公司信息（用于开票）
/// </summary>
public class CompanyInfo : ValueObject
{
    public string CompanyName { get; private set; }
    public string TaxId { get; private set; }
    public string? Address { get; private set; }
    public string? Phone { get; private set; }
    public string? BankName { get; private set; }
    public string? BankAccount { get; private set; }
    
    private CompanyInfo() { }
    
    public CompanyInfo(
        string companyName,
        string taxId,
        string? address = null,
        string? phone = null,
        string? bankName = null,
        string? bankAccount = null)
    {
        if (string.IsNullOrWhiteSpace(companyName))
            throw new ArgumentException("公司名称不能为空", nameof(companyName));
            
        if (string.IsNullOrWhiteSpace(taxId))
            throw new ArgumentException("税号不能为空", nameof(taxId));
        
        CompanyName = companyName;
        TaxId = taxId;
        Address = address;
        Phone = phone;
        BankName = bankName;
        BankAccount = bankAccount;
    }
    
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return CompanyName;
        yield return TaxId;
        yield return Address ?? string.Empty;
        yield return Phone ?? string.Empty;
        yield return BankName ?? string.Empty;
        yield return BankAccount ?? string.Empty;
    }
}