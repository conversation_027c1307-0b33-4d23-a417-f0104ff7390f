using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Audit;

/// <summary>
/// 审计日志实体
/// </summary>
public class AuditLog : Entity
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid? UserId { get; set; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户类型 (Customer/Admin/System)
    /// </summary>
    public string UserType { get; set; } = string.Empty;
    
    /// <summary>
    /// 操作类型 (Create/Update/Delete/Read/Login/Logout等)
    /// </summary>
    public string Action { get; set; } = string.Empty;
    
    /// <summary>
    /// 实体类型
    /// </summary>
    public string? EntityType { get; set; }
    
    /// <summary>
    /// 实体ID
    /// </summary>
    public string? EntityId { get; set; }
    
    /// <summary>
    /// 操作描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 原始值（JSON格式）
    /// </summary>
    public string? OldValues { get; set; }
    
    /// <summary>
    /// 新值（JSON格式）
    /// </summary>
    public string? NewValues { get; set; }
    
    /// <summary>
    /// 变更的属性
    /// </summary>
    public string? ChangedProperties { get; set; }
    
    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户代理
    /// </summary>
    public string? UserAgent { get; set; }
    
    /// <summary>
    /// 请求ID（用于追踪）
    /// </summary>
    public string? RequestId { get; set; }
    
    /// <summary>
    /// 业务关联ID（用于追踪业务流程）
    /// </summary>
    public string? CorrelationId { get; set; }
    
    /// <summary>
    /// JWT令牌的JTI（用于追踪认证）
    /// </summary>
    public string? JwtId { get; set; }
    
    /// <summary>
    /// 模块名称
    /// </summary>
    public string Module { get; set; } = string.Empty;
    
    /// <summary>
    /// 控制器名称
    /// </summary>
    public string? ControllerName { get; set; }
    
    /// <summary>
    /// 操作名称
    /// </summary>
    public string? ActionName { get; set; }
    
    /// <summary>
    /// 请求方法 (GET/POST/PUT/DELETE等)
    /// </summary>
    public string? HttpMethod { get; set; }
    
    /// <summary>
    /// 请求URL
    /// </summary>
    public string? RequestUrl { get; set; }
    
    /// <summary>
    /// 请求参数（脱敏后）
    /// </summary>
    public string? RequestParameters { get; set; }
    
    /// <summary>
    /// 响应状态码
    /// </summary>
    public int? ResponseStatusCode { get; set; }
    
    /// <summary>
    /// 执行时长（毫秒）
    /// </summary>
    public long? ExecutionDuration { get; set; }
    
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 异常堆栈（仅开发环境记录）
    /// </summary>
    public string? ExceptionStack { get; set; }
    
    /// <summary>
    /// 风险等级 (Low/Medium/High/Critical)
    /// </summary>
    public string RiskLevel { get; set; } = "Low";
    
    /// <summary>
    /// 是否敏感操作
    /// </summary>
    public bool IsSensitive { get; set; }
    
    /// <summary>
    /// 客户端信息（设备、浏览器等）
    /// </summary>
    public string? ClientInfo { get; set; }
    
    /// <summary>
    /// 地理位置
    /// </summary>
    public string? GeoLocation { get; set; }
    
    /// <summary>
    /// 附加数据（JSON格式）
    /// </summary>
    public string? AdditionalData { get; set; }

    /// <summary>
    /// 创建审计日志
    /// </summary>
    public static AuditLog Create(
        string action,
        string module,
        string description,
        Guid? userId = null,
        string? userName = null,
        string? userType = null)
    {
        return new AuditLog
        {
            Id = Guid.NewGuid(),
            Action = action,
            Module = module,
            Description = description,
            UserId = userId,
            UserName = userName ?? "System",
            UserType = userType ?? "System",
            IsSuccess = true,
            RiskLevel = DetermineRiskLevel(action, module)
        };
    }

    /// <summary>
    /// 标记为失败
    /// </summary>
    public void MarkAsFailed(string errorMessage, string? exceptionStack = null)
    {
        IsSuccess = false;
        ErrorMessage = errorMessage;
        ExceptionStack = exceptionStack;
        RiskLevel = "High"; // 失败的操作通常风险较高
    }

    /// <summary>
    /// 设置实体信息
    /// </summary>
    public void SetEntityInfo(string entityType, string entityId)
    {
        EntityType = entityType;
        EntityId = entityId;
    }

    /// <summary>
    /// 设置变更信息
    /// </summary>
    public void SetChangeInfo(string? oldValues, string? newValues, string? changedProperties)
    {
        OldValues = oldValues;
        NewValues = newValues;
        ChangedProperties = changedProperties;
    }

    /// <summary>
    /// 设置请求信息
    /// </summary>
    public void SetRequestInfo(
        string httpMethod,
        string requestUrl,
        string? requestParameters,
        string ipAddress,
        string? userAgent,
        string? requestId = null,
        string? correlationId = null,
        string? jwtId = null)
    {
        HttpMethod = httpMethod;
        RequestUrl = requestUrl;
        RequestParameters = requestParameters;
        IpAddress = ipAddress;
        UserAgent = userAgent;
        RequestId = requestId;
        CorrelationId = correlationId;
        JwtId = jwtId;
    }

    /// <summary>
    /// 设置响应信息
    /// </summary>
    public void SetResponseInfo(int statusCode, long executionDuration)
    {
        ResponseStatusCode = statusCode;
        ExecutionDuration = executionDuration;
    }

    /// <summary>
    /// 判断风险等级
    /// </summary>
    private static string DetermineRiskLevel(string action, string module)
    {
        // 关键风险操作（需要立即关注）
        var criticalRiskActions = new[] { "DeleteAll", "DropDatabase", "SystemShutdown", "EmergencyAccess" };
        var criticalRiskModules = new[] { "SystemCore", "DatabaseAdmin", "EmergencyOverride" };
        
        if (criticalRiskActions.Any(a => action.Contains(a, StringComparison.OrdinalIgnoreCase)) ||
            criticalRiskModules.Any(m => module.Contains(m, StringComparison.OrdinalIgnoreCase)))
        {
            return "Critical";
        }
        
        // 高风险操作
        var highRiskActions = new[] { "Delete", "Grant", "Revoke", "Reset", "Export", "Refund", "Transfer" };
        var highRiskModules = new[] { "Security", "Payment", "User", "Permission", "Finance", "Compliance" };

        if (highRiskActions.Any(a => action.Contains(a, StringComparison.OrdinalIgnoreCase)) ||
            highRiskModules.Any(m => module.Contains(m, StringComparison.OrdinalIgnoreCase)))
        {
            return "High";
        }

        // 中等风险操作
        var mediumRiskActions = new[] { "Update", "Change", "Modify", "Approve", "Reject", "Enable", "Disable" };
        if (mediumRiskActions.Any(a => action.Contains(a, StringComparison.OrdinalIgnoreCase)))
        {
            return "Medium";
        }

        return "Low";
    }
    
    /// <summary>
    /// 判断是否需要自动审计的敏感操作
    /// </summary>
    public static bool IsSensitiveOperation(string action, string module, string? entityType = null)
    {
        // 支付相关操作
        if (module.Contains("Payment", StringComparison.OrdinalIgnoreCase) ||
            action.Contains("Payment", StringComparison.OrdinalIgnoreCase) ||
            action.Contains("Refund", StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }
        
        // 权限变更操作
        if (module.Contains("Permission", StringComparison.OrdinalIgnoreCase) ||
            action.Contains("Grant", StringComparison.OrdinalIgnoreCase) ||
            action.Contains("Revoke", StringComparison.OrdinalIgnoreCase) ||
            action.Contains("Role", StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }
        
        // 数据删除操作
        if (action.Contains("Delete", StringComparison.OrdinalIgnoreCase) ||
            action.Contains("Purge", StringComparison.OrdinalIgnoreCase) ||
            action.Contains("Truncate", StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }
        
        // 用户管理敏感操作
        if (entityType == "CustomerUser" || entityType == "AdminUser")
        {
            if (action.Contains("Password", StringComparison.OrdinalIgnoreCase) ||
                action.Contains("Email", StringComparison.OrdinalIgnoreCase) ||
                action.Contains("Phone", StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }
        }
        
        // 系统配置变更
        if (module.Contains("System", StringComparison.OrdinalIgnoreCase) ||
            module.Contains("Config", StringComparison.OrdinalIgnoreCase) ||
            module.Contains("Setting", StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }
        
        return false;
    }
}