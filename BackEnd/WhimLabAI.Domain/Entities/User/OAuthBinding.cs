using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.User;

public class OAuthBinding : Entity
{
    public string Provider { get; private set; }
    public string ProviderUserId { get; private set; }
    public string? DisplayName { get; private set; }
    public string? Email { get; private set; }
    public string? Avatar { get; private set; }
    public DateTime BindAt { get; private set; }
    public DateTime LastUsedAt { get; private set; }
    public string? AccessToken { get; private set; }
    public string? RefreshToken { get; private set; }
    public DateTime? TokenExpiresAt { get; private set; }
    public Dictionary<string, string> ExtraData { get; private set; }
    
    private OAuthBinding() : base()
    {
        ExtraData = new Dictionary<string, string>();
    }
    
    public OAuthBinding(
        string provider,
        string providerUserId,
        string? displayName = null,
        string? email = null,
        string? avatar = null) : base()
    {
        Provider = provider ?? throw new ArgumentNullException(nameof(provider));
        ProviderUserId = providerUserId ?? throw new ArgumentNullException(nameof(providerUserId));
        DisplayName = displayName;
        Email = email;
        Avatar = avatar;
        BindAt = DateTime.UtcNow;
        LastUsedAt = DateTime.UtcNow;
        ExtraData = new Dictionary<string, string>();
    }
    
    public void UpdateTokens(
        string accessToken,
        string? refreshToken = null,
        DateTime? expiresAt = null)
    {
        AccessToken = accessToken;
        RefreshToken = refreshToken;
        TokenExpiresAt = expiresAt;
        UpdateTimestamp();
    }
    
    public void UpdateProfile(
        string? displayName = null,
        string? email = null,
        string? avatar = null)
    {
        if (displayName != null) DisplayName = displayName;
        if (email != null) Email = email;
        if (avatar != null) Avatar = avatar;
        UpdateTimestamp();
    }
    
    public void RecordUsage()
    {
        LastUsedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void SetExtraData(string key, string value)
    {
        ExtraData[key] = value;
        UpdateTimestamp();
    }
    
    public bool IsTokenExpired()
    {
        return TokenExpiresAt.HasValue && TokenExpiresAt.Value <= DateTime.UtcNow;
    }
}