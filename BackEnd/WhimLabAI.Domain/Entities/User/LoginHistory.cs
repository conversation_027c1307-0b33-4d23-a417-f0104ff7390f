using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.User;

public class LoginHistory : Entity
{
    public Guid UserId { get; private set; }
    public DateTime LoginAt { get; private set; }
    public string? LoginIp { get; private set; }
    public string? Location { get; private set; }
    public string? Device { get; private set; }
    public string? Browser { get; private set; }
    public string? UserAgent { get; private set; }
    public LoginMethod LoginMethod { get; private set; }
    public bool IsSuccess { get; private set; }
    public string? FailureReason { get; private set; }
    public string? SessionToken { get; private set; }
    public DateTime? LogoutAt { get; private set; }
    
    public CustomerUser User { get; private set; } = null!;
    
    private LoginHistory() : base()
    {
    }
    
    public LoginHistory(
        Guid userId,
        string? loginIp,
        LoginMethod loginMethod,
        bool isSuccess,
        string? device = null,
        string? browser = null,
        string? userAgent = null,
        string? failureReason = null) : base()
    {
        UserId = userId;
        LoginAt = DateTime.UtcNow;
        LoginIp = loginIp;
        LoginMethod = loginMethod;
        IsSuccess = isSuccess;
        Device = device;
        Browser = browser;
        UserAgent = userAgent;
        FailureReason = failureReason;
    }
    
    public void SetLocation(string location)
    {
        Location = location;
        UpdateTimestamp();
    }
    
    public void SetSessionToken(string sessionToken)
    {
        SessionToken = sessionToken;
        UpdateTimestamp();
    }
    
    public void RecordLogout()
    {
        LogoutAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
}