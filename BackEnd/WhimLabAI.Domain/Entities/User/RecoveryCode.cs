using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.Entities.User;

/// <summary>
/// Represents a recovery code for MFA authentication
/// Recovery codes are one-time use codes that can be used when the user doesn't have access to their MFA device
/// </summary>
public class RecoveryCode : Entity
{
    public Guid UserId { get; private set; }
    public string Code { get; private set; }
    public bool IsUsed { get; private set; }
    public DateTime? UsedAt { get; private set; }
    public string? UsedByIp { get; private set; }
    public DateTime ExpiresAt { get; private set; }
    public string UserType { get; private set; } // "Customer" or "Admin"
    
    // Navigation properties
    public CustomerUser? CustomerUser { get; private set; }
    public AdminUser? AdminUser { get; private set; }
    
    private RecoveryCode() : base()
    {
        // Required for EF Core
    }
    
    public RecoveryCode(Guid userId, string code, string userType, int expiryDays = 365) : base()
    {
        if (userId == Guid.Empty)
            throw new ValidationException(nameof(userId), "用户ID不能为空");
            
        if (string.IsNullOrWhiteSpace(code))
            throw new ValidationException(nameof(code), "恢复码不能为空");
            
        if (code.Length < 8)
            throw new ValidationException(nameof(code), "恢复码长度不能少于8个字符");
            
        if (userType != "Customer" && userType != "Admin")
            throw new ValidationException(nameof(userType), "用户类型必须是Customer或Admin");
            
        UserId = userId;
        Code = code;
        UserType = userType;
        IsUsed = false;
        ExpiresAt = DateTime.UtcNow.AddDays(expiryDays);
    }
    
    /// <summary>
    /// Use the recovery code
    /// </summary>
    /// <param name="ipAddress">IP address from where the code was used</param>
    /// <exception cref="BusinessException">Thrown when the code is already used or expired</exception>
    public void Use(string? ipAddress = null)
    {
        if (IsUsed)
            throw new BusinessException("该恢复码已被使用");
            
        if (DateTime.UtcNow > ExpiresAt)
            throw new BusinessException("该恢复码已过期");
            
        IsUsed = true;
        UsedAt = DateTime.UtcNow;
        UsedByIp = ipAddress;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// Check if the recovery code is valid (not used and not expired)
    /// </summary>
    public bool IsValid()
    {
        return !IsUsed && DateTime.UtcNow <= ExpiresAt;
    }
    
    /// <summary>
    /// Get the number of days until expiration
    /// </summary>
    public int GetDaysUntilExpiration()
    {
        if (IsUsed || DateTime.UtcNow > ExpiresAt)
            return 0;
            
        return Math.Max(0, (int)(ExpiresAt - DateTime.UtcNow).TotalDays);
    }
}