using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Domain.DomainEvents;
using WhimLabAI.Domain.Entities.Auth;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.Entities.User;

public class AdminUser : AggregateRoot
{
    private readonly List<AdminUserRole> _userRoles = new();
    private readonly List<RecoveryCode> _recoveryCodes = new();
    
    public string Username { get; private set; }
    public Email Email { get; private set; }
    public PhoneNumber? Phone { get; private set; }
    public Password PasswordHash { get; private set; }
    public string? Nickname { get; private set; }
    public string? Avatar { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsSuperAdmin { get; private set; }
    public UserStatus Status { get; private set; }
    public DateTime? PasswordExpiredAt { get; private set; }
    public DateTime? LastLoginAt { get; private set; }
    public string? LastLoginIp { get; private set; }
    public int FailedLoginAttempts { get; private set; }
    public DateTime? LockedUntil { get; private set; }
    public bool TwoFactorEnabled { get; private set; }
    public string? TwoFactorSecret { get; private set; }
    
    // IP白名单功能
    public bool EnableIpWhitelist { get; private set; }
    public List<string> IpWhitelist { get; private set; } = new();
    
    public IReadOnlyCollection<AdminUserRole> UserRoles => _userRoles.AsReadOnly();
    public IReadOnlyCollection<RecoveryCode> RecoveryCodes => _recoveryCodes.AsReadOnly();
    
    private AdminUser() : base()
    {
    }
    
    public AdminUser(
        string username,
        string email,
        string password,
        string? phone = null,
        bool isSuperAdmin = false) : base()
    {
        SetUsername(username);
        Email = Email.Create(email);
        PasswordHash = Password.CreateFromPlainText(password);
        Phone = PhoneNumber.CreateOrNull(phone);
        IsSuperAdmin = isSuperAdmin;
        IsActive = true;
        Status = UserStatus.Active;
        FailedLoginAttempts = 0;
    }
    
    public void ChangePassword(string currentPassword, string newPassword)
    {
        if (!PasswordHash.Verify(currentPassword))
            throw new UnauthorizedException("当前密码不正确");
            
        PasswordHash = Password.CreateFromPlainText(newPassword);
        // 密码修改后，设置下次过期时间为90天后
        PasswordExpiredAt = DateTime.UtcNow.AddDays(90);
        UpdateTimestamp();
    }
    
    public void ResetPassword(string newPassword, bool requireChange = true)
    {
        PasswordHash = Password.CreateFromPlainText(newPassword);
        PasswordExpiredAt = requireChange ? DateTime.UtcNow : null;
        FailedLoginAttempts = 0;
        LockedUntil = null;
        UpdateTimestamp();
    }
    
    public void UpdateProfile(
        string? nickname = null,
        string? email = null,
        string? phone = null,
        string? avatar = null)
    {
        if (nickname != null) Nickname = nickname;
        if (email != null) Email = Email.Create(email);
        if (phone != null) Phone = PhoneNumber.CreateOrNull(phone);
        if (avatar != null) Avatar = avatar;
        UpdateTimestamp();
    }
    
    public void RecordSuccessfulLogin(string ipAddress)
    {
        LastLoginAt = DateTime.UtcNow;
        LastLoginIp = ipAddress;
        FailedLoginAttempts = 0;
        LockedUntil = null;
        UpdateTimestamp();
        
        // 触发登录成功领域事件
        RaiseDomainEvent(new AdminUserLoggedInEvent(Id, Username, ipAddress, TwoFactorEnabled));
    }
    
    public void RecordFailedLogin()
    {
        FailedLoginAttempts++;
        
        // Lock account after 5 failed attempts
        if (FailedLoginAttempts >= 5)
        {
            LockedUntil = DateTime.UtcNow.AddMinutes(15); // 根据业务需求锁定15分钟
        }
        
        UpdateTimestamp();
    }
    
    public bool IsLocked()
    {
        return LockedUntil.HasValue && LockedUntil.Value > DateTime.UtcNow;
    }
    
    public bool IsPasswordExpired()
    {
        // 如果没有设置过期时间，认为密码未过期（兼容旧数据）
        if (!PasswordExpiredAt.HasValue)
            return false;
            
        return PasswordExpiredAt.Value <= DateTime.UtcNow;
    }
    
    public int GetPasswordExpiryDays()
    {
        if (!PasswordExpiredAt.HasValue)
            return int.MaxValue; // 永不过期
            
        var remaining = PasswordExpiredAt.Value - DateTime.UtcNow;
        return Math.Max(0, (int)Math.Floor(remaining.TotalDays));
    }
    
    public int GetLockRemainingMinutes()
    {
        if (!IsLocked())
            return 0;
            
        var remaining = LockedUntil!.Value - DateTime.UtcNow;
        return Math.Max(0, (int)Math.Ceiling(remaining.TotalMinutes));
    }
    
    public void Unlock()
    {
        FailedLoginAttempts = 0;
        LockedUntil = null;
        UpdateTimestamp();
    }
    
    
    
    public List<string> GetAllPermissions()
    {
        if (IsSuperAdmin)
            return new List<string> { "*" }; // All permissions
            
        return _userRoles
            .SelectMany(ur => ur.Role?.Permissions ?? Enumerable.Empty<Auth.RolePermission>())
            .Where(rp => rp.Permission?.IsEnabled == true)
            .Select(rp => rp.Permission.Code)
            .Distinct()
            .ToList();
    }
    
    public void Activate()
    {
        IsActive = true;
        Status = UserStatus.Active;
        UpdateTimestamp();
    }
    
    public void Deactivate()
    {
        if (IsSuperAdmin)
            throw new BusinessException("不能禁用超级管理员");
            
        IsActive = false;
        Status = UserStatus.Inactive;
        UpdateTimestamp();
    }
    
    public void UpdateStatus(UserStatus newStatus)
    {
        if (IsSuperAdmin && newStatus != UserStatus.Active)
            throw new BusinessException("超级管理员必须保持活跃状态");
            
        Status = newStatus;
        IsActive = (newStatus == UserStatus.Active);
        UpdateTimestamp();
    }
    
    public void EnableTwoFactor(string secret)
    {
        TwoFactorEnabled = true;
        TwoFactorSecret = secret;
        UpdateTimestamp();
    }
    
    public void DisableTwoFactor()
    {
        TwoFactorEnabled = false;
        TwoFactorSecret = null;
        UpdateTimestamp();
    }
    
    // IP白名单管理方法
    public void EnableIpWhitelistFeature()
    {
        EnableIpWhitelist = true;
        UpdateTimestamp();
    }
    
    public void DisableIpWhitelistFeature()
    {
        EnableIpWhitelist = false;
        UpdateTimestamp();
    }
    
    public void SetIpWhitelist(params string[] ips)
    {
        IpWhitelist.Clear();
        if (ips != null && ips.Any())
        {
            IpWhitelist.AddRange(ips.Where(ip => !string.IsNullOrWhiteSpace(ip)).Distinct());
        }
        UpdateTimestamp();
    }
    
    public void AddIpToWhitelist(string ip)
    {
        if (string.IsNullOrWhiteSpace(ip))
            throw new ValidationException("IP", "IP地址不能为空");
            
        if (!IpWhitelist.Contains(ip))
        {
            IpWhitelist.Add(ip);
            UpdateTimestamp();
        }
    }
    
    public void RemoveIpFromWhitelist(string ip)
    {
        if (IpWhitelist.Remove(ip))
        {
            UpdateTimestamp();
        }
    }
    
    public bool IsIpAllowed(string ipAddress)
    {
        // 如果未启用IP白名单，允许所有IP
        if (!EnableIpWhitelist)
            return true;
            
        // 如果白名单为空，不允许任何IP（避免误操作）
        if (!IpWhitelist.Any())
            return false;
            
        return IpWhitelist.Contains(ipAddress);
    }
    
    public void AssignRole(Guid roleId, Guid? assignedBy = null)
    {
        if (_userRoles.Any(ur => ur.RoleId == roleId))
            return; // 角色已分配，忽略
            
        var userRole = new AdminUserRole(Id, roleId, assignedBy);
        _userRoles.Add(userRole);
        UpdateTimestamp();
        
        RaiseDomainEvent(new RoleAssignedToAdminUserEvent(Id, roleId, assignedBy));
    }
    
    public void RemoveRole(Guid roleId)
    {
        var userRole = _userRoles.FirstOrDefault(ur => ur.RoleId == roleId);
        if (userRole == null)
            return; // 角色不存在，忽略
            
        _userRoles.Remove(userRole);
        UpdateTimestamp();
        
        RaiseDomainEvent(new RoleRemovedFromAdminUserEvent(Id, roleId));
    }
    
    public void UpdateRoles(IEnumerable<Guid> roleIds, Guid? assignedBy = null)
    {
        // 移除不在新列表中的角色
        var toRemove = _userRoles
            .Where(ur => !roleIds.Contains(ur.RoleId))
            .ToList();
        
        foreach (var userRole in toRemove)
        {
            _userRoles.Remove(userRole);
        }
        
        // 添加新角色
        foreach (var roleId in roleIds)
        {
            if (!_userRoles.Any(ur => ur.RoleId == roleId))
            {
                _userRoles.Add(new AdminUserRole(Id, roleId, assignedBy));
            }
        }
        
        UpdateTimestamp();
    }
    
    public bool HasRole(string roleCode)
    {
        return _userRoles.Any(ur => ur.Role?.Code == roleCode);
    }
    
    public bool HasPermission(string permissionCode)
    {
        if (IsSuperAdmin)
            return true; // 超级管理员拥有所有权限
            
        return _userRoles
            .SelectMany(ur => ur.Role?.Permissions ?? Enumerable.Empty<RolePermission>())
            .Any(rp => rp.Permission?.Code == permissionCode && rp.Permission.IsEnabled);
    }
    
    public IEnumerable<string> GetPermissionCodes()
    {
        if (IsSuperAdmin)
            return new[] { "*" }; // 超级管理员返回通配符
            
        return _userRoles
            .SelectMany(ur => ur.Role?.Permissions ?? Enumerable.Empty<RolePermission>())
            .Where(rp => rp.Permission?.IsEnabled == true)
            .Select(rp => rp.Permission.Code)
            .Distinct();
    }
    
    private void SetUsername(string username)
    {
        if (string.IsNullOrWhiteSpace(username))
            throw new ValidationException("Username", "用户名不能为空");
            
        if (username.Length < 3 || username.Length > 20)
            throw new ValidationException("Username", "用户名长度必须在3-20个字符之间");
            
        Username = username;
    }
}