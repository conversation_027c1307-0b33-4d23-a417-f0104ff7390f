using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.User;

public class CustomerProfile : Entity
{
    public Guid UserId { get; private set; }
    public string? Company { get; private set; }
    public string? Department { get; private set; }
    public string? JobTitle { get; private set; }
    public string? WorkEmail { get; private set; }
    public string? WorkPhone { get; private set; }
    public string? Address { get; private set; }
    public string? City { get; private set; }
    public string? Province { get; private set; }
    public string? PostalCode { get; private set; }
    public string? Country { get; private set; }
    public string? Website { get; private set; }
    public string? LinkedIn { get; private set; }
    public string? Twitter { get; private set; }
    public string? GitHub { get; private set; }
    public Dictionary<string, string> CustomFields { get; private set; }
    
    public CustomerUser User { get; private set; } = null!;
    
    private CustomerProfile() : base()
    {
        CustomFields = new Dictionary<string, string>();
    }
    
    public CustomerProfile(Guid userId) : base()
    {
        UserId = userId;
        CustomFields = new Dictionary<string, string>();
    }
    
    public void UpdateWorkInfo(
        string? company = null,
        string? department = null,
        string? jobTitle = null,
        string? workEmail = null,
        string? workPhone = null)
    {
        if (company != null) Company = company;
        if (department != null) Department = department;
        if (jobTitle != null) JobTitle = jobTitle;
        if (workEmail != null) WorkEmail = workEmail;
        if (workPhone != null) WorkPhone = workPhone;
        UpdateTimestamp();
    }
    
    public void UpdateAddress(
        string? address = null,
        string? city = null,
        string? province = null,
        string? postalCode = null,
        string? country = null)
    {
        if (address != null) Address = address;
        if (city != null) City = city;
        if (province != null) Province = province;
        if (postalCode != null) PostalCode = postalCode;
        if (country != null) Country = country;
        UpdateTimestamp();
    }
    
    public void UpdateSocialLinks(
        string? website = null,
        string? linkedIn = null,
        string? twitter = null,
        string? gitHub = null)
    {
        if (website != null) Website = website;
        if (linkedIn != null) LinkedIn = linkedIn;
        if (twitter != null) Twitter = twitter;
        if (gitHub != null) GitHub = gitHub;
        UpdateTimestamp();
    }
    
    public void SetCustomField(string key, string value)
    {
        CustomFields[key] = value;
        UpdateTimestamp();
    }
    
    public void RemoveCustomField(string key)
    {
        if (CustomFields.Remove(key))
        {
            UpdateTimestamp();
        }
    }
}