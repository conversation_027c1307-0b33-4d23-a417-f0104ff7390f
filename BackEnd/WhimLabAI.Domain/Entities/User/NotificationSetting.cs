using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.User;

public class NotificationSetting : ValueObject
{
    public bool EmailNotification { get; private set; }
    public bool SmsNotification { get; private set; }
    public bool SystemNotification { get; private set; }
    public bool PromotionNotification { get; private set; }
    public bool SecurityAlert { get; private set; }
    public bool QuotaAlert { get; private set; }
    public bool NewFeatureNotification { get; private set; }
    public bool NewsletterSubscription { get; private set; }
    
    public NotificationSetting()
    {
        // Default settings
        EmailNotification = true;
        SmsNotification = false;
        SystemNotification = true;
        PromotionNotification = false;
        SecurityAlert = true;
        QuotaAlert = true;
        NewFeatureNotification = true;
        NewsletterSubscription = false;
    }
    
    public NotificationSetting(
        bool emailNotification,
        bool smsNotification,
        bool systemNotification,
        bool promotionNotification,
        bool securityAlert,
        bool quotaAlert,
        bool newFeatureNotification,
        bool newsletterSubscription)
    {
        EmailNotification = emailNotification;
        SmsNotification = smsNotification;
        SystemNotification = systemNotification;
        PromotionNotification = promotionNotification;
        SecurityAlert = securityAlert;
        QuotaAlert = quotaAlert;
        NewFeatureNotification = newFeatureNotification;
        NewsletterSubscription = newsletterSubscription;
    }
    
    public NotificationSetting WithEmailNotification(bool enabled)
    {
        return new NotificationSetting(
            enabled,
            SmsNotification,
            SystemNotification,
            PromotionNotification,
            SecurityAlert,
            QuotaAlert,
            NewFeatureNotification,
            NewsletterSubscription);
    }
    
    public NotificationSetting WithSmsNotification(bool enabled)
    {
        return new NotificationSetting(
            EmailNotification,
            enabled,
            SystemNotification,
            PromotionNotification,
            SecurityAlert,
            QuotaAlert,
            NewFeatureNotification,
            NewsletterSubscription);
    }
    
    public NotificationSetting EnableAll()
    {
        return new NotificationSetting(
            true, true, true, true, true, true, true, true);
    }
    
    public NotificationSetting DisableAll()
    {
        return new NotificationSetting(
            false, false, false, false, false, false, false, false);
    }
    
    public NotificationSetting EnableEssentialOnly()
    {
        return new NotificationSetting(
            true,    // Email
            false,   // SMS
            true,    // System
            false,   // Promotion
            true,    // Security
            true,    // Quota
            false,   // New Feature
            false    // Newsletter
        );
    }
    
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return EmailNotification;
        yield return SmsNotification;
        yield return SystemNotification;
        yield return PromotionNotification;
        yield return SecurityAlert;
        yield return QuotaAlert;
        yield return NewFeatureNotification;
        yield return NewsletterSubscription;
    }
}