using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.Auth;

namespace WhimLabAI.Domain.Entities.User;

/// <summary>
/// 管理员用户角色关联实体
/// </summary>
public class AdminUserRole : Entity
{
    public Guid AdminUserId { get; private set; }
    public Guid RoleId { get; private set; }
    
    /// <summary>
    /// 分配时间
    /// </summary>
    public DateTime AssignedAt { get; private set; }
    
    /// <summary>
    /// 分配者ID（执行分配操作的管理员）
    /// </summary>
    public Guid? AssignedBy { get; private set; }
    
    // 导航属性
    public AdminUser AdminUser { get; private set; } = null!;
    public Role Role { get; private set; } = null!;
    
    private AdminUserRole() : base()
    {
        // Required for EF Core
    }
    
    public AdminUserRole(Guid adminUserId, Guid roleId, Guid? assignedBy = null) : base()
    {
        AdminUserId = adminUserId;
        RoleId = roleId;
        AssignedAt = DateTime.UtcNow;
        AssignedBy = assignedBy;
    }
}