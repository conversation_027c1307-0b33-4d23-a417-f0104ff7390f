using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.User;

public class QRCodeSession : Entity
{
    public string SessionId { get; private set; }
    public string QRCodeData { get; private set; }
    public QRCodeSessionStatus Status { get; private set; }
    public DateTime ExpiresAt { get; private set; }
    public string? ClientIp { get; private set; }
    public string? UserAgent { get; private set; }
    public string? DeviceId { get; private set; }
    public string? DeviceName { get; private set; }
    public string? DeviceType { get; private set; }
    public Guid? UserId { get; private set; }
    public DateTime? ScannedAt { get; private set; }
    public DateTime? AuthenticatedAt { get; private set; }
    public string? ScannedByDeviceId { get; private set; }
    public string? ScannedByIp { get; private set; }
    
    // Navigation properties
    public CustomerUser? User { get; private set; }
    
    private QRCodeSession() : base()
    {
    }
    
    public QRCodeSession(
        string sessionId,
        string qrCodeData,
        int expirationMinutes = 5,
        string? clientIp = null,
        string? userAgent = null,
        string? deviceId = null,
        string? deviceName = null,
        string? deviceType = null) : base()
    {
        SessionId = sessionId ?? throw new ArgumentNullException(nameof(sessionId));
        QRCodeData = qrCodeData ?? throw new ArgumentNullException(nameof(qrCodeData));
        Status = QRCodeSessionStatus.Pending;
        ExpiresAt = DateTime.UtcNow.AddMinutes(expirationMinutes);
        ClientIp = clientIp;
        UserAgent = userAgent;
        DeviceId = deviceId;
        DeviceName = deviceName;
        DeviceType = deviceType;
    }
    
    public bool IsExpired => DateTime.UtcNow > ExpiresAt;
    
    public void MarkAsScanned(string scannedByDeviceId, string scannedByIp)
    {
        if (Status != QRCodeSessionStatus.Pending)
            throw new InvalidOperationException("只有待扫描状态的二维码可以被标记为已扫描");
            
        if (IsExpired)
            throw new InvalidOperationException("二维码已过期");
            
        Status = QRCodeSessionStatus.Scanned;
        ScannedAt = DateTime.UtcNow;
        ScannedByDeviceId = scannedByDeviceId;
        ScannedByIp = scannedByIp;
        UpdateTimestamp();
    }
    
    public void MarkAsAuthenticated(Guid userId)
    {
        if (Status != QRCodeSessionStatus.Scanned)
            throw new InvalidOperationException("只有已扫描状态的二维码可以被标记为已认证");
            
        if (IsExpired)
            throw new InvalidOperationException("二维码已过期");
            
        Status = QRCodeSessionStatus.Authenticated;
        UserId = userId;
        AuthenticatedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void MarkAsExpired()
    {
        if (Status == QRCodeSessionStatus.Authenticated)
            throw new InvalidOperationException("已认证的二维码不能被标记为过期");
            
        Status = QRCodeSessionStatus.Expired;
        UpdateTimestamp();
    }
    
    public void MarkAsCancelled()
    {
        if (Status == QRCodeSessionStatus.Authenticated)
            throw new InvalidOperationException("已认证的二维码不能被取消");
            
        Status = QRCodeSessionStatus.Cancelled;
        UpdateTimestamp();
    }
    
    public void Reject()
    {
        if (Status != QRCodeSessionStatus.Scanned)
            throw new InvalidOperationException("只有已扫描状态的二维码可以被拒绝");
            
        Status = QRCodeSessionStatus.Rejected;
        UpdateTimestamp();
    }
}