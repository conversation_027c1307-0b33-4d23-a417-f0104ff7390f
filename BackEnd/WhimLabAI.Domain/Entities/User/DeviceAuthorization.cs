using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.User;

public class DeviceAuthorization : Entity
{
    public string DeviceId { get; private set; }
    public string DeviceName { get; private set; }
    public string? DeviceInfo { get; private set; }
    public string? DeviceType { get; private set; }
    public string? OperatingSystem { get; private set; }
    public DateTime AuthorizedAt { get; private set; }
    public DateTime LastAccessAt { get; private set; }
    public bool IsActive { get; private set; }
    public string? IpAddress { get; private set; }
    
    private DeviceAuthorization() : base()
    {
    }
    
    public DeviceAuthorization(
        string deviceId,
        string deviceName,
        string? deviceInfo = null) : base()
    {
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        DeviceName = deviceName ?? throw new ArgumentNullException(nameof(deviceName));
        DeviceInfo = deviceInfo;
        AuthorizedAt = DateTime.UtcNow;
        LastAccessAt = DateTime.UtcNow;
        IsActive = true;
        
        ParseDeviceInfo();
    }
    
    public void UpdateLastAccess(string? ipAddress = null)
    {
        LastAccessAt = DateTime.UtcNow;
        if (!string.IsNullOrWhiteSpace(ipAddress))
            IpAddress = ipAddress;
        UpdateTimestamp();
    }
    
    public void Deactivate()
    {
        IsActive = false;
        UpdateTimestamp();
    }
    
    public void Activate()
    {
        IsActive = true;
        LastAccessAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void UpdateDeviceName(string deviceName)
    {
        if (string.IsNullOrWhiteSpace(deviceName))
            throw new ArgumentNullException(nameof(deviceName));
            
        DeviceName = deviceName;
        UpdateTimestamp();
    }
    
    private void ParseDeviceInfo()
    {
        if (string.IsNullOrWhiteSpace(DeviceInfo))
            return;
            
        // Parse device info to extract device type and OS
        // This is a simplified implementation
        if (DeviceInfo.Contains("iPhone") || DeviceInfo.Contains("iPad"))
        {
            DeviceType = "iOS";
            OperatingSystem = "iOS";
        }
        else if (DeviceInfo.Contains("Android"))
        {
            DeviceType = "Android";
            OperatingSystem = "Android";
        }
        else if (DeviceInfo.Contains("Windows"))
        {
            DeviceType = "Desktop";
            OperatingSystem = "Windows";
        }
        else if (DeviceInfo.Contains("Mac"))
        {
            DeviceType = "Desktop";
            OperatingSystem = "macOS";
        }
        else if (DeviceInfo.Contains("Linux"))
        {
            DeviceType = "Desktop";
            OperatingSystem = "Linux";
        }
    }
}