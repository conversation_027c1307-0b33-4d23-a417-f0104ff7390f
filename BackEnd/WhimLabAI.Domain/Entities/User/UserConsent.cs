using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.User;

/// <summary>
/// 用户同意记录
/// </summary>
public class UserConsent : Entity
{
    /// <summary>
    /// 客户用户ID（当UserType为Customer时）
    /// </summary>
    public Guid? CustomerUserId { get; private set; }
    
    /// <summary>
    /// 管理员用户ID（当UserType为Admin时）
    /// </summary>
    public Guid? AdminUserId { get; private set; }
    
    /// <summary>
    /// 用户类型 (Customer/Admin)
    /// </summary>
    public string UserType { get; private set; } = string.Empty;
    
    /// <summary>
    /// 同意类型（隐私政策、服务条款、营销推广等）
    /// </summary>
    public string ConsentType { get; private set; } = string.Empty;
    
    /// <summary>
    /// 同意版本
    /// </summary>
    public string Version { get; private set; } = string.Empty;
    
    /// <summary>
    /// 同意时间
    /// </summary>
    public DateTime ConsentedAt { get; private set; }
    
    /// <summary>
    /// 同意IP地址
    /// </summary>
    public string? IpAddress { get; private set; }
    
    /// <summary>
    /// 用户代理
    /// </summary>
    public string? UserAgent { get; private set; }
    
    /// <summary>
    /// 是否已撤销
    /// </summary>
    public bool IsRevoked { get; private set; }
    
    /// <summary>
    /// 撤销时间
    /// </summary>
    public DateTime? RevokedAt { get; private set; }
    
    /// <summary>
    /// 同意文本内容（用于存档）
    /// </summary>
    public string? ConsentText { get; private set; }
    
    /// <summary>
    /// 元数据
    /// </summary>
    public Dictionary<string, object>? Metadata { get; private set; }
    
    private UserConsent() : base()
    {
        Metadata = new Dictionary<string, object>();
    }
    
    /// <summary>
    /// 创建客户用户同意记录
    /// </summary>
    public static UserConsent CreateForCustomerUser(
        Guid customerUserId,
        string consentType,
        string version,
        string? ipAddress = null,
        string? userAgent = null,
        string? consentText = null)
    {
        return new UserConsent
        {
            Id = Guid.NewGuid(),
            CustomerUserId = customerUserId,
            UserType = "Customer",
            ConsentType = consentType,
            Version = version,
            ConsentedAt = DateTime.UtcNow,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            ConsentText = consentText,
            IsRevoked = false
        };
    }
    
    /// <summary>
    /// 创建管理员用户同意记录
    /// </summary>
    public static UserConsent CreateForAdminUser(
        Guid adminUserId,
        string consentType,
        string version,
        string? ipAddress = null,
        string? userAgent = null,
        string? consentText = null)
    {
        return new UserConsent
        {
            Id = Guid.NewGuid(),
            AdminUserId = adminUserId,
            UserType = "Admin",
            ConsentType = consentType,
            Version = version,
            ConsentedAt = DateTime.UtcNow,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            ConsentText = consentText,
            IsRevoked = false
        };
    }
    
    /// <summary>
    /// 撤销同意
    /// </summary>
    public void Revoke()
    {
        if (IsRevoked)
        {
            throw new InvalidOperationException("同意已经被撤销");
        }
        
        IsRevoked = true;
        RevokedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 设置元数据
    /// </summary>
    public void SetMetadata(string key, object value)
    {
        Metadata ??= new Dictionary<string, object>();
        Metadata[key] = value;
        UpdateTimestamp();
    }
}

/// <summary>
/// 同意类型常量
/// </summary>
public static class ConsentTypes
{
    public const string PrivacyPolicy = "PrivacyPolicy";
    public const string TermsOfService = "TermsOfService";
    public const string Marketing = "Marketing";
    public const string DataProcessing = "DataProcessing";
    public const string Cookies = "Cookies";
    public const string ThirdPartySharing = "ThirdPartySharing";
}