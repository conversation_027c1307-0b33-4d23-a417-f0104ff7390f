using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.DomainEvents;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;
using WhimLabAI.Shared.Utilities;

namespace WhimLabAI.Domain.Entities.User;

public class CustomerUser : AggregateRoot
{
    private readonly List<OAuthBinding> _oAuthBindings = new();
    private readonly List<DeviceAuthorization> _deviceAuthorizations = new();
    private readonly List<RecoveryCode> _recoveryCodes = new();
    
    // Security-related fields
    private int _loginFailedCount;
    private DateTime? _lockedUntil;
    private bool _twoFactorEnabled;
    private string? _twoFactorSecret;
    private string? _refreshToken;
    private DateTime? _refreshTokenExpiry;
    
    public string Username { get; private set; }
    public Email? Email { get; private set; }
    public PhoneNumber? Phone { get; private set; }
    public Password PasswordHash { get; private set; }
    public string? Avatar { get; private set; }
    public string? Nickname { get; private set; }
    public Gender Gender { get; private set; }
    public DateTime? Birthday { get; private set; }
    public string? Region { get; private set; }
    public string? Industry { get; private set; }
    public string? Position { get; private set; }
    public string? Bio { get; private set; }
    public bool IsEmailVerified { get; private set; }
    public bool IsPhoneVerified { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsBanned { get; private set; }
    public DateTime? LastLoginAt { get; private set; }
    public string? LastLoginIp { get; private set; }
    public string? RegisterIp { get; private set; }
    public CustomerProfile? Profile { get; private set; }
    public NotificationSetting NotificationSetting { get; private set; }
    
    public IReadOnlyCollection<OAuthBinding> OAuthBindings => _oAuthBindings.AsReadOnly();
    public IReadOnlyCollection<DeviceAuthorization> DeviceAuthorizations => _deviceAuthorizations.AsReadOnly();
    public IReadOnlyCollection<RecoveryCode> RecoveryCodes => _recoveryCodes.AsReadOnly();
    
    // Security properties
    public int LoginFailedCount => _loginFailedCount;
    public bool TwoFactorEnabled => _twoFactorEnabled;
    public string? RefreshToken => _refreshToken;
    
    private CustomerUser() : base()
    {
        // Required for EF Core
    }
    
    public CustomerUser(
        string username,
        string password,
        string? email = null,
        string? phone = null,
        string? registerIp = null) : base()
    {
        SetUsername(username);
        PasswordHash = Password.CreateFromPlainText(password);
        Email = Email.CreateOrNull(email);
        Phone = PhoneNumber.CreateOrNull(phone);
        RegisterIp = registerIp;
        
        Gender = Gender.Unknown;
        IsActive = true;
        IsBanned = false;
        IsEmailVerified = false;
        IsPhoneVerified = false;
        
        NotificationSetting = new NotificationSetting();
        
        RaiseDomainEvent(new CustomerUserRegisteredEvent(Id, username, email, phone));
    }
    
    public void ChangePassword(string currentPassword, string newPassword)
    {
        if (!PasswordHash.Verify(currentPassword))
            throw new UnauthorizedException("当前密码不正确");
            
        PasswordHash = Password.CreateFromPlainText(newPassword);
        UpdateTimestamp();
    }
    
    public void ResetPassword(string newPassword)
    {
        PasswordHash = Password.CreateFromPlainText(newPassword);
        UpdateTimestamp();
    }
    
    public void UpdateProfile(
        string? nickname = null,
        Gender? gender = null,
        DateTime? birthday = null,
        string? region = null,
        string? industry = null,
        string? position = null,
        string? bio = null)
    {
        if (nickname != null) Nickname = nickname;
        if (gender.HasValue) Gender = gender.Value;
        if (birthday.HasValue) Birthday = birthday.Value;
        if (region != null) Region = region;
        if (industry != null) Industry = industry;
        if (position != null) Position = position;
        if (bio != null) Bio = bio;
        
        UpdateTimestamp();
    }
    
    public void UpdateAvatar(string avatarUrl)
    {
        Avatar = avatarUrl;
        UpdateTimestamp();
    }
    
    public void UpdateEmail(string email)
    {
        Email = Email.Create(email);
        IsEmailVerified = false;
        UpdateTimestamp();
    }
    
    public void UpdatePhone(string phone)
    {
        Phone = PhoneNumber.Create(phone);
        IsPhoneVerified = false;
        UpdateTimestamp();
    }
    
    public void VerifyEmail()
    {
        if (Email == null)
            throw new BusinessException("没有设置邮箱地址");
            
        IsEmailVerified = true;
        UpdateTimestamp();
    }
    
    public void VerifyPhone()
    {
        if (Phone == null)
            throw new BusinessException("没有设置手机号码");
            
        IsPhoneVerified = true;
        UpdateTimestamp();
    }
    
    public void RecordLogin(string ipAddress)
    {
        _loginFailedCount = 0;
        _lockedUntil = null;
        LastLoginAt = DateTime.UtcNow;
        LastLoginIp = ipAddress;
        UpdateTimestamp();
        
        RaiseDomainEvent(new CustomerUserLoggedInEvent(Id, Username, ipAddress));
    }
    
    // Account locking methods
    public bool IsLocked() => _lockedUntil.HasValue && _lockedUntil.Value > DateTime.UtcNow;
    
    public int GetLockRemainingMinutes()
    {
        if (!IsLocked()) return 0;
        return (int)(_lockedUntil.Value - DateTime.UtcNow).TotalMinutes;
    }
    
    public void RecordFailedLogin()
    {
        _loginFailedCount++;
        UpdateTimestamp();
        
        if (_loginFailedCount >= 5)
        {
            _lockedUntil = DateTime.UtcNow.AddMinutes(15);
            RaiseDomainEvent(new CustomerUserLockedEvent(Id, Username, _lockedUntil.Value));
        }
    }
    
    // Refresh token methods
    public void SetRefreshToken(string token, int expiryDays = 7)
    {
        _refreshToken = token;
        _refreshTokenExpiry = DateTime.UtcNow.AddDays(expiryDays);
        UpdateTimestamp();
    }
    
    public void ClearRefreshToken()
    {
        _refreshToken = null;
        _refreshTokenExpiry = null;
        UpdateTimestamp();
    }
    
    public bool IsRefreshTokenValid(string token)
    {
        return !string.IsNullOrEmpty(_refreshToken) 
            && _refreshToken == token 
            && _refreshTokenExpiry.HasValue 
            && _refreshTokenExpiry.Value > DateTime.UtcNow;
    }
    
    // Two-factor authentication methods
    public void EnableTwoFactor(string secret)
    {
        _twoFactorEnabled = true;
        _twoFactorSecret = secret;
        UpdateTimestamp();
        RaiseDomainEvent(new CustomerUserTwoFactorEnabledEvent(Id, Username));
    }
    
    public void DisableTwoFactor()
    {
        _twoFactorEnabled = false;
        _twoFactorSecret = null;
        UpdateTimestamp();
        RaiseDomainEvent(new CustomerUserTwoFactorDisabledEvent(Id, Username));
    }
    
    public bool ValidateTwoFactorCode(string code)
    {
        if (!_twoFactorEnabled || string.IsNullOrEmpty(_twoFactorSecret))
            return false;
            
        // Note: Actual TOTP validation should be done in the application layer
        // using ITotpService. This method is kept for backward compatibility.
        // The application service should use ITotpService.ValidateCode directly.
        return !string.IsNullOrEmpty(code) && code.Length == 6 && int.TryParse(code, out _);
    }
    
    public string? GetTwoFactorSecret()
    {
        return _twoFactorSecret;
    }
    
    public void Ban(string reason = "")
    {
        if (IsBanned)
            throw new BusinessException("用户已被封禁");
            
        IsBanned = true;
        IsActive = false;
        UpdateTimestamp();
    }
    
    public void Unban()
    {
        if (!IsBanned)
            throw new BusinessException("用户未被封禁");
            
        IsBanned = false;
        IsActive = true;
        UpdateTimestamp();
    }
    
    public void SetActive()
    {
        IsActive = true;
        IsBanned = false;
        UpdateTimestamp();
    }
    
    public void SetInactive()
    {
        IsActive = false;
        UpdateTimestamp();
    }
    
    public void ResetLoginFailures()
    {
        _loginFailedCount = 0;
        _lockedUntil = null;
        UpdateTimestamp();
    }
    
    public void Deactivate()
    {
        IsActive = false;
        UpdateTimestamp();
    }
    
    public void Activate()
    {
        if (IsBanned)
            throw new BusinessException("被封禁的用户不能激活");
            
        IsActive = true;
        UpdateTimestamp();
    }
    
    public void BindOAuthAccount(string provider, string providerUserId, string? displayName = null)
    {
        if (_oAuthBindings.Any(x => x.Provider == provider))
            throw new BusinessException($"已经绑定了{provider}账号");
            
        var binding = new OAuthBinding(provider, providerUserId, displayName);
        _oAuthBindings.Add(binding);
        UpdateTimestamp();
    }
    
    public void UnbindOAuthAccount(string provider)
    {
        var binding = _oAuthBindings.FirstOrDefault(x => x.Provider == provider);
        if (binding == null)
            throw new BusinessException($"未绑定{provider}账号");
            
        _oAuthBindings.Remove(binding);
        UpdateTimestamp();
    }
    
    public void AuthorizeDevice(string deviceId, string deviceName, string? deviceInfo = null)
    {
        var existingDevice = _deviceAuthorizations.FirstOrDefault(x => x.DeviceId == deviceId);
        if (existingDevice != null)
        {
            existingDevice.UpdateLastAccess();
        }
        else
        {
            var device = new DeviceAuthorization(deviceId, deviceName, deviceInfo);
            _deviceAuthorizations.Add(device);
        }
        UpdateTimestamp();
    }
    
    public void RevokeDevice(string deviceId)
    {
        var device = _deviceAuthorizations.FirstOrDefault(x => x.DeviceId == deviceId);
        if (device != null)
        {
            _deviceAuthorizations.Remove(device);
            UpdateTimestamp();
        }
    }
    
    public void UpdateNotificationSettings(NotificationSetting settings)
    {
        NotificationSetting = settings ?? throw new ArgumentNullException(nameof(settings));
        UpdateTimestamp();
    }
    
    private void SetUsername(string username)
    {
        if (string.IsNullOrWhiteSpace(username))
            throw new ValidationException("Username", "用户名不能为空");
            
        if (username.Length < 4 || username.Length > 20)
            throw new ValidationException("Username", "用户名长度必须在4-20个字符之间");
            
        if (!ValidationHelper.IsValidUsername(username))
            throw new ValidationException("Username", "用户名格式不正确");
            
        Username = username;
    }
}