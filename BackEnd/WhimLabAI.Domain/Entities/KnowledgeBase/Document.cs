using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.KnowledgeBase;

/// <summary>
/// 知识库文档实体
/// </summary>
public class Document : Entity, ISoftDelete
{
    /// <summary>
    /// 知识库ID
    /// </summary>
    public Guid KnowledgeBaseId { get; private set; }
    
    /// <summary>
    /// 文档名称
    /// </summary>
    public string Name { get; private set; } = string.Empty;
    
    /// <summary>
    /// 文档类型
    /// </summary>
    public DocumentType DocumentType { get; private set; }
    
    /// <summary>
    /// 内容类型（MIME）
    /// </summary>
    public string ContentType { get; private set; } = string.Empty;
    
    /// <summary>
    /// 原始文件URL
    /// </summary>
    public string? OriginalFileUrl { get; private set; }
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; private set; }
    
    /// <summary>
    /// 文件哈希（用于去重）
    /// </summary>
    public string FileHash { get; private set; } = string.Empty;
    
    /// <summary>
    /// 文档状态
    /// </summary>
    public DocumentStatus Status { get; private set; } = DocumentStatus.Pending;
    
    /// <summary>
    /// 处理开始时间
    /// </summary>
    public DateTime? ProcessingStartedAt { get; private set; }
    
    /// <summary>
    /// 处理完成时间
    /// </summary>
    public DateTime? ProcessingCompletedAt { get; private set; }
    
    /// <summary>
    /// 处理错误信息
    /// </summary>
    public string? ProcessingError { get; private set; }
    
    /// <summary>
    /// 分块数量
    /// </summary>
    public int ChunkCount { get; private set; }
    
    /// <summary>
    /// 总字符数
    /// </summary>
    public int TotalCharacters { get; private set; }
    
    /// <summary>
    /// 总Token数
    /// </summary>
    public int TotalTokens { get; private set; }
    
    /// <summary>
    /// 元数据（JSON）
    /// </summary>
    public string? Metadata { get; private set; }
    
    /// <summary>
    /// 软删除标记
    /// </summary>
    public bool IsDeleted { get; private set; }
    
    /// <summary>
    /// 删除时间
    /// </summary>
    public DateTime? DeletedAt { get; private set; }
    
    /// <summary>
    /// 文档块集合
    /// </summary>
    private readonly List<DocumentChunk> _chunks = new();
    public IReadOnlyList<DocumentChunk> Chunks => _chunks.AsReadOnly();
    
    /// <summary>
    /// 关联的知识库
    /// </summary>
    public KnowledgeBase? KnowledgeBase { get; private set; }

    protected Document() { }

    public Document(
        Guid knowledgeBaseId,
        string name,
        DocumentType documentType,
        string contentType,
        long fileSize,
        string fileHash,
        string? originalFileUrl = null)
    {
        KnowledgeBaseId = knowledgeBaseId;
        Name = name;
        DocumentType = documentType;
        ContentType = contentType;
        FileSize = fileSize;
        FileHash = fileHash;
        OriginalFileUrl = originalFileUrl;
    }

    public void StartProcessing()
    {
        Status = DocumentStatus.Processing;
        ProcessingStartedAt = DateTime.UtcNow;
        ProcessingError = null;
        UpdatedAt = DateTime.UtcNow;
    }

    public void CompleteProcessing(int chunkCount, int totalCharacters, int totalTokens)
    {
        Status = DocumentStatus.Processed;
        ProcessingCompletedAt = DateTime.UtcNow;
        ChunkCount = chunkCount;
        TotalCharacters = totalCharacters;
        TotalTokens = totalTokens;
        UpdatedAt = DateTime.UtcNow;
    }

    public void FailProcessing(string error)
    {
        Status = DocumentStatus.Failed;
        ProcessingError = error;
        ProcessingCompletedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateMetadata(string? metadata)
    {
        Metadata = metadata;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Delete()
    {
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Restore()
    {
        IsDeleted = false;
        DeletedAt = null;
        UpdatedAt = DateTime.UtcNow;
    }
    
    /// <summary>
    /// 标记文档为已处理（用于嵌入完成后）
    /// </summary>
    public void MarkAsProcessed()
    {
        if (Status != DocumentStatus.Processed)
        {
            Status = DocumentStatus.Processed;
            ProcessingCompletedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}