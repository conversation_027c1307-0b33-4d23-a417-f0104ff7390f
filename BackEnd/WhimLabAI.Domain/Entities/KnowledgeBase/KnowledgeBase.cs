using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.KnowledgeBase;

/// <summary>
/// 知识库实体
/// </summary>
public class KnowledgeBase : Entity, ISoftDelete
{
    /// <summary>
    /// 知识库名称
    /// </summary>
    public string Name { get; private set; } = string.Empty;
    
    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; private set; }
    
    /// <summary>
    /// 所有者ID（可以是用户或组织）
    /// </summary>
    public Guid OwnerId { get; private set; }
    
    /// <summary>
    /// 所有者类型
    /// </summary>
    public OwnerType OwnerType { get; private set; }
    
    /// <summary>
    /// 向量数据库类型
    /// </summary>
    public VectorDatabaseType VectorDbType { get; private set; }
    
    /// <summary>
    /// 向量数据库配置（JSON）
    /// </summary>
    public string VectorDbConfig { get; private set; } = "{}";
    
    /// <summary>
    /// 嵌入模型
    /// </summary>
    public string EmbeddingModel { get; private set; } = "text-embedding-3-small";
    
    /// <summary>
    /// 向量维度
    /// </summary>
    public int VectorDimension { get; private set; } = 1536;
    
    /// <summary>
    /// 分块策略配置（JSON）
    /// </summary>
    public string ChunkingConfig { get; private set; } = "{}";
    
    /// <summary>
    /// 文档数量
    /// </summary>
    public int DocumentCount { get; private set; }
    
    /// <summary>
    /// 总向量数量
    /// </summary>
    public int TotalVectors { get; private set; }
    
    /// <summary>
    /// 存储大小（字节）
    /// </summary>
    public long StorageSize { get; private set; }
    
    /// <summary>
    /// 是否公开
    /// </summary>
    public bool IsPublic { get; private set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public KnowledgeBaseStatus Status { get; private set; } = KnowledgeBaseStatus.Active;
    
    /// <summary>
    /// 元数据（JSON）
    /// </summary>
    public string? Metadata { get; private set; }
    
    /// <summary>
    /// 软删除标记
    /// </summary>
    public bool IsDeleted { get; private set; }
    
    /// <summary>
    /// 删除时间
    /// </summary>
    public DateTime? DeletedAt { get; private set; }
    
    /// <summary>
    /// 文档集合
    /// </summary>
    private readonly List<Document> _documents = new();
    public IReadOnlyList<Document> Documents => _documents.AsReadOnly();

    protected KnowledgeBase() { }

    public KnowledgeBase(
        string name,
        Guid ownerId,
        OwnerType ownerType,
        VectorDatabaseType vectorDbType,
        string embeddingModel,
        string? description = null)
    {
        Name = name;
        OwnerId = ownerId;
        OwnerType = ownerType;
        VectorDbType = vectorDbType;
        EmbeddingModel = embeddingModel;
        Description = description;
        
        // Set default vector dimension based on embedding model
        VectorDimension = embeddingModel switch
        {
            "text-embedding-ada-002" => 1536,
            "text-embedding-3-small" => 1536,
            "text-embedding-3-large" => 3072,
            _ => 1536
        };
    }

    public void UpdateName(string name)
    {
        Name = name;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateDescription(string? description)
    {
        Description = description;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateVectorDbConfig(string config)
    {
        VectorDbConfig = config;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateChunkingConfig(string config)
    {
        ChunkingConfig = config;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateStatus(KnowledgeBaseStatus status)
    {
        Status = status;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateStatistics(int documentCount, int totalVectors, long storageSize)
    {
        DocumentCount = documentCount;
        TotalVectors = totalVectors;
        StorageSize = storageSize;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetPublic(bool isPublic)
    {
        IsPublic = isPublic;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateMetadata(string? metadata)
    {
        Metadata = metadata;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Delete()
    {
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Restore()
    {
        IsDeleted = false;
        DeletedAt = null;
        UpdatedAt = DateTime.UtcNow;
    }
}