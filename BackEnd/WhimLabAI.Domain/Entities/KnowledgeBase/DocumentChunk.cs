using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.KnowledgeBase;

/// <summary>
/// 文档块实体（用于向量存储）
/// </summary>
public class DocumentChunk : Entity
{
    /// <summary>
    /// 文档ID
    /// </summary>
    public Guid DocumentId { get; private set; }
    
    /// <summary>
    /// 知识库ID
    /// </summary>
    public Guid KnowledgeBaseId { get; private set; }
    
    /// <summary>
    /// 块索引（在文档中的顺序）
    /// </summary>
    public int ChunkIndex { get; private set; }
    
    /// <summary>
    /// 块内容
    /// </summary>
    public string Content { get; private set; } = string.Empty;
    
    /// <summary>
    /// 内容哈希（用于去重）
    /// </summary>
    public string ContentHash { get; private set; } = string.Empty;
    
    /// <summary>
    /// 字符数
    /// </summary>
    public int CharacterCount { get; private set; }
    
    /// <summary>
    /// Token数
    /// </summary>
    public int TokenCount { get; private set; }
    
    /// <summary>
    /// 向量ID（在向量数据库中的ID）
    /// </summary>
    public string? VectorId { get; private set; }
    
    /// <summary>
    /// 是否已嵌入
    /// </summary>
    public bool IsEmbedded { get; private set; }
    
    /// <summary>
    /// 嵌入时间
    /// </summary>
    public DateTime? EmbeddedAt { get; private set; }
    
    /// <summary>
    /// 元数据（JSON）- 存储位置信息、页码等
    /// </summary>
    public string? Metadata { get; private set; }
    
    /// <summary>
    /// 关联的文档
    /// </summary>
    public Document? Document { get; private set; }

    protected DocumentChunk() { }

    public DocumentChunk(
        Guid documentId,
        Guid knowledgeBaseId,
        int chunkIndex,
        string content,
        string contentHash,
        int characterCount,
        int tokenCount,
        string? metadata = null)
    {
        DocumentId = documentId;
        KnowledgeBaseId = knowledgeBaseId;
        ChunkIndex = chunkIndex;
        Content = content;
        ContentHash = contentHash;
        CharacterCount = characterCount;
        TokenCount = tokenCount;
        Metadata = metadata;
    }

    public void MarkAsEmbedded(string vectorId)
    {
        VectorId = vectorId;
        IsEmbedded = true;
        EmbeddedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateContent(string content, string contentHash, int characterCount, int tokenCount)
    {
        Content = content;
        ContentHash = contentHash;
        CharacterCount = characterCount;
        TokenCount = tokenCount;
        IsEmbedded = false;
        VectorId = null;
        EmbeddedAt = null;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateMetadata(string? metadata)
    {
        Metadata = metadata;
        UpdatedAt = DateTime.UtcNow;
    }
}