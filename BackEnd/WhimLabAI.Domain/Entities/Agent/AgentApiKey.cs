using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Exceptions;
using System.Security.Cryptography;
using System.Text;

namespace WhimLabAI.Domain.Entities.Agent;

/// <summary>
/// Agent API密钥实体
/// 支持两种策略：
/// 1. Semantic Kernel - 共享系统级供应商密钥
/// 2. Dify - 每个Agent实例独立密钥
/// </summary>
public class AgentApiKey : Entity
{
    public Guid AgentId { get; private set; }
    public string KeyType { get; private set; } // "Dify" or "SystemProvider"
    public string ProviderName { get; private set; } // "OpenAI", "Anthropic", "Dify", etc.
    public string EncryptedKey { get; private set; }
    public string? DifyAppId { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    
    private AgentApiKey() : base() { }
    
    /// <summary>
    /// 创建Dify类型的API密钥
    /// </summary>
    public static AgentApiKey CreateForDify(
        Guid agentId, 
        string apiKey, 
        string difyAppId)
    {
        if (string.IsNullOrWhiteSpace(apiKey))
            throw new ValidationException("ApiKey", "API密钥不能为空");
            
        if (string.IsNullOrWhiteSpace(difyAppId))
            throw new ValidationException("DifyAppId", "Dify应用ID不能为空");
            
        return new AgentApiKey
        {
            Id = Guid.NewGuid(),
            AgentId = agentId,
            KeyType = "Dify",
            ProviderName = "Dify",
            EncryptedKey = EncryptApiKey(apiKey),
            DifyAppId = difyAppId,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };
    }
    
    /// <summary>
    /// 创建系统供应商类型的API密钥
    /// </summary>
    public static AgentApiKey CreateForSystemProvider(
        Guid agentId,
        string providerName,
        string apiKey)
    {
        if (string.IsNullOrWhiteSpace(providerName))
            throw new ValidationException("ProviderName", "供应商名称不能为空");
            
        if (string.IsNullOrWhiteSpace(apiKey))
            throw new ValidationException("ApiKey", "API密钥不能为空");
            
        return new AgentApiKey
        {
            Id = Guid.NewGuid(),
            AgentId = agentId,
            KeyType = "SystemProvider",
            ProviderName = providerName,
            EncryptedKey = EncryptApiKey(apiKey),
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };
    }
    
    /// <summary>
    /// 更新API密钥
    /// </summary>
    public void UpdateApiKey(string newApiKey)
    {
        if (string.IsNullOrWhiteSpace(newApiKey))
            throw new ValidationException("ApiKey", "API密钥不能为空");
            
        EncryptedKey = EncryptApiKey(newApiKey);
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 设置过期时间
    /// </summary>
    public void SetExpiration(DateTime? expiresAt)
    {
        ExpiresAt = expiresAt;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 停用密钥
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 激活密钥
    /// </summary>
    public void Activate()
    {
        IsActive = true;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 检查密钥是否过期
    /// </summary>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
    }
    
    /// <summary>
    /// 检查密钥是否可用
    /// </summary>
    public bool IsValid()
    {
        return IsActive && !IsExpired();
    }
    
    /// <summary>
    /// 加密API密钥
    /// TODO: 实际应用中应使用更安全的加密方式
    /// </summary>
    private static string EncryptApiKey(string apiKey)
    {
        // 简单的Base64编码，实际应用中需要使用AES等加密算法
        var bytes = Encoding.UTF8.GetBytes(apiKey);
        return Convert.ToBase64String(bytes);
    }
    
    /// <summary>
    /// 解密API密钥
    /// TODO: 实际应用中应使用相应的解密方式
    /// </summary>
    public string DecryptApiKey()
    {
        // 简单的Base64解码，实际应用中需要使用相应的解密算法
        var bytes = Convert.FromBase64String(EncryptedKey);
        return Encoding.UTF8.GetString(bytes);
    }
}