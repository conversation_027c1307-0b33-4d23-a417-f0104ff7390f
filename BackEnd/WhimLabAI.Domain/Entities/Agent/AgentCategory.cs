using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Agent;

public class AgentCategory : Entity
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string? Description { get; private set; }
    public string? Icon { get; private set; }
    public Guid? ParentId { get; private set; }
    public int SortOrder { get; private set; }
    public bool IsActive { get; private set; }
    public int AgentCount { get; private set; }
    
    public AgentCategory? Parent { get; private set; }
    public ICollection<AgentCategory> Children { get; private set; } = new List<AgentCategory>();
    public ICollection<Agent> Agents { get; private set; } = new List<Agent>();
    
    private AgentCategory() : base()
    {
    }
    
    public AgentCategory(
        string name,
        string displayName,
        string? description = null,
        string? icon = null,
        Guid? parentId = null) : base()
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description;
        Icon = icon;
        ParentId = parentId;
        SortOrder = 0;
        IsActive = true;
        AgentCount = 0;
    }
    
    public void Update(
        string displayName,
        string? description = null,
        string? icon = null)
    {
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description;
        Icon = icon;
        UpdateTimestamp();
    }
    
    public void SetParent(Guid? parentId)
    {
        if (parentId == Id)
            throw new InvalidOperationException("分类不能作为自己的父分类");
            
        ParentId = parentId;
        UpdateTimestamp();
    }
    
    public void SetSortOrder(int sortOrder)
    {
        SortOrder = sortOrder;
        UpdateTimestamp();
    }
    
    public void Activate()
    {
        IsActive = true;
        UpdateTimestamp();
    }
    
    public void Deactivate()
    {
        IsActive = false;
        UpdateTimestamp();
    }
    
    public void IncrementAgentCount()
    {
        AgentCount++;
        UpdateTimestamp();
    }
    
    public void DecrementAgentCount()
    {
        if (AgentCount > 0)
        {
            AgentCount--;
            UpdateTimestamp();
        }
    }
    
    public void RecalculateAgentCount(int count)
    {
        AgentCount = Math.Max(0, count);
        UpdateTimestamp();
    }
    
    public bool IsDescendantOf(Guid categoryId)
    {
        var current = Parent;
        while (current != null)
        {
            if (current.Id == categoryId)
                return true;
            current = current.Parent;
        }
        return false;
    }
    
    public List<AgentCategory> GetAllDescendants()
    {
        var descendants = new List<AgentCategory>();
        AddDescendants(this, descendants);
        return descendants;
    }
    
    private void AddDescendants(AgentCategory category, List<AgentCategory> descendants)
    {
        foreach (var child in category.Children)
        {
            descendants.Add(child);
            AddDescendants(child, descendants);
        }
    }
}