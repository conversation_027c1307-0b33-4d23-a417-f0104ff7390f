using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Agent;

/// <summary>
/// 评价有用性标记
/// </summary>
public class RatingHelpfulness : Entity
{
    /// <summary>
    /// 评价ID
    /// </summary>
    public Guid RatingId { get; private set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; private set; }
    
    /// <summary>
    /// 是否有用
    /// </summary>
    public bool IsHelpful { get; private set; }
    
    /// <summary>
    /// 标记时间
    /// </summary>
    public DateTime MarkedAt { get; private set; }
    
    private RatingHelpfulness() : base()
    {
    }
    
    public RatingHelpfulness(Guid ratingId, Guid userId, bool isHelpful) : base()
    {
        RatingId = ratingId;
        UserId = userId;
        IsHelpful = isHelpful;
        MarkedAt = DateTime.UtcNow;
    }
    
    public void Update(bool isHelpful)
    {
        IsHelpful = isHelpful;
        MarkedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
}