using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Agent;

public class AgentTag : ValueObject
{
    public string Name { get; }
    public string NormalizedName { get; }
    
    public AgentTag(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("标签名称不能为空", nameof(name));
            
        if (name.Length > 50)
            throw new ArgumentException("标签名称不能超过50个字符", nameof(name));
            
        Name = name.Trim();
        NormalizedName = Name.ToLowerInvariant();
    }
    
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return NormalizedName;
    }
    
    public override string ToString() => Name;
    
    public static implicit operator string(AgentTag tag) => tag.Name;
}