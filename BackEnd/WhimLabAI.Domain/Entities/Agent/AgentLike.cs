using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Agent;

/// <summary>
/// Agent点赞记录
/// </summary>
public class AgentLike : Entity
{
    /// <summary>
    /// Agent ID
    /// </summary>
    public Guid AgentId { get; private set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; private set; }
    
    /// <summary>
    /// 点赞时间
    /// </summary>
    public DateTime LikedAt { get; private set; }
    
    /// <summary>
    /// 导航属性 - Agent
    /// </summary>
    public virtual Agent Agent { get; private set; } = null!;
    
    private AgentLike() : base()
    {
    }
    
    public AgentLike(Guid agentId, Guid userId) : base()
    {
        AgentId = agentId;
        UserId = userId;
        LikedAt = DateTime.UtcNow;
    }
}