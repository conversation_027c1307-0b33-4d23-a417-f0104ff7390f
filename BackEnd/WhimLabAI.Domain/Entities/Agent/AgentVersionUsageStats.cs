using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Agent;

/// <summary>
/// AI代理版本使用统计
/// </summary>
public class AgentVersionUsageStats : Entity
{
    public Guid AgentVersionId { get; private set; }
    public DateTime Date { get; private set; }
    public int ConversationCount { get; private set; }
    public int MessageCount { get; private set; }
    public long TotalTokensConsumed { get; private set; }
    public int UniqueUserCount { get; private set; }
    public double AverageResponseTime { get; private set; }
    public int ErrorCount { get; private set; }
    public double SuccessRate { get; private set; }
    
    public AgentVersion AgentVersion { get; private set; } = null!;
    
    private AgentVersionUsageStats() : base()
    {
    }
    
    public AgentVersionUsageStats(Guid agentVersionId, DateTime date) : base()
    {
        AgentVersionId = agentVersionId;
        Date = date.Date; // Store only date part
        ConversationCount = 0;
        MessageCount = 0;
        TotalTokensConsumed = 0;
        UniqueUserCount = 0;
        AverageResponseTime = 0;
        ErrorCount = 0;
        SuccessRate = 100;
    }
    
    public void IncrementUsage(int messages, long tokens, double responseTime, bool hasError = false)
    {
        MessageCount += messages;
        TotalTokensConsumed += tokens;
        
        // Update average response time
        if (MessageCount > 0)
        {
            AverageResponseTime = ((AverageResponseTime * (MessageCount - messages)) + (responseTime * messages)) / MessageCount;
        }
        
        if (hasError)
        {
            ErrorCount++;
        }
        
        // Calculate success rate
        if (MessageCount > 0)
        {
            SuccessRate = ((double)(MessageCount - ErrorCount) / MessageCount) * 100;
        }
        
        UpdateTimestamp();
    }
    
    public void IncrementConversation()
    {
        ConversationCount++;
        UpdateTimestamp();
    }
    
    public void UpdateUniqueUsers(int count)
    {
        UniqueUserCount = count;
        UpdateTimestamp();
    }
}