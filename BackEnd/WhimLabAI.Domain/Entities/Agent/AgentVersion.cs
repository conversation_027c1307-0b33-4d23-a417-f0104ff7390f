using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.Entities.Agent;

public class AgentVersion : Entity
{
    private readonly List<string> _plugins = new();
    private readonly List<string> _knowledgeBases = new();
    private readonly List<AgentVersionReview> _reviews = new();
    private readonly List<AgentVersionUsageStats> _usageStats = new();
    
    public Guid AgentId { get; private set; }
    public int VersionNumber { get; private set; }
    public ModelConfiguration? ModelConfig { get; private set; }
    public string? SystemPrompt { get; private set; }
    public string? UserPrompt { get; private set; }
    public string? ChangeLog { get; private set; }
    public AgentStatus Status { get; private set; }
    public DateTime? PublishedAt { get; private set; }
    public string? PublishedBy { get; private set; }
    
    // Approval workflow fields
    public ReviewStatus ReviewStatus { get; private set; }
    public DateTime? SubmittedForReviewAt { get; private set; }
    public DateTime? LastPublishAttemptAt { get; private set; }
    public int PublishAttemptCount { get; private set; }
    
    public Agent Agent { get; private set; } = null!;
    public IReadOnlyCollection<string> Plugins => _plugins.AsReadOnly();
    public IReadOnlyCollection<string> KnowledgeBases => _knowledgeBases.AsReadOnly();
    public IReadOnlyCollection<AgentVersionReview> Reviews => _reviews.AsReadOnly();
    public IReadOnlyCollection<AgentVersionUsageStats> UsageStats => _usageStats.AsReadOnly();
    
    private AgentVersion() : base()
    {
    }
    
    public AgentVersion(Guid agentId, int versionNumber) : base()
    {
        AgentId = agentId;
        VersionNumber = versionNumber;
        Status = AgentStatus.Draft;
        ReviewStatus = ReviewStatus.Pending;
        PublishAttemptCount = 0;
    }
    
    public void UpdateConfiguration(
        ModelConfiguration modelConfig,
        string? systemPrompt = null,
        string? userPrompt = null,
        List<string>? plugins = null,
        List<string>? knowledgeBases = null)
    {
        ModelConfig = modelConfig ?? throw new ArgumentNullException(nameof(modelConfig));
        SystemPrompt = systemPrompt;
        UserPrompt = userPrompt;
        
        _plugins.Clear();
        if (plugins != null)
            _plugins.AddRange(plugins.Distinct());
            
        _knowledgeBases.Clear();
        if (knowledgeBases != null)
            _knowledgeBases.AddRange(knowledgeBases.Distinct());
            
        UpdateTimestamp();
    }
    
    public void UpdatePrompts(string? systemPrompt, string? userPrompt)
    {
        SystemPrompt = systemPrompt;
        UserPrompt = userPrompt;
        UpdateTimestamp();
    }
    
    public void SetChangeLog(string changeLog)
    {
        ChangeLog = changeLog;
        UpdateTimestamp();
    }
    
    public void AddPlugin(string pluginId)
    {
        if (string.IsNullOrWhiteSpace(pluginId))
            return;
            
        if (!_plugins.Contains(pluginId))
        {
            _plugins.Add(pluginId);
            UpdateTimestamp();
        }
    }
    
    public void RemovePlugin(string pluginId)
    {
        if (_plugins.Remove(pluginId))
        {
            UpdateTimestamp();
        }
    }
    
    public void AddKnowledgeBase(string knowledgeBaseId)
    {
        if (string.IsNullOrWhiteSpace(knowledgeBaseId))
            return;
            
        if (!_knowledgeBases.Contains(knowledgeBaseId))
        {
            _knowledgeBases.Add(knowledgeBaseId);
            UpdateTimestamp();
        }
    }
    
    public void RemoveKnowledgeBase(string knowledgeBaseId)
    {
        if (_knowledgeBases.Remove(knowledgeBaseId))
        {
            UpdateTimestamp();
        }
    }
    
    public void Publish(string? publishedBy = null)
    {
        // Check if version is approved
        if (ReviewStatus != ReviewStatus.Approved)
        {
            throw new BusinessException("版本必须通过审核才能发布");
        }
        
        // Check publishing frequency restriction
        if (LastPublishAttemptAt.HasValue)
        {
            var hoursSinceLastAttempt = (DateTime.UtcNow - LastPublishAttemptAt.Value).TotalHours;
            if (hoursSinceLastAttempt < 24)
            {
                throw new BusinessException($"发布太频繁，请在 {24 - (int)hoursSinceLastAttempt} 小时后重试");
            }
        }
        
        Status = AgentStatus.Published;
        PublishedAt = DateTime.UtcNow;
        PublishedBy = publishedBy;
        LastPublishAttemptAt = DateTime.UtcNow;
        PublishAttemptCount++;
        UpdateTimestamp();
    }
    
    public void Archive()
    {
        Status = AgentStatus.Archived;
        UpdateTimestamp();
    }
    
    public bool IsValid()
    {
        return ModelConfig != null && 
               !string.IsNullOrWhiteSpace(SystemPrompt);
    }
    
    public AgentVersion Clone(int newVersionNumber)
    {
        var clone = new AgentVersion(AgentId, newVersionNumber)
        {
            ModelConfig = ModelConfig,
            SystemPrompt = SystemPrompt,
            UserPrompt = UserPrompt
        };
        
        clone._plugins.AddRange(_plugins);
        clone._knowledgeBases.AddRange(_knowledgeBases);
        
        return clone;
    }
    
    // Approval workflow methods
    public void SubmitForReview()
    {
        if (!IsValid())
        {
            throw new BusinessException("版本配置不完整，无法提交审核");
        }
        
        if (Status == AgentStatus.Published)
        {
            throw new BusinessException("已发布的版本不能再次提交审核");
        }
        
        ReviewStatus = ReviewStatus.Pending;
        SubmittedForReviewAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void AddReview(Guid reviewerId, ReviewStatus status, string? comments)
    {
        var review = new AgentVersionReview(Id, reviewerId, status, comments);
        _reviews.Add(review);
        
        // Update version review status based on the review
        ReviewStatus = status;
        UpdateTimestamp();
    }
    
    public void RecordUsage(int messages, long tokens, double responseTime, bool hasError = false)
    {
        var today = DateTime.UtcNow.Date;
        var todayStats = _usageStats.FirstOrDefault(s => s.Date == today);
        
        if (todayStats == null)
        {
            todayStats = new AgentVersionUsageStats(Id, today);
            _usageStats.Add(todayStats);
        }
        
        todayStats.IncrementUsage(messages, tokens, responseTime, hasError);
    }
    
    public bool CanPublish()
    {
        // Check if approved
        if (ReviewStatus != ReviewStatus.Approved)
            return false;
            
        // Check if not already published
        if (Status == AgentStatus.Published)
            return false;
            
        // Check publishing frequency
        if (LastPublishAttemptAt.HasValue)
        {
            var hoursSinceLastAttempt = (DateTime.UtcNow - LastPublishAttemptAt.Value).TotalHours;
            if (hoursSinceLastAttempt < 24)
                return false;
        }
        
        return true;
    }
    
    public Dictionary<string, object> GetDetailedComparison(AgentVersion other)
    {
        var comparison = new Dictionary<string, object>();
        
        // Compare basic fields
        if (SystemPrompt != other.SystemPrompt)
        {
            comparison["SystemPrompt"] = new { Old = SystemPrompt, New = other.SystemPrompt };
        }
        
        if (UserPrompt != other.UserPrompt)
        {
            comparison["UserPrompt"] = new { Old = UserPrompt, New = other.UserPrompt };
        }
        
        // Compare model configuration
        if (ModelConfig?.ModelType != other.ModelConfig?.ModelType)
        {
            comparison["ModelType"] = new { Old = ModelConfig?.ModelType, New = other.ModelConfig?.ModelType };
        }
        
        if (ModelConfig?.ModelName != other.ModelConfig?.ModelName)
        {
            comparison["ModelName"] = new { Old = ModelConfig?.ModelName, New = other.ModelConfig?.ModelName };
        }
        
        if (ModelConfig?.Temperature != other.ModelConfig?.Temperature)
        {
            comparison["Temperature"] = new { Old = ModelConfig?.Temperature, New = other.ModelConfig?.Temperature };
        }
        
        if (ModelConfig?.MaxTokens != other.ModelConfig?.MaxTokens)
        {
            comparison["MaxTokens"] = new { Old = ModelConfig?.MaxTokens, New = other.ModelConfig?.MaxTokens };
        }
        
        // Compare plugins
        var addedPlugins = other._plugins.Except(_plugins).ToList();
        var removedPlugins = _plugins.Except(other._plugins).ToList();
        if (addedPlugins.Any() || removedPlugins.Any())
        {
            comparison["Plugins"] = new { Added = addedPlugins, Removed = removedPlugins };
        }
        
        // Compare knowledge bases
        var addedKbs = other._knowledgeBases.Except(_knowledgeBases).ToList();
        var removedKbs = _knowledgeBases.Except(other._knowledgeBases).ToList();
        if (addedKbs.Any() || removedKbs.Any())
        {
            comparison["KnowledgeBases"] = new { Added = addedKbs, Removed = removedKbs };
        }
        
        return comparison;
    }
}