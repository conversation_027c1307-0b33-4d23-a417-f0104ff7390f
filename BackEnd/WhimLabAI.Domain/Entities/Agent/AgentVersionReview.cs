using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.Agent;

/// <summary>
/// AI代理版本审核记录
/// </summary>
public class AgentVersionReview : Entity
{
    public Guid AgentVersionId { get; private set; }
    public Guid ReviewerId { get; private set; }
    public ReviewStatus Status { get; private set; }
    public string? Comments { get; private set; }
    public DateTime ReviewedAt { get; private set; }
    public Dictionary<string, object> ReviewDetails { get; private set; }
    
    public AgentVersion AgentVersion { get; private set; } = null!;
    
    private AgentVersionReview() : base()
    {
        ReviewDetails = new Dictionary<string, object>();
    }
    
    public AgentVersionReview(
        Guid agentVersionId,
        Guid reviewerId,
        ReviewStatus status,
        string? comments = null) : base()
    {
        AgentVersionId = agentVersionId;
        ReviewerId = reviewerId;
        Status = status;
        Comments = comments;
        ReviewedAt = DateTime.UtcNow;
        ReviewDetails = new Dictionary<string, object>();
    }
    
    public void UpdateReview(ReviewStatus status, string? comments)
    {
        Status = status;
        Comments = comments;
        ReviewedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void AddReviewDetail(string key, object value)
    {
        ReviewDetails[key] = value;
        UpdateTimestamp();
    }
}