using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Agent;

public class AgentRating : Entity
{
    public Guid UserId { get; private set; }
    public int Score { get; private set; }
    public string? Feedback { get; private set; }
    public DateTime RatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public bool IsVerifiedPurchase { get; private set; }
    public int HelpfulCount { get; private set; }
    public int UnhelpfulCount { get; private set; }
    
    private AgentRating() : base()
    {
    }
    
    public AgentRating(
        Guid userId,
        int score,
        string? feedback = null,
        bool isVerifiedPurchase = false) : base()
    {
        UserId = userId;
        SetScore(score);
        Feedback = feedback;
        RatedAt = DateTime.UtcNow;
        IsVerifiedPurchase = isVerifiedPurchase;
        HelpfulCount = 0;
        UnhelpfulCount = 0;
    }
    
    public void Update(int score, string? feedback = null)
    {
        SetScore(score);
        Feedback = feedback;
        UpdatedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void MarkAsHelpful()
    {
        HelpfulCount++;
        UpdateTimestamp();
    }
    
    public void MarkAsUnhelpful()
    {
        UnhelpfulCount++;
        UpdateTimestamp();
    }
    
    public void UnmarkAsHelpful()
    {
        if (HelpfulCount > 0)
        {
            HelpfulCount--;
            UpdateTimestamp();
        }
    }
    
    public void UnmarkAsUnhelpful()
    {
        if (UnhelpfulCount > 0)
        {
            UnhelpfulCount--;
            UpdateTimestamp();
        }
    }
    
    public double GetHelpfulnessScore()
    {
        var total = HelpfulCount + UnhelpfulCount;
        if (total == 0) return 0;
        return (double)HelpfulCount / total;
    }
    
    private void SetScore(int score)
    {
        if (score < 1 || score > 5)
            throw new ArgumentOutOfRangeException(nameof(score), "评分必须在1-5之间");
            
        Score = score;
    }
}