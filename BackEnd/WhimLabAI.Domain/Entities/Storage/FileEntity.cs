using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Storage;

/// <summary>
/// 文件实体
/// </summary>
public class FileEntity : Entity
{
    public string FileName { get; private set; }
    public string FileKey { get; private set; }
    public string ContentType { get; private set; }
    public long FileSize { get; private set; }
    public string? FileHash { get; private set; }
    public string StorageProvider { get; private set; }
    public string BucketName { get; private set; }
    public string FilePath { get; private set; }
    public Guid? UserId { get; private set; }
    public string? UserType { get; private set; } // "Customer" or "Admin"
    public string? Purpose { get; private set; }
    public bool IsTemporary { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public bool IsDeleted { get; private set; }
    public DateTime? DeletedAt { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }
    
    private FileEntity() : base() 
    {
        Metadata = new Dictionary<string, string>();
    }
    
    public FileEntity(
        string fileName,
        string fileKey,
        string contentType,
        long fileSize,
        string storageProvider,
        string bucketName,
        string filePath,
        Guid? userId = null,
        string? userType = null,
        string? purpose = null,
        bool isTemporary = false,
        DateTime? expiresAt = null,
        string? fileHash = null) : base()
    {
        FileName = fileName;
        FileKey = fileKey;
        ContentType = contentType;
        FileSize = fileSize;
        FileHash = fileHash;
        StorageProvider = storageProvider;
        BucketName = bucketName;
        FilePath = filePath;
        UserId = userId;
        UserType = userType;
        Purpose = purpose;
        IsTemporary = isTemporary;
        ExpiresAt = expiresAt;
        IsDeleted = false;
        Metadata = new Dictionary<string, string>();
    }
    
    public void MarkAsDeleted()
    {
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void AddMetadata(string key, string value)
    {
        Metadata[key] = value;
        UpdateTimestamp();
    }
    
    public void SetExpiration(DateTime expiresAt)
    {
        ExpiresAt = expiresAt;
        IsTemporary = true;
        UpdateTimestamp();
    }
    
    public bool IsExpired()
    {
        return IsTemporary && ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }
}