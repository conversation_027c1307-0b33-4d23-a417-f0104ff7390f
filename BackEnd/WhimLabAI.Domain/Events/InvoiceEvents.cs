using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.ValueObjects;

namespace WhimLabAI.Domain.Events;

/// <summary>
/// 发票已发出事件
/// </summary>
public class InvoiceIssuedEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public Money TotalAmount { get; }
    
    public InvoiceIssuedEvent(Guid invoiceId, Guid customerUserId, Money totalAmount) 
        : base(invoiceId)
    {
        CustomerUserId = customerUserId;
        TotalAmount = totalAmount;
    }
}

/// <summary>
/// 发票已支付事件
/// </summary>
public class InvoicePaidEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public Money TotalAmount { get; }
    public string PaymentMethod { get; }
    
    public InvoicePaidEvent(Guid invoiceId, Guid customerUserId, Money totalAmount, string paymentMethod) 
        : base(invoiceId)
    {
        CustomerUserId = customerUserId;
        TotalAmount = totalAmount;
        PaymentMethod = paymentMethod;
    }
}

/// <summary>
/// 发票已逾期事件
/// </summary>
public class InvoiceOverdueEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public Money TotalAmount { get; }
    
    public InvoiceOverdueEvent(Guid invoiceId, Guid customerUserId, Money totalAmount) 
        : base(invoiceId)
    {
        CustomerUserId = customerUserId;
        TotalAmount = totalAmount;
    }
}

/// <summary>
/// 发票已取消事件
/// </summary>
public class InvoiceCancelledEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public string Reason { get; }
    
    public InvoiceCancelledEvent(Guid invoiceId, Guid customerUserId, string reason) 
        : base(invoiceId)
    {
        CustomerUserId = customerUserId;
        Reason = reason;
    }
}

/// <summary>
/// 发票已退款事件
/// </summary>
public class InvoiceRefundedEvent : DomainEvent
{
    public Guid CustomerUserId { get; }
    public Money TotalAmount { get; }
    public string Reason { get; }
    
    public InvoiceRefundedEvent(Guid invoiceId, Guid customerUserId, Money totalAmount, string reason) 
        : base(invoiceId)
    {
        CustomerUserId = customerUserId;
        TotalAmount = totalAmount;
        Reason = reason;
    }
}