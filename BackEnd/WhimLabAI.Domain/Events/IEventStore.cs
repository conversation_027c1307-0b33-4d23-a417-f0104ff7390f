using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Events;

/// <summary>
/// 事件存储接口
/// </summary>
public interface IEventStore
{
    /// <summary>
    /// 保存事件
    /// </summary>
    Task SaveEventsAsync<TAggregate>(
        Guid aggregateId, 
        IEnumerable<IDomainEvent> events, 
        int expectedVersion,
        CancellationToken cancellationToken = default) 
        where TAggregate : AggregateRoot;

    /// <summary>
    /// 获取聚合根的所有事件
    /// </summary>
    Task<IReadOnlyList<IDomainEvent>> GetEventsAsync(
        Guid aggregateId, 
        int? fromVersion = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 从事件重建聚合根
    /// </summary>
    Task<TAggregate?> GetAggregateAsync<TAggregate>(
        Guid aggregateId,
        CancellationToken cancellationToken = default) 
        where TAggregate : AggregateRoot, new();

    /// <summary>
    /// 保存聚合根快照
    /// </summary>
    Task SaveSnapshotAsync<TAggregate>(
        TAggregate aggregate,
        CancellationToken cancellationToken = default) 
        where TAggregate : AggregateRoot;

    /// <summary>
    /// 获取聚合根的最新快照
    /// </summary>
    Task<TAggregate?> GetAggregateFromSnapshotAsync<TAggregate>(
        Guid aggregateId,
        CancellationToken cancellationToken = default) 
        where TAggregate : AggregateRoot;
}