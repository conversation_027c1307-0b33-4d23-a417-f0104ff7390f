using System.Text.Json;
using StackExchange.Redis;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Caching;

public class RedisCacheService : ICacheService
{
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _database;
    private readonly JsonSerializerOptions _jsonOptions;

    public RedisCacheService(IConnectionMultiplexer redis)
    {
        _redis = redis;
        _database = _redis.GetDatabase();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            WriteIndented = false
        };
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        var value = await _database.StringGetAsync(key);
        if (value.IsNullOrEmpty)
        {
            return default;
        }

        return JsonSerializer.Deserialize<T>(value!, _jsonOptions);
    }

    public async Task<string?> GetStringAsync(string key, CancellationToken cancellationToken = default)
    {
        var value = await _database.StringGetAsync(key);
        return value.IsNullOrEmpty ? null : value.ToString();
    }

    public async Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        var json = JsonSerializer.Serialize(value, _jsonOptions);
        return await _database.StringSetAsync(key, json, expiry);
    }

    public async Task<bool> SetStringAsync(string key, string value, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        return await _database.StringSetAsync(key, value, expiry);
    }

    public async Task<bool> RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        return await _database.KeyDeleteAsync(key);
    }

    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        return await _database.KeyExistsAsync(key);
    }

    public async Task<bool> ExpireAsync(string key, TimeSpan expiry, CancellationToken cancellationToken = default)
    {
        return await _database.KeyExpireAsync(key, expiry);
    }

    public async Task<TimeSpan?> GetTimeToLiveAsync(string key, CancellationToken cancellationToken = default)
    {
        var ttl = await _database.KeyTimeToLiveAsync(key);
        return ttl;
    }

    // Hash operations
    public async Task<bool> HashSetAsync<T>(string key, string field, T value, CancellationToken cancellationToken = default)
    {
        var json = JsonSerializer.Serialize(value, _jsonOptions);
        await _database.HashSetAsync(key, field, json);
        return true;
    }

    public async Task<T?> HashGetAsync<T>(string key, string field, CancellationToken cancellationToken = default)
    {
        var value = await _database.HashGetAsync(key, field);
        if (value.IsNullOrEmpty)
        {
            return default;
        }

        return JsonSerializer.Deserialize<T>(value!, _jsonOptions);
    }

    public async Task<Dictionary<string, T>> HashGetAllAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        var hashEntries = await _database.HashGetAllAsync(key);
        var result = new Dictionary<string, T>();

        foreach (var entry in hashEntries)
        {
            if (!entry.Value.IsNullOrEmpty)
            {
                var value = JsonSerializer.Deserialize<T>(entry.Value!, _jsonOptions);
                if (value != null)
                {
                    result[entry.Name!] = value;
                }
            }
        }

        return result;
    }

    public async Task<bool> HashDeleteAsync(string key, string field, CancellationToken cancellationToken = default)
    {
        return await _database.HashDeleteAsync(key, field);
    }

    public async Task<bool> HashExistsAsync(string key, string field, CancellationToken cancellationToken = default)
    {
        return await _database.HashExistsAsync(key, field);
    }

    // List operations
    public async Task<long> ListPushAsync<T>(string key, T value, bool toEnd = true, CancellationToken cancellationToken = default)
    {
        var json = JsonSerializer.Serialize(value, _jsonOptions);
        return toEnd
            ? await _database.ListRightPushAsync(key, json)
            : await _database.ListLeftPushAsync(key, json);
    }

    public async Task<T?> ListPopAsync<T>(string key, bool fromEnd = true, CancellationToken cancellationToken = default)
    {
        var value = fromEnd
            ? await _database.ListRightPopAsync(key)
            : await _database.ListLeftPopAsync(key);

        if (value.IsNullOrEmpty)
        {
            return default;
        }

        return JsonSerializer.Deserialize<T>(value!, _jsonOptions);
    }

    public async Task<List<T>> ListRangeAsync<T>(string key, long start = 0, long stop = -1, CancellationToken cancellationToken = default)
    {
        var values = await _database.ListRangeAsync(key, start, stop);
        var result = new List<T>();

        foreach (var value in values)
        {
            if (!value.IsNullOrEmpty)
            {
                var item = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                if (item != null)
                {
                    result.Add(item);
                }
            }
        }

        return result;
    }

    public async Task<long> ListLengthAsync(string key, CancellationToken cancellationToken = default)
    {
        return await _database.ListLengthAsync(key);
    }

    // Set operations
    public async Task<bool> SetAddAsync<T>(string key, T value, CancellationToken cancellationToken = default)
    {
        var json = JsonSerializer.Serialize(value, _jsonOptions);
        return await _database.SetAddAsync(key, json);
    }

    public async Task<bool> SetRemoveAsync<T>(string key, T value, CancellationToken cancellationToken = default)
    {
        var json = JsonSerializer.Serialize(value, _jsonOptions);
        return await _database.SetRemoveAsync(key, json);
    }

    public async Task<bool> SetContainsAsync<T>(string key, T value, CancellationToken cancellationToken = default)
    {
        var json = JsonSerializer.Serialize(value, _jsonOptions);
        return await _database.SetContainsAsync(key, json);
    }

    public async Task<HashSet<T>> SetMembersAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        var values = await _database.SetMembersAsync(key);
        var result = new HashSet<T>();

        foreach (var value in values)
        {
            if (!value.IsNullOrEmpty)
            {
                var item = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                if (item != null)
                {
                    result.Add(item);
                }
            }
        }

        return result;
    }

    // Atomic operations
    public async Task<long> IncrementAsync(string key, long value = 1, CancellationToken cancellationToken = default)
    {
        return await _database.StringIncrementAsync(key, value);
    }

    public async Task<long> DecrementAsync(string key, long value = 1, CancellationToken cancellationToken = default)
    {
        return await _database.StringDecrementAsync(key, value);
    }

    // Pattern operations
    public async Task<List<string>> GetKeysAsync(string pattern, CancellationToken cancellationToken = default)
    {
        var keys = new List<string>();
        var endpoints = _redis.GetEndPoints();
        
        foreach (var endpoint in endpoints)
        {
            var server = _redis.GetServer(endpoint);
            
            // Use SCAN instead of KEYS for production safety
            // SCAN is cursor-based and doesn't block the server
            var pageSize = 250; // Redis default page size
            await foreach (var key in server.KeysAsync(pattern: pattern, pageSize: pageSize))
            {
                if (cancellationToken.IsCancellationRequested)
                    break;
                    
                keys.Add(key.ToString());
            }
        }

        return keys;
    }

    public async Task<bool> RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        var keys = await GetKeysAsync(pattern, cancellationToken);
        if (keys.Any())
        {
            var redisKeys = keys.Select(k => (RedisKey)k).ToArray();
            var deletedCount = await _database.KeyDeleteAsync(redisKeys);
            return deletedCount > 0;
        }
        return false;
    }

    // Transaction support
    public async Task<bool> LockAsync(string key, string value, TimeSpan expiry, CancellationToken cancellationToken = default)
    {
        return await _database.StringSetAsync(key, value, expiry, When.NotExists);
    }

    public async Task<bool> UnlockAsync(string key, string value, CancellationToken cancellationToken = default)
    {
        var script = @"
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end";
        
        var result = await _database.ScriptEvaluateAsync(script, new RedisKey[] { key }, new RedisValue[] { value });
        return (bool)result;
    }
    
    // Cache-Aside Pattern implementation
    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        // Try to get from cache first
        var cached = await GetAsync<T>(key, cancellationToken);
        if (cached != null)
        {
            return cached;
        }
        
        // Use distributed lock to prevent cache stampede
        var lockKey = $"{key}:lock";
        var lockValue = Guid.NewGuid().ToString();
        var lockExpiry = TimeSpan.FromSeconds(30); // Lock timeout
        
        // Try to acquire lock
        var lockAcquired = await LockAsync(lockKey, lockValue, lockExpiry, cancellationToken);
        if (!lockAcquired)
        {
            // Wait a bit and try to get from cache again
            await Task.Delay(100, cancellationToken);
            cached = await GetAsync<T>(key, cancellationToken);
            if (cached != null)
            {
                return cached;
            }
            
            // If still no cache, execute factory anyway (failsafe)
        }
        
        try
        {
            // Double-check after acquiring lock
            cached = await GetAsync<T>(key, cancellationToken);
            if (cached != null)
            {
                return cached;
            }
            
            // Execute factory function
            var value = await factory();
            
            // Store in cache
            if (value != null)
            {
                await SetAsync(key, value, expiry, cancellationToken);
            }
            
            return value;
        }
        finally
        {
            // Release lock if we acquired it
            if (lockAcquired)
            {
                await UnlockAsync(lockKey, lockValue, cancellationToken);
            }
        }
    }
}