using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Caching;

/// <summary>
/// 缓存预热服务，在应用启动时预加载常用数据到缓存
/// </summary>
public class CacheWarmingService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CacheWarmingService> _logger;
    private readonly CacheOptions _cacheOptions;

    public CacheWarmingService(
        IServiceProvider serviceProvider,
        ILogger<CacheWarmingService> logger,
        IOptions<CacheOptions> cacheOptions)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _cacheOptions = cacheOptions.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_cacheOptions.EnableCacheWarming)
        {
            _logger.LogInformation("Cache warming is disabled");
            return;
        }

        _logger.LogInformation("Starting cache warming service");

        try
        {
            await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken); // 等待应用完全启动

            var stopwatch = Stopwatch.StartNew();
            await WarmUpCacheAsync(stoppingToken);
            stopwatch.Stop();

            _logger.LogInformation("Cache warming completed in {ElapsedMilliseconds}ms", stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache warming");
        }
    }

    private async Task WarmUpCacheAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var cacheService = scope.ServiceProvider.GetRequiredService<ICacheService>();

        var warmingTasks = new List<Task>
        {
            // WarmUpSubscriptionPlansAsync(scope, cancellationToken),
            // WarmUpAgentCategoriesAsync(scope, cancellationToken),
            // WarmUpPermissionsAsync(scope, cancellationToken),
            // WarmUpTopRatedAgentsAsync(scope, cancellationToken),
            WarmUpSystemConfigurationAsync(scope, cancellationToken)
        };

        await Task.WhenAll(warmingTasks);
    }

    private async Task WarmUpSubscriptionPlansAsync(IServiceScope scope, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Warming up subscription plans cache");
            
            var subscriptionPlanService = scope.ServiceProvider.GetRequiredService<ISubscriptionPlanService>();
            
            // 预热所有订阅计划
            await subscriptionPlanService.GetAllAsync(cancellationToken);
            
            // 预热活跃订阅计划
            await subscriptionPlanService.GetActiveAsync(cancellationToken);
            
            _logger.LogDebug("Subscription plans cache warmed up successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to warm up subscription plans cache");
        }
    }

    private async Task WarmUpAgentCategoriesAsync(IServiceScope scope, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Warming up agent categories cache");
            
            var agentService = scope.ServiceProvider.GetRequiredService<IAgentService>();
            
            // 预热代理分类
            await agentService.GetCategoriesAsync(cancellationToken);
            
            // 预热热门代理
            await agentService.GetTopRatedAsync(10, cancellationToken);
            
            // 预热第一页已发布的代理
            await agentService.GetPublishedAsync(1, 20, null, null, cancellationToken);
            
            _logger.LogDebug("Agent categories cache warmed up successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to warm up agent categories cache");
        }
    }

    private async Task WarmUpPermissionsAsync(IServiceScope scope, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Warming up permissions cache");
            
            var permissionService = scope.ServiceProvider.GetRequiredService<IPermissionService>();
            
            // 预热所有权限
            await permissionService.GetAllAsync(cancellationToken);
            
            _logger.LogDebug("Permissions cache warmed up successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to warm up permissions cache");
        }
    }

    private async Task WarmUpTopRatedAgentsAsync(IServiceScope scope, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Warming up top rated agents cache");
            
            var agentService = scope.ServiceProvider.GetRequiredService<IAgentService>();
            
            // 预热评分最高的代理
            await agentService.GetTopRatedAsync(10, cancellationToken);
            await agentService.GetTopRatedAsync(20, cancellationToken);
            
            _logger.LogDebug("Top rated agents cache warmed up successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to warm up top rated agents cache");
        }
    }

    private async Task WarmUpSystemConfigurationAsync(IServiceScope scope, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Warming up system configuration cache");
            
            var cacheService = scope.ServiceProvider.GetRequiredService<ICacheService>();
            
            // 这里可以预热系统配置
            // 例如：功能开关、系统设置等
            await cacheService.SetAsync(
                CacheKeys.Configuration.GetFeatureFlags(),
                new Dictionary<string, bool>
                {
                    ["EnableNewUI"] = true,
                    ["EnableBetaFeatures"] = false,
                    ["EnableMaintenanceMode"] = false
                },
                CacheExpiration.Configuration,
                cancellationToken
            );
            
            _logger.LogDebug("System configuration cache warmed up successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to warm up system configuration cache");
        }
    }
}