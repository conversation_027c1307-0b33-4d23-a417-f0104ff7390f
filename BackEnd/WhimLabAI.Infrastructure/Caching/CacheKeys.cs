using System;

namespace WhimLabAI.Infrastructure.Caching;

/// <summary>
/// 缓存键管理器，统一管理所有缓存键
/// </summary>
public static class CacheKeys
{
    // 缓存键前缀
    private const string Prefix = "whimlabai:";
    
    // 用户相关缓存键
    public static class User
    {
        public static string GetProfile(Guid userId) => $"{Prefix}user:profile:{userId}";
        public static string GetPermissions(Guid userId) => $"{Prefix}user:permissions:{userId}";
        public static string GetRoles(Guid userId) => $"{Prefix}user:roles:{userId}";
        public static string GetTokenQuota(Guid userId) => $"{Prefix}user:quota:{userId}";
        public static string GetSubscription(Guid userId) => $"{Prefix}user:subscription:{userId}";
        public static string GetActiveSubscription(Guid userId) => $"{Prefix}user:subscription:active:{userId}";
    }
    
    // AI代理相关缓存键
    public static class Agent
    {
        public static string GetDetails(Guid agentId) => $"{Prefix}agent:details:{agentId}";
        public static string GetList(int page, int pageSize, string? status = null, string? category = null) 
            => $"{Prefix}agent:list:p{page}:s{pageSize}:st{status ?? "all"}:c{category ?? "all"}";
        public static string GetPublishedList(int page, int pageSize) => $"{Prefix}agent:published:p{page}:s{pageSize}";
        public static string GetUserAgents(Guid userId) => $"{Prefix}agent:user:{userId}";
        public static string GetCategories() => $"{Prefix}agent:categories:all";
        public static string GetTopRated(int count) => $"{Prefix}agent:toprated:{count}";
        public static string GetMarketplace(int page, int pageSize, string? category = null, string? sortBy = null) 
            => $"{Prefix}agent:marketplace:p{page}:s{pageSize}:c{category ?? "all"}:sort{sortBy ?? "default"}";
    }
    
    // 订阅计划相关缓存键
    public static class SubscriptionPlan
    {
        public static string GetAll() => $"{Prefix}subscription:plans:all";
        public static string GetActive() => $"{Prefix}subscription:plans:active";
        public static string GetById(Guid planId) => $"{Prefix}subscription:plan:{planId}";
    }
    
    // 权限相关缓存键
    public static class Permission
    {
        public static string GetAll() => $"{Prefix}permission:all";
        public static string GetByRole(Guid roleId) => $"{Prefix}permission:role:{roleId}";
        public static string GetUserPermissions(Guid userId) => $"{Prefix}permission:user:{userId}";
    }
    
    // 会话相关缓存键
    public static class Session
    {
        public static string GetUserSession(Guid userId) => $"{Prefix}session:user:{userId}";
        public static string GetRefreshToken(string token) => $"{Prefix}session:refresh:{token}";
        public static string GetApiKey(string apiKey) => $"{Prefix}session:apikey:{apiKey}";
    }
    
    // 对话相关缓存键
    public static class Conversation
    {
        public static string GetDetails(Guid conversationId) => $"{Prefix}conversation:details:{conversationId}";
        public static string GetUserConversations(Guid userId, int page, int pageSize) 
            => $"{Prefix}conversation:user:{userId}:p{page}:s{pageSize}";
        public static string GetMessages(Guid conversationId, int page, int pageSize) 
            => $"{Prefix}conversation:messages:{conversationId}:p{page}:s{pageSize}";
        public static string GetContext(Guid conversationId) => $"{Prefix}conversation:context:{conversationId}";
    }
    
    // 统计相关缓存键
    public static class Statistics
    {
        public static string GetDashboard(string period) => $"{Prefix}stats:dashboard:{period}";
        public static string GetUserStats(Guid userId) => $"{Prefix}stats:user:{userId}";
        public static string GetAgentStats(Guid agentId) => $"{Prefix}stats:agent:{agentId}";
        public static string GetSystemMetrics() => $"{Prefix}stats:system:metrics";
        public static string GetTokenUsage(Guid userId, string period) => $"{Prefix}stats:tokenusage:{userId}:{period}";
    }
    
    // 支付相关缓存键
    public static class Payment
    {
        public static string GetOrderStatus(string orderNo) => $"{Prefix}payment:order:{orderNo}";
        public static string GetUserOrders(Guid userId, int page, int pageSize) 
            => $"{Prefix}payment:orders:user:{userId}:p{page}:s{pageSize}";
        public static string GetPaymentMethods() => $"{Prefix}payment:methods:all";
    }
    
    // 通知相关缓存键
    public static class Notification
    {
        public static string GetUnreadCount(Guid userId) => $"{Prefix}notification:unread:{userId}";
        public static string GetUserNotifications(Guid userId, int page, int pageSize) 
            => $"{Prefix}notification:user:{userId}:p{page}:s{pageSize}";
    }
    
    // 文件相关缓存键
    public static class File
    {
        public static string GetMetadata(Guid fileId) => $"{Prefix}file:metadata:{fileId}";
        public static string GetAccessUrl(Guid fileId) => $"{Prefix}file:url:{fileId}";
    }
    
    // 知识库相关缓存键
    public static class KnowledgeBase
    {
        public static string GetDetails(Guid knowledgeBaseId) => $"{Prefix}kb:details:{knowledgeBaseId}";
        public static string GetUserKnowledgeBases(Guid userId) => $"{Prefix}kb:user:{userId}";
        public static string GetDocuments(Guid knowledgeBaseId) => $"{Prefix}kb:documents:{knowledgeBaseId}";
    }
    
    // 搜索相关缓存键
    public static class Search
    {
        public static string GetResults(string query, int page, int pageSize) 
            => $"{Prefix}search:results:{GetQueryHash(query)}:p{page}:s{pageSize}";
        public static string GetSuggestions(string query) => $"{Prefix}search:suggestions:{GetQueryHash(query)}";
        
        private static string GetQueryHash(string query)
        {
            // 简单的哈希实现，实际可以使用更复杂的哈希算法
            return query.ToLowerInvariant().Replace(" ", "_").Replace(":", "_");
        }
    }
    
    // 配置相关缓存键
    public static class Configuration
    {
        public static string GetSystemConfig() => $"{Prefix}config:system";
        public static string GetFeatureFlags() => $"{Prefix}config:features";
        public static string GetMaintenanceMode() => $"{Prefix}config:maintenance";
    }
}