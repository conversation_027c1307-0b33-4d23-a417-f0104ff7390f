using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Caching;

/// <summary>
/// 缓存失效服务，提供集中式的缓存失效管理
/// </summary>
public class CacheInvalidationService : ICacheInvalidationService
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<CacheInvalidationService> _logger;

    public CacheInvalidationService(
        ICacheService cacheService,
        ILogger<CacheInvalidationService> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    /// <summary>
    /// 使用户相关的所有缓存失效
    /// </summary>
    public async Task InvalidateUserCacheAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Invalidating all cache for user {UserId}", userId);

        var tasks = new[]
        {
            _cacheService.RemoveAsync(CacheKeys.User.GetProfile(userId), cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.User.GetPermissions(userId), cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.User.GetRoles(userId), cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.User.GetTokenQuota(userId), cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.User.GetSubscription(userId), cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.User.GetActiveSubscription(userId), cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.Statistics.GetUserStats(userId), cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.Agent.GetUserAgents(userId), cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:conversation:user:{userId}:*", cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:notification:*:{userId}*", cancellationToken)
        };

        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// 使AI代理相关的所有缓存失效
    /// </summary>
    public async Task InvalidateAgentCacheAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Invalidating all cache for agent {AgentId}", agentId);

        // 获取代理详情以找到所有者
        var agentCacheKey = CacheKeys.Agent.GetDetails(agentId);
        var agent = await _cacheService.GetAsync<dynamic>(agentCacheKey, cancellationToken);

        var tasks = new[]
        {
            _cacheService.RemoveAsync(agentCacheKey, cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.Statistics.GetAgentStats(agentId), cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:agent:list:*", cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:agent:published:*", cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:agent:marketplace:*", cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:agent:toprated:*", cancellationToken)
        };

        await Task.WhenAll(tasks);

        // 如果能获取到代理信息，使代理所有者的缓存失效
        if (agent != null && agent.CreatedBy != null)
        {
            await _cacheService.RemoveAsync(CacheKeys.Agent.GetUserAgents((Guid)agent.CreatedBy), cancellationToken);
        }
    }

    /// <summary>
    /// 使订阅相关的所有缓存失效
    /// </summary>
    public async Task InvalidateSubscriptionCacheAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Invalidating subscription cache for user {UserId}", userId);

        var tasks = new[]
        {
            _cacheService.RemoveAsync(CacheKeys.User.GetSubscription(userId), cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.User.GetActiveSubscription(userId), cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.User.GetTokenQuota(userId), cancellationToken)
        };

        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// 使权限相关的所有缓存失效
    /// </summary>
    public async Task InvalidatePermissionCacheAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Invalidating all permission cache");

        var tasks = new[]
        {
            _cacheService.RemoveAsync(CacheKeys.Permission.GetAll(), cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:permission:role:*", cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:permission:user:*", cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:user:permissions:*", cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:user:roles:*", cancellationToken)
        };

        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// 使对话相关的缓存失效
    /// </summary>
    public async Task InvalidateConversationCacheAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Invalidating conversation cache for conversation {ConversationId}", conversationId);

        var tasks = new[]
        {
            _cacheService.RemoveAsync(CacheKeys.Conversation.GetDetails(conversationId), cancellationToken),
            _cacheService.RemoveAsync(CacheKeys.Conversation.GetContext(conversationId), cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:conversation:messages:{conversationId}:*", cancellationToken),
            _cacheService.RemoveByPatternAsync($"*:conversation:user:{userId}:*", cancellationToken)
        };

        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// 使统计数据缓存失效
    /// </summary>
    public async Task InvalidateStatisticsCacheAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Invalidating all statistics cache");

        await _cacheService.RemoveByPatternAsync($"*:stats:*", cancellationToken);
    }

    /// <summary>
    /// 使搜索结果缓存失效
    /// </summary>
    public async Task InvalidateSearchCacheAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Invalidating all search cache");

        await _cacheService.RemoveByPatternAsync($"*:search:*", cancellationToken);
    }

    /// <summary>
    /// 使所有缓存失效（谨慎使用）
    /// </summary>
    public async Task InvalidateAllCacheAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("Invalidating ALL cache - this operation may impact performance");

        // 使用缓存键前缀清除所有缓存
        await _cacheService.RemoveByPatternAsync("whimlabai:*", cancellationToken);
    }
}