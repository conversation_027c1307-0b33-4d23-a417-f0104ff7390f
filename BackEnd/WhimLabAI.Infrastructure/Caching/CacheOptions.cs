using System;

namespace WhimLabAI.Infrastructure.Caching;

/// <summary>
/// 缓存配置选项
/// </summary>
public class CacheOptions
{
    /// <summary>
    /// 默认缓存过期时间（分钟）
    /// </summary>
    public int DefaultExpirationMinutes { get; set; } = 30;
    
    /// <summary>
    /// 短期缓存过期时间（分钟）
    /// </summary>
    public int ShortExpirationMinutes { get; set; } = 5;
    
    /// <summary>
    /// 长期缓存过期时间（分钟）
    /// </summary>
    public int LongExpirationMinutes { get; set; } = 1440; // 24小时
    
    /// <summary>
    /// 用户信息缓存过期时间（分钟）
    /// </summary>
    public int UserProfileExpirationMinutes { get; set; } = 60;
    
    /// <summary>
    /// AI代理列表缓存过期时间（分钟）
    /// </summary>
    public int AgentListExpirationMinutes { get; set; } = 15;
    
    /// <summary>
    /// 订阅计划缓存过期时间（分钟）
    /// </summary>
    public int SubscriptionPlanExpirationMinutes { get; set; } = 1440; // 24小时
    
    /// <summary>
    /// 权限数据缓存过期时间（分钟）
    /// </summary>
    public int PermissionExpirationMinutes { get; set; } = 120;
    
    /// <summary>
    /// 会话数据缓存过期时间（分钟）
    /// </summary>
    public int SessionExpirationMinutes { get; set; } = 60;
    
    /// <summary>
    /// 统计数据缓存过期时间（分钟）
    /// </summary>
    public int StatisticsExpirationMinutes { get; set; } = 5;
    
    /// <summary>
    /// 搜索结果缓存过期时间（分钟）
    /// </summary>
    public int SearchResultExpirationMinutes { get; set; } = 10;
    
    /// <summary>
    /// 是否启用缓存预热
    /// </summary>
    public bool EnableCacheWarming { get; set; } = true;
    
    /// <summary>
    /// 缓存预热批次大小
    /// </summary>
    public int CacheWarmingBatchSize { get; set; } = 100;
    
    /// <summary>
    /// 是否启用缓存压缩
    /// </summary>
    public bool EnableCompression { get; set; } = false;
    
    /// <summary>
    /// 压缩阈值（字节）
    /// </summary>
    public int CompressionThreshold { get; set; } = 1024; // 1KB
}

/// <summary>
/// 缓存过期时间预设
/// </summary>
public static class CacheExpiration
{
    public static readonly TimeSpan VeryShort = TimeSpan.FromMinutes(1);
    public static readonly TimeSpan Short = TimeSpan.FromMinutes(5);
    public static readonly TimeSpan Medium = TimeSpan.FromMinutes(30);
    public static readonly TimeSpan Long = TimeSpan.FromHours(1);
    public static readonly TimeSpan VeryLong = TimeSpan.FromHours(24);
    
    // 特定业务场景的过期时间
    public static readonly TimeSpan UserProfile = TimeSpan.FromHours(1);
    public static readonly TimeSpan AgentList = TimeSpan.FromMinutes(15);
    public static readonly TimeSpan SubscriptionPlan = TimeSpan.FromDays(1);
    public static readonly TimeSpan Permission = TimeSpan.FromHours(2);
    public static readonly TimeSpan Session = TimeSpan.FromHours(1);
    public static readonly TimeSpan Statistics = TimeSpan.FromMinutes(5);
    public static readonly TimeSpan SearchResult = TimeSpan.FromMinutes(10);
    public static readonly TimeSpan Configuration = TimeSpan.FromHours(12);
}