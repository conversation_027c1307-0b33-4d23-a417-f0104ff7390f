using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Infrastructure.Auditing;
using WhimLabAI.Shared.Dtos.Audit;

namespace WhimLabAI.Infrastructure.BackgroundJobs;

/// <summary>
/// 审计日志维护后台任务
/// 负责定期清理、归档和优化审计日志
/// </summary>
public class AuditLogMaintenanceJob : BackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<AuditLogMaintenanceJob> _logger;
    private readonly AuditOptions _auditOptions;
    private readonly AuditArchiveOptions _archiveOptions;
    private Timer? _cleanupTimer;
    private Timer? _archiveTimer;

    public AuditLogMaintenanceJob(
        IServiceScopeFactory scopeFactory,
        ILogger<AuditLogMaintenanceJob> logger,
        IOptions<AuditOptions> auditOptions,
        IOptions<AuditArchiveOptions> archiveOptions)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
        _auditOptions = auditOptions.Value;
        _archiveOptions = archiveOptions.Value;
    }

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (_auditOptions.EnableAutoCleanup)
        {
            // 设置清理定时器（每天执行）
            _cleanupTimer = new Timer(
                async _ => await PerformCleanupAsync(stoppingToken),
                null,
                TimeSpan.FromMinutes(1), // 启动后1分钟首次执行
                TimeSpan.FromHours(24)   // 每24小时执行一次
            );
        }

        if (_archiveOptions.AutoArchiveEnabled)
        {
            // 设置归档定时器（每天执行）
            _archiveTimer = new Timer(
                async _ => await PerformArchiveAsync(stoppingToken),
                null,
                TimeSpan.FromMinutes(2), // 启动后2分钟首次执行
                TimeSpan.FromHours(24)   // 每24小时执行一次
            );
        }

        return Task.CompletedTask;
    }

    private async Task PerformCleanupAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting audit log cleanup job");

            using var scope = _scopeFactory.CreateScope();
            var auditLogService = scope.ServiceProvider.GetRequiredService<IAuditLogApplicationService>();

            // 清理旧日志
            await auditLogService.CleanupOldLogsAsync(_auditOptions.CleanupRetentionDays, cancellationToken);

            _logger.LogInformation("Audit log cleanup job completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during audit log cleanup");
        }
    }

    private async Task PerformArchiveAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting audit log archive job");

            using var scope = _scopeFactory.CreateScope();
            var auditLogService = scope.ServiceProvider.GetRequiredService<IAuditLogApplicationService>();

            // 创建归档请求
            var archiveRequest = new AuditLogArchiveRequestDto
            {
                ArchiveBeforeDate = DateTime.UtcNow.AddDays(-_archiveOptions.RetentionDays),
                ArchiveLocation = _archiveOptions.DefaultArchiveLocation,
                CompressArchive = true,
                DeleteAfterArchive = _archiveOptions.DeleteAfterArchive,
                ExcludeSensitiveData = false
            };

            var result = await auditLogService.ArchiveLogsAsync(archiveRequest, cancellationToken);

            if (result.Success)
            {
                _logger.LogInformation(
                    "Audit log archive job completed successfully. Archived {Count} logs to {Path}",
                    result.ArchivedCount, result.ArchivePath);
            }
            else
            {
                _logger.LogError("Audit log archive job failed: {Error}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during audit log archive");
        }
    }

    public override void Dispose()
    {
        _cleanupTimer?.Dispose();
        _archiveTimer?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// 审计日志性能优化任务
/// 定期分析和优化审计日志查询性能
/// </summary>
public class AuditLogPerformanceOptimizationJob : BackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<AuditLogPerformanceOptimizationJob> _logger;
    private Timer? _optimizationTimer;

    public AuditLogPerformanceOptimizationJob(
        IServiceScopeFactory scopeFactory,
        ILogger<AuditLogPerformanceOptimizationJob> logger)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
    }

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // 每周执行一次性能优化
        _optimizationTimer = new Timer(
            async _ => await OptimizePerformanceAsync(stoppingToken),
            null,
            TimeSpan.FromHours(1),     // 启动后1小时首次执行
            TimeSpan.FromDays(7)       // 每7天执行一次
        );

        return Task.CompletedTask;
    }

    private async Task OptimizePerformanceAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting audit log performance optimization");

            using var scope = _scopeFactory.CreateScope();
            
            // 这里可以执行以下优化任务：
            // 1. 重建索引
            // 2. 更新统计信息
            // 3. 分析查询性能
            // 4. 识别慢查询
            // 5. 建议新索引

            await Task.Delay(100, cancellationToken); // 模拟操作

            _logger.LogInformation("Audit log performance optimization completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during performance optimization");
        }
    }

    public override void Dispose()
    {
        _optimizationTimer?.Dispose();
        base.Dispose();
    }
}