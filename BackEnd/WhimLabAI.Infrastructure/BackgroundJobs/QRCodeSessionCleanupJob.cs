using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.BackgroundJobs;

/// <summary>
/// 扫码会话清理后台任务
/// 负责定期清理过期的二维码会话记录并发送过期通知
/// </summary>
public class QRCodeSessionCleanupJob : BackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<QRCodeSessionCleanupJob> _logger;
    private Timer? _cleanupTimer;
    private const int CleanupIntervalMinutes = 5; // 每5分钟执行一次
    private const int ExpiryWarningMinutes = 1; // 提前1分钟发送过期提醒

    public QRCodeSessionCleanupJob(
        IServiceScopeFactory scopeFactory,
        ILogger<QRCodeSessionCleanupJob> logger)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
    }

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("QRCode session cleanup job is starting");

        // 设置定时器，每5分钟执行一次
        _cleanupTimer = new Timer(
            async _ => await PerformCleanupAsync(stoppingToken),
            null,
            TimeSpan.FromSeconds(30), // 启动后30秒首次执行
            TimeSpan.FromMinutes(CleanupIntervalMinutes) // 每5分钟执行一次
        );

        return Task.CompletedTask;
    }

    private async Task PerformCleanupAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Starting QRCode session cleanup");

            using var scope = _scopeFactory.CreateScope();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            var qrCodeSessionRepository = scope.ServiceProvider.GetRequiredService<IQRCodeSessionRepository>();
            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

            // 1. 获取所有会话记录，包括即将过期和已过期的
            var allSessions = await qrCodeSessionRepository.GetAllAsync(cancellationToken);
            var currentTime = DateTime.UtcNow;

            // 2. 处理即将过期的会话（提前1分钟发送通知）
            var soonToExpireSessions = allSessions
                .Where(s => s.Status == QRCodeSessionStatus.Scanned && 
                           s.UserId.HasValue &&
                           !s.IsExpired &&
                           s.ExpiresAt <= currentTime.AddMinutes(ExpiryWarningMinutes) &&
                           s.ExpiresAt > currentTime)
                .ToList();

            foreach (var session in soonToExpireSessions)
            {
                try
                {
                    // 发送即将过期通知
                    await notificationService.SendNotificationAsync(new SendNotificationDto
                    {
                        UserId = session.UserId!.Value,
                        Title = "扫码登录即将过期",
                        Content = $"您的扫码登录会话将在{ExpiryWarningMinutes}分钟内过期，请尽快完成登录确认。",
                        Type = "qrcode_expiry_warning",
                        Level = "warning",
                        Metadata = new Dictionary<string, object>
                        {
                            { "sessionId", session.SessionId },
                            { "expiresAt", session.ExpiresAt }
                        }
                    }, cancellationToken);

                    _logger.LogDebug("Sent expiry warning notification for session {SessionId}", session.SessionId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send expiry warning notification for session {SessionId}", session.SessionId);
                }
            }

            // 3. 标记过期的会话
            var expiredPendingSessions = allSessions
                .Where(s => s.IsExpired && 
                           (s.Status == QRCodeSessionStatus.Pending || s.Status == QRCodeSessionStatus.Scanned))
                .ToList();

            foreach (var session in expiredPendingSessions)
            {
                try
                {
                    session.MarkAsExpired();
                    qrCodeSessionRepository.Update(session);
                    _logger.LogDebug("Marked session {SessionId} as expired", session.SessionId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to mark session {SessionId} as expired", session.SessionId);
                }
            }

            // 4. 删除需要清理的会话
            var sessionsToDelete = allSessions
                .Where(s => ShouldDeleteSession(s, currentTime))
                .ToList();

            if (sessionsToDelete.Any())
            {
                qrCodeSessionRepository.RemoveRange(sessionsToDelete);

                _logger.LogInformation(
                    "Cleaned up {Count} QRCode sessions (Expired: {ExpiredCount}, Cancelled/Rejected: {CancelledCount})",
                    sessionsToDelete.Count,
                    sessionsToDelete.Count(s => s.Status == QRCodeSessionStatus.Expired),
                    sessionsToDelete.Count(s => s.Status == QRCodeSessionStatus.Cancelled || s.Status == QRCodeSessionStatus.Rejected)
                );
            }
            else
            {
                _logger.LogDebug("No QRCode sessions to clean up");
            }

            // 5. 保存所有更改
            if (expiredPendingSessions.Any() || sessionsToDelete.Any())
            {
                await unitOfWork.SaveChangesAsync(cancellationToken);
                
                if (expiredPendingSessions.Any())
                {
                    _logger.LogInformation("Updated {Count} sessions to expired status", expiredPendingSessions.Count);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during QRCode session cleanup");
        }
    }

    /// <summary>
    /// 判断会话是否应该被删除
    /// </summary>
    private bool ShouldDeleteSession(Domain.Entities.User.QRCodeSession session, DateTime currentTime)
    {
        // 规则1: 删除所有已过期超过1小时的会话
        if (session.Status == QRCodeSessionStatus.Expired && session.ExpiresAt.AddHours(1) < currentTime)
        {
            return true;
        }

        // 规则2: 删除已取消或已拒绝且超过1小时的会话
        if ((session.Status == QRCodeSessionStatus.Cancelled || session.Status == QRCodeSessionStatus.Rejected) &&
            session.UpdatedAt.AddHours(1) < currentTime)
        {
            return true;
        }

        // 规则3: 删除已认证且超过24小时的会话（保留一定时间用于审计）
        if (session.Status == QRCodeSessionStatus.Authenticated && 
            session.AuthenticatedAt.HasValue &&
            session.AuthenticatedAt.Value.AddHours(24) < currentTime)
        {
            return true;
        }

        // 规则4: 处理异常情况 - 删除创建超过24小时但仍处于待扫描状态的会话
        if (session.Status == QRCodeSessionStatus.Pending && session.CreatedAt.AddHours(24) < currentTime)
        {
            return true;
        }

        return false;
    }

    public override void Dispose()
    {
        _cleanupTimer?.Dispose();
        _logger.LogInformation("QRCode session cleanup job is stopping");
        base.Dispose();
    }
}