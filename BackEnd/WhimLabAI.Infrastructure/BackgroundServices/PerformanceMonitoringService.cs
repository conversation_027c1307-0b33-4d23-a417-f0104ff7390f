using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Dtos.Performance;
using System.Text.Json;

namespace WhimLabAI.Infrastructure.BackgroundServices;

/// <summary>
/// 性能监控后台服务
/// </summary>
public class PerformanceMonitoringService : BackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<PerformanceMonitoringService> _logger;
    private readonly IConfiguration _configuration;
    private readonly PerformanceMonitoringOptions _options;
    private readonly PerformanceMetricsCollector _metricsCollector;
    private Timer? _timer;

    public PerformanceMonitoringService(
        IServiceScopeFactory scopeFactory,
        ILogger<PerformanceMonitoringService> logger,
        IConfiguration configuration)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
        _configuration = configuration;
        _options = LoadOptions();
        _metricsCollector = new PerformanceMetricsCollector();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_options.Enabled)
        {
            _logger.LogInformation("Performance monitoring is disabled");
            return;
        }

        _logger.LogInformation("Performance monitoring service started with interval: {Interval} seconds", 
            _options.CollectionIntervalSeconds);

        // 使用定时器定期收集性能指标
        _timer = new Timer(
            async _ => await CollectPerformanceMetrics(stoppingToken),
            null,
            TimeSpan.FromSeconds(_options.InitialDelaySeconds),
            TimeSpan.FromSeconds(_options.CollectionIntervalSeconds));

        // 保持服务运行
        await Task.Delay(Timeout.Infinite, stoppingToken);
    }

    private async Task CollectPerformanceMetrics(CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            var cacheService = scope.ServiceProvider.GetRequiredService<ICacheService>();

            // 收集系统指标
            var metrics = _metricsCollector.CollectMetrics();

            // 存储到缓存用于实时监控
            var cacheKey = $"performance:metrics:{DateTime.UtcNow:yyyyMMddHHmmss}";
            await cacheService.SetAsync(cacheKey, metrics, TimeSpan.FromHours(1), cancellationToken);

            // 存储最新指标
            await cacheService.SetAsync("performance:metrics:latest", metrics, TimeSpan.FromDays(1), cancellationToken);

            // 检查阈值并发出警报
            await CheckPerformanceThresholds(metrics, cacheService, cancellationToken);

            // 记录指标摘要
            if (_options.LogMetrics)
            {
                _logger.LogInformation("Performance metrics collected: CPU={Cpu:F1}%, Memory={Memory:N0}MB, Threads={Threads}",
                    metrics.CpuUsagePercent, metrics.MemoryUsageMB, metrics.ThreadCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting performance metrics");
        }
    }

    private async Task CheckPerformanceThresholds(
        PerformanceMetrics metrics, 
        ICacheService cacheService,
        CancellationToken cancellationToken)
    {
        var alerts = new List<PerformanceAlert>();

        // CPU阈值检查
        if (metrics.CpuUsagePercent > _options.CpuThresholdPercent)
        {
            alerts.Add(new PerformanceAlert
            {
                AlertType = "CPU",
                Severity = metrics.CpuUsagePercent > 90 ? "Critical" : "Warning",
                Description = $"CPU usage is {metrics.CpuUsagePercent:F1}% (threshold: {_options.CpuThresholdPercent}%)",
                Value = metrics.CpuUsagePercent,
                Threshold = _options.CpuThresholdPercent,
                Timestamp = DateTime.UtcNow,
                Source = "System"
            });
        }

        // 内存阈值检查
        if (metrics.MemoryUsagePercent > _options.MemoryThresholdPercent)
        {
            alerts.Add(new PerformanceAlert
            {
                AlertType = "Memory",
                Severity = metrics.MemoryUsagePercent > 90 ? "Critical" : "Warning",
                Description = $"Memory usage is {metrics.MemoryUsagePercent:F1}% (threshold: {_options.MemoryThresholdPercent}%)",
                Value = metrics.MemoryUsagePercent,
                Threshold = _options.MemoryThresholdPercent,
                Timestamp = DateTime.UtcNow,
                Source = "System"
            });
        }

        // 线程数阈值检查
        if (metrics.ThreadCount > _options.ThreadCountThreshold)
        {
            alerts.Add(new PerformanceAlert
            {
                AlertType = "Threads",
                Severity = "Warning",
                Description = $"Thread count is {metrics.ThreadCount} (threshold: {_options.ThreadCountThreshold})",
                Value = metrics.ThreadCount,
                Threshold = _options.ThreadCountThreshold,
                Timestamp = DateTime.UtcNow,
                Source = "System"
            });
        }

        // GC压力检查
        if (metrics.Gen2CollectionsPerMinute > _options.Gen2CollectionsThreshold)
        {
            alerts.Add(new PerformanceAlert
            {
                AlertType = "GC",
                Severity = "Warning",
                Description = $"Gen2 GC collections per minute: {metrics.Gen2CollectionsPerMinute} (threshold: {_options.Gen2CollectionsThreshold})",
                Value = metrics.Gen2CollectionsPerMinute,
                Threshold = _options.Gen2CollectionsThreshold,
                Timestamp = DateTime.UtcNow,
                Source = "System"
            });
        }

        // 存储警报
        if (alerts.Any())
        {
            await cacheService.SetAsync("performance:alerts:latest", alerts, TimeSpan.FromHours(1), cancellationToken);
            
            foreach (var alert in alerts)
            {
                _logger.LogWarning("Performance alert: {AlertType} - {Description}", alert.AlertType, alert.Description);
            }
        }
    }

    private PerformanceMonitoringOptions LoadOptions()
    {
        var options = new PerformanceMonitoringOptions();
        _configuration.GetSection("PerformanceMonitoring").Bind(options);
        return options;
    }

    public override void Dispose()
    {
        _timer?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// 性能监控选项
/// </summary>
public class PerformanceMonitoringOptions
{
    public bool Enabled { get; set; } = true;
    public int CollectionIntervalSeconds { get; set; } = 60;
    public int InitialDelaySeconds { get; set; } = 10;
    public bool LogMetrics { get; set; } = true;
    public double CpuThresholdPercent { get; set; } = 80;
    public double MemoryThresholdPercent { get; set; } = 85;
    public int ThreadCountThreshold { get; set; } = 500;
    public double Gen2CollectionsThreshold { get; set; } = 1;
}

/// <summary>
/// 性能指标收集器
/// </summary>
public class PerformanceMetricsCollector
{
    private readonly Process _currentProcess;
    private DateTime _lastCpuCheck;
    private TimeSpan _lastTotalProcessorTime;
    private int _lastGen2Collections;
    private DateTime _lastGcCheck;

    public PerformanceMetricsCollector()
    {
        _currentProcess = Process.GetCurrentProcess();
        _lastCpuCheck = DateTime.UtcNow;
        _lastTotalProcessorTime = _currentProcess.TotalProcessorTime;
        _lastGen2Collections = GC.CollectionCount(2);
        _lastGcCheck = DateTime.UtcNow;
    }

    public PerformanceMetrics CollectMetrics()
    {
        var metrics = new PerformanceMetrics
        {
            Timestamp = DateTime.UtcNow,
            MachineName = Environment.MachineName
        };

        // CPU使用率
        var currentTime = DateTime.UtcNow;
        var currentTotalProcessorTime = _currentProcess.TotalProcessorTime;
        var cpuUsedMs = (currentTotalProcessorTime - _lastTotalProcessorTime).TotalMilliseconds;
        var totalMsPassed = (currentTime - _lastCpuCheck).TotalMilliseconds;
        
        if (totalMsPassed > 0)
        {
            metrics.CpuUsagePercent = (cpuUsedMs / (Environment.ProcessorCount * totalMsPassed)) * 100;
        }
        
        _lastCpuCheck = currentTime;
        _lastTotalProcessorTime = currentTotalProcessorTime;

        // 内存使用
        metrics.WorkingSetMB = _currentProcess.WorkingSet64 / (1024 * 1024);
        metrics.PrivateMemoryMB = _currentProcess.PrivateMemorySize64 / (1024 * 1024);
        metrics.VirtualMemoryMB = _currentProcess.VirtualMemorySize64 / (1024 * 1024);
        metrics.GCHeapSizeMB = GC.GetTotalMemory(false) / (1024 * 1024);
        
        // 计算内存使用百分比（假设16GB总内存）
        var totalMemoryMB = 16 * 1024;
        metrics.MemoryUsageMB = metrics.WorkingSetMB;
        metrics.MemoryUsagePercent = (metrics.WorkingSetMB / totalMemoryMB) * 100;

        // GC统计
        metrics.Gen0Collections = GC.CollectionCount(0);
        metrics.Gen1Collections = GC.CollectionCount(1);
        metrics.Gen2Collections = GC.CollectionCount(2);
        
        // 计算Gen2收集频率
        var gen2Diff = metrics.Gen2Collections - _lastGen2Collections;
        var minutesPassed = (currentTime - _lastGcCheck).TotalMinutes;
        if (minutesPassed > 0)
        {
            metrics.Gen2CollectionsPerMinute = gen2Diff / minutesPassed;
        }
        _lastGen2Collections = metrics.Gen2Collections;
        _lastGcCheck = currentTime;

        // 线程统计
        metrics.ThreadCount = _currentProcess.Threads.Count;
        metrics.ThreadPoolThreads = ThreadPool.ThreadCount;
        ThreadPool.GetAvailableThreads(out var availableWorkerThreads, out var availableCompletionPortThreads);
        metrics.AvailableThreadPoolThreads = availableWorkerThreads;

        // 句柄数
        metrics.HandleCount = _currentProcess.HandleCount;

        return metrics;
    }
}

/// <summary>
/// 性能指标
/// </summary>
public class PerformanceMetrics
{
    public DateTime Timestamp { get; set; }
    public string MachineName { get; set; } = string.Empty;
    
    // CPU指标
    public double CpuUsagePercent { get; set; }
    
    // 内存指标
    public long WorkingSetMB { get; set; }
    public long PrivateMemoryMB { get; set; }
    public long VirtualMemoryMB { get; set; }
    public long GCHeapSizeMB { get; set; }
    public long MemoryUsageMB { get; set; }
    public double MemoryUsagePercent { get; set; }
    
    // GC指标
    public int Gen0Collections { get; set; }
    public int Gen1Collections { get; set; }
    public int Gen2Collections { get; set; }
    public double Gen2CollectionsPerMinute { get; set; }
    
    // 线程指标
    public int ThreadCount { get; set; }
    public int ThreadPoolThreads { get; set; }
    public int AvailableThreadPoolThreads { get; set; }
    
    // 其他指标
    public int HandleCount { get; set; }
    
    public Dictionary<string, object> CustomMetrics { get; set; } = new();
}