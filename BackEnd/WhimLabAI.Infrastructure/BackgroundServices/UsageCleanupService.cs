using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Infrastructure.BackgroundServices;

/// <summary>
/// 使用记录清理服务 - 定期清理旧的使用记录
/// </summary>
public class UsageCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<UsageCleanupService> _logger;
    private readonly int _daysToKeep = 180; // Keep 6 months of data
    private readonly TimeSpan _cleanupInterval = TimeSpan.FromDays(1); // Run daily

    public UsageCleanupService(
        IServiceProvider serviceProvider,
        ILogger<UsageCleanupService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Usage Cleanup Service started");

        // Wait for initial delay
        await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);

        while (!stoppingToken.IsCancellationRequested)
        {
            var now = DateTime.UtcNow;
            
            // Run cleanup at 3 AM UTC
            if (now.Hour == 3)
            {
                try
                {
                    await CleanupOldUsageRecords(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in usage cleanup service");
                }
                
                // Wait until next day
                await Task.Delay(TimeSpan.FromHours(23), stoppingToken);
            }
            else
            {
                // Check again in an hour
                await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
            }
        }

        _logger.LogInformation("Usage Cleanup Service stopped");
    }

    private async Task CleanupOldUsageRecords(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
        
        _logger.LogInformation("Starting usage records cleanup (keeping last {Days} days)", _daysToKeep);

        try
        {
            // Delete old usage records
            var cutoffDate = DateTime.UtcNow.AddDays(-_daysToKeep);
            var usageRecordRepo = unitOfWork.Repository<UsageRecord>();
            var oldRecords = await usageRecordRepo.GetAsync(r => r.CreatedAt < cutoffDate, cancellationToken);
            
            var recordsToDelete = oldRecords.ToList();
            if (recordsToDelete.Any())
            {
                foreach (var record in recordsToDelete)
                {
                    usageRecordRepo.Remove(record);
                }
                await unitOfWork.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Successfully cleaned up {Count} old usage records", recordsToDelete.Count);
            }
            else
            {
                _logger.LogInformation("No old usage records to clean up");
            }

            // Also clean up orphaned records (where subscription no longer exists)
            await CleanupOrphanedRecords(unitOfWork, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up usage records");
        }
    }

    private async Task CleanupOrphanedRecords(IUnitOfWork unitOfWork, CancellationToken cancellationToken)
    {
        try
        {
            var usageRecordRepo = unitOfWork.Repository<UsageRecord>();
            var allRecords = await usageRecordRepo.GetAllAsync(cancellationToken);
            var recordsList = allRecords.ToList();
            
            var orphanedCount = 0;
            var subscriptionRepo = unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            
            foreach (var record in recordsList)
            {
                var subscription = await subscriptionRepo.GetByIdAsync(record.SubscriptionId, cancellationToken);
                
                if (subscription == null)
                {
                    usageRecordRepo.Remove(record);
                    orphanedCount++;
                }
            }

            if (orphanedCount > 0)
            {
                await unitOfWork.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Cleaned up {Count} orphaned usage records", orphanedCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up orphaned usage records");
        }
    }
}