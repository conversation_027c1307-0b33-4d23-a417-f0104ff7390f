using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Infrastructure.BackgroundServices;

/// <summary>
/// Token重置后台服务 - 每日检查需要重置月度配额的订阅
/// </summary>
public class TokenResetService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<TokenResetService> _logger;
    private readonly TimeSpan _checkInterval = TimeSpan.FromHours(6); // Check every 6 hours

    public TokenResetService(
        IServiceProvider serviceProvider,
        ILogger<TokenResetService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Token Reset Service started");

        // Wait for a short delay to ensure all services are ready
        await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CheckAndResetTokens(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in token reset service");
            }

            await Task.Delay(_checkInterval, stoppingToken);
        }

        _logger.LogInformation("Token Reset Service stopped");
    }

    private async Task CheckAndResetTokens(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
        
        _logger.LogDebug("Checking for subscriptions needing token reset");

        try
        {
            // Get subscriptions that need token reset
            var subscriptionRepo = unitOfWork.Repository<Subscription>();
            var now = DateTime.UtcNow;
            var subscriptions = await subscriptionRepo.GetAsync(
                s => s.Status == WhimLabAI.Shared.Enums.SubscriptionStatus.Active &&
                     s.NextResetDate <= now,
                cancellationToken);
            var subscriptionsList = subscriptions.ToList();

            if (subscriptionsList.Any())
            {
                _logger.LogInformation("Found {Count} subscriptions needing token reset", subscriptionsList.Count);

                foreach (var subscription in subscriptionsList)
                {
                    try
                    {
                        // Reset tokens for the subscription
                        subscription.ResetMonthlyTokens();
                        await unitOfWork.SaveChangesAsync(cancellationToken);
                        
                        _logger.LogInformation("Reset monthly tokens for subscription {SubscriptionId} (User: {UserId})", 
                            subscription.Id, subscription.CustomerUserId);
                        
                        // Send notification to user about token reset
                        try
                        {
                            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
                            var planRepo = unitOfWork.Repository<SubscriptionPlan>();
                            var plan = await planRepo.GetByIdAsync(subscription.PlanId, cancellationToken);
                            
                            if (plan != null)
                            {
                                // Send token reset notification with correct parameters
                                await notificationService.NotifyTokenUsageAsync(
                                    subscription.CustomerUserId,
                                    plan.MonthlyTokens, // Remaining tokens after reset
                                    plan.MonthlyTokens, // Total tokens
                                    cancellationToken);
                                
                                _logger.LogInformation("Sent token reset notification to user {UserId} - Tokens reset to {Tokens}", 
                                    subscription.CustomerUserId, plan.MonthlyTokens);
                            }
                        }
                        catch (Exception notifyEx)
                        {
                            _logger.LogError(notifyEx, "Failed to send token reset notification for subscription {SubscriptionId}", subscription.Id);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error resetting tokens for subscription {SubscriptionId}", subscription.Id);
                    }
                }
            }
            else
            {
                _logger.LogDebug("No subscriptions need token reset at this time");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking for token resets");
        }
    }
}