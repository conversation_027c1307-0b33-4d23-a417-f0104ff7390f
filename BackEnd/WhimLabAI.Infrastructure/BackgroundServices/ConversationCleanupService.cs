using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.Infrastructure.BackgroundServices;

public class ConversationCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ConversationCleanupService> _logger;
    private readonly ConversationCleanupOptions _options;
    private readonly TimeSpan _period;

    public ConversationCleanupService(
        IServiceProvider serviceProvider,
        ILogger<ConversationCleanupService> logger,
        IOptions<ConversationCleanupOptions> options)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options.Value;
        _period = TimeSpan.FromHours(_options.RunIntervalHours);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_options.Enabled)
        {
            _logger.LogInformation("Conversation cleanup service is disabled");
            return;
        }

        _logger.LogInformation("Conversation cleanup service is starting");

        // Wait for initial delay
        await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformCleanupAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during conversation cleanup");
            }

            await Task.Delay(_period, stoppingToken);
        }
    }

    private async Task PerformCleanupAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting conversation cleanup task");

        using var scope = _serviceProvider.CreateScope();
        var conversationService = scope.ServiceProvider.GetRequiredService<IConversationService>();

        var policy = new ConversationCleanupPolicyDto
        {
            DaysToKeep = _options.DaysToKeep,
            ArchiveBeforeDelete = _options.ArchiveBeforeDelete,
            KeepPinnedConversations = _options.KeepPinnedConversations,
            KeepConversationsWithAttachments = _options.KeepConversationsWithAttachments
        };

        var result = await conversationService.CleanupOldConversationsAsync(policy, cancellationToken);

        if (result.IsSuccess)
        {
            _logger.LogInformation("Conversation cleanup completed. Cleaned up {Count} conversations", result.Value);
        }
        else
        {
            _logger.LogError("Conversation cleanup failed: {Error}", result.Error);
        }
    }
}

public class ConversationCleanupOptions
{
    public bool Enabled { get; set; } = true;
    public int RunIntervalHours { get; set; } = 24; // Run once a day
    public int DaysToKeep { get; set; } = 90; // Keep conversations for 90 days
    public bool ArchiveBeforeDelete { get; set; } = true;
    public bool KeepPinnedConversations { get; set; } = true;
    public bool KeepConversationsWithAttachments { get; set; } = true;
}