using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;

namespace WhimLabAI.Infrastructure.BackgroundServices;

/// <summary>
/// 订阅维护后台服务
/// </summary>
public class SubscriptionMaintenanceService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SubscriptionMaintenanceService> _logger;
    private readonly TimeSpan _checkInterval = TimeSpan.FromHours(1); // Check every hour

    public SubscriptionMaintenanceService(
        IServiceProvider serviceProvider,
        ILogger<SubscriptionMaintenanceService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Subscription Maintenance Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformMaintenanceTasks(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in subscription maintenance service");
            }

            await Task.Delay(_checkInterval, stoppingToken);
        }

        _logger.LogInformation("Subscription Maintenance Service stopped");
    }

    private async Task PerformMaintenanceTasks(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var renewalService = scope.ServiceProvider.GetRequiredService<ISubscriptionRenewalService>();

        var now = DateTime.UtcNow;
        _logger.LogInformation("Starting subscription maintenance at {Time}", now);

        // Process expiring subscriptions (run every hour)
        try
        {
            var result = await renewalService.ProcessExpiringSubscriptionsAsync(cancellationToken);
            if (result.IsSuccess)
            {
                _logger.LogInformation("Processed {Count} expiring subscriptions", result.Value);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing expiring subscriptions");
        }

        // Reset monthly tokens (run at midnight UTC on the 1st of each month)
        if (now.Hour == 0 && now.Day == 1)
        {
            try
            {
                var result = await renewalService.ProcessMonthlyResetAsync(cancellationToken);
                if (result.IsSuccess)
                {
                    _logger.LogInformation("Reset tokens for {Count} subscriptions", result.Value);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing monthly reset");
            }
        }

        // Send renewal reminders (run at 10 AM UTC daily)
        if (now.Hour == 10)
        {
            try
            {
                await renewalService.SendRenewalRemindersAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending renewal reminders");
            }
        }

        // Cleanup expired subscriptions (run at 2 AM UTC on Sundays)
        if (now.Hour == 2 && now.DayOfWeek == DayOfWeek.Sunday)
        {
            try
            {
                await renewalService.CleanupExpiredSubscriptionsAsync(90, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up expired subscriptions");
            }
        }

        _logger.LogInformation("Completed subscription maintenance at {Time}", DateTime.UtcNow);
    }
}