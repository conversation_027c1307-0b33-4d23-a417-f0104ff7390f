using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Options;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.BackgroundServices;

/// <summary>
/// 会话清理后台服务 - 定期清理超时的会话
/// </summary>
public class SessionCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SessionCleanupService> _logger;
    private readonly SessionTimeoutOptions _options;
    private readonly IAuditLogger _auditLogger;

    public SessionCleanupService(
        IServiceProvider serviceProvider,
        ILogger<SessionCleanupService> logger,
        IOptions<SessionTimeoutOptions> options,
        IAuditLogger auditLogger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options.Value;
        _auditLogger = auditLogger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_options.Enabled)
        {
            _logger.LogInformation("会话超时功能已禁用，会话清理服务不会运行");
            return;
        }

        _logger.LogInformation("会话清理服务已启动");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(TimeSpan.FromMinutes(_options.CleanupIntervalMinutes), stoppingToken);
                await CleanupExpiredSessionsAsync(stoppingToken);
            }
            catch (TaskCanceledException)
            {
                // 正常取消，不记录错误
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "会话清理过程中发生错误");
                // 继续运行，等待下一个清理周期
            }
        }

        _logger.LogInformation("会话清理服务已停止");
    }

    private async Task CleanupExpiredSessionsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var adminSessionRepository = scope.ServiceProvider.GetRequiredService<IAdminSessionRepository>();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            _logger.LogDebug("开始清理超时的管理员会话");

            // 获取所有活动会话
            var activeSessions = await adminSessionRepository.GetAsync(
                s => s.IsActive,
                cancellationToken);

            var expiredCount = 0;
            var timeoutCount = 0;
            var now = DateTime.UtcNow;
            var timeoutThreshold = now.AddMinutes(-_options.AdminSessionTimeoutMinutes);

            foreach (var session in activeSessions)
            {
                // 检查会话是否已过期
                if (session.IsExpired())
                {
                    expiredCount++;
                    continue; // 已经标记为过期的会话会在下次登录时清理
                }

                // 检查会话是否因无活动而超时
                if (session.LastActivityAt < timeoutThreshold)
                {
                    session.InvalidateSession();
                    adminSessionRepository.Update(session);
                    timeoutCount++;

                    // 记录审计日志
                    await _auditLogger.LogSecurityEventAsync(
                        "SessionTimeout",
                        $"管理员会话因超时被清理: SessionId={session.Id}, AdminUserId={session.AdminUserId}, LastActivity={session.LastActivityAt:yyyy-MM-dd HH:mm:ss}",
                        "Info",
                        new { 
                            SessionId = session.Id, 
                            AdminUserId = session.AdminUserId,
                            LastActivityAt = session.LastActivityAt,
                            TimeoutMinutes = _options.AdminSessionTimeoutMinutes
                        });

                    _logger.LogInformation(
                        "清理超时的管理员会话: SessionId={SessionId}, AdminUserId={AdminUserId}, LastActivity={LastActivity}",
                        session.Id, session.AdminUserId, session.LastActivityAt);
                }
            }

            if (timeoutCount > 0)
            {
                await unitOfWork.SaveChangesAsync(cancellationToken);
            }

            _logger.LogInformation(
                "会话清理完成: 检查会话数={TotalCount}, 已过期={ExpiredCount}, 因超时清理={TimeoutCount}",
                activeSessions.Count(), expiredCount, timeoutCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理超时会话时发生错误");
            throw;
        }
    }
}