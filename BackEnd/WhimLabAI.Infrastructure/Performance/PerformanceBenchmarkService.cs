using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Npgsql;
using StackExchange.Redis;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Audit;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.Performance;

/// <summary>
/// 性能基准测试服务实现
/// </summary>
public class PerformanceBenchmarkService : IPerformanceBenchmarkService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<PerformanceBenchmarkService> _logger;
    private readonly IDbContextFactory<WhimLabAIDbContext> _dbContextFactory;
    private readonly ICacheService _cacheService;
    private readonly IConnectionMultiplexer _redis;
    private readonly JsonSerializerOptions _jsonOptions;

    public PerformanceBenchmarkService(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<PerformanceBenchmarkService> logger,
        IDbContextFactory<WhimLabAIDbContext> dbContextFactory,
        ICacheService cacheService,
        IConnectionMultiplexer redis)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
        _dbContextFactory = dbContextFactory;
        _cacheService = cacheService;
        _redis = redis;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };
    }

    public async Task<Result<ApiEndpointBenchmark>> BenchmarkApiEndpointAsync(
        string endpoint,
        HttpMethod method,
        object? requestBody = null,
        int iterations = 100,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var httpClient = _httpClientFactory.CreateClient("Benchmark");
            var baseUrl = _configuration.GetValue<string>("ExternalServices:Benchmark:BaseUrl") ?? "https://localhost:15801";
            var fullUrl = $"{baseUrl}{endpoint}";

            var benchmark = new ApiEndpointBenchmark
            {
                Endpoint = endpoint,
                Method = method,
                Iterations = iterations,
                StartTime = DateTime.UtcNow
            };

            var responseTimes = new List<double>();
            var statusCodes = new Dictionary<int, int>();
            var stopwatch = new Stopwatch();

            for (int i = 0; i < iterations; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    var request = new HttpRequestMessage(method, fullUrl);

                    if (requestBody != null)
                    {
                        var json = JsonSerializer.Serialize(requestBody, _jsonOptions);
                        request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                    }

                    stopwatch.Restart();
                    var response = await httpClient.SendAsync(request, cancellationToken);
                    stopwatch.Stop();

                    var responseTime = stopwatch.Elapsed.TotalMilliseconds;
                    responseTimes.Add(responseTime);

                    var statusCode = (int)response.StatusCode;
                    statusCodes[statusCode] = statusCodes.GetValueOrDefault(statusCode) + 1;

                    if (response.IsSuccessStatusCode)
                        benchmark.SuccessfulRequests++;
                    else
                        benchmark.FailedRequests++;

                    // 避免过载服务器
                    if (i < iterations - 1)
                        await Task.Delay(10, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Request {Iteration} failed for endpoint {Endpoint}", i, endpoint);
                    benchmark.FailedRequests++;
                }
            }

            benchmark.EndTime = DateTime.UtcNow;
            benchmark.ResponseTimes = responseTimes;
            benchmark.StatusCodeDistribution = statusCodes;

            // 计算统计数据
            if (responseTimes.Any())
            {
                responseTimes.Sort();
                benchmark.AverageResponseTime = responseTimes.Average();
                benchmark.MinResponseTime = responseTimes.Min();
                benchmark.MaxResponseTime = responseTimes.Max();
                benchmark.MedianResponseTime = GetPercentile(responseTimes, 50);
                benchmark.P95ResponseTime = GetPercentile(responseTimes, 95);
                benchmark.P99ResponseTime = GetPercentile(responseTimes, 99);
                benchmark.RequestsPerSecond = benchmark.TotalDuration.TotalSeconds > 0
                    ? iterations / benchmark.TotalDuration.TotalSeconds
                    : 0;
            }

            _logger.LogInformation("API endpoint benchmark completed for {Endpoint}: Avg={Avg}ms, P95={P95}ms",
                endpoint, benchmark.AverageResponseTime, benchmark.P95ResponseTime);

            return Result<ApiEndpointBenchmark>.Success(benchmark);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to benchmark API endpoint {Endpoint}", endpoint);
            return Result<ApiEndpointBenchmark>.Failure($"Benchmark failed: {ex.Message}");
        }
    }

    public async Task<Result<DatabaseBenchmark>> BenchmarkDatabaseOperationAsync(
        string operationName,
        Func<Task> operation,
        int iterations = 100,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var benchmark = new DatabaseBenchmark
            {
                OperationName = operationName,
                Iterations = iterations,
                StartTime = DateTime.UtcNow
            };

            var executionTimes = new List<double>();
            var stopwatch = new Stopwatch();

            // 预热
            await operation();

            for (int i = 0; i < iterations; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    stopwatch.Restart();
                    await operation();
                    stopwatch.Stop();

                    var executionTime = stopwatch.Elapsed.TotalMilliseconds;
                    executionTimes.Add(executionTime);
                    benchmark.SuccessfulOperations++;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Database operation {Operation} failed at iteration {Iteration}",
                        operationName, i);
                    benchmark.FailedOperations++;
                }
            }

            benchmark.EndTime = DateTime.UtcNow;
            benchmark.ExecutionTimes = executionTimes;

            // 计算统计数据
            if (executionTimes.Any())
            {
                executionTimes.Sort();
                benchmark.AverageExecutionTime = executionTimes.Average();
                benchmark.MinExecutionTime = executionTimes.Min();
                benchmark.MaxExecutionTime = executionTimes.Max();
                benchmark.MedianExecutionTime = GetPercentile(executionTimes, 50);
                benchmark.P95ExecutionTime = GetPercentile(executionTimes, 95);
                benchmark.P99ExecutionTime = GetPercentile(executionTimes, 99);
                benchmark.OperationsPerSecond = benchmark.TotalDuration.TotalSeconds > 0
                    ? iterations / benchmark.TotalDuration.TotalSeconds
                    : 0;
            }

            _logger.LogInformation("Database operation benchmark completed for {Operation}: Avg={Avg}ms, P95={P95}ms",
                operationName, benchmark.AverageExecutionTime, benchmark.P95ExecutionTime);

            return Result<DatabaseBenchmark>.Success(benchmark);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to benchmark database operation {Operation}", operationName);
            return Result<DatabaseBenchmark>.Failure($"Benchmark failed: {ex.Message}");
        }
    }

    public async Task<Result<CacheBenchmark>> BenchmarkCacheOperationAsync(
        string operationName,
        Func<Task> writeOperation,
        Func<Task> readOperation,
        int iterations = 1000,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var benchmark = new CacheBenchmark
            {
                OperationName = operationName,
                Iterations = iterations,
                StartTime = DateTime.UtcNow
            };

            var writeTimes = new List<double>();
            var readTimes = new List<double>();
            var stopwatch = new Stopwatch();

            // 写操作基准测试
            for (int i = 0; i < iterations; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    stopwatch.Restart();
                    await writeOperation();
                    stopwatch.Stop();

                    writeTimes.Add(stopwatch.Elapsed.TotalMilliseconds);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Cache write operation failed at iteration {Iteration}", i);
                }
            }

            // 读操作基准测试
            var random = new Random();
            for (int i = 0; i < iterations; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    // 模拟缓存命中/未命中
                    if (random.NextDouble() > 0.1) // 90% 命中率
                    {
                        stopwatch.Restart();
                        await readOperation();
                        stopwatch.Stop();

                        readTimes.Add(stopwatch.Elapsed.TotalMilliseconds);
                        benchmark.CacheHits++;
                    }
                    else
                    {
                        // 模拟缓存未命中
                        stopwatch.Restart();
                        await writeOperation();
                        await readOperation();
                        stopwatch.Stop();

                        readTimes.Add(stopwatch.Elapsed.TotalMilliseconds);
                        benchmark.CacheMisses++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Cache read operation failed at iteration {Iteration}", i);
                }
            }

            benchmark.EndTime = DateTime.UtcNow;
            benchmark.WriteTimes = writeTimes;
            benchmark.ReadTimes = readTimes;

            // 计算写操作统计
            if (writeTimes.Any())
            {
                writeTimes.Sort();
                benchmark.AverageWriteTime = writeTimes.Average();
                benchmark.MinWriteTime = writeTimes.Min();
                benchmark.MaxWriteTime = writeTimes.Max();
                benchmark.MedianWriteTime = GetPercentile(writeTimes, 50);
                benchmark.WritesPerSecond = writeTimes.Count / benchmark.TotalDuration.TotalSeconds;
            }

            // 计算读操作统计
            if (readTimes.Any())
            {
                readTimes.Sort();
                benchmark.AverageReadTime = readTimes.Average();
                benchmark.MinReadTime = readTimes.Min();
                benchmark.MaxReadTime = readTimes.Max();
                benchmark.MedianReadTime = GetPercentile(readTimes, 50);
                benchmark.ReadsPerSecond = readTimes.Count / benchmark.TotalDuration.TotalSeconds;
            }

            _logger.LogInformation("Cache operation benchmark completed for {Operation}: " +
                "Write Avg={WriteAvg}ms, Read Avg={ReadAvg}ms, Hit Rate={HitRate:P}",
                operationName, benchmark.AverageWriteTime, benchmark.AverageReadTime, benchmark.CacheHitRate);

            return Result<CacheBenchmark>.Success(benchmark);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to benchmark cache operation {Operation}", operationName);
            return Result<CacheBenchmark>.Failure($"Benchmark failed: {ex.Message}");
        }
    }

    public async Task<Result<ConcurrencyBenchmark>> BenchmarkConcurrencyAsync(
        string operationName,
        Func<Task> operation,
        int concurrentUsers = 10,
        int requestsPerUser = 100,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var benchmark = new ConcurrencyBenchmark
            {
                OperationName = operationName,
                ConcurrentUsers = concurrentUsers,
                RequestsPerUser = requestsPerUser,
                StartTime = DateTime.UtcNow
            };

            var allResponseTimes = new List<double>();
            var timeSlots = new Dictionary<DateTime, ConcurrencyTimeSlot>();
            var semaphore = new SemaphoreSlim(concurrentUsers);
            var tasks = new List<Task>();

            for (int user = 0; user < concurrentUsers; user++)
            {
                var userTask = Task.Run(async () =>
                {
                    var stopwatch = new Stopwatch();

                    for (int request = 0; request < requestsPerUser; request++)
                    {
                        if (cancellationToken.IsCancellationRequested)
                            break;

                        await semaphore.WaitAsync(cancellationToken);

                        try
                        {
                            var slotTime = DateTime.UtcNow.Truncate(TimeSpan.FromSeconds(1));

                            stopwatch.Restart();
                            await operation();
                            stopwatch.Stop();

                            var responseTime = stopwatch.Elapsed.TotalMilliseconds;

                            lock (allResponseTimes)
                            {
                                allResponseTimes.Add(responseTime);
                                benchmark.SuccessfulRequests++;

                                // 更新时间片统计
                                if (!timeSlots.ContainsKey(slotTime))
                                {
                                    timeSlots[slotTime] = new ConcurrencyTimeSlot
                                    {
                                        Timestamp = slotTime
                                    };
                                }

                                var slot = timeSlots[slotTime];
                                slot.ActiveRequests++;
                                slot.AverageResponseTime =
                                    (slot.AverageResponseTime * (slot.ActiveRequests - 1) + responseTime) /
                                    slot.ActiveRequests;
                            }
                        }
                        catch (Exception ex)
                        {
                            lock (allResponseTimes)
                            {
                                benchmark.FailedRequests++;

                                var slotTime = DateTime.UtcNow.Truncate(TimeSpan.FromSeconds(1));
                                if (timeSlots.ContainsKey(slotTime))
                                {
                                    timeSlots[slotTime].Errors++;
                                }
                            }

                            _logger.LogWarning(ex, "Concurrent operation failed for user {User}, request {Request}",
                                user, request);
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    }
                }, cancellationToken);

                tasks.Add(userTask);
            }

            await Task.WhenAll(tasks);

            benchmark.EndTime = DateTime.UtcNow;

            // 计算统计数据
            if (allResponseTimes.Any())
            {
                allResponseTimes.Sort();
                benchmark.AverageResponseTime = allResponseTimes.Average();
                benchmark.MinResponseTime = allResponseTimes.Min();
                benchmark.MaxResponseTime = allResponseTimes.Max();
                benchmark.MedianResponseTime = GetPercentile(allResponseTimes, 50);
                benchmark.P95ResponseTime = GetPercentile(allResponseTimes, 95);
                benchmark.P99ResponseTime = GetPercentile(allResponseTimes, 99);
                benchmark.RequestsPerSecond = benchmark.TotalDuration.TotalSeconds > 0
                    ? benchmark.TotalRequests / benchmark.TotalDuration.TotalSeconds
                    : 0;
            }

            // 计算时间片的RPS
            foreach (var slot in timeSlots.Values.OrderBy(s => s.Timestamp))
            {
                slot.RequestsPerSecond = slot.ActiveRequests;
                benchmark.TimeSlots.Add(slot);
            }

            _logger.LogInformation("Concurrency benchmark completed for {Operation}: " +
                "Users={Users}, RPS={RPS:F2}, Avg={Avg}ms, Error Rate={ErrorRate:P}",
                operationName, concurrentUsers, benchmark.RequestsPerSecond,
                benchmark.AverageResponseTime, benchmark.ErrorRate);

            return Result<ConcurrencyBenchmark>.Success(benchmark);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to benchmark concurrency for {Operation}", operationName);
            return Result<ConcurrencyBenchmark>.Failure($"Benchmark failed: {ex.Message}");
        }
    }

    public async Task<Result<MemoryBenchmark>> BenchmarkMemoryUsageAsync(
        string operationName,
        Func<Task> operation,
        int iterations = 50,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 强制GC以获得准确的初始内存使用情况
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var benchmark = new MemoryBenchmark
            {
                OperationName = operationName,
                Iterations = iterations,
                StartTime = DateTime.UtcNow,
                InitialMemoryUsage = GC.GetTotalMemory(false)
            };

            var initialGen0 = GC.CollectionCount(0);
            var initialGen1 = GC.CollectionCount(1);
            var initialGen2 = GC.CollectionCount(2);
            var initialAllocated = GC.GetTotalAllocatedBytes();

            var memorySnapshots = new List<MemorySnapshot>();
            var peakMemory = benchmark.InitialMemoryUsage;

            for (int i = 0; i < iterations; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                await operation();

                var currentMemory = GC.GetTotalMemory(false);
                if (currentMemory > peakMemory)
                    peakMemory = currentMemory;

                // 每10次迭代记录一次快照
                if (i % 10 == 0)
                {
                    var snapshot = new MemorySnapshot
                    {
                        Timestamp = DateTime.UtcNow,
                        WorkingSet = currentMemory,
                        GCHeapSize = GC.GetTotalMemory(false),
                        Gen0Size = 0, // GC generation sizes not directly available in .NET
                        Gen1Size = 0,
                        Gen2Size = 0
                    };
                    memorySnapshots.Add(snapshot);
                }
            }

            // 最终GC以获得准确的最终内存使用情况
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            benchmark.EndTime = DateTime.UtcNow;
            benchmark.FinalMemoryUsage = GC.GetTotalMemory(false);
            benchmark.PeakMemoryUsage = peakMemory;
            benchmark.AverageMemoryUsage = memorySnapshots.Any()
                ? (long)memorySnapshots.Average(s => s.WorkingSet)
                : benchmark.FinalMemoryUsage;

            // GC统计
            benchmark.Gen0Collections = GC.CollectionCount(0) - initialGen0;
            benchmark.Gen1Collections = GC.CollectionCount(1) - initialGen1;
            benchmark.Gen2Collections = GC.CollectionCount(2) - initialGen2;
            benchmark.TotalAllocatedBytes = GC.GetTotalAllocatedBytes() - initialAllocated;

            // 计算分配速率
            if (benchmark.TotalDuration.TotalSeconds > 0)
            {
                benchmark.AllocationRatePerSecond = benchmark.TotalAllocatedBytes / benchmark.TotalDuration.TotalSeconds;
            }

            benchmark.Snapshots = memorySnapshots;

            _logger.LogInformation("Memory benchmark completed for {Operation}: " +
                "Leaked={Leaked:N0} bytes, Peak={Peak:N0} bytes, GC Gen2={Gen2}",
                operationName, benchmark.MemoryLeaked, benchmark.PeakMemoryUsage, benchmark.Gen2Collections);

            return Result<MemoryBenchmark>.Success(benchmark);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to benchmark memory usage for {Operation}", operationName);
            return Result<MemoryBenchmark>.Failure($"Benchmark failed: {ex.Message}");
        }
    }

    public async Task<Result<PerformanceTestSuite>> RunFullPerformanceTestSuiteAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var testSuite = new PerformanceTestSuite
            {
                ExecutedAt = DateTime.UtcNow,
                Environment = _configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                Version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "Unknown"
            };

            var suiteStopwatch = Stopwatch.StartNew();

            // 1. API端点测试
            await RunApiEndpointTests(testSuite, cancellationToken);

            // 2. 数据库操作测试
            await RunDatabaseTests(testSuite, cancellationToken);

            // 3. 缓存操作测试
            await RunCacheTests(testSuite, cancellationToken);

            // 4. 并发测试
            await RunConcurrencyTests(testSuite, cancellationToken);

            // 5. 内存测试
            await RunMemoryTests(testSuite, cancellationToken);

            // 6. 收集系统指标
            testSuite.SystemMetrics = CollectSystemMetrics();

            suiteStopwatch.Stop();
            testSuite.TotalExecutionTime = suiteStopwatch.Elapsed;

            _logger.LogInformation("Full performance test suite completed in {Duration}",
                testSuite.TotalExecutionTime);

            return Result<PerformanceTestSuite>.Success(testSuite);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to run full performance test suite");
            return Result<PerformanceTestSuite>.Failure($"Test suite failed: {ex.Message}");
        }
    }

    public Result<PerformanceComparison> ComparePerformanceResults(
        PerformanceTestSuite baseline,
        PerformanceTestSuite current)
    {
        try
        {
            var comparison = new PerformanceComparison
            {
                ComparedAt = DateTime.UtcNow,
                BaselineVersion = baseline.Version,
                CurrentVersion = current.Version
            };

            // 比较API端点
            foreach (var endpoint in baseline.ApiEndpoints.Keys.Intersect(current.ApiEndpoints.Keys))
            {
                var baselineEndpoint = baseline.ApiEndpoints[endpoint];
                var currentEndpoint = current.ApiEndpoints[endpoint];

                comparison.Changes.Add(new PerformanceChange
                {
                    Category = "API",
                    Operation = endpoint,
                    Metric = "Average Response Time",
                    BaselineValue = baselineEndpoint.AverageResponseTime,
                    CurrentValue = currentEndpoint.AverageResponseTime
                });

                comparison.Changes.Add(new PerformanceChange
                {
                    Category = "API",
                    Operation = endpoint,
                    Metric = "P95 Response Time",
                    BaselineValue = baselineEndpoint.P95ResponseTime,
                    CurrentValue = currentEndpoint.P95ResponseTime
                });
            }

            // 比较数据库操作
            foreach (var operation in baseline.DatabaseOperations.Keys.Intersect(current.DatabaseOperations.Keys))
            {
                var baselineOp = baseline.DatabaseOperations[operation];
                var currentOp = current.DatabaseOperations[operation];

                comparison.Changes.Add(new PerformanceChange
                {
                    Category = "Database",
                    Operation = operation,
                    Metric = "Average Execution Time",
                    BaselineValue = baselineOp.AverageExecutionTime,
                    CurrentValue = currentOp.AverageExecutionTime
                });
            }

            // 生成摘要
            comparison.Summary = GenerateComparisonSummary(comparison.Changes);

            return Result<PerformanceComparison>.Success(comparison);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to compare performance results");
            return Result<PerformanceComparison>.Failure($"Comparison failed: {ex.Message}");
        }
    }

    public async Task<Result<PerformanceReport>> GeneratePerformanceReportAsync(
        PerformanceTestSuite testSuite,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var report = new PerformanceReport
            {
                GeneratedAt = DateTime.UtcNow,
                TestResults = testSuite,
                Analysis = AnalyzePerformanceResults(testSuite),
                Recommendations = GenerateRecommendations(testSuite)
            };

            // 保存报告到文件系统或数据库
            await SavePerformanceReport(report, cancellationToken);

            _logger.LogInformation("Performance report generated: {ReportId}", report.ReportId);

            return Result<PerformanceReport>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate performance report");
            return Result<PerformanceReport>.Failure($"Report generation failed: {ex.Message}");
        }
    }

    #region Private Methods

    private double GetPercentile(List<double> sortedData, int percentile)
    {
        if (sortedData.Count == 0)
            return 0;

        var index = (int)Math.Ceiling(percentile / 100.0 * sortedData.Count) - 1;
        return sortedData[Math.Max(0, Math.Min(index, sortedData.Count - 1))];
    }

    private async Task RunApiEndpointTests(PerformanceTestSuite testSuite, CancellationToken cancellationToken)
    {
        var endpoints = new[]
        {
            ("/api/auth/login", HttpMethod.Post),
            ("/api/agents", HttpMethod.Get),
            ("/api/conversations", HttpMethod.Get),
            ("/health", HttpMethod.Get)
        };

        foreach (var (endpoint, method) in endpoints)
        {
            var result = await BenchmarkApiEndpointAsync(endpoint, method, null, 50, cancellationToken);
            if (result.IsSuccess)
            {
                testSuite.ApiEndpoints[endpoint] = result.Value!;
            }
        }
    }

    private async Task RunDatabaseTests(PerformanceTestSuite testSuite, CancellationToken cancellationToken)
    {
        // 使用实际的数据库操作
        var dbOperations = new Dictionary<string, Func<Task>>
        {
            ["SelectCustomerUsers"] = async () =>
            {
                using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
                var users = await context.CustomerUsers
                    .AsNoTracking()
                    .Take(100)
                    .ToListAsync(cancellationToken);
            },
            ["SelectAgents"] = async () =>
            {
                using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
                var agents = await context.Agents
                    .AsNoTracking()
                    .Include(a => a.Versions)
                    .Take(50)
                    .ToListAsync(cancellationToken);
            },
            ["SelectConversations"] = async () =>
            {
                using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
                var conversations = await context.Conversations
                    .AsNoTracking()
                    .Where(c => c.CreatedAt >= DateTime.UtcNow.AddDays(-7))
                    .Take(100)
                    .ToListAsync(cancellationToken);
            },
            ["InsertAuditLog"] = async () =>
            {
                using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
                var log = AuditLog.Create(
                    action: "BenchmarkTest",
                    module: "Performance",
                    description: "Performance benchmark test run",
                    userId: null,
                    userName: "BenchmarkService"
                );
                log.SetEntityInfo("PerformanceTest", Guid.NewGuid().ToString());
                log.AdditionalData = JsonSerializer.Serialize(new { TestRun = DateTime.UtcNow });

                context.AuditLogs.Add(log);
                await context.SaveChangesAsync(cancellationToken);
            },
            ["UpdateSubscription"] = async () =>
            {
                using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
                var subscription = await context.Subscriptions
                    .OrderBy(s => s.CreatedAt)
                    .FirstOrDefaultAsync(cancellationToken);
                if (subscription != null)
                {
                    // Call a method to trigger UpdatedAt update
                    context.Entry(subscription).State = EntityState.Modified;
                    await context.SaveChangesAsync(cancellationToken);
                }
            },
            ["ComplexQuery"] = async () =>
            {
                using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
                var query = await context.CustomerUsers
                    .AsNoTracking()
                    .Where(u => u.IsActive)
                    .Select(u => new
                    {
                        u.Id,
                        u.Username,
                        ConversationCount = context.Conversations.Count(c => c.CustomerUserId == u.Id),
                        LastActivity = context.Conversations
                            .Where(c => c.CustomerUserId == u.Id)
                            .OrderByDescending(c => c.CreatedAt)
                            .Select(c => c.CreatedAt)
                            .FirstOrDefault()
                    })
                    .Take(50)
                    .ToListAsync(cancellationToken);
            }
        };

        foreach (var (operation, func) in dbOperations)
        {
            var result = await BenchmarkDatabaseOperationAsync(operation, func, 50, cancellationToken);
            if (result.IsSuccess)
            {
                testSuite.DatabaseOperations[operation] = result.Value!;
            }
        }
    }

    private async Task RunCacheTests(PerformanceTestSuite testSuite, CancellationToken cancellationToken)
    {
        var random = new Random();

        // 字符串缓存测试
        var stringCacheKey = $"benchmark:string:{Guid.NewGuid()}";
        var stringValue = "This is a test value for benchmarking cache performance";

        var stringWriteOp = async () =>
        {
            await _cacheService.SetAsync(stringCacheKey, stringValue, TimeSpan.FromMinutes(5), cancellationToken);
        };

        var stringReadOp = async () =>
        {
            var value = await _cacheService.GetAsync<string>(stringCacheKey, cancellationToken);
        };

        var stringResult = await BenchmarkCacheOperationAsync("StringCache", stringWriteOp, stringReadOp, 500, cancellationToken);
        if (stringResult.IsSuccess)
        {
            testSuite.CacheOperations["StringCache"] = stringResult.Value!;
        }

        // 对象缓存测试
        var objectCacheKey = $"benchmark:object:{Guid.NewGuid()}";
        var testObject = new
        {
            Id = Guid.NewGuid(),
            Name = "BenchmarkTest",
            Data = Enumerable.Range(1, 100).Select(i => new { Index = i, Value = $"Value{i}" }).ToList(),
            Timestamp = DateTime.UtcNow
        };

        var objectWriteOp = async () =>
        {
            await _cacheService.SetAsync(objectCacheKey, testObject, TimeSpan.FromMinutes(5), cancellationToken);
        };

        var objectReadOp = async () =>
        {
            var value = await _cacheService.GetAsync<object>(objectCacheKey, cancellationToken);
        };

        var objectResult = await BenchmarkCacheOperationAsync("ObjectCache", objectWriteOp, objectReadOp, 300, cancellationToken);
        if (objectResult.IsSuccess)
        {
            testSuite.CacheOperations["ObjectCache"] = objectResult.Value!;
        }

        // 列表缓存测试
        var listCacheKey = $"benchmark:list:{Guid.NewGuid()}";
        var listValues = Enumerable.Range(1, 50).Select(i => $"Item{i}").ToList();

        var listWriteOp = async () =>
        {
            var db = _redis.GetDatabase();
            foreach (var value in listValues)
            {
                await db.ListRightPushAsync(listCacheKey, value);
            }
            await db.KeyExpireAsync(listCacheKey, TimeSpan.FromMinutes(5));
        };

        var listReadOp = async () =>
        {
            var db = _redis.GetDatabase();
            var values = await db.ListRangeAsync(listCacheKey, 0, -1);
        };

        var listResult = await BenchmarkCacheOperationAsync("ListCache", listWriteOp, listReadOp, 200, cancellationToken);
        if (listResult.IsSuccess)
        {
            testSuite.CacheOperations["ListCache"] = listResult.Value!;
        }

        // 清理测试键
        try
        {
            await _cacheService.RemoveAsync(stringCacheKey, cancellationToken);
            await _cacheService.RemoveAsync(objectCacheKey, cancellationToken);
            var db = _redis.GetDatabase();
            await db.KeyDeleteAsync(listCacheKey);
        }
        catch { }
    }

    private async Task RunConcurrencyTests(PerformanceTestSuite testSuite, CancellationToken cancellationToken)
    {
        var operation = async () => await Task.Delay(Random.Shared.Next(10, 50));

        var result = await BenchmarkConcurrencyAsync("SimulatedAPI", operation, 20, 50, cancellationToken);
        if (result.IsSuccess)
        {
            testSuite.ConcurrencyTests["SimulatedAPI"] = result.Value!;
        }
    }

    private async Task RunMemoryTests(PerformanceTestSuite testSuite, CancellationToken cancellationToken)
    {
        var memoryOp = async () =>
        {
            var data = new byte[1024 * 1024]; // 1MB
            await Task.Delay(10);
        };

        var result = await BenchmarkMemoryUsageAsync("MemoryAllocation", memoryOp, 20, cancellationToken);
        if (result.IsSuccess)
        {
            testSuite.MemoryTests["MemoryAllocation"] = result.Value!;
        }
    }

    private SystemMetrics CollectSystemMetrics()
    {
        var process = Process.GetCurrentProcess();

        // 计算CPU使用率
        var startTime = DateTime.UtcNow;
        var startCpuUsage = process.TotalProcessorTime;

        System.Threading.Thread.Sleep(100); // 短暂等待以测量CPU使用

        var endTime = DateTime.UtcNow;
        var endCpuUsage = process.TotalProcessorTime;

        var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
        var totalMsPassed = (endTime - startTime).TotalMilliseconds;
        var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);
        var cpuUsagePercentage = cpuUsageTotal * 100;

        // 获取活动数据库连接数
        int activeDbConnections = 0;
        try
        {
            using var context = _dbContextFactory.CreateDbContext();
            var connectionString = context.Database.GetConnectionString();
            using var connection = new NpgsqlConnection(connectionString);
            connection.Open();
            using var command = new NpgsqlCommand(
                "SELECT count(*) FROM pg_stat_activity WHERE state = 'active' AND datname = current_database()",
                connection);
            activeDbConnections = Convert.ToInt32(command.ExecuteScalar());
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get active database connections");
        }

        // 获取Redis连接数
        int redisConnections = 0;
        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            var info = server.Info();
            var clientsSection = info.FirstOrDefault(g => g.Key == "Clients");
            if (clientsSection.Key != null)
            {
                var connectedClients = clientsSection.FirstOrDefault(i => i.Key == "connected_clients");
                if (!connectedClients.Equals(default(KeyValuePair<string, string>)) && int.TryParse(connectedClients.Value, out int clients))
                {
                    redisConnections = clients;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get Redis connections");
        }

        return new SystemMetrics
        {
            CpuUsage = Math.Round(cpuUsagePercentage, 2),
            MemoryUsage = process.WorkingSet64,
            ThreadCount = process.Threads.Count,
            ActiveConnections = activeDbConnections + redisConnections,
            DiskIORead = 0, // 需要平台特定实现
            DiskIOWrite = 0, // 需要平台特定实现
            NetworkIn = 0, // 需要平台特定实现
            NetworkOut = 0 // 需要平台特定实现
        };
    }

    private PerformanceAnalysis AnalyzePerformanceResults(PerformanceTestSuite testSuite)
    {
        var analysis = new PerformanceAnalysis();

        // 识别瓶颈
        foreach (var api in testSuite.ApiEndpoints.Where(a => a.Value.P95ResponseTime > 1000))
        {
            analysis.Bottlenecks.Add(new PerformanceBottleneck
            {
                Component = "API",
                Issue = $"Slow endpoint: {api.Key}",
                Impact = api.Value.P95ResponseTime,
                Severity = api.Value.P95ResponseTime > 5000 ? "Critical" : "High"
            });
        }

        // 资源利用率
        analysis.ResourceUtilization = new ResourceUtilization
        {
            CpuUtilization = testSuite.SystemMetrics.CpuUsage,
            MemoryUtilization = (double)testSuite.SystemMetrics.MemoryUsage / (16L * 1024 * 1024 * 1024) * 100
        };

        // 可扩展性分析
        var concurrencyTest = testSuite.ConcurrencyTests.Values.FirstOrDefault();
        if (concurrencyTest != null)
        {
            analysis.Scalability = new ScalabilityAnalysis
            {
                MaxThroughput = concurrencyTest.RequestsPerSecond,
                OptimalConcurrency = concurrencyTest.ConcurrentUsers,
                BreakingPoint = concurrencyTest.ErrorRate > 0.05 ? concurrencyTest.ConcurrentUsers : 0,
                ScalabilityGrade = concurrencyTest.ErrorRate < 0.01 ? "Excellent" :
                                  concurrencyTest.ErrorRate < 0.05 ? "Good" : "Poor"
            };
        }

        return analysis;
    }

    private List<BenchmarkPerformanceRecommendation> GenerateRecommendations(PerformanceTestSuite testSuite)
    {
        var recommendations = new List<BenchmarkPerformanceRecommendation>();

        // API性能建议
        var slowApis = testSuite.ApiEndpoints
            .Where(a => a.Value.P95ResponseTime > 1000)
            .ToList();

        if (slowApis.Any())
        {
            recommendations.Add(new BenchmarkPerformanceRecommendation
            {
                Area = "API Performance",
                Issue = $"{slowApis.Count} endpoints have P95 response time > 1s",
                Recommendation = "Implement caching, optimize database queries, or add pagination",
                Priority = "High",
                ExpectedImprovement = 50
            });
        }

        // 内存建议
        var memoryTests = testSuite.MemoryTests.Values.Where(m => m.MemoryLeaked > 10 * 1024 * 1024).ToList();
        if (memoryTests.Any())
        {
            recommendations.Add(new BenchmarkPerformanceRecommendation
            {
                Area = "Memory Management",
                Issue = "Potential memory leaks detected",
                Recommendation = "Review object disposal patterns and implement proper cleanup",
                Priority = "Critical",
                ExpectedImprovement = 30
            });
        }

        // 缓存建议
        var cacheTests = testSuite.CacheOperations.Values.Where(c => c.CacheHitRate < 0.8).ToList();
        if (cacheTests.Any())
        {
            recommendations.Add(new BenchmarkPerformanceRecommendation
            {
                Area = "Caching",
                Issue = "Low cache hit rate",
                Recommendation = "Review cache key strategies and TTL settings",
                Priority = "Medium",
                ExpectedImprovement = 20
            });
        }

        return recommendations;
    }

    private BenchmarkPerformanceSummary GenerateComparisonSummary(List<PerformanceChange> changes)
    {
        var summary = new BenchmarkPerformanceSummary
        {
            TotalImprovements = changes.Count(c => c.Type == ChangeType.Improvement),
            TotalRegressions = changes.Count(c => c.Type == ChangeType.Regression),
            TotalNoChange = changes.Count(c => c.Type == ChangeType.NoChange)
        };

        summary.CriticalRegressions = changes
            .Where(c => c.Type == ChangeType.Regression && Math.Abs(c.ChangePercent) > 20)
            .Select(c => $"{c.Category}/{c.Operation}: {c.Metric} regressed by {c.ChangePercent:F1}%")
            .ToList();

        summary.SignificantImprovements = changes
            .Where(c => c.Type == ChangeType.Improvement && Math.Abs(c.ChangePercent) > 20)
            .Select(c => $"{c.Category}/{c.Operation}: {c.Metric} improved by {Math.Abs(c.ChangePercent):F1}%")
            .ToList();

        var avgChange = changes.Any() ? changes.Average(c => c.ChangePercent) : 0;
        summary.OverallPerformanceChange = avgChange;

        return summary;
    }

    private async Task SavePerformanceReport(PerformanceReport report, CancellationToken cancellationToken)
    {
        var reportPath = Path.Combine("performance-reports", $"{report.ReportId}.json");
        var reportJson = JsonSerializer.Serialize(report, _jsonOptions);

        Directory.CreateDirectory("performance-reports");
        await File.WriteAllTextAsync(reportPath, reportJson, cancellationToken);
    }

    #endregion
}

/// <summary>
/// DateTime扩展方法
/// </summary>
public static class DateTimeExtensions
{
    public static DateTime Truncate(this DateTime dateTime, TimeSpan timeSpan)
    {
        if (timeSpan == TimeSpan.Zero) return dateTime;
        if (dateTime == DateTime.MinValue || dateTime == DateTime.MaxValue) return dateTime;
        return dateTime.AddTicks(-(dateTime.Ticks % timeSpan.Ticks));
    }
}
