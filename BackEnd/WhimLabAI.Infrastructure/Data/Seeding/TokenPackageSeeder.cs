using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// Token包种子数据
/// </summary>
public class TokenPackageSeeder : EntitySeederBase<TokenPackage>
{
    public TokenPackageSeeder(WhimLabAIDbContext context, ILogger<TokenPackageSeeder> logger) 
        : base(context, logger)
    {
    }

    public override async Task SeedAsync()
    {
        // 检查是否已有Token包
        if (await Context.TokenPackages.AnyAsync())
        {
            LogSeedingSkipped("TokenPackage");
            return;
        }

        var packages = new List<TokenPackage>();

        // 小额Token包 - 适合轻度用户
        var smallPackage = new TokenPackage(
            name: "轻量包",
            tokenAmount: 10000,
            price: 19.9m,
            description: "适合轻度使用的用户，包含基础Token额度",
            originalPrice: 29.9m,
            isLimited: false,
            sortOrder: 1
        );
        SetEntityId(smallPackage, Guid.Parse("d1111111-1111-1111-1111-111111111111"));
        smallPackage.SetFeatures(new List<string>
        {
            "10,000 tokens",
            "30天有效期",
            "基础对话功能",
            "邮件支持"
        });
        packages.Add(smallPackage);

        // 标准Token包 - 适合普通用户
        var standardPackage = new TokenPackage(
            name: "标准包",
            tokenAmount: 50000,
            price: 79.9m,
            description: "适合日常使用的用户，性价比最高",
            originalPrice: 99.9m,
            isLimited: false,
            sortOrder: 2
        );
        SetEntityId(standardPackage, Guid.Parse("d222**************-2222-************"));
        standardPackage.SetFeatures(new List<string>
        {
            "50,000 tokens",
            "60天有效期",
            "高级对话功能",
            "优先邮件支持",
            "API访问"
        });
        packages.Add(standardPackage);

        // 专业Token包 - 适合重度用户
        var proPackage = new TokenPackage(
            name: "专业包",
            tokenAmount: 200000,
            price: 299.9m,
            description: "适合专业用户和小团队，包含大量Token",
            originalPrice: 399.9m,
            isLimited: false,
            sortOrder: 3
        );
        SetEntityId(proPackage, Guid.Parse("d333**************-3333-************"));
        proPackage.SetFeatures(new List<string>
        {
            "200,000 tokens",
            "90天有效期",
            "所有对话功能",
            "专属客服支持",
            "高级API访问",
            "插件支持"
        });
        packages.Add(proPackage);

        // 企业Token包 - 适合企业用户
        var enterprisePackage = new TokenPackage(
            name: "企业包",
            tokenAmount: 1000000,
            price: 1299.9m,
            description: "适合大型企业和团队，提供海量Token和专属服务",
            originalPrice: 1599.9m,
            isLimited: false,
            sortOrder: 4
        );
        SetEntityId(enterprisePackage, Guid.Parse("d444**************-4444-************"));
        enterprisePackage.SetFeatures(new List<string>
        {
            "1,000,000 tokens",
            "180天有效期",
            "所有功能无限制",
            "专属客服经理",
            "企业级API",
            "所有插件",
            "定制化服务",
            "技术支持"
        });
        packages.Add(enterprisePackage);

        // 限时特惠包 - 促销活动
        var promotionPackage = new TokenPackage(
            name: "限时特惠包",
            tokenAmount: 100000,
            price: 99.9m,
            description: "限时特惠活动，数量有限，先到先得",
            originalPrice: 199.9m,
            isLimited: true,
            sortOrder: 5
        );
        SetEntityId(promotionPackage, Guid.Parse("d555**************-5555-************"));
        promotionPackage.SetFeatures(new List<string>
        {
            "100,000 tokens",
            "45天有效期",
            "限时5折优惠",
            "所有基础功能",
            "优先支持"
        });
        promotionPackage.SetLimitedTimeOffer(DateTime.UtcNow, DateTime.UtcNow.AddDays(30));
        promotionPackage.SetStockLimit(1000);
        promotionPackage.SetPurchaseLimit(2);
        packages.Add(promotionPackage);

        LogSeeding("TokenPackage", packages.Count);
        await Context.TokenPackages.AddRangeAsync(packages);
        await Context.SaveChangesAsync();
        LogSeedingComplete("TokenPackage");
    }
}
