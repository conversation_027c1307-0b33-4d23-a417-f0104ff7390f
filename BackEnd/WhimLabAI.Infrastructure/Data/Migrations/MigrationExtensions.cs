using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WhimLabAI.Infrastructure.Data.Seeding;

namespace WhimLabAI.Infrastructure.Data.Migrations;

/// <summary>
/// 数据库迁移扩展方法
/// </summary>
public static class MigrationExtensions
{
    /// <summary>
    /// 应用数据库迁移
    /// </summary>
    public static async Task<IHost> MigrateDatabaseAsync(this IHost host)
    {
        using var scope = host.Services.CreateScope();
        var services = scope.ServiceProvider;
        var logger = services.GetRequiredService<ILogger<WhimLabAIDbContext>>();

        try
        {
            logger.LogInformation("Starting database migration...");
            
            var context = services.GetRequiredService<WhimLabAIDbContext>();
            
            // 获取待应用的迁移
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                logger.LogInformation("Found {Count} pending migrations", pendingMigrations.Count());
                foreach (var migration in pendingMigrations)
                {
                    logger.LogInformation("Pending migration: {Migration}", migration);
                }
                
                // 应用迁移
                await context.Database.MigrateAsync();
                logger.LogInformation("Database migration completed successfully");
            }
            else
            {
                logger.LogInformation("No pending migrations found");
            }

            // 种子数据
            await SeedDataAsync(services);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while migrating the database");
            throw;
        }

        return host;
    }

    /// <summary>
    /// 种子数据
    /// </summary>
    private static async Task SeedDataAsync(IServiceProvider services, bool includeSampleData = false)
    {
        var logger = services.GetRequiredService<ILogger<WhimLabAIDbContext>>();
        
        try
        {
            logger.LogInformation("Starting data seeding...");

            // 使用UnifiedDataSeeder
            var unifiedSeeder = new UnifiedDataSeeder(
                services,
                services.GetRequiredService<ILogger<UnifiedDataSeeder>>()
            );
            
            // 如果没有明确指定，则在开发环境包含示例数据
            if (!includeSampleData)
            {
                includeSampleData = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";
            }
            
            await unifiedSeeder.SeedAllAsync(includeSampleData);
            
            logger.LogInformation("Data seeding completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while seeding the database");
            throw;
        }
    }

    /// <summary>
    /// 检查数据库是否可以连接
    /// </summary>
    public static async Task<bool> CanConnectAsync(this WhimLabAIDbContext context)
    {
        try
        {
            return await context.Database.CanConnectAsync();
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取数据库版本信息
    /// </summary>
    public static async Task<DatabaseVersionInfo> GetDatabaseVersionAsync(this WhimLabAIDbContext context)
    {
        var info = new DatabaseVersionInfo
        {
            CanConnect = await context.CanConnectAsync()
        };

        if (info.CanConnect)
        {
            var appliedMigrations = await context.Database.GetAppliedMigrationsAsync();
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();

            info.AppliedMigrations = appliedMigrations.ToList();
            info.PendingMigrations = pendingMigrations.ToList();
            info.LastMigration = appliedMigrations.LastOrDefault();
            info.DatabaseExists = await context.Database.CanConnectAsync();
        }

        return info;
    }

    /// <summary>
    /// 备份数据库
    /// </summary>
    public static async Task<string> BackupDatabaseAsync(this WhimLabAIDbContext context, string backupPath)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        var backupFileName = $"whimlabai_backup_{timestamp}.sql";
        var fullPath = System.IO.Path.Combine(backupPath, backupFileName);

        // PostgreSQL backup command
        var connectionString = context.Database.GetConnectionString();
        var builder = new Npgsql.NpgsqlConnectionStringBuilder(connectionString);

        var pgDumpCommand = $"PGPASSWORD={builder.Password} pg_dump -h {builder.Host} -p {builder.Port} -U {builder.Username} -d {builder.Database} -f {fullPath}";
        
        // Execute backup (simplified - in production use proper process execution)
        await Task.Run(() =>
        {
            System.Diagnostics.Process.Start("bash", $"-c \"{pgDumpCommand}\"");
        });

        return fullPath;
    }

    /// <summary>
    /// 回滚到指定迁移
    /// </summary>
    public static async Task RollbackToMigrationAsync(this WhimLabAIDbContext context, string targetMigration)
    {
        await context.Database.MigrateAsync(); // EF Core doesn't have built-in rollback
        // In production, you would need to generate and apply down migrations
    }
}

/// <summary>
/// 数据库版本信息
/// </summary>
public class DatabaseVersionInfo
{
    public bool CanConnect { get; set; }
    public bool DatabaseExists { get; set; }
    public List<string> AppliedMigrations { get; set; } = new();
    public List<string> PendingMigrations { get; set; } = new();
    public string? LastMigration { get; set; }
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
}