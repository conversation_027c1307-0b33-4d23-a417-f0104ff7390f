using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WhimLabAI.Infrastructure.Data.Seeding;

namespace WhimLabAI.Infrastructure.Data.Migrations;

/// <summary>
/// 数据库迁移运行器
/// </summary>
public class MigrationRunner : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MigrationRunner> _logger;
    private readonly IHostEnvironment _environment;

    public MigrationRunner(
        IServiceProvider serviceProvider,
        ILogger<MigrationRunner> logger,
        IHostEnvironment environment)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _environment = environment;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Migration runner started");

        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<WhimLabAIDbContext>();

        try
        {
            // 检查数据库连接
            var canConnect = await context.Database.CanConnectAsync(cancellationToken);
            if (!canConnect)
            {
                _logger.LogError("Cannot connect to database. Please ensure PostgreSQL is running and connection string is correct.");
                return;
            }

            _logger.LogInformation("Database connection successful");

            // 获取版本信息
            var versionInfo = await context.GetDatabaseVersionAsync();
            LogVersionInfo(versionInfo);

            // 检查是否需要运行迁移
            if (versionInfo.PendingMigrations.Any())
            {
                _logger.LogInformation("Found {Count} pending migrations. Starting migration...", 
                    versionInfo.PendingMigrations.Count);

                var stopwatch = Stopwatch.StartNew();
                await context.Database.MigrateAsync(cancellationToken);
                stopwatch.Stop();

                _logger.LogInformation("Database migration completed in {ElapsedMilliseconds}ms", 
                    stopwatch.ElapsedMilliseconds);

                // 运行种子数据
                await RunSeedersAsync(context, cancellationToken);
            }
            else
            {
                _logger.LogInformation("Database is up to date. No migrations needed.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during database migration");
            if (!_environment.IsDevelopment())
            {
                throw; // 在生产环境中重新抛出异常
            }
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Migration runner stopped");
        return Task.CompletedTask;
    }

    private async Task RunSeedersAsync(WhimLabAIDbContext context, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Running data seeders...");

        // Create loggers for seeders
        var loggerFactory = _serviceProvider.GetRequiredService<ILoggerFactory>();
        var configuration = _serviceProvider.GetRequiredService<IConfiguration>();
        
        var seeders = new List<IDataSeeder>
        {
            new Seeding.RoleSeeder(context, loggerFactory.CreateLogger<Seeding.RoleSeeder>()),
            new Seeding.PermissionSeeder(context, loggerFactory.CreateLogger<Seeding.PermissionSeeder>()),
            new Seeding.SubscriptionPlanSeeder(context, loggerFactory.CreateLogger<Seeding.SubscriptionPlanSeeder>()),
            new Seeding.AdminUserSeeder(context, loggerFactory.CreateLogger<Seeding.AdminUserSeeder>(), configuration)
        };
        
        // Add seeders that don't require constructor parameters
        seeders.Add(new Seeding.SystemConfigurationSeeder());
        seeders.Add(new Seeding.AIProviderSeeder());

        foreach (var seeder in seeders)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            try
            {
                _logger.LogInformation("Running {SeederName}...", seeder.GetType().Name);
                await seeder.SeedAsync(context);
                await context.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("{SeederName} completed successfully", seeder.GetType().Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running {SeederName}", seeder.GetType().Name);
                throw;
            }
        }

        _logger.LogInformation("All data seeders completed successfully");
    }

    private void LogVersionInfo(DatabaseVersionInfo versionInfo)
    {
        _logger.LogInformation("Database Version Information:");
        _logger.LogInformation("  Can Connect: {CanConnect}", versionInfo.CanConnect);
        _logger.LogInformation("  Database Exists: {DatabaseExists}", versionInfo.DatabaseExists);
        _logger.LogInformation("  Applied Migrations: {AppliedCount}", versionInfo.AppliedMigrations.Count);
        
        if (versionInfo.AppliedMigrations.Any())
        {
            _logger.LogInformation("  Last Migration: {LastMigration}", versionInfo.LastMigration);
        }

        if (versionInfo.PendingMigrations.Any())
        {
            _logger.LogInformation("  Pending Migrations:");
            foreach (var migration in versionInfo.PendingMigrations)
            {
                _logger.LogInformation("    - {Migration}", migration);
            }
        }
    }
}

/// <summary>
/// 迁移运行器扩展方法
/// </summary>
public static class MigrationRunnerExtensions
{
    /// <summary>
    /// 添加自动迁移服务
    /// </summary>
    public static IServiceCollection AddAutomaticMigration(this IServiceCollection services)
    {
        services.AddHostedService<MigrationRunner>();
        return services;
    }

    /// <summary>
    /// 手动运行迁移
    /// </summary>
    public static async Task<IHost> RunMigrationsAsync(this IHost host)
    {
        using var scope = host.Services.CreateScope();
        var runner = new MigrationRunner(
            scope.ServiceProvider,
            scope.ServiceProvider.GetRequiredService<ILogger<MigrationRunner>>(),
            scope.ServiceProvider.GetRequiredService<IHostEnvironment>());

        await runner.StartAsync(CancellationToken.None);
        return host;
    }
}