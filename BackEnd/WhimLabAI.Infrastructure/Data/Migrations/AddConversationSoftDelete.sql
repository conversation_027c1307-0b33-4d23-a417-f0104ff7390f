-- Add soft delete fields to Conversations table
ALTER TABLE "Conversations" 
ADD COLUMN IF NOT EXISTS "IsDeleted" BOOLEAN NOT NULL DEFAULT FALSE;

ALTER TABLE "Conversations"
ADD COLUMN IF NOT EXISTS "DeletedAt" TIMESTAMP WITH TIME ZONE NULL;

-- Create index for soft deleted conversations
CREATE INDEX IF NOT EXISTS IX_Conversations_IsDeleted_UpdatedAt 
ON "Conversations" ("IsDeleted", "UpdatedAt" DESC)
WHERE "IsDeleted" = true;

-- Update existing conversations to set IsDeleted = false
UPDATE "Conversations" 
SET "IsDeleted" = FALSE 
WHERE "IsDeleted" IS NULL;