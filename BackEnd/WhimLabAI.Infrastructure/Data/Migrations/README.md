# Database Migrations

## Prerequisites
Ensure you have Entity Framework Core tools installed:
```bash
dotnet tool install --global dotnet-ef
```

## Creating Migrations

Navigate to the Infrastructure project directory:
```bash
cd BackEnd/WhimLabAI.Infrastructure
```

Add a new migration:
```bash
dotnet ef migrations add InitialCreate --project WhimLabAI.Infrastructure.csproj --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj --context WhimLabAIDbContext
```

## Updating Database

Apply migrations:
```bash
dotnet ef database update --project WhimLabAI.Infrastructure.csproj --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj --context WhimLabAIDbContext
```

## Removing Last Migration

If you need to remove the last migration:
```bash
dotnet ef migrations remove --project WhimLabAI.Infrastructure.csproj --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj --context WhimLabAIDbContext
```

## Generating SQL Scripts

Generate SQL script for production deployment:
```bash
dotnet ef migrations script --project WhimLabAI.Infrastructure.csproj --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj --context WhimLabAIDbContext --output InitialCreate.sql
```

## Important Notes

1. Always review generated migrations before applying them
2. Test migrations in a development environment first
3. Back up production database before applying migrations
4. Consider using migration bundles for production deployments