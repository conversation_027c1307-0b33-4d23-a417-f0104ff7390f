using System;
using System.CommandLine;
using System.CommandLine.Invocation;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WhimLabAI.Infrastructure.Data.Seeding;

namespace WhimLabAI.Infrastructure.Data.Migrations;

/// <summary>
/// 数据库迁移命令行工具
/// </summary>
public class MigrationCommand
{
    public static RootCommand CreateRootCommand()
    {
        var rootCommand = new RootCommand("WhimLabAI Database Migration Tool");

        // 迁移命令
        var migrateCommand = new Command("migrate", "Apply database migrations");
        migrateCommand.SetHandler(async (InvocationContext context) =>
        {
            await ExecuteMigrateAsync();
        });
        rootCommand.AddCommand(migrateCommand);

        // 种子数据命令
        var seedCommand = new Command("seed", "Seed initial data");
        seedCommand.SetHandler(async (InvocationContext context) =>
        {
            await ExecuteSeedAsync();
        });
        rootCommand.AddCommand(seedCommand);

        // 状态命令
        var statusCommand = new Command("status", "Check migration status");
        statusCommand.SetHandler(async (InvocationContext context) =>
        {
            await ExecuteStatusAsync();
        });
        rootCommand.AddCommand(statusCommand);

        // 回滚命令
        var rollbackCommand = new Command("rollback", "Rollback to specific migration");
        var migrationOption = new Option<string>("--migration", "Target migration name");
        rollbackCommand.AddOption(migrationOption);
        rollbackCommand.SetHandler(async (string? migration) =>
        {
            await ExecuteRollbackAsync(migration);
        }, migrationOption);
        rootCommand.AddCommand(rollbackCommand);

        // 重置命令
        var resetCommand = new Command("reset", "Reset database (DROP and recreate)");
        var forceOption = new Option<bool>("--force", "Force reset without confirmation");
        resetCommand.AddOption(forceOption);
        resetCommand.SetHandler(async (bool force) =>
        {
            await ExecuteResetAsync(force);
        }, forceOption);
        rootCommand.AddCommand(resetCommand);

        // 备份命令
        var backupCommand = new Command("backup", "Backup database");
        var pathOption = new Option<string>("--path", () => "./backups", "Backup directory path");
        backupCommand.AddOption(pathOption);
        backupCommand.SetHandler(async (string path) =>
        {
            await ExecuteBackupAsync(path);
        }, pathOption);
        rootCommand.AddCommand(backupCommand);

        return rootCommand;
    }

    private static async Task ExecuteMigrateAsync()
    {
        Console.WriteLine("Starting database migration...");

        using var host = CreateHost();
        var context = host.Services.GetRequiredService<WhimLabAIDbContext>();
        var logger = host.Services.GetRequiredService<ILogger<MigrationCommand>>();

        try
        {
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            if (!pendingMigrations.Any())
            {
                Console.WriteLine("Database is up to date. No migrations to apply.");
                return;
            }

            Console.WriteLine($"Found {pendingMigrations.Count()} pending migrations:");
            foreach (var migration in pendingMigrations)
            {
                Console.WriteLine($"  - {migration}");
            }

            Console.WriteLine("\nApplying migrations...");
            await context.Database.MigrateAsync();
            
            Console.WriteLine("Migrations applied successfully!");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Migration failed");
            Console.WriteLine($"ERROR: {ex.Message}");
            Environment.Exit(1);
        }
    }

    private static async Task ExecuteSeedAsync()
    {
        Console.WriteLine("Starting data seeding...");

        using var host = CreateHost();
        var context = host.Services.GetRequiredService<WhimLabAIDbContext>();
        var logger = host.Services.GetRequiredService<ILogger<MigrationCommand>>();

        try
        {
            var loggerFactory = host.Services.GetRequiredService<ILoggerFactory>();
            var configuration = host.Services.GetRequiredService<IConfiguration>();
            
            var seeders = new IDataSeeder[]
            {
                new RoleSeeder(context, loggerFactory.CreateLogger<RoleSeeder>()),
                new PermissionSeeder(context, loggerFactory.CreateLogger<PermissionSeeder>()),
                new SubscriptionPlanSeeder(context, loggerFactory.CreateLogger<SubscriptionPlanSeeder>()),
                new AdminUserSeeder(context, loggerFactory.CreateLogger<AdminUserSeeder>(), configuration),
                new SystemConfigurationSeeder(),
                new AIProviderSeeder()
            };

            foreach (var seeder in seeders)
            {
                Console.WriteLine($"Running {seeder.GetType().Name}...");
                await seeder.SeedAsync(context);
                await context.SaveChangesAsync();
            }

            Console.WriteLine("Data seeding completed successfully!");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Seeding failed");
            Console.WriteLine($"ERROR: {ex.Message}");
            Environment.Exit(1);
        }
    }

    private static async Task ExecuteStatusAsync()
    {
        Console.WriteLine("Checking database migration status...\n");

        using var host = CreateHost();
        var context = host.Services.GetRequiredService<WhimLabAIDbContext>();

        try
        {
            var canConnect = await context.Database.CanConnectAsync();
            Console.WriteLine($"Database Connection: {(canConnect ? "✓ Connected" : "✗ Cannot connect")}");

            if (!canConnect)
            {
                Console.WriteLine("\nPlease check your connection string and ensure PostgreSQL is running.");
                return;
            }

            var appliedMigrations = await context.Database.GetAppliedMigrationsAsync();
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();

            Console.WriteLine($"\nApplied Migrations ({appliedMigrations.Count()}):");
            foreach (var migration in appliedMigrations)
            {
                Console.WriteLine($"  ✓ {migration}");
            }

            if (pendingMigrations.Any())
            {
                Console.WriteLine($"\nPending Migrations ({pendingMigrations.Count()}):");
                foreach (var migration in pendingMigrations)
                {
                    Console.WriteLine($"  ○ {migration}");
                }
            }
            else
            {
                Console.WriteLine("\n✓ Database is up to date!");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ERROR: {ex.Message}");
            Environment.Exit(1);
        }
    }

    private static async Task ExecuteRollbackAsync(string? targetMigration)
    {
        if (string.IsNullOrEmpty(targetMigration))
        {
            Console.WriteLine("ERROR: Please specify target migration with --migration option");
            Environment.Exit(1);
        }

        Console.WriteLine($"Rolling back to migration: {targetMigration}");
        Console.WriteLine("WARNING: This operation may cause data loss!");
        Console.Write("Continue? (y/N): ");
        
        var confirmation = Console.ReadLine();
        if (confirmation?.ToLower() != "y")
        {
            Console.WriteLine("Operation cancelled.");
            return;
        }

        using var host = CreateHost();
        var context = host.Services.GetRequiredService<WhimLabAIDbContext>();

        try
        {
            // Note: EF Core doesn't have built-in rollback functionality
            // This would need to be implemented using down migrations
            Console.WriteLine("Note: Rollback functionality requires manual implementation of down migrations.");
            Console.WriteLine("Consider using database backups for rollback scenarios.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ERROR: {ex.Message}");
            Environment.Exit(1);
        }
    }

    private static async Task ExecuteResetAsync(bool force)
    {
        if (!force)
        {
            Console.WriteLine("WARNING: This will DELETE all data and recreate the database!");
            Console.Write("Are you sure? Type 'DELETE' to confirm: ");
            
            var confirmation = Console.ReadLine();
            if (confirmation != "DELETE")
            {
                Console.WriteLine("Operation cancelled.");
                return;
            }
        }

        Console.WriteLine("Resetting database...");

        using var host = CreateHost();
        var context = host.Services.GetRequiredService<WhimLabAIDbContext>();

        try
        {
            Console.WriteLine("Dropping database...");
            await context.Database.EnsureDeletedAsync();
            
            Console.WriteLine("Creating database...");
            await context.Database.EnsureCreatedAsync();
            
            Console.WriteLine("Applying migrations...");
            await context.Database.MigrateAsync();
            
            Console.WriteLine("Running seed data...");
            await ExecuteSeedAsync();
            
            Console.WriteLine("Database reset completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ERROR: {ex.Message}");
            Environment.Exit(1);
        }
    }

    private static async Task ExecuteBackupAsync(string backupPath)
    {
        Console.WriteLine($"Creating database backup to: {backupPath}");

        // Ensure backup directory exists
        Directory.CreateDirectory(backupPath);

        using var host = CreateHost();
        var context = host.Services.GetRequiredService<WhimLabAIDbContext>();
        var configuration = host.Services.GetRequiredService<IConfiguration>();

        try
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            var builder = new Npgsql.NpgsqlConnectionStringBuilder(connectionString);
            
            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
            var backupFileName = $"whimlabai_backup_{timestamp}.sql";
            var fullPath = Path.Combine(backupPath, backupFileName);

            Console.WriteLine($"Backing up database: {builder.Database}");
            
            // Execute pg_dump
            var processInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = "pg_dump",
                Arguments = $"-h {builder.Host} -p {builder.Port} -U {builder.Username} -d {builder.Database} -f \"{fullPath}\"",
                UseShellExecute = false,
                RedirectStandardError = true,
                Environment = { ["PGPASSWORD"] = builder.Password }
            };

            using var process = System.Diagnostics.Process.Start(processInfo);
            if (process != null)
            {
                await process.WaitForExitAsync();
                
                if (process.ExitCode == 0)
                {
                    Console.WriteLine($"Backup completed successfully: {fullPath}");
                    
                    var fileInfo = new FileInfo(fullPath);
                    Console.WriteLine($"Backup size: {fileInfo.Length / 1024 / 1024:F2} MB");
                }
                else
                {
                    var error = await process.StandardError.ReadToEndAsync();
                    Console.WriteLine($"Backup failed: {error}");
                    Environment.Exit(1);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ERROR: {ex.Message}");
            Console.WriteLine("Make sure pg_dump is installed and in your PATH");
            Environment.Exit(1);
        }
    }

    private static IHost CreateHost()
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        return Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                services.AddDbContext<WhimLabAIDbContext>(options =>
                {
                    options.UseNpgsql(configuration.GetConnectionString("DefaultConnection"));
                });
                
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                });
            })
            .Build();
    }
}

/// <summary>
/// 迁移工具程序入口
/// </summary>
public class MigrationProgram
{
    public static async Task<int> Main(string[] args)
    {
        var rootCommand = MigrationCommand.CreateRootCommand();
        return await rootCommand.InvokeAsync(args);
    }
}