-- Add indexes for conversation search optimization

-- Index for searching by title
CREATE INDEX IF NOT EXISTS IX_Conversations_Title 
ON "Conversations" ("Title");

-- Index for searching by user and archive status
CREATE INDEX IF NOT EXISTS IX_Conversations_CustomerUserId_IsArchived 
ON "Conversations" ("CustomerUserId", "IsArchived");

-- Index for searching by agent
CREATE INDEX IF NOT EXISTS IX_Conversations_AgentId 
ON "Conversations" ("AgentId");

-- Index for searching by date range
CREATE INDEX IF NOT EXISTS IX_Conversations_CreatedAt 
ON "Conversations" ("CreatedAt" DESC);

-- Index for searching by last message time
CREATE INDEX IF NOT EXISTS IX_Conversations_LastMessageAt 
ON "Conversations" ("LastMessageAt" DESC);

-- Index for pinned conversations (using JSONB)
CREATE INDEX IF NOT EXISTS IX_Conversations_Metadata_IsPinned 
ON "Conversations" USING GIN ("Metadata" jsonb_path_ops)
WHERE "Metadata" @> '{"IsPinned": true}';

-- Index for soft deleted conversations
CREATE INDEX IF NOT EXISTS IX_Conversations_IsDeleted 
ON "Conversations" ("IsDeleted", "UpdatedAt" DESC)
WHERE "IsDeleted" = true;

-- Composite index for common query patterns
CREATE INDEX IF NOT EXISTS IX_Conversations_User_Agent_Date 
ON "Conversations" ("CustomerUserId", "AgentId", "CreatedAt" DESC);

-- Index for message content search (if needed)
CREATE INDEX IF NOT EXISTS IX_ConversationMessages_Content_GIN
ON "ConversationMessages" USING GIN (to_tsvector('english', "Content"));

-- Index for message conversation relationship
CREATE INDEX IF NOT EXISTS IX_ConversationMessages_ConversationId 
ON "ConversationMessages" ("ConversationId", "CreatedAt" DESC);

-- Index for message role filtering
CREATE INDEX IF NOT EXISTS IX_ConversationMessages_Role 
ON "ConversationMessages" ("Role");

-- Analyze tables to update statistics
ANALYZE "Conversations";
ANALYZE "ConversationMessages";