# EF Core Database Migrations

## Prerequisites

1. Install EF Core CLI tools:
```bash
dotnet tool install --global dotnet-ef
```

2. Ensure you have a valid connection string in appsettings.json or environment variables.

## Creating Migrations

### First-time Setup

From the solution root directory:

```bash
# Create initial migration
dotnet ef migrations add InitialCreate \
  --project BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj \
  --startup-project FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
  --context WhimLabAIDbContext \
  --output-dir Data/Migrations
```

### Adding New Migrations

```bash
# Add a new migration (replace MigrationName with descriptive name)
dotnet ef migrations add MigrationName \
  --project BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj \
  --startup-project FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
  --context WhimLabAIDbContext \
  --output-dir Data/Migrations
```

## Applying Migrations

### Update Database

```bash
# Apply all pending migrations
dotnet ef database update \
  --project BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj \
  --startup-project FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
  --context WhimLabAIDbContext
```

### Update to Specific Migration

```bash
# Update to specific migration
dotnet ef database update MigrationName \
  --project BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj \
  --startup-project FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
  --context WhimLabAIDbContext
```

## Rollback Migrations

```bash
# Rollback to previous migration
dotnet ef database update PreviousMigrationName \
  --project BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj \
  --startup-project FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
  --context WhimLabAIDbContext

# Rollback all migrations (empty database)
dotnet ef database update 0 \
  --project BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj \
  --startup-project FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
  --context WhimLabAIDbContext
```

## Removing Migrations

```bash
# Remove last migration (only if not applied to database)
dotnet ef migrations remove \
  --project BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj \
  --startup-project FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
  --context WhimLabAIDbContext
```

## Generating SQL Scripts

```bash
# Generate SQL script for all migrations
dotnet ef migrations script \
  --project BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj \
  --startup-project FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
  --context WhimLabAIDbContext \
  --output Migrations.sql

# Generate SQL script from specific migration to latest
dotnet ef migrations script FromMigration \
  --project BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj \
  --startup-project FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
  --context WhimLabAIDbContext \
  --output Migrations.sql

# Generate idempotent script (can be run multiple times)
dotnet ef migrations script \
  --idempotent \
  --project BackEnd/WhimLabAI.Infrastructure/WhimLabAI.Infrastructure.csproj \
  --startup-project FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj \
  --context WhimLabAIDbContext \
  --output Migrations.sql
```

## Connection String Configuration

Ensure your connection string is properly configured:

### appsettings.json
```json
{
  "ConnectionStrings": {
    "PostgreSQL": "Host=localhost;Database=WhimLabAI;Username=postgres;Password=yourpassword"
  }
}
```

### Environment Variable
```bash
export ConnectionStrings__PostgreSQL="Host=localhost;Database=WhimLabAI;Username=postgres;Password=yourpassword"
```

## Common Issues

### 1. Connection String Not Found
- Ensure the WebApi project has the correct appsettings.json
- Check environment variables
- Verify the connection string name matches in Startup.cs

### 2. PostgreSQL Extensions
Some features may require PostgreSQL extensions:
```sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
```

### 3. Migration Assembly
If you get assembly errors, ensure the Infrastructure project references:
- Microsoft.EntityFrameworkCore.Design
- Npgsql.EntityFrameworkCore.PostgreSQL

## Best Practices

1. **Always review generated migrations** before applying them
2. **Test migrations** on a development database first
3. **Backup production database** before applying migrations
4. **Use descriptive migration names** (e.g., AddUserTwoFactorAuth, CreateOrderIndexes)
5. **Don't edit migration files** after they've been applied
6. **Keep migrations small and focused** on a single change
7. **Version control all migration files**