using Microsoft.EntityFrameworkCore.Migrations;

namespace WhimLabAI.Infrastructure.Data.Migrations;

/// <summary>
/// 添加性能优化索引
/// </summary>
public partial class AddPerformanceIndexes : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // CustomerUsers表索引
        migrationBuilder.CreateIndex(
            name: "IX_CustomerUsers_Email",
            table: "CustomerUsers",
            column: "Email",
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_CustomerUsers_Phone",
            table: "CustomerUsers",
            column: "Phone");

        migrationBuilder.CreateIndex(
            name: "IX_CustomerUsers_Username",
            table: "CustomerUsers",
            column: "Username",
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_CustomerUsers_Status",
            table: "CustomerUsers",
            column: "Status");

        migrationBuilder.CreateIndex(
            name: "IX_CustomerUsers_CreatedAt",
            table: "CustomerUsers",
            column: "CreatedAt");

        // AdminUsers表索引
        migrationBuilder.CreateIndex(
            name: "IX_AdminUsers_Email",
            table: "AdminUsers",
            column: "Email",
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_AdminUsers_Phone",
            table: "AdminUsers",
            column: "Phone");

        migrationBuilder.CreateIndex(
            name: "IX_AdminUsers_Username",
            table: "AdminUsers",
            column: "Username",
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_AdminUsers_Status",
            table: "AdminUsers",
            column: "Status");

        // Agents表索引
        migrationBuilder.CreateIndex(
            name: "IX_Agents_Name",
            table: "Agents",
            column: "Name");

        migrationBuilder.CreateIndex(
            name: "IX_Agents_Status",
            table: "Agents",
            column: "Status");

        migrationBuilder.CreateIndex(
            name: "IX_Agents_Category",
            table: "Agents",
            column: "Category");

        migrationBuilder.CreateIndex(
            name: "IX_Agents_CreatedAt",
            table: "Agents",
            column: "CreatedAt");

        migrationBuilder.CreateIndex(
            name: "IX_Agents_Rating",
            table: "Agents",
            column: "Rating");

        // 复合索引：按状态和分类查询
        migrationBuilder.CreateIndex(
            name: "IX_Agents_Status_Category",
            table: "Agents",
            columns: new[] { "Status", "Category" });

        // Conversations表索引
        migrationBuilder.CreateIndex(
            name: "IX_Conversations_UserId",
            table: "Conversations",
            column: "UserId");

        migrationBuilder.CreateIndex(
            name: "IX_Conversations_AgentId",
            table: "Conversations",
            column: "AgentId");

        migrationBuilder.CreateIndex(
            name: "IX_Conversations_CreatedAt",
            table: "Conversations",
            column: "CreatedAt");

        migrationBuilder.CreateIndex(
            name: "IX_Conversations_IsArchived",
            table: "Conversations",
            column: "IsArchived");

        // 复合索引：用户的对话列表
        migrationBuilder.CreateIndex(
            name: "IX_Conversations_UserId_IsArchived_CreatedAt",
            table: "Conversations",
            columns: new[] { "UserId", "IsArchived", "CreatedAt" });

        // Messages表索引
        migrationBuilder.CreateIndex(
            name: "IX_Messages_ConversationId",
            table: "Messages",
            column: "ConversationId");

        migrationBuilder.CreateIndex(
            name: "IX_Messages_CreatedAt",
            table: "Messages",
            column: "CreatedAt");

        // 复合索引：对话中的消息列表
        migrationBuilder.CreateIndex(
            name: "IX_Messages_ConversationId_CreatedAt",
            table: "Messages",
            columns: new[] { "ConversationId", "CreatedAt" });

        // Subscriptions表索引
        migrationBuilder.CreateIndex(
            name: "IX_Subscriptions_UserId",
            table: "Subscriptions",
            column: "UserId");

        migrationBuilder.CreateIndex(
            name: "IX_Subscriptions_Status",
            table: "Subscriptions",
            column: "Status");

        migrationBuilder.CreateIndex(
            name: "IX_Subscriptions_ExpiresAt",
            table: "Subscriptions",
            column: "ExpiresAt");

        // 复合索引：查找用户的有效订阅
        migrationBuilder.CreateIndex(
            name: "IX_Subscriptions_UserId_Status_ExpiresAt",
            table: "Subscriptions",
            columns: new[] { "UserId", "Status", "ExpiresAt" });

        // Orders表索引
        migrationBuilder.CreateIndex(
            name: "IX_Orders_UserId",
            table: "Orders",
            column: "UserId");

        migrationBuilder.CreateIndex(
            name: "IX_Orders_Status",
            table: "Orders",
            column: "Status");

        migrationBuilder.CreateIndex(
            name: "IX_Orders_PaymentStatus",
            table: "Orders",
            column: "PaymentStatus");

        migrationBuilder.CreateIndex(
            name: "IX_Orders_CreatedAt",
            table: "Orders",
            column: "CreatedAt");

        // 复合索引：用户订单查询
        migrationBuilder.CreateIndex(
            name: "IX_Orders_UserId_Status_CreatedAt",
            table: "Orders",
            columns: new[] { "UserId", "Status", "CreatedAt" });

        // TokenUsages表索引
        migrationBuilder.CreateIndex(
            name: "IX_TokenUsages_UserId",
            table: "TokenUsages",
            column: "UserId");

        migrationBuilder.CreateIndex(
            name: "IX_TokenUsages_ConversationId",
            table: "TokenUsages",
            column: "ConversationId");

        migrationBuilder.CreateIndex(
            name: "IX_TokenUsages_Date",
            table: "TokenUsages",
            column: "Date");

        // 复合索引：用户在特定日期的使用量
        migrationBuilder.CreateIndex(
            name: "IX_TokenUsages_UserId_Date",
            table: "TokenUsages",
            columns: new[] { "UserId", "Date" });

        // AuditLogs表索引
        migrationBuilder.CreateIndex(
            name: "IX_AuditLogs_UserId",
            table: "AuditLogs",
            column: "UserId");

        migrationBuilder.CreateIndex(
            name: "IX_AuditLogs_Timestamp",
            table: "AuditLogs",
            column: "Timestamp");

        migrationBuilder.CreateIndex(
            name: "IX_AuditLogs_EntityType",
            table: "AuditLogs",
            column: "EntityType");

        migrationBuilder.CreateIndex(
            name: "IX_AuditLogs_Action",
            table: "AuditLogs",
            column: "Action");

        // 复合索引：按用户和时间查询审计日志
        migrationBuilder.CreateIndex(
            name: "IX_AuditLogs_UserId_Timestamp",
            table: "AuditLogs",
            columns: new[] { "UserId", "Timestamp" });

        // AgentRatings表索引
        migrationBuilder.CreateIndex(
            name: "IX_AgentRatings_AgentId",
            table: "AgentRatings",
            column: "AgentId");

        migrationBuilder.CreateIndex(
            name: "IX_AgentRatings_UserId",
            table: "AgentRatings",
            column: "UserId");

        migrationBuilder.CreateIndex(
            name: "IX_AgentRatings_CreatedAt",
            table: "AgentRatings",
            column: "CreatedAt");

        // 唯一索引：用户对代理的唯一评分
        migrationBuilder.CreateIndex(
            name: "IX_AgentRatings_UserId_AgentId",
            table: "AgentRatings",
            columns: new[] { "UserId", "AgentId" },
            unique: true);

        // Coupons表索引
        migrationBuilder.CreateIndex(
            name: "IX_Coupons_Code",
            table: "Coupons",
            column: "Code",
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_Coupons_Status",
            table: "Coupons",
            column: "Status");

        migrationBuilder.CreateIndex(
            name: "IX_Coupons_ValidFrom_ValidTo",
            table: "Coupons",
            columns: new[] { "ValidFrom", "ValidTo" });

        // CouponUsages表索引
        migrationBuilder.CreateIndex(
            name: "IX_CouponUsages_CouponId",
            table: "CouponUsages",
            column: "CouponId");

        migrationBuilder.CreateIndex(
            name: "IX_CouponUsages_UserId",
            table: "CouponUsages",
            column: "UserId");

        migrationBuilder.CreateIndex(
            name: "IX_CouponUsages_OrderId",
            table: "CouponUsages",
            column: "OrderId");

        // LoginHistories表索引
        migrationBuilder.CreateIndex(
            name: "IX_LoginHistories_UserId",
            table: "LoginHistories",
            column: "UserId");

        migrationBuilder.CreateIndex(
            name: "IX_LoginHistories_LoginTime",
            table: "LoginHistories",
            column: "LoginTime");

        migrationBuilder.CreateIndex(
            name: "IX_LoginHistories_IpAddress",
            table: "LoginHistories",
            column: "IpAddress");

        // 复合索引：用户登录历史
        migrationBuilder.CreateIndex(
            name: "IX_LoginHistories_UserId_LoginTime",
            table: "LoginHistories",
            columns: new[] { "UserId", "LoginTime" });

        // OAuthStates表索引
        migrationBuilder.CreateIndex(
            name: "IX_OAuthStates_State",
            table: "OAuthStates",
            column: "State",
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_OAuthStates_ExpiresAt",
            table: "OAuthStates",
            column: "ExpiresAt");

        // SystemSettings表索引
        migrationBuilder.CreateIndex(
            name: "IX_SystemSettings_Key",
            table: "SystemSettings",
            column: "Key",
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_SystemSettings_Category",
            table: "SystemSettings",
            column: "Category");

        // ApiKeys表索引（如果存在）
        migrationBuilder.Sql(@"
            CREATE INDEX IF NOT EXISTS ""IX_ApiKeys_Key"" ON ""ApiKeys"" (""Key"");
            CREATE INDEX IF NOT EXISTS ""IX_ApiKeys_UserId"" ON ""ApiKeys"" (""UserId"");
            CREATE INDEX IF NOT EXISTS ""IX_ApiKeys_Status"" ON ""ApiKeys"" (""Status"");
            CREATE INDEX IF NOT EXISTS ""IX_ApiKeys_ExpiresAt"" ON ""ApiKeys"" (""ExpiresAt"");
        ");

        // 全文搜索索引（PostgreSQL特定）
        migrationBuilder.Sql(@"
            -- 为Agent名称和描述创建全文搜索索引
            CREATE INDEX IF NOT EXISTS ""IX_Agents_SearchVector"" ON ""Agents"" 
            USING GIN (to_tsvector('simple', ""Name"" || ' ' || COALESCE(""Description"", '')));
            
            -- 为Message内容创建全文搜索索引（可选，根据需求）
            -- CREATE INDEX IF NOT EXISTS ""IX_Messages_SearchVector"" ON ""Messages"" 
            -- USING GIN (to_tsvector('simple', ""Content""));
        ");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        // CustomerUsers表索引
        migrationBuilder.DropIndex("IX_CustomerUsers_Email", "CustomerUsers");
        migrationBuilder.DropIndex("IX_CustomerUsers_Phone", "CustomerUsers");
        migrationBuilder.DropIndex("IX_CustomerUsers_Username", "CustomerUsers");
        migrationBuilder.DropIndex("IX_CustomerUsers_Status", "CustomerUsers");
        migrationBuilder.DropIndex("IX_CustomerUsers_CreatedAt", "CustomerUsers");

        // AdminUsers表索引
        migrationBuilder.DropIndex("IX_AdminUsers_Email", "AdminUsers");
        migrationBuilder.DropIndex("IX_AdminUsers_Phone", "AdminUsers");
        migrationBuilder.DropIndex("IX_AdminUsers_Username", "AdminUsers");
        migrationBuilder.DropIndex("IX_AdminUsers_Status", "AdminUsers");

        // Agents表索引
        migrationBuilder.DropIndex("IX_Agents_Name", "Agents");
        migrationBuilder.DropIndex("IX_Agents_Status", "Agents");
        migrationBuilder.DropIndex("IX_Agents_Category", "Agents");
        migrationBuilder.DropIndex("IX_Agents_CreatedAt", "Agents");
        migrationBuilder.DropIndex("IX_Agents_Rating", "Agents");
        migrationBuilder.DropIndex("IX_Agents_Status_Category", "Agents");

        // Conversations表索引
        migrationBuilder.DropIndex("IX_Conversations_UserId", "Conversations");
        migrationBuilder.DropIndex("IX_Conversations_AgentId", "Conversations");
        migrationBuilder.DropIndex("IX_Conversations_CreatedAt", "Conversations");
        migrationBuilder.DropIndex("IX_Conversations_IsArchived", "Conversations");
        migrationBuilder.DropIndex("IX_Conversations_UserId_IsArchived_CreatedAt", "Conversations");

        // Messages表索引
        migrationBuilder.DropIndex("IX_Messages_ConversationId", "Messages");
        migrationBuilder.DropIndex("IX_Messages_CreatedAt", "Messages");
        migrationBuilder.DropIndex("IX_Messages_ConversationId_CreatedAt", "Messages");

        // Subscriptions表索引
        migrationBuilder.DropIndex("IX_Subscriptions_UserId", "Subscriptions");
        migrationBuilder.DropIndex("IX_Subscriptions_Status", "Subscriptions");
        migrationBuilder.DropIndex("IX_Subscriptions_ExpiresAt", "Subscriptions");
        migrationBuilder.DropIndex("IX_Subscriptions_UserId_Status_ExpiresAt", "Subscriptions");

        // Orders表索引
        migrationBuilder.DropIndex("IX_Orders_UserId", "Orders");
        migrationBuilder.DropIndex("IX_Orders_Status", "Orders");
        migrationBuilder.DropIndex("IX_Orders_PaymentStatus", "Orders");
        migrationBuilder.DropIndex("IX_Orders_CreatedAt", "Orders");
        migrationBuilder.DropIndex("IX_Orders_UserId_Status_CreatedAt", "Orders");

        // TokenUsages表索引
        migrationBuilder.DropIndex("IX_TokenUsages_UserId", "TokenUsages");
        migrationBuilder.DropIndex("IX_TokenUsages_ConversationId", "TokenUsages");
        migrationBuilder.DropIndex("IX_TokenUsages_Date", "TokenUsages");
        migrationBuilder.DropIndex("IX_TokenUsages_UserId_Date", "TokenUsages");

        // AuditLogs表索引
        migrationBuilder.DropIndex("IX_AuditLogs_UserId", "AuditLogs");
        migrationBuilder.DropIndex("IX_AuditLogs_Timestamp", "AuditLogs");
        migrationBuilder.DropIndex("IX_AuditLogs_EntityType", "AuditLogs");
        migrationBuilder.DropIndex("IX_AuditLogs_Action", "AuditLogs");
        migrationBuilder.DropIndex("IX_AuditLogs_UserId_Timestamp", "AuditLogs");

        // AgentRatings表索引
        migrationBuilder.DropIndex("IX_AgentRatings_AgentId", "AgentRatings");
        migrationBuilder.DropIndex("IX_AgentRatings_UserId", "AgentRatings");
        migrationBuilder.DropIndex("IX_AgentRatings_CreatedAt", "AgentRatings");
        migrationBuilder.DropIndex("IX_AgentRatings_UserId_AgentId", "AgentRatings");

        // Coupons表索引
        migrationBuilder.DropIndex("IX_Coupons_Code", "Coupons");
        migrationBuilder.DropIndex("IX_Coupons_Status", "Coupons");
        migrationBuilder.DropIndex("IX_Coupons_ValidFrom_ValidTo", "Coupons");

        // CouponUsages表索引
        migrationBuilder.DropIndex("IX_CouponUsages_CouponId", "CouponUsages");
        migrationBuilder.DropIndex("IX_CouponUsages_UserId", "CouponUsages");
        migrationBuilder.DropIndex("IX_CouponUsages_OrderId", "CouponUsages");

        // LoginHistories表索引
        migrationBuilder.DropIndex("IX_LoginHistories_UserId", "LoginHistories");
        migrationBuilder.DropIndex("IX_LoginHistories_LoginTime", "LoginHistories");
        migrationBuilder.DropIndex("IX_LoginHistories_IpAddress", "LoginHistories");
        migrationBuilder.DropIndex("IX_LoginHistories_UserId_LoginTime", "LoginHistories");

        // OAuthStates表索引
        migrationBuilder.DropIndex("IX_OAuthStates_State", "OAuthStates");
        migrationBuilder.DropIndex("IX_OAuthStates_ExpiresAt", "OAuthStates");

        // SystemSettings表索引
        migrationBuilder.DropIndex("IX_SystemSettings_Key", "SystemSettings");
        migrationBuilder.DropIndex("IX_SystemSettings_Category", "SystemSettings");

        // 删除SQL创建的索引
        migrationBuilder.Sql(@"
            DROP INDEX IF EXISTS ""IX_ApiKeys_Key"";
            DROP INDEX IF EXISTS ""IX_ApiKeys_UserId"";
            DROP INDEX IF EXISTS ""IX_ApiKeys_Status"";
            DROP INDEX IF EXISTS ""IX_ApiKeys_ExpiresAt"";
            DROP INDEX IF EXISTS ""IX_Agents_SearchVector"";
        ");
    }
}