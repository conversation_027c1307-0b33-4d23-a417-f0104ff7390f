using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class RecoveryCodeRepository : Repository<RecoveryCode>, IRecoveryCodeRepository
{
    public RecoveryCodeRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<RecoveryCode>> GetValidCodesAsync(
        Guid userId, 
        string userType, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(rc => rc.UserId == userId 
                && rc.UserType == userType 
                && !rc.IsUsed 
                && rc.ExpiresAt > DateTime.UtcNow)
            .OrderBy(rc => rc.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<RecoveryCode?> GetByCodeAsync(
        string code, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(rc => rc.Code == code, cancellationToken);
    }

    public async Task DeleteUserCodesAsync(
        Guid userId, 
        string userType, 
        CancellationToken cancellationToken = default)
    {
        var codes = await _dbSet
            .Where(rc => rc.UserId == userId && rc.UserType == userType)
            .ToListAsync(cancellationToken);
        
        if (codes.Any())
        {
            _dbSet.RemoveRange(codes);
        }
    }

    public async Task<int> CountValidCodesAsync(
        Guid userId, 
        string userType, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .CountAsync(rc => rc.UserId == userId 
                && rc.UserType == userType 
                && !rc.IsUsed 
                && rc.ExpiresAt > DateTime.UtcNow, 
                cancellationToken);
    }

    public async Task<bool> IsCodeValidAsync(
        string code, 
        Guid userId, 
        string userType, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AnyAsync(rc => rc.Code == code 
                && rc.UserId == userId 
                && rc.UserType == userType 
                && !rc.IsUsed 
                && rc.ExpiresAt > DateTime.UtcNow, 
                cancellationToken);
    }

    public async Task<IEnumerable<RecoveryCode>> GetAllUserCodesAsync(
        Guid userId, 
        string userType, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(rc => rc.UserId == userId && rc.UserType == userType)
            .OrderByDescending(rc => rc.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}