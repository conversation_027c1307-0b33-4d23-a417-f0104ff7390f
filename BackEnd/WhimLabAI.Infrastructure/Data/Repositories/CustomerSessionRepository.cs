using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.System;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class CustomerSessionRepository : Repository<CustomerUserSession>, ICustomerSessionRepository
{
    public CustomerSessionRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<CustomerUserSession?> GetByTokenAsync(
        string token, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(s => s.SessionToken == token, cancellationToken);
    }

    public async Task<IEnumerable<CustomerUserSession>> GetActiveSessionsAsync(
        Guid customerUserId, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(s => s.CustomerUserId == customerUserId && s.IsActive && s.ExpiresAt > DateTime.UtcNow)
            .OrderByDescending(s => s.LastActivityAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> InvalidateSessionAsync(
        string token, 
        CancellationToken cancellationToken = default)
    {
        var session = await GetByTokenAsync(token, cancellationToken);
        if (session == null)
            return false;

        session.InvalidateSession();
        _dbSet.Update(session);
        return true;
    }

    public async Task<bool> InvalidateAllCustomerSessionsAsync(
        Guid customerUserId, 
        CancellationToken cancellationToken = default)
    {
        var sessions = await _dbSet
            .Where(s => s.CustomerUserId == customerUserId && s.IsActive)
            .ToListAsync(cancellationToken);

        foreach (var session in sessions)
        {
            session.InvalidateSession();
        }

        _dbSet.UpdateRange(sessions);
        return sessions.Any();
    }

    public async Task<IEnumerable<CustomerUserSession>> GetExpiredSessionsAsync(
        int days, 
        CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-days);
        return await _dbSet
            .Where(s => s.ExpiresAt <= DateTime.UtcNow || s.LastActivityAt <= cutoffDate)
            .ToListAsync(cancellationToken);
    }
}