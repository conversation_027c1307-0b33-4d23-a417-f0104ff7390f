using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Shared.Enums;
using RefundStatus = WhimLabAI.Shared.Enums.RefundStatus;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class RefundRepository : Repository<RefundRecord>, IRefundRepository
{
    private readonly ILogger<RefundRepository> _logger;

    public RefundRepository(WhimLabAIDbContext context, ILogger<RefundRepository> logger)
        : base(context)
    {
        _logger = logger;
    }

    public async Task<RefundRecord?> GetByRefundNoAsync(string refundNo, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(r => r.RefundNo == refundNo, cancellationToken);
    }

    public async Task<IEnumerable<RefundRecord>> GetOrderRefundsAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(r => r.OrderId == orderId)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RefundRecord>> GetPendingRefundsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(r => r.Status == RefundStatus.Pending || r.Status == RefundStatus.Processing)
            .OrderBy(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> UpdateRefundStatusAsync(Guid refundId, RefundStatus status, string? refundTransactionId = null, string? failureReason = null, CancellationToken cancellationToken = default)
    {
        var refund = await _dbSet.FindAsync([refundId], cancellationToken);
        if (refund == null)
        {
            return false;
        }

        var oldStatus = refund.Status;
        
        // Use domain methods to update status
        switch (status)
        {
            case RefundStatus.Processing:
                refund.StartProcessing();
                break;
            case RefundStatus.Completed:
                refund.MarkAsCompleted(refundTransactionId);
                break;
            case RefundStatus.Failed:
                refund.MarkAsFailed(failureReason ?? "Refund failed");
                break;
            case RefundStatus.Cancelled:
                refund.Cancel(failureReason);
                break;
            default:
                throw new ArgumentException($"Invalid status transition to {status}");
        }

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Refund {RefundId} status changed from {OldStatus} to {NewStatus}",
            refundId, oldStatus, status);

        return true;
    }

    public async Task<decimal> GetTotalRefundedAmountAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(r => r.OrderId == orderId && r.Status == RefundStatus.Completed)
            .SumAsync(r => r.RefundAmount.Amount, cancellationToken);
    }

    public async Task<decimal> GetOrderRefundedAmountAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await GetTotalRefundedAmountAsync(orderId, cancellationToken);
    }

    public async Task<bool> HasPendingRefundAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AnyAsync(r => r.OrderId == orderId &&
                         (r.Status == RefundStatus.Pending || r.Status == RefundStatus.Processing),
                      cancellationToken);
    }

    public async Task<object> GetRefundStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var refunds = await _dbSet
            .Where(r => r.CreatedAt >= startDate && r.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);

        var successfulRefunds = refunds.Where(r => r.Status == RefundStatus.Completed).ToList();

        return new
        {
            TotalRefunds = refunds.Count,
            SuccessfulRefunds = successfulRefunds.Count,
            FailedRefunds = refunds.Count(r => r.Status == RefundStatus.Failed),
            PendingRefunds = refunds.Count(r => r.Status == RefundStatus.Pending),
            ProcessingRefunds = refunds.Count(r => r.Status == RefundStatus.Processing),
            TotalAmount = successfulRefunds.Sum(r => r.RefundAmount.Amount),
            RefundsByReason = refunds
                .GroupBy(r => r.Reason)
                .ToDictionary(g => g.Key, g => new
                {
                    Count = g.Count(),
                    Amount = g.Where(r => r.Status == RefundStatus.Completed).Sum(r => r.RefundAmount.Amount)
                }),
            DailyRefunds = successfulRefunds
                .GroupBy(r => r.CompletedAt?.Date ?? r.CreatedAt.Date)
                .OrderBy(g => g.Key)
                .ToDictionary(g => g.Key, g => new
                {
                    Count = g.Count(),
                    Amount = g.Sum(r => r.RefundAmount.Amount)
                }),
            AverageProcessingTime = successfulRefunds.Any()
                ? successfulRefunds.Average(r => (r.CompletedAt!.Value - r.CreatedAt).TotalHours)
                : 0
        };
    }

    public async Task<IEnumerable<object>> GetUserRefundsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var userOrders = await _context.Orders
            .Where(o => o.CustomerUserId == userId)
            .Select(o => o.Id)
            .ToListAsync(cancellationToken);

        return await _dbSet
            .Where(r => userOrders.Contains(r.OrderId))
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> CanRefundOrderAsync(Guid orderId, decimal amount, CancellationToken cancellationToken = default)
    {
        // Check if order exists and is completed
        var order = await _context.Orders
            .FirstOrDefaultAsync(o => o.Id == orderId && o.Status == OrderStatus.Paid, cancellationToken);

        if (order == null)
        {
            return false;
        }

        // Check if there's a pending refund
        if (await HasPendingRefundAsync(orderId, cancellationToken))
        {
            return false;
        }

        // Check if total refunded amount would exceed order amount
        var totalRefunded = await GetTotalRefundedAmountAsync(orderId, cancellationToken);
        if (totalRefunded + amount > order.FinalAmount.Amount)
        {
            return false;
        }

        return true;
    }

    public async Task<IEnumerable<object>> GetRefundsByPaymentMethodAsync(PaymentMethod method, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        // Get order IDs for payments with the specified method
        var orderIds = await _context.PaymentTransactions
            .Where(p => p.PaymentMethod == method && p.Status == TransactionStatus.Success)
            .Select(p => p.OrderId)
            .Distinct()
            .ToListAsync(cancellationToken);

        return await _dbSet
            .Where(r => orderIds.Contains(r.OrderId) &&
                       r.CreatedAt >= startDate &&
                       r.CreatedAt <= endDate)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}
