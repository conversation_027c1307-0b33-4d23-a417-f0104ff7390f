using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class QRCodeSessionRepository : Repository<QRCodeSession>, IQRCodeSessionRepository
{
    public QRCodeSessionRepository(WhimLabAIDbContext context) : base(context)
    {
    }
    
    public async Task<QRCodeSession?> GetBySessionIdAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(s => s.User)
            .FirstOrDefaultAsync(s => s.SessionId == sessionId, cancellationToken);
    }
    
    public async Task<QRCodeSession?> GetActiveSessionBySessionIdAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(s => s.User)
            .FirstOrDefaultAsync(s => s.SessionId == sessionId && 
                                    s.Status != QRCodeSessionStatus.Expired && 
                                    s.Status != QRCodeSessionStatus.Cancelled &&
                                    s.ExpiresAt > DateTime.UtcNow, cancellationToken);
    }
    
    public async Task<IEnumerable<QRCodeSession>> GetExpiredSessionsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(s => s.ExpiresAt <= DateTime.UtcNow && s.Status == QRCodeSessionStatus.Pending)
            .ToListAsync(cancellationToken);
    }
    
    public async Task<int> DeleteExpiredSessionsAsync(CancellationToken cancellationToken = default)
    {
        var expiredSessions = await _dbSet
            .Where(s => s.ExpiresAt <= DateTime.UtcNow && 
                       (s.Status == QRCodeSessionStatus.Pending || s.Status == QRCodeSessionStatus.Scanned))
            .ToListAsync(cancellationToken);
            
        _dbSet.RemoveRange(expiredSessions);
        return expiredSessions.Count;
    }
    
    public async Task<IEnumerable<QRCodeSession>> GetUserSessionsAsync(Guid userId, int limit = 10, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(s => s.UserId == userId)
            .OrderByDescending(s => s.CreatedAt)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }
}