using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.KnowledgeBase;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 文档仓储实现
/// </summary>
public class DocumentRepository : Repository<Document>, IDocumentRepository
{
    public DocumentRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Document>> GetByKnowledgeBaseIdAsync(
        Guid knowledgeBaseId,
        bool includeDeleted = false,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Set<Document>()
            .Where(d => d.KnowledgeBaseId == knowledgeBaseId);

        if (!includeDeleted)
        {
            query = query.Where(d => !d.IsDeleted);
        }

        return await query
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<Document?> GetByFileHashAsync(
        string fileHash,
        Guid knowledgeBaseId,
        CancellationToken cancellationToken = default)
    {
        return await _context.Set<Document>()
            .FirstOrDefaultAsync(d => 
                d.FileHash == fileHash && 
                d.KnowledgeBaseId == knowledgeBaseId && 
                !d.IsDeleted,
                cancellationToken);
    }

    public async Task<IEnumerable<Document>> GetPendingDocumentsAsync(
        int batchSize,
        CancellationToken cancellationToken = default)
    {
        return await _context.Set<Document>()
            .Where(d => d.Status == DocumentStatus.Pending && !d.IsDeleted)
            .OrderBy(d => d.CreatedAt)
            .Take(batchSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<Document> Items, int TotalCount)> SearchAsync(
        Guid knowledgeBaseId,
        string? keyword,
        DocumentType? documentType,
        DocumentStatus? status,
        DateTime? startDate,
        DateTime? endDate,
        int pageIndex,
        int pageSize,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Set<Document>()
            .Where(d => d.KnowledgeBaseId == knowledgeBaseId && !d.IsDeleted);

        if (!string.IsNullOrWhiteSpace(keyword))
        {
            query = query.Where(d => EF.Functions.ILike(d.Name, $"%{keyword}%"));
        }

        if (documentType.HasValue)
        {
            query = query.Where(d => d.DocumentType == documentType.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(d => d.Status == status.Value);
        }

        if (startDate.HasValue)
        {
            query = query.Where(d => d.CreatedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(d => d.CreatedAt <= endDate.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var items = await query
            .OrderByDescending(d => d.CreatedAt)
            .Skip(pageIndex * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<Dictionary<string, object>> GetStatisticsAsync(
        Guid knowledgeBaseId,
        CancellationToken cancellationToken = default)
    {
        var documents = await _context.Set<Document>()
            .Where(d => d.KnowledgeBaseId == knowledgeBaseId && !d.IsDeleted)
            .ToListAsync(cancellationToken);

        var stats = new Dictionary<string, object>
        {
            ["DocumentCount"] = documents.Count,
            ["ProcessedCount"] = documents.Count(d => d.Status == DocumentStatus.Processed),
            ["FailedCount"] = documents.Count(d => d.Status == DocumentStatus.Failed),
            ["TotalVectors"] = documents.Sum(d => d.ChunkCount),
            ["StorageSize"] = documents.Sum(d => d.FileSize)
        };

        // Group by document type
        var documentsByType = documents
            .GroupBy(d => d.DocumentType)
            .ToDictionary(g => g.Key, g => g.Count());
        stats["DocumentsByType"] = documentsByType;

        // Group by status
        var documentsByStatus = documents
            .GroupBy(d => d.Status)
            .ToDictionary(g => g.Key, g => g.Count());
        stats["DocumentsByStatus"] = documentsByStatus;

        return stats;
    }

    public async Task<int> DeleteBatchAsync(
        IEnumerable<Guid> documentIds,
        CancellationToken cancellationToken = default)
    {
        var docIds = documentIds.ToList();
        if (!docIds.Any())
            return 0;

        return await _context.Set<Document>()
            .Where(d => docIds.Contains(d.Id))
            .ExecuteUpdateAsync(
                setter => setter.SetProperty(d => d.IsDeleted, true)
                               .SetProperty(d => d.DeletedAt, DateTime.UtcNow)
                               .SetProperty(d => d.UpdatedAt, DateTime.UtcNow),
                cancellationToken);
    }

    public async Task<IEnumerable<DocumentChunk>> GetDocumentChunksAsync(
        Guid documentId,
        CancellationToken cancellationToken = default)
    {
        return await _context.Set<DocumentChunk>()
            .Where(c => c.DocumentId == documentId)
            .OrderBy(c => c.ChunkIndex)
            .ToListAsync(cancellationToken);
    }
}