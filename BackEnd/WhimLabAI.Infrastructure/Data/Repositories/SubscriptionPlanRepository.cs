using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class SubscriptionPlanRepository : Repository<SubscriptionPlan>, ISubscriptionPlanRepository
{
    private readonly ILogger<SubscriptionPlanRepository> _logger;

    public SubscriptionPlanRepository(WhimLabAIDbContext context, ILogger<SubscriptionPlanRepository> logger) 
        : base(context)
    {
        _logger = logger;
    }

    public async Task<SubscriptionPlan?> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        // SubscriptionPlan doesn't have Code property, use Name instead
        return await _dbSet.FirstOrDefaultAsync(p => p.Name == code && p.IsActive, cancellationToken);
    }

    public async Task<SubscriptionPlan?> GetDefaultPlanAsync(CancellationToken cancellationToken = default)
    {
        // Default plan is Free tier
        return await _dbSet.FirstOrDefaultAsync(p => p.Tier == SubscriptionTier.Free && p.IsActive, cancellationToken);
    }

    public async Task<SubscriptionPlan?> GetPlanByTierAsync(SubscriptionTier tier, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(p => p.Tier == tier && p.IsActive, cancellationToken);
    }

    public async Task<IReadOnlyList<SubscriptionPlan>> GetActivePlansAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.IsActive)
            .OrderBy(p => p.Price.Amount)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<SubscriptionPlan>> GetPlansByFeatureAsync(string feature, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.IsActive && p.Features.Contains(feature))
            .OrderBy(p => p.Price.Amount)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> IsPlanUpgradeAsync(Guid currentPlanId, Guid targetPlanId, CancellationToken cancellationToken = default)
    {
        var currentPlan = await _dbSet.FindAsync([currentPlanId], cancellationToken);
        var targetPlan = await _dbSet.FindAsync([targetPlanId], cancellationToken);

        if (currentPlan == null || targetPlan == null)
        {
            return false;
        }

        return targetPlan.Tier > currentPlan.Tier;
    }

    public async Task<decimal> CalculateProratedAmountAsync(Guid currentPlanId, Guid targetPlanId, int daysRemaining, CancellationToken cancellationToken = default)
    {
        var currentPlan = await _dbSet.FindAsync([currentPlanId], cancellationToken);
        var targetPlan = await _dbSet.FindAsync([targetPlanId], cancellationToken);

        if (currentPlan == null || targetPlan == null)
        {
            return 0;
        }

        var dailyCurrentCost = currentPlan.Price.Amount / 30m;
        var dailyTargetCost = targetPlan.Price.Amount / 30m;
        var proratedAmount = (dailyTargetCost - dailyCurrentCost) * daysRemaining;

        return Math.Max(0, proratedAmount);
    }

    public async Task<object> GetPlanComparisonAsync(Guid planId1, Guid planId2, CancellationToken cancellationToken = default)
    {
        var plan1 = await _dbSet.FindAsync([planId1], cancellationToken);
        var plan2 = await _dbSet.FindAsync([planId2], cancellationToken);

        if (plan1 == null || plan2 == null)
        {
            return new { Error = "One or both plans not found" };
        }

        return new
        {
            Plan1 = new
            {
                plan1.Name,
                // Code property doesn't exist
                plan1.Tier,
                Price = plan1.Price.Amount,
                plan1.MonthlyTokens,
                plan1.Features,
                plan1.MaxAgents,
                plan1.AllowCustomAgent
            },
            Plan2 = new
            {
                plan2.Name,
                // Code property doesn't exist
                plan2.Tier,
                Price = plan2.Price.Amount,
                plan2.MonthlyTokens,
                plan2.Features,
                plan2.MaxAgents,
                plan2.AllowCustomAgent
            },
            Differences = new
            {
                PriceDiff = plan2.Price.Amount - plan1.Price.Amount,
                TokensDiff = plan2.MonthlyTokens - plan1.MonthlyTokens,
                AgentsDiff = (plan2.MaxAgents == -1 ? int.MaxValue : plan2.MaxAgents) - 
                           (plan1.MaxAgents == -1 ? int.MaxValue : plan1.MaxAgents),
                CustomAgentUpgrade = !plan1.AllowCustomAgent && plan2.AllowCustomAgent
            }
        };
    }

    public async Task<bool> UpdatePlanPricingAsync(Guid planId, decimal newPrice, CancellationToken cancellationToken = default)
    {
        var plan = await _dbSet.FindAsync([planId], cancellationToken);
        if (plan == null)
        {
            return false;
        }

        plan.Update(
            plan.Name,
            plan.Description,
            Domain.ValueObjects.Money.Create(newPrice, plan.Price.Currency),
            plan.TokenQuota);
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Updated pricing for plan {PlanId} to {NewPrice}", planId, newPrice);
        return true;
    }

    public async Task<bool> UpdatePlanFeaturesAsync(Guid planId, List<string> features, CancellationToken cancellationToken = default)
    {
        var plan = await _dbSet.FindAsync([planId], cancellationToken);
        if (plan == null)
        {
            return false;
        }

        plan.SetFeatures(features);
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Updated features for plan {PlanId}", planId);
        return true;
    }

    public async Task<bool> DeactivatePlanAsync(Guid planId, CancellationToken cancellationToken = default)
    {
        var plan = await _dbSet.FindAsync([planId], cancellationToken);
        if (plan == null)
        {
            return false;
        }

        // Check if any active subscriptions are using this plan
        var hasActiveSubscriptions = await _context.Subscriptions
            .AnyAsync(s => s.PlanId == planId && s.Status == SubscriptionStatus.Active, cancellationToken);

        if (hasActiveSubscriptions)
        {
            _logger.LogWarning("Cannot deactivate plan {PlanId} - has active subscriptions", planId);
            return false;
        }

        plan.Deactivate();
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Deactivated plan {PlanId}", planId);
        return true;
    }
}