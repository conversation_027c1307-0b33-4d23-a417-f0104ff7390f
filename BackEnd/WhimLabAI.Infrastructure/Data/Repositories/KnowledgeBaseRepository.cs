using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.KnowledgeBase;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 知识库仓储实现
/// </summary>
public class KnowledgeBaseRepository : Repository<KnowledgeBase>, IKnowledgeBaseRepository
{
    public KnowledgeBaseRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<KnowledgeBase>> GetByOwnerAsync(
        Guid ownerId,
        OwnerType ownerType,
        bool includeDeleted = false,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Set<KnowledgeBase>()
            .Where(kb => kb.OwnerId == ownerId && kb.OwnerType == ownerType);

        if (!includeDeleted)
        {
            query = query.Where(kb => !kb.IsDeleted);
        }

        return await query
            .OrderByDescending(kb => kb.UpdatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<KnowledgeBase?> GetByNameAndOwnerAsync(
        string name,
        Guid ownerId,
        OwnerType ownerType,
        CancellationToken cancellationToken = default)
    {
        return await _context.Set<KnowledgeBase>()
            .FirstOrDefaultAsync(kb => 
                kb.Name == name && 
                kb.OwnerId == ownerId && 
                kb.OwnerType == ownerType && 
                !kb.IsDeleted,
                cancellationToken);
    }

    public async Task<IEnumerable<KnowledgeBase>> GetPublicKnowledgeBasesAsync(
        int pageIndex,
        int pageSize,
        CancellationToken cancellationToken = default)
    {
        return await _context.Set<KnowledgeBase>()
            .Where(kb => kb.IsPublic && !kb.IsDeleted && kb.Status == KnowledgeBaseStatus.Active)
            .OrderByDescending(kb => kb.TotalVectors)
            .ThenByDescending(kb => kb.UpdatedAt)
            .Skip(pageIndex * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<KnowledgeBase> Items, int TotalCount)> SearchAsync(
        string? keyword,
        Guid? ownerId,
        OwnerType? ownerType,
        VectorDatabaseType? vectorDbType,
        KnowledgeBaseStatus? status,
        bool? isPublic,
        int pageIndex,
        int pageSize,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Set<KnowledgeBase>()
            .Where(kb => !kb.IsDeleted);

        if (!string.IsNullOrWhiteSpace(keyword))
        {
            query = query.Where(kb => 
                EF.Functions.ILike(kb.Name, $"%{keyword}%") ||
                (kb.Description != null && EF.Functions.ILike(kb.Description, $"%{keyword}%")));
        }

        if (ownerId.HasValue)
        {
            query = query.Where(kb => kb.OwnerId == ownerId.Value);
        }

        if (ownerType.HasValue)
        {
            query = query.Where(kb => kb.OwnerType == ownerType.Value);
        }

        if (vectorDbType.HasValue)
        {
            query = query.Where(kb => kb.VectorDbType == vectorDbType.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(kb => kb.Status == status.Value);
        }

        if (isPublic.HasValue)
        {
            query = query.Where(kb => kb.IsPublic == isPublic.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var items = await query
            .OrderByDescending(kb => kb.UpdatedAt)
            .Skip(pageIndex * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<Dictionary<string, object>> GetStatisticsAsync(
        Guid knowledgeBaseId,
        CancellationToken cancellationToken = default)
    {
        var kb = await _context.Set<KnowledgeBase>()
            .FirstOrDefaultAsync(k => k.Id == knowledgeBaseId, cancellationToken);

        if (kb == null)
        {
            return new Dictionary<string, object>();
        }

        var documentCount = await _context.Set<Document>()
            .CountAsync(d => d.KnowledgeBaseId == knowledgeBaseId && !d.IsDeleted, cancellationToken);

        var chunkCount = await _context.Set<DocumentChunk>()
            .CountAsync(c => c.KnowledgeBaseId == knowledgeBaseId, cancellationToken);

        var embeddedChunkCount = await _context.Set<DocumentChunk>()
            .CountAsync(c => c.KnowledgeBaseId == knowledgeBaseId && c.IsEmbedded, cancellationToken);

        var totalTokens = await _context.Set<DocumentChunk>()
            .Where(c => c.KnowledgeBaseId == knowledgeBaseId)
            .SumAsync(c => c.TokenCount, cancellationToken);

        var lastProcessedAt = await _context.Set<Document>()
            .Where(d => d.KnowledgeBaseId == knowledgeBaseId && d.ProcessingCompletedAt != null)
            .OrderByDescending(d => d.ProcessingCompletedAt)
            .Select(d => d.ProcessingCompletedAt)
            .FirstOrDefaultAsync(cancellationToken);

        return new Dictionary<string, object>
        {
            ["DocumentCount"] = documentCount,
            ["ChunkCount"] = chunkCount,
            ["EmbeddedChunkCount"] = embeddedChunkCount,
            ["TotalTokens"] = totalTokens,
            ["StorageSize"] = kb.StorageSize,
            ["LastProcessedAt"] = lastProcessedAt ?? DateTime.MinValue
        };
    }

    public async Task<int> UpdateStatusBatchAsync(
        IEnumerable<Guid> knowledgeBaseIds,
        KnowledgeBaseStatus status,
        CancellationToken cancellationToken = default)
    {
        var kbIds = knowledgeBaseIds.ToList();
        if (!kbIds.Any())
            return 0;

        return await _context.Set<KnowledgeBase>()
            .Where(kb => kbIds.Contains(kb.Id))
            .ExecuteUpdateAsync(
                setter => setter.SetProperty(kb => kb.Status, status)
                               .SetProperty(kb => kb.UpdatedAt, DateTime.UtcNow),
                cancellationToken);
    }
}