using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Audit;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Specifications;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 审计日志仓储实现
/// 仅负责数据访问，不包含业务逻辑
/// </summary>
public class AuditLogRepository : Repository<AuditLog>, IAuditLogRepository
{
    public AuditLogRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<List<UserActivitySummary>> GetUserActivitySummaryAsync(
        DateTime startDate,
        DateTime endDate,
        int topCount,
        CancellationToken cancellationToken = default)
    {
        var query = from log in _context.Set<AuditLog>()
                    where log.CreatedAt >= startDate && log.CreatedAt <= endDate
                    group log by new { log.UserId, log.UserName } into g
                    orderby g.Count() descending
                    select new UserActivitySummary
                    {
                        UserId = g.Key.UserId.ToString() ?? string.Empty,
                        UserName = g.Key.UserName,
                        ActionCount = g.Count(),
                        LastActivityTime = g.Max(x => x.CreatedAt)
                    };

        return await query.Take(topCount).ToListAsync(cancellationToken);
    }

    public async Task<AuditLogStats> GetStatsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var logs = await _context.Set<AuditLog>()
            .Where(x => x.CreatedAt >= startDate && x.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);

        var stats = new AuditLogStats
        {
            TotalActions = logs.Count,
            UniqueUsers = logs.Select(x => x.UserId).Distinct().Count(),
            TopActions = logs.GroupBy(x => x.Action)
                .OrderByDescending(g => g.Count())
                .Take(10)
                .ToDictionary(g => g.Key, g => g.Count()),
            DailyActivityCounts = logs.GroupBy(x => x.CreatedAt.Date)
                .ToDictionary(g => g.Key, g => g.Count())
        };

        return stats;
    }

    public async Task<int> DeleteOldLogsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default)
    {
        var logsToDelete = await _context.Set<AuditLog>()
            .Where(x => x.CreatedAt < cutoffDate)
            .ToListAsync(cancellationToken);

        _context.Set<AuditLog>().RemoveRange(logsToDelete);
        return logsToDelete.Count;
    }

    public async Task<List<AuditLog>> GetListAsync(
        Specification<AuditLog> specification,
        int pageIndex,
        int pageSize,
        string orderBy,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Set<AuditLog>().AsQueryable();

        // Apply specification
        query = query.Where(specification.ToExpression());

        // Apply ordering
        query = ApplyOrdering(query, orderBy);

        // Apply pagination
        query = query.Skip((pageIndex - 1) * pageSize).Take(pageSize);

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<int> CountAsync(Specification<AuditLog> specification, CancellationToken cancellationToken = default)
    {
        return await _context.Set<AuditLog>()
            .CountAsync(specification.ToExpression(), cancellationToken);
    }

    private IQueryable<AuditLog> ApplyOrdering(IQueryable<AuditLog> query, string orderBy)
    {
        return orderBy?.ToLower() switch
        {
            "createdat" => query.OrderBy(x => x.CreatedAt),
            "createdat desc" => query.OrderByDescending(x => x.CreatedAt),
            "action" => query.OrderBy(x => x.Action),
            "action desc" => query.OrderByDescending(x => x.Action),
            "username" => query.OrderBy(x => x.UserName),
            "username desc" => query.OrderByDescending(x => x.UserName),
            _ => query.OrderByDescending(x => x.CreatedAt)
        };
    }
    
    public async Task<IEnumerable<AuditLog>> GetOldNonSensitiveLogsAsync(int daysOld, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
        return await _dbSet
            .Where(log => log.CreatedAt < cutoffDate && !log.IsSensitive)
            .ToListAsync(cancellationToken);
    }
    
    public async Task BulkInsertAsync(IEnumerable<AuditLog> auditLogs, CancellationToken cancellationToken = default)
    {
        // 使用 AddRange 进行批量插入，EF Core 会优化为批量操作
        await _dbSet.AddRangeAsync(auditLogs, cancellationToken);
        
        // 注意：SaveChanges 应该由 UnitOfWork 或调用方负责
        // 这里只负责将实体添加到上下文中
    }
    
    public async Task<List<AuditLog>> GetHighRiskOperationsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(log => log.CreatedAt >= startDate && 
                         log.CreatedAt <= endDate &&
                         (log.RiskLevel == "High" || log.RiskLevel == "Critical"))
            .OrderByDescending(log => log.CreatedAt)
            .ToListAsync(cancellationToken);
    }
    
    public async Task<Dictionary<string, int>> GetFailureStatsByModuleAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var failureLogs = await _dbSet
            .Where(log => log.CreatedAt >= startDate && 
                         log.CreatedAt <= endDate &&
                         !log.IsSuccess)
            .GroupBy(log => log.Module)
            .Select(g => new { Module = g.Key, Count = g.Count() })
            .ToListAsync(cancellationToken);
            
        return failureLogs.ToDictionary(x => x.Module, x => x.Count);
    }
    
    public async Task<Dictionary<int, int>> GetUserActivityTimeDistributionAsync(
        Guid userId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var activities = await _dbSet
            .Where(log => log.UserId == userId &&
                         log.CreatedAt >= startDate &&
                         log.CreatedAt <= endDate)
            .GroupBy(log => log.CreatedAt.Hour)
            .Select(g => new { Hour = g.Key, Count = g.Count() })
            .ToListAsync(cancellationToken);
            
        // 初始化所有24小时的计数
        var distribution = Enumerable.Range(0, 24).ToDictionary(h => h, h => 0);
        
        // 填充实际数据
        foreach (var activity in activities)
        {
            distribution[activity.Hour] = activity.Count;
        }
        
        return distribution;
    }
}