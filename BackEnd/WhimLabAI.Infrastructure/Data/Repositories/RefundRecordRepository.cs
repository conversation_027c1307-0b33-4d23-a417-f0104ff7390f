using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 退款记录仓储实现
/// </summary>
public class RefundRecordRepository : Repository<RefundRecord>, IRefundRecordRepository
{
    public RefundRecordRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<RefundRecord?> GetByRefundNoAsync(string refundNo, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(x => x.RefundNo == refundNo, cancellationToken);
    }

    public async Task<IEnumerable<RefundRecord>> GetOrderRefundsAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(x => x.OrderId == orderId).ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RefundRecord>> GetPendingRefundsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(x => x.Status == RefundStatus.Pending).ToListAsync(cancellationToken);
    }

    public async Task<bool> UpdateRefundStatusAsync(Guid refundId, RefundStatus newStatus, string? transactionId = null, CancellationToken cancellationToken = default)
    {
        var refund = await GetByIdAsync(refundId, cancellationToken);
        if (refund == null) return false;

        // Update status based on the new status
        switch (newStatus)
        {
            case RefundStatus.Processing:
                refund.StartProcessing();
                break;
            case RefundStatus.Completed:
                refund.MarkAsCompleted(transactionId);
                break;
            case RefundStatus.Failed:
                refund.MarkAsFailed("Processing failed");
                break;
            case RefundStatus.Cancelled:
                refund.Cancel();
                break;
        }
        
        Update(refund);
        await _context.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<decimal> GetTotalRefundedAmountAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.OrderId == orderId && x.Status == RefundStatus.Completed)
            .Select(x => x.RefundAmount.Amount)
            .SumAsync(cancellationToken);
    }

    public async Task<bool> HasPendingRefundAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(x => x.OrderId == orderId && x.Status == RefundStatus.Pending, cancellationToken);
    }

    public async Task<RefundStatistics> GetRefundStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var refunds = await _dbSet
            .Where(x => x.CreatedAt >= startDate && x.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);

        return new RefundStatistics
        {
            TotalRefunds = refunds.Count,
            TotalAmount = refunds.Sum(x => x.RefundAmount.Amount),
            SuccessfulRefunds = refunds.Count(x => x.Status == RefundStatus.Completed),
            FailedRefunds = refunds.Count(x => x.Status == RefundStatus.Failed),
            PendingRefunds = refunds.Count(x => x.Status == RefundStatus.Pending),
            RefundsByStatus = refunds.GroupBy(x => x.Status).ToDictionary(g => g.Key, g => g.Count()),
            RefundsByPaymentMethod = new Dictionary<PaymentMethod, decimal>(), // PaymentMethod is not on RefundRecord
            RefundsByReason = refunds.GroupBy(x => x.Reason ?? "Unknown").ToDictionary(g => g.Key, g => g.Sum(x => x.RefundAmount.Amount))
        };
    }

    public async Task<IEnumerable<RefundRecord>> GetUserRefundsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(x => x.Order)
            .Where(x => x.Order.CustomerUserId == userId)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> CanRefundOrderAsync(Guid orderId, decimal amount, CancellationToken cancellationToken = default)
    {
        var order = await _context.Orders.FindAsync(new object[] { orderId }, cancellationToken);
        if (order == null) return false;

        var totalRefunded = await GetTotalRefundedAmountAsync(orderId, cancellationToken);
        return (totalRefunded + amount) <= order.FinalAmount.Amount;
    }

    public async Task<IEnumerable<RefundRecord>> GetRefundsByPaymentMethodAsync(PaymentMethod method, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        // Since RefundRecord doesn't have PaymentMethod, we need to join with Order
        return await _dbSet
            .Include(x => x.Order)
            .Where(x => x.Order.PaymentMethod == method && x.CreatedAt >= startDate && x.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);
    }

    public async Task UpdateAsync(RefundRecord refund, CancellationToken cancellationToken = default)
    {
        Update(refund);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var refund = await GetByIdAsync(id, cancellationToken);
        if (refund != null)
        {
            Remove(refund);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}