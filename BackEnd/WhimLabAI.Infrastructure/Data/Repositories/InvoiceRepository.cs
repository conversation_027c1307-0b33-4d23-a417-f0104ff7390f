using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Finance;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 发票仓储实现
/// </summary>
public class InvoiceRepository : Repository<Invoice>, IInvoiceRepository
{
    public InvoiceRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<Invoice?> GetByInvoiceNumberAsync(string invoiceNumber, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Items)
            .FirstOrDefaultAsync(i => i.InvoiceNumber == invoiceNumber, cancellationToken);
    }

    public async Task<List<Invoice>> GetByCustomerUserIdAsync(Guid customerUserId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Items)
            .Where(i => i.CustomerUserId == customerUserId)
            .OrderByDescending(i => i.IssueDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Invoice>> GetBySubscriptionIdAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Items)
            .Where(i => i.SubscriptionId == subscriptionId)
            .OrderByDescending(i => i.IssueDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<Invoice?> GetByOrderIdAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Items)
            .FirstOrDefaultAsync(i => i.OrderId == orderId, cancellationToken);
    }

    public async Task<List<Invoice>> GetOverdueInvoicesAsync(DateTime currentDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Items)
            .Where(i => i.Status == InvoiceStatus.Issued && i.DueDate < currentDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<string> GenerateInvoiceNumberAsync(CancellationToken cancellationToken = default)
    {
        var year = DateTime.UtcNow.Year;
        var month = DateTime.UtcNow.Month;
        var prefix = $"INV{year:D4}{month:D2}";
        
        // Get the latest invoice number for this month
        var latestInvoice = await _dbSet
            .Where(i => i.InvoiceNumber.StartsWith(prefix))
            .OrderByDescending(i => i.InvoiceNumber)
            .FirstOrDefaultAsync(cancellationToken);
        
        int sequence = 1;
        if (latestInvoice != null)
        {
            // Extract the sequence number from the latest invoice
            var lastSequence = latestInvoice.InvoiceNumber.Substring(prefix.Length);
            if (int.TryParse(lastSequence, out var seq))
            {
                sequence = seq + 1;
            }
        }
        
        return $"{prefix}{sequence:D6}";
    }

    public async Task<List<Invoice>> GetByCustomerUserIdAndDateRangeAsync(
        Guid customerUserId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Items)
            .Where(i => i.CustomerUserId == customerUserId 
                     && i.IssueDate >= startDate 
                     && i.IssueDate <= endDate)
            .OrderByDescending(i => i.IssueDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> InvoiceNumberExistsAsync(string invoiceNumber, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(i => i.InvoiceNumber == invoiceNumber, cancellationToken);
    }
}