using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class AgentRepository : Repository<Agent>, IAgentRepository
{
    public AgentRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Agent>> GetPublishedAgentsAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Category)
            .Include(a => a.Tags)
            .Include(a => a.Ratings)
            .Where(a => a.Status == AgentStatus.Published)
            .OrderByDescending(a => a.PublishedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .AsNoTracking() // Read-only query
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Agent>> GetAgentsByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        if (!Enum.TryParse<AgentStatus>(status, out var agentStatus))
        {
            return Enumerable.Empty<Agent>();
        }

        return await _dbSet
            .Include(a => a.Category)
            .Where(a => a.Status == agentStatus)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Agent>> GetAgentsByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Category)
            .Include(a => a.Tags)
            .Where(a => a.Category != null && a.Category.Name == category)
            .OrderByDescending(a => a.PublishedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Agent>> GetAgentsByCreatorAsync(Guid creatorId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Category)
            .Include(a => a.Tags)
            .Include(a => a.Versions)
            .Where(a => a.CreatorId == creatorId)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<AgentVersion>> GetAgentVersionsAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        var agent = await _dbSet
            .Include(a => a.Versions)
            .FirstOrDefaultAsync(a => a.Id == agentId, cancellationToken);

        return agent?.Versions ?? Enumerable.Empty<AgentVersion>();
    }

    public async Task<Agent?> GetAgentByUniqueKeyAsync(string uniqueKey, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Category)
            .Include(a => a.Tags)
            .Include(a => a.Versions)
            .Include(a => a.Ratings)
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .FirstOrDefaultAsync(a => a.UniqueKey == uniqueKey, cancellationToken);
    }

    public async Task<bool> IsUniqueKeyAvailableAsync(string uniqueKey, Guid? excludeAgentId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(a => a.UniqueKey == uniqueKey);
        
        if (excludeAgentId.HasValue)
        {
            query = query.Where(a => a.Id != excludeAgentId.Value);
        }

        return !await query.AnyAsync(cancellationToken);
    }

    // Additional methods for extended functionality
    public async Task<Agent?> GetWithCategoryAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Category)
            .FirstOrDefaultAsync(a => a.Id == agentId, cancellationToken);
    }

    public async Task<Agent?> GetWithVersionsAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Versions)
            .FirstOrDefaultAsync(a => a.Id == agentId, cancellationToken);
    }

    public async Task<Agent?> GetWithFullDetailsAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Category)
            .Include(a => a.Tags)
            .Include(a => a.Versions)
            .Include(a => a.Ratings)
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .FirstOrDefaultAsync(a => a.Id == agentId, cancellationToken);
    }

    public async Task<AgentVersion?> GetPublishedVersionAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        var agent = await _dbSet
            .Include(a => a.Versions)
            .FirstOrDefaultAsync(a => a.Id == agentId, cancellationToken);

        return agent?.Versions.FirstOrDefault(v => v.Status == AgentStatus.Published);
    }

    public override async Task<Agent?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(a => a.Versions)
            .Include(a => a.Category)
            .Include(a => a.Tags)
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    public async Task<IReadOnlyList<Agent>> SearchAgentsAsync(string keyword, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Include(a => a.Category)
            .Include(a => a.Tags)
            .Where(a => a.Status == AgentStatus.Published);

        if (!string.IsNullOrWhiteSpace(keyword))
        {
            keyword = keyword.ToLower();
            query = query.Where(a => 
                a.Name.ToLower().Contains(keyword) ||
                a.Description.ToLower().Contains(keyword) ||
                a.Tags.Any(t => t.Name.ToLower().Contains(keyword)));
        }

        return await query
            .OrderByDescending(a => a.UsageCount)
            .ThenByDescending(a => a.PublishedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .AsNoTracking() // Read-only query
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetTotalCountAsync(AgentStatus? status = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();
        
        if (status.HasValue)
        {
            query = query.Where(a => a.Status == status.Value);
        }

        return await query.CountAsync(cancellationToken);
    }
    
    /// <summary>
    /// 根据分类统计Agent数量
    /// </summary>
    public async Task<int> CountByCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.CategoryId == categoryId)
            .CountAsync(cancellationToken);
    }
    
    /// <summary>
    /// 获取热门标签
    /// </summary>
    public async Task<List<(string Tag, int Count)>> GetPopularTagsAsync(int count, CancellationToken cancellationToken = default)
    {
        var tags = await _dbSet
            .Where(a => a.Status == AgentStatus.Published)
            .SelectMany(a => a.Tags)
            .GroupBy(t => t.Name)
            .Select(g => new { Tag = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .Take(count)
            .ToListAsync(cancellationToken);
            
        return tags.Select(x => (x.Tag, x.Count)).ToList();
    }
    
    /// <summary>
    /// 根据评价ID获取Agent
    /// </summary>
    public async Task<Agent?> GetAgentByRatingIdAsync(Guid ratingId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(a => a.Ratings.Any(r => r.Id == ratingId))
            .FirstOrDefaultAsync(cancellationToken);
    }
}