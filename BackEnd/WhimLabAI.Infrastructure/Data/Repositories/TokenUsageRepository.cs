using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// Token使用记录仓储实现
/// </summary>
public class TokenUsageRepository : Repository<TokenUsage>, ITokenUsageRepository
{
    public TokenUsageRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<TokenUsage> RecordUsageAsync(Guid subscriptionId, Guid? conversationId, int tokensUsed, string? model, UsageType usageType, CancellationToken cancellationToken = default)
    {
        var usage = new TokenUsage(subscriptionId, tokensUsed, null, conversationId, null, model, usageType);
        await AddAsync(usage, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
        return usage;
    }

    public async Task<IEnumerable<TokenUsage>> GetUsageByDateRangeAsync(Guid subscriptionId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.SubscriptionId == subscriptionId && x.UsedAt >= startDate && x.UsedAt <= endDate)
            .OrderByDescending(x => x.UsedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<DateTime, int>> GetDailyUsageAsync(Guid subscriptionId, int days, CancellationToken cancellationToken = default)
    {
        var startDate = DateTime.UtcNow.Date.AddDays(-days);
        
        var dailyUsage = await _dbSet
            .Where(x => x.SubscriptionId == subscriptionId && x.UsedAt >= startDate)
            .GroupBy(x => x.UsedAt.Date)
            .Select(g => new { Date = g.Key, Tokens = g.Sum(x => x.Tokens) })
            .ToListAsync(cancellationToken);

        return dailyUsage.ToDictionary(x => x.Date, x => x.Tokens);
    }

    public async Task<Dictionary<string, int>> GetUsageByModelAsync(Guid subscriptionId, DateTime? startDate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(x => x.SubscriptionId == subscriptionId);
        
        if (startDate.HasValue)
        {
            query = query.Where(x => x.UsedAt >= startDate.Value);
        }

        var modelUsage = await query
            .GroupBy(x => x.Model ?? "Unknown")
            .Select(g => new { Model = g.Key, Tokens = g.Sum(x => x.Tokens) })
            .ToListAsync(cancellationToken);

        return modelUsage.ToDictionary(x => x.Model, x => x.Tokens);
    }

    public async Task<int> GetTotalUsageAsync(Guid subscriptionId, DateTime? startDate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(x => x.SubscriptionId == subscriptionId);
        
        if (startDate.HasValue)
        {
            query = query.Where(x => x.UsedAt >= startDate.Value);
        }

        return await query.SumAsync(x => x.Tokens, cancellationToken);
    }

    public async Task<UsageStatistics> GetUsageStatisticsAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        var usages = await _dbSet.Where(x => x.SubscriptionId == subscriptionId).ToListAsync(cancellationToken);
        
        return new UsageStatistics
        {
            TotalTokens = usages.Sum(x => x.Tokens),
            TotalRequests = usages.Count,
            AverageTokensPerRequest = usages.Any() ? (double)usages.Sum(x => x.Tokens) / usages.Count : 0,
            TokensByModel = usages.GroupBy(x => x.Model ?? "Unknown").ToDictionary(g => g.Key, g => g.Sum(x => x.Tokens)),
            TokensByType = usages.GroupBy(x => x.Type).ToDictionary(g => g.Key, g => g.Sum(x => x.Tokens)),
            FirstUsageDate = usages.MinBy(x => x.UsedAt)?.UsedAt,
            LastUsageDate = usages.MaxBy(x => x.UsedAt)?.UsedAt
        };
    }

    public async Task<bool> DeleteOldRecordsAsync(int daysToKeep, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
        var recordsToDelete = await _dbSet.Where(x => x.UsedAt < cutoffDate).ToListAsync(cancellationToken);
        
        if (recordsToDelete.Any())
        {
            _dbSet.RemoveRange(recordsToDelete);
            await _context.SaveChangesAsync(cancellationToken);
            return true;
        }
        
        return false;
    }

    public async Task<IEnumerable<TopUserUsage>> GetTopUsersAsync(DateTime startDate, DateTime endDate, int topCount = 10, CancellationToken cancellationToken = default)
    {
        var topUsers = await _dbSet
            .Where(x => x.UsedAt >= startDate && x.UsedAt <= endDate)
            .Include(x => x.Subscription)
            .GroupBy(x => x.Subscription.CustomerUserId)
            .Select(g => new TopUserUsage
            {
                UserId = g.Key,
                UserName = null, // User name would need to be retrieved separately
                TotalTokens = g.Sum(x => x.Tokens),
                RequestCount = g.Count()
            })
            .OrderByDescending(x => x.TotalTokens)
            .Take(topCount)
            .ToListAsync(cancellationToken);

        return topUsers;
    }

    public async Task UpdateAsync(TokenUsage usage, CancellationToken cancellationToken = default)
    {
        Update(usage);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var usage = await GetByIdAsync(id, cancellationToken);
        if (usage != null)
        {
            Remove(usage);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}