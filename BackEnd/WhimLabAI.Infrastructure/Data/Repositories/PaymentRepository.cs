using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class PaymentRepository : Repository<PaymentTransaction>, IPaymentRepository
{
    private readonly ILogger<PaymentRepository> _logger;

    public PaymentRepository(WhimLabAIDbContext context, ILogger<PaymentRepository> logger) 
        : base(context)
    {
        _logger = logger;
    }

    public async Task<PaymentTransaction?> GetByTransactionIdAsync(string transactionId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(p => p.TransactionId == transactionId, cancellationToken);
    }

    public async Task<PaymentTransaction?> GetByPaymentNoAsync(string paymentNo, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(p => p.PaymentNo == paymentNo, cancellationToken);
    }

    public async Task<PaymentTransaction?> GetByOrderIdAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.OrderId == orderId)
            .OrderByDescending(p => p.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<IEnumerable<PaymentTransaction>> GetOrderPaymentsAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.OrderId == orderId)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<PaymentTransaction>> GetPendingPaymentsAsync(TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow.Subtract(timeout);
        
        return await _dbSet
            .Where(p => p.Status == TransactionStatus.Pending && p.CreatedAt < cutoffTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> UpdatePaymentStatusAsync(Guid paymentId, TransactionStatus newStatus, string? transactionId = null, CancellationToken cancellationToken = default)
    {
        var payment = await _dbSet.FindAsync([paymentId], cancellationToken);
        if (payment == null)
        {
            return false;
        }

        var oldStatus = payment.Status;
        
        if (!string.IsNullOrEmpty(transactionId))
        {
            payment.SetTransactionId(transactionId);
        }
        
        // Use domain methods to update status
        switch (newStatus)
        {
            case TransactionStatus.Success:
                payment.MarkAsSuccess();
                break;
            case TransactionStatus.Failed:
                payment.MarkAsFailed();
                break;
            case TransactionStatus.Cancelled:
                payment.MarkAsCancelled();
                break;
        }

        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Payment {PaymentId} status changed from {OldStatus} to {NewStatus}", 
            paymentId, oldStatus, newStatus);
        
        return true;
    }

    public async Task<object> GetPaymentStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var payments = await _dbSet
            .Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);

        var successfulPayments = payments.Where(p => p.Status == TransactionStatus.Success).ToList();
        
        return new
        {
            TotalPayments = payments.Count,
            SuccessfulPayments = successfulPayments.Count,
            FailedPayments = payments.Count(p => p.Status == TransactionStatus.Failed),
            PendingPayments = payments.Count(p => p.Status == TransactionStatus.Pending),
            TotalAmount = successfulPayments.Sum(p => p.Amount.Amount),
            PaymentsByMethod = successfulPayments
                .GroupBy(p => p.PaymentMethod)
                .ToDictionary(g => g.Key.ToString(), g => new
                {
                    Count = g.Count(),
                    Amount = g.Sum(p => p.Amount.Amount)
                }),
            DailyPayments = successfulPayments
                .GroupBy(p => p.CompletedAt?.Date ?? p.CreatedAt.Date)
                .OrderBy(g => g.Key)
                .ToDictionary(g => g.Key, g => new
                {
                    Count = g.Count(),
                    Amount = g.Sum(p => p.Amount.Amount)
                }),
            SuccessRate = payments.Any() ? (double)successfulPayments.Count / payments.Count * 100 : 0
        };
    }

    public async Task<bool> HasDuplicatePaymentAsync(Guid orderId, string paymentMethod, decimal amount, TimeSpan window, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow.Subtract(window);
        
        return await _dbSet
            .AnyAsync(p => p.OrderId == orderId &&
                          p.PaymentMethod.ToString() == paymentMethod &&
                          p.Amount.Amount == amount &&
                          p.CreatedAt >= startTime &&
                          p.Status == TransactionStatus.Success,
                      cancellationToken);
    }

    public async Task<decimal> GetUserTotalPaymentsAsync(Guid userId, TransactionStatus? status = null, CancellationToken cancellationToken = default)
    {
        var orders = await _context.Orders
            .Where(o => o.CustomerUserId == userId)
            .Select(o => o.Id)
            .ToListAsync(cancellationToken);

        var query = _dbSet.Where(p => orders.Contains(p.OrderId));
        
        if (status.HasValue)
        {
            query = query.Where(p => p.Status == status.Value);
        }
        
        return await query.SumAsync(p => p.Amount.Amount, cancellationToken);
    }

    public async Task<IEnumerable<PaymentTransaction>> GetPaymentsByDateRangeAsync(DateTime startDate, DateTime endDate, PaymentMethod? method = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate);
            
        if (method.HasValue)
        {
            query = query.Where(p => p.PaymentMethod == method.Value);
        }
        
        return await query
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<PaymentTransaction?> GetLatestPaymentForOrderAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.OrderId == orderId)
            .OrderByDescending(p => p.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<bool> SavePaymentCallbackAsync(Guid paymentId, string callbackData, CancellationToken cancellationToken = default)
    {
        var payment = await _dbSet.FindAsync([paymentId], cancellationToken);
        if (payment == null)
        {
            return false;
        }

        // Store callback data in RawData dictionary
        payment.AddRawData("CallbackData", callbackData);
        payment.AddRawData("CallbackAt", DateTime.UtcNow.ToString("O"));
        
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Saved callback data for payment {PaymentId}", paymentId);
        return true;
    }
    
    public async Task<IEnumerable<PaymentTransaction>> GetExpiredPendingPaymentsAsync(CancellationToken cancellationToken = default)
    {
        // Consider payments pending for more than 30 minutes as expired
        var expiryTime = DateTime.UtcNow.AddMinutes(-30);
        return await _dbSet
            .Where(p => p.Status == TransactionStatus.Pending && p.CreatedAt < expiryTime)
            .ToListAsync(cancellationToken);
    }
}