using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.EventSourcing;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class EventStoreRepository : IEventStoreRepository
{
    private readonly WhimLabAIDbContext _context;

    public EventStoreRepository(WhimLabAIDbContext context)
    {
        _context = context;
    }

    public async Task SaveEventAsync(EventStore @event, CancellationToken cancellationToken = default)
    {
        await _context.EventStores.AddAsync(@event, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task SaveEventsAsync(IEnumerable<EventStore> events, CancellationToken cancellationToken = default)
    {
        await _context.EventStores.AddRangeAsync(events, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<EventStore>> GetEventsAsync(Guid aggregateId, int? fromVersion = null, CancellationToken cancellationToken = default)
    {
        var query = _context.EventStores
            .Where(e => e.AggregateId == aggregateId);

        if (fromVersion.HasValue)
        {
            query = query.Where(e => e.Version > fromVersion.Value);
        }

        return await query
            .OrderBy(e => e.Version)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<EventStore>> GetEventsAsync(Guid aggregateId, DateTime from, DateTime to, CancellationToken cancellationToken = default)
    {
        return await _context.EventStores
            .Where(e => e.AggregateId == aggregateId && 
                       e.OccurredOn >= from && 
                       e.OccurredOn <= to)
            .OrderBy(e => e.Version)
            .ToListAsync(cancellationToken);
    }

    public async Task<Snapshot?> GetLatestSnapshotAsync(Guid aggregateId, CancellationToken cancellationToken = default)
    {
        return await _context.Snapshots
            .Where(s => s.AggregateId == aggregateId)
            .OrderByDescending(s => s.Version)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task SaveSnapshotAsync(Snapshot snapshot, CancellationToken cancellationToken = default)
    {
        await _context.Snapshots.AddAsync(snapshot, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<int> GetLatestVersionAsync(Guid aggregateId, CancellationToken cancellationToken = default)
    {
        var latestEvent = await _context.EventStores
            .Where(e => e.AggregateId == aggregateId)
            .OrderByDescending(e => e.Version)
            .FirstOrDefaultAsync(cancellationToken);

        return latestEvent?.Version ?? 0;
    }
}