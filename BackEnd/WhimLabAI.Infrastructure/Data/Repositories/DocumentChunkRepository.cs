using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.KnowledgeBase;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 文档块仓储实现
/// </summary>
public class DocumentChunkRepository : Repository<DocumentChunk>, IDocumentChunkRepository
{
    public DocumentChunkRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<DocumentChunk>> GetByDocumentIdAsync(
        Guid documentId,
        CancellationToken cancellationToken = default)
    {
        return await _context.Set<DocumentChunk>()
            .Where(c => c.DocumentId == documentId)
            .OrderBy(c => c.ChunkIndex)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DocumentChunk>> GetByKnowledgeBaseIdAsync(
        Guid knowledgeBaseId,
        int skip,
        int take,
        CancellationToken cancellationToken = default)
    {
        return await _context.Set<DocumentChunk>()
            .Where(c => c.KnowledgeBaseId == knowledgeBaseId)
            .OrderBy(c => c.CreatedAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DocumentChunk>> GetUnembeddedChunksAsync(
        Guid knowledgeBaseId,
        int batchSize,
        CancellationToken cancellationToken = default)
    {
        return await _context.Set<DocumentChunk>()
            .Where(c => c.KnowledgeBaseId == knowledgeBaseId && !c.IsEmbedded)
            .OrderBy(c => c.CreatedAt)
            .Take(batchSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<DocumentChunk?> GetByVectorIdAsync(
        string vectorId,
        CancellationToken cancellationToken = default)
    {
        return await _context.Set<DocumentChunk>()
            .FirstOrDefaultAsync(c => c.VectorId == vectorId, cancellationToken);
    }

    public async Task<IEnumerable<DocumentChunk>> GetByVectorIdsAsync(
        IEnumerable<string> vectorIds,
        CancellationToken cancellationToken = default)
    {
        var vectorIdList = vectorIds.ToList();
        if (!vectorIdList.Any())
            return Enumerable.Empty<DocumentChunk>();

        return await _context.Set<DocumentChunk>()
            .Where(c => c.VectorId != null && vectorIdList.Contains(c.VectorId))
            .ToListAsync(cancellationToken);
    }

    public async Task<int> UpdateEmbeddingStatusBatchAsync(
        IEnumerable<(Guid ChunkId, string VectorId)> updates,
        CancellationToken cancellationToken = default)
    {
        var updateList = updates.ToList();
        if (!updateList.Any())
            return 0;

        var updateCount = 0;
        foreach (var (chunkId, vectorId) in updateList)
        {
            var affected = await _context.Set<DocumentChunk>()
                .Where(c => c.Id == chunkId)
                .ExecuteUpdateAsync(
                    setter => setter
                        .SetProperty(c => c.VectorId, vectorId)
                        .SetProperty(c => c.IsEmbedded, true)
                        .SetProperty(c => c.EmbeddedAt, DateTime.UtcNow)
                        .SetProperty(c => c.UpdatedAt, DateTime.UtcNow),
                    cancellationToken);
            
            updateCount += affected;
        }

        return updateCount;
    }

    public async Task<int> DeleteByDocumentIdAsync(
        Guid documentId,
        CancellationToken cancellationToken = default)
    {
        return await _context.Set<DocumentChunk>()
            .Where(c => c.DocumentId == documentId)
            .ExecuteDeleteAsync(cancellationToken);
    }

    public async Task<IEnumerable<DocumentChunk>> FindDuplicatesByHashAsync(
        IEnumerable<string> contentHashes,
        Guid knowledgeBaseId,
        CancellationToken cancellationToken = default)
    {
        var hashList = contentHashes.ToList();
        if (!hashList.Any())
            return Enumerable.Empty<DocumentChunk>();

        return await _context.Set<DocumentChunk>()
            .Where(c => c.KnowledgeBaseId == knowledgeBaseId && hashList.Contains(c.ContentHash))
            .ToListAsync(cancellationToken);
    }
}