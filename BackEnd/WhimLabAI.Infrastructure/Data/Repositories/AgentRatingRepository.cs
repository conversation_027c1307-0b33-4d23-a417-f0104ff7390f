using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// Agent评价仓储实现
/// </summary>
public class AgentRatingRepository : Repository<AgentRating>, IAgentRatingRepository
{
    public AgentRatingRepository(WhimLabAIDbContext context) : base(context)
    {
    }
    
    /// <summary>
    /// 根据Agent和用户获取评价
    /// </summary>
    public async Task<AgentRating?> GetByAgentAndUserAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default)
    {
        var agent = await _context.Set<Agent>()
            .Include(a => a.Ratings)
            .FirstOrDefaultAsync(a => a.Id == agentId, cancellationToken);
            
        return agent?.Ratings.FirstOrDefault(r => r.UserId == userId);
    }
    
    /// <summary>
    /// 获取Agent的评价列表
    /// </summary>
    public async Task<List<AgentRating>> GetByAgentIdAsync(Guid agentId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        var agent = await _context.Set<Agent>()
            .Include(a => a.Ratings)
            .FirstOrDefaultAsync(a => a.Id == agentId, cancellationToken);
            
        if (agent == null)
            return new List<AgentRating>();
            
        return agent.Ratings
            .OrderByDescending(r => r.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }
    
    /// <summary>
    /// 获取Agent的评价数量
    /// </summary>
    public async Task<int> CountByAgentIdAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        var agent = await _context.Set<Agent>()
            .Include(a => a.Ratings)
            .FirstOrDefaultAsync(a => a.Id == agentId, cancellationToken);
            
        return agent?.Ratings.Count ?? 0;
    }
    
    /// <summary>
    /// 获取用户的评价列表
    /// </summary>
    public async Task<List<AgentRating>> GetByUserIdAsync(Guid userId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.UserId == userId)
            .OrderByDescending(x => x.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }
    
    /// <summary>
    /// 计算Agent的平均评分
    /// </summary>
    public async Task<double> CalculateAverageRatingAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        var agent = await _context.Set<Agent>()
            .Include(a => a.Ratings)
            .FirstOrDefaultAsync(a => a.Id == agentId, cancellationToken);
            
        if (agent == null || !agent.Ratings.Any())
            return 0;
            
        return agent.Ratings.Average(r => r.Score);
    }
}