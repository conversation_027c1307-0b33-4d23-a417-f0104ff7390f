using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 评价有用性标记仓储实现
/// </summary>
public class RatingHelpfulnessRepository : Repository<RatingHelpfulness>, IRatingHelpfulnessRepository
{
    public RatingHelpfulnessRepository(WhimLabAIDbContext context) : base(context)
    {
    }
    
    /// <summary>
    /// 根据评价和用户获取标记
    /// </summary>
    public async Task<RatingHelpfulness?> GetByRatingAndUserAsync(Guid ratingId, Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.RatingId == ratingId && x.UserId == userId, cancellationToken);
    }
    
    /// <summary>
    /// 批量获取用户对评价的标记
    /// </summary>
    public async Task<Dictionary<Guid, bool?>> GetUserMarksForRatingsAsync(Guid userId, List<Guid> ratingIds, CancellationToken cancellationToken = default)
    {
        var marks = await _dbSet
            .Where(x => x.UserId == userId && ratingIds.Contains(x.RatingId))
            .Select(x => new { x.RatingId, x.IsHelpful })
            .ToListAsync(cancellationToken);
            
        var result = new Dictionary<Guid, bool?>();
        
        foreach (var ratingId in ratingIds)
        {
            var mark = marks.FirstOrDefault(x => x.RatingId == ratingId);
            result[ratingId] = mark?.IsHelpful;
        }
        
        return result;
    }
}