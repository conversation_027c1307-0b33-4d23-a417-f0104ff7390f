using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class UsageRecordRepository : Repository<UsageRecord>, IUsageRecordRepository
{
    private readonly ILogger<UsageRecordRepository> _logger;

    public UsageRecordRepository(WhimLabAIDbContext context, ILogger<UsageRecordRepository> logger) 
        : base(context)
    {
        _logger = logger;
    }

    public async Task<int> GetUserMonthlyUsageAsync(Guid userId, DateTime month, CancellationToken cancellationToken = default)
    {
        var startOfMonth = new DateTime(month.Year, month.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1);
        
        var subscriptionIds = await _context.Subscriptions
            .Where(s => s.CustomerUserId == userId)
            .Select(s => s.Id)
            .ToListAsync(cancellationToken);
        
        return await _dbSet
            .Where(u => subscriptionIds.Contains(u.SubscriptionId) && 
                       u.UsageTime >= startOfMonth && 
                       u.UsageTime < endOfMonth)
            .SumAsync(u => u.TokensUsed, cancellationToken);
    }

    public async Task<IEnumerable<UsageRecord>> GetUserUsageHistoryAsync(Guid userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var subscriptionIds = await _context.Subscriptions
            .Where(s => s.CustomerUserId == userId)
            .Select(s => s.Id)
            .ToListAsync(cancellationToken);
        
        return await _dbSet
            .Where(u => subscriptionIds.Contains(u.SubscriptionId) && 
                       u.UsageTime >= startDate && 
                       u.UsageTime <= endDate)
            .OrderByDescending(u => u.UsageTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetConversationTokenUsageAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(u => u.ConversationId == conversationId)
            .SumAsync(u => u.TokensUsed, cancellationToken);
    }

    public async Task RecordUsageAsync(Guid subscriptionId, Guid conversationId, int tokens, string model, CancellationToken cancellationToken = default)
    {
        // Get required information for UsageRecord constructor
        var userId = Guid.Empty; // This needs to be passed as parameter
        var agentId = Guid.Empty; // This needs to be passed as parameter
        
        var record = new UsageRecord(
            subscriptionId: subscriptionId,
            userId: userId,
            conversationId: conversationId,
            agentId: agentId,
            modelName: model,
            modelProvider: "OpenAI", // Default provider, should be determined from model
            tokensUsed: tokens,
            costAmount: 0 // Cost calculation should be done here
        );

        await _dbSet.AddAsync(record, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogDebug("Recorded usage: {Tokens} tokens for subscription {SubscriptionId}", tokens, subscriptionId);
    }

    public async Task<IEnumerable<UsageRecord>> GetUsageByDateRangeAsync(Guid subscriptionId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(u => u.SubscriptionId == subscriptionId &&
                       u.UsageTime >= startDate &&
                       u.UsageTime <= endDate)
            .OrderBy(u => u.UsageTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<DateTime, int>> GetDailyUsageAsync(Guid subscriptionId, int days, CancellationToken cancellationToken = default)
    {
        var startDate = DateTime.UtcNow.Date.AddDays(-days + 1);
        
        var dailyUsage = await _dbSet
            .Where(u => u.SubscriptionId == subscriptionId && u.UsageTime >= startDate)
            .GroupBy(u => u.UsageTime.Date)
            .Select(g => new
            {
                Date = g.Key,
                Tokens = g.Sum(u => u.TokensUsed)
            })
            .ToDictionaryAsync(x => x.Date, x => x.Tokens, cancellationToken);

        // Fill in missing dates with 0
        var result = new Dictionary<DateTime, int>();
        for (var date = startDate; date <= DateTime.UtcNow.Date; date = date.AddDays(1))
        {
            result[date] = dailyUsage.TryGetValue(date, out var tokens) ? tokens : 0;
        }

        return result;
    }

    public async Task<Dictionary<string, int>> GetUsageByModelAsync(Guid subscriptionId, DateTime? startDate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(u => u.SubscriptionId == subscriptionId);
        
        if (startDate.HasValue)
        {
            query = query.Where(u => u.UsageTime >= startDate.Value);
        }

        return await query
            .GroupBy(u => u.ModelName)
            .Select(g => new
            {
                Model = g.Key,
                Tokens = g.Sum(u => u.TokensUsed)
            })
            .ToDictionaryAsync(x => x.Model, x => x.Tokens, cancellationToken);
    }

    public async Task<int> GetTotalUsageAsync(Guid subscriptionId, DateTime? startDate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(u => u.SubscriptionId == subscriptionId);
        
        if (startDate.HasValue)
        {
            query = query.Where(u => u.UsageTime >= startDate.Value);
        }

        return await query.SumAsync(u => u.TokensUsed, cancellationToken);
    }

    public async Task<object> GetUsageStatisticsAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
        var sevenDaysAgo = DateTime.UtcNow.AddDays(-7);
        var today = DateTime.UtcNow.Date;

        var allUsage = await _dbSet
            .Where(u => u.SubscriptionId == subscriptionId && u.UsageTime >= thirtyDaysAgo)
            .ToListAsync(cancellationToken);

        var totalThirtyDays = allUsage.Sum(u => u.TokensUsed);
        var totalSevenDays = allUsage.Where(u => u.UsageTime >= sevenDaysAgo).Sum(u => u.TokensUsed);
        var totalToday = allUsage.Where(u => u.UsageTime >= today).Sum(u => u.TokensUsed);

        var dailyAverage = totalThirtyDays / 30;
        var weeklyAverage = totalSevenDays / 7;

        var peakDay = allUsage
            .GroupBy(u => u.UsageTime.Date)
            .Select(g => new { Date = g.Key, Total = g.Sum(u => u.TokensUsed) })
            .OrderByDescending(x => x.Total)
            .FirstOrDefault();

        var modelUsage = allUsage
            .GroupBy(u => u.ModelName)
            .Select(g => new { Model = g.Key, Total = g.Sum(u => u.TokensUsed), Count = g.Count() })
            .OrderByDescending(x => x.Total)
            .ToList();

        return new
        {
            TotalThirtyDays = totalThirtyDays,
            TotalSevenDays = totalSevenDays,
            TotalToday = totalToday,
            DailyAverage = dailyAverage,
            WeeklyAverage = weeklyAverage,
            PeakDay = peakDay?.Date,
            PeakDayUsage = peakDay?.Total ?? 0,
            ModelUsage = modelUsage
        };
    }

    public async Task<bool> DeleteOldRecordsAsync(int daysToKeep, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
        
        var recordsToDelete = await _dbSet
            .Where(u => u.UsageTime < cutoffDate)
            .ToListAsync(cancellationToken);

        if (recordsToDelete.Any())
        {
            _dbSet.RemoveRange(recordsToDelete);
            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Deleted {Count} usage records older than {Date}", 
                recordsToDelete.Count, cutoffDate);
            return true;
        }

        return false;
    }

    public async Task<IEnumerable<object>> GetTopUsersAsync(DateTime startDate, DateTime endDate, int topCount = 10, CancellationToken cancellationToken = default)
    {
        var topUsers = await _dbSet
            .Where(u => u.UsageTime >= startDate && u.UsageTime <= endDate)
            .GroupBy(u => u.SubscriptionId)
            .Select(g => new
            {
                SubscriptionId = g.Key,
                TotalTokens = g.Sum(u => u.TokensUsed),
                ConversationCount = g.Select(u => u.ConversationId).Distinct().Count(),
                RecordCount = g.Count()
            })
            .OrderByDescending(x => x.TotalTokens)
            .Take(topCount)
            .ToListAsync(cancellationToken);

        // Get subscription details
        var result = new List<object>();
        foreach (var user in topUsers)
        {
            var subscription = await _context.Subscriptions
                .Include(s => s.Plan)
                .FirstOrDefaultAsync(s => s.Id == user.SubscriptionId, cancellationToken);

            if (subscription != null)
            {
                result.Add(new
                {
                    user.SubscriptionId,
                    subscription.CustomerUserId,
                    PlanName = subscription.Plan?.Name,
                    user.TotalTokens,
                    user.ConversationCount,
                    user.RecordCount
                });
            }
        }

        return result;
    }
}