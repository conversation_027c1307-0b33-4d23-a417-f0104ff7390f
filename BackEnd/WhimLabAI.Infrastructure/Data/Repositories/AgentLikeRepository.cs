using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// Agent点赞仓储实现
/// </summary>
public class AgentLikeRepository : Repository<AgentLike>, IAgentLikeRepository
{
    public AgentLikeRepository(WhimLabAIDbContext context) : base(context)
    {
    }
    
    /// <summary>
    /// 根据Agent和用户获取点赞记录
    /// </summary>
    public async Task<AgentLike?> GetByAgentAndUserAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.AgentId == agentId && x.UserId == userId, cancellationToken);
    }
    
    /// <summary>
    /// 获取Agent的点赞用户列表
    /// </summary>
    public async Task<List<AgentLike>> GetByAgentIdAsync(Guid agentId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.AgentId == agentId)
            .OrderByDescending(x => x.LikedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }
    
    /// <summary>
    /// 获取用户点赞的Agent列表
    /// </summary>
    public async Task<List<AgentLike>> GetByUserIdAsync(Guid userId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.UserId == userId)
            .OrderByDescending(x => x.LikedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }
    
    /// <summary>
    /// 批量检查用户是否点赞了指定的Agents
    /// </summary>
    public async Task<Dictionary<Guid, bool>> CheckUserLikesAsync(Guid userId, List<Guid> agentIds, CancellationToken cancellationToken = default)
    {
        var likes = await _dbSet
            .Where(x => x.UserId == userId && agentIds.Contains(x.AgentId))
            .Select(x => x.AgentId)
            .ToListAsync(cancellationToken);
            
        return agentIds.ToDictionary(
            agentId => agentId,
            agentId => likes.Contains(agentId)
        );
    }
    
    /// <summary>
    /// 获取点赞了某个Agent的用户ID列表
    /// </summary>
    public async Task<List<Guid>> GetLikedUserIdsAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.AgentId == agentId)
            .Select(x => x.UserId)
            .ToListAsync(cancellationToken);
    }
    
    /// <summary>
    /// 获取用户点赞的Agent ID列表
    /// </summary>
    public async Task<List<Guid>> GetUserLikedAgentIdsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.UserId == userId)
            .Select(x => x.AgentId)
            .ToListAsync(cancellationToken);
    }
}