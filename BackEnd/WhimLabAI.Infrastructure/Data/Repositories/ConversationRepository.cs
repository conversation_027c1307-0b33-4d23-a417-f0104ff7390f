using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Conversation;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class ConversationRepository : Repository<Conversation>, IConversationRepository
{
    public ConversationRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Conversation>> GetUserConversationsAsync(Guid userId, bool includeArchived = false, CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Include(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1)) // Include last message
            .Where(c => c.CustomerUserId == userId);

        if (!includeArchived)
        {
            query = query.Where(c => !c.IsArchived);
        }

        return await query
            .OrderByDescending(c => c.UpdatedAt)
            .AsNoTracking() // Read-only query
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ConversationMessage>> GetConversationMessagesAsync(Guid conversationId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        var conversation = await _dbSet
            .Include(c => c.Messages)
                .ThenInclude(m => m.Attachments)
            .Include(c => c.Messages)
                .ThenInclude(m => m.Rating)
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .AsNoTracking() // Read-only query
            .FirstOrDefaultAsync(c => c.Id == conversationId, cancellationToken);

        if (conversation == null)
        {
            return Enumerable.Empty<ConversationMessage>();
        }

        return conversation.Messages
            .OrderByDescending(m => m.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .Reverse() // Reverse to get chronological order
            .ToList();
    }
    
    public async Task<IEnumerable<Conversation>> GetOldConversationsAsync(int daysOld, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
        return await _dbSet
            .Where(c => c.LastMessageAt < cutoffDate && c.IsArchived)
            .ToListAsync(cancellationToken);
    }

    public async Task<ConversationMessage?> GetLastMessageAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        var conversation = await _dbSet
            .Include(c => c.Messages)
            .FirstOrDefaultAsync(c => c.Id == conversationId, cancellationToken);

        return conversation?.Messages
            .OrderByDescending(m => m.CreatedAt)
            .FirstOrDefault();
    }

    public async Task<int> GetUserConversationCountAsync(Guid userId, Guid? agentId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(c => c.CustomerUserId == userId);

        if (agentId.HasValue)
        {
            query = query.Where(c => c.AgentId == agentId.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    public async Task<int> GetUserTokenUsageAsync(Guid userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        // Use database aggregation instead of loading all messages into memory
        return await _context.Set<ConversationMessage>()
            .Where(m => m.Conversation.CustomerUserId == userId && 
                       m.Conversation.CreatedAt >= startDate && 
                       m.Conversation.CreatedAt <= endDate)
            .SumAsync(m => m.TokenCount, cancellationToken);
    }

    public async Task<bool> UserOwnsConversationAsync(Guid userId, Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AnyAsync(c => c.Id == conversationId && c.CustomerUserId == userId, cancellationToken);
    }

    // Additional methods for extended functionality
    public async Task<Conversation?> GetWithMessagesAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(c => c.Messages)
                .ThenInclude(m => m.Attachments)
            .FirstOrDefaultAsync(c => c.Id == conversationId, cancellationToken);
    }

    public async Task<Conversation?> GetWithFullDetailsAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(c => c.Messages)
                .ThenInclude(m => m.Attachments)
            .Include(c => c.Messages)
                .ThenInclude(m => m.Rating)
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .FirstOrDefaultAsync(c => c.Id == conversationId, cancellationToken);
    }

    public async Task<IReadOnlyList<Conversation>> GetActiveConversationsAsync(Guid userId, int count = 10, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1))
            .Where(c => c.CustomerUserId == userId && !c.IsArchived)
            .OrderByDescending(c => c.UpdatedAt)
            .Take(count)
            .AsNoTracking() // Read-only query
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Conversation>> GetConversationsByAgentAsync(Guid agentId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(c => c.AgentId == agentId)
            .OrderByDescending(c => c.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<Guid, int>> GetAgentUsageStatsAsync(Guid agentId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var stats = await _dbSet
            .Where(c => c.AgentId == agentId && 
                       c.CreatedAt >= startDate && 
                       c.CreatedAt <= endDate)
            .GroupBy(c => c.CustomerUserId)
            .Select(g => new { UserId = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.UserId, x => x.Count, cancellationToken);

        return stats;
    }

    public async Task<int> GetTotalMessageCountAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _context.Set<ConversationMessage>()
            .CountAsync(m => m.ConversationId == conversationId, cancellationToken);
    }

    public async Task<int> GetTotalTokenUsageAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _context.Set<ConversationMessage>()
            .Where(m => m.ConversationId == conversationId)
            .SumAsync(m => m.TokenCount, cancellationToken);
    }

    public async Task<ConversationMessage?> GetMessageByIdAsync(Guid messageId, CancellationToken cancellationToken = default)
    {
        return await _context.Set<ConversationMessage>()
            .Include(m => m.Attachments)
            .Include(m => m.Rating)
            .FirstOrDefaultAsync(m => m.Id == messageId, cancellationToken);
    }
    
    /// <summary>
    /// 检查用户是否使用过指定的Agent
    /// </summary>
    public async Task<bool> HasUserUsedAgentAsync(Guid userId, Guid agentId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AnyAsync(c => c.CustomerUserId == userId && c.AgentId == agentId, cancellationToken);
    }
    
    /// <summary>
    /// 获取用户置顶对话的数量
    /// </summary>
    public async Task<int> GetPinnedConversationCountAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        // PostgreSQL jsonb query using EF Core
        // Note: EF Core 7.0+ supports JsonContains and direct JSON queries
        return await _dbSet
            .Where(c => c.CustomerUserId == userId && 
                       EF.Functions.JsonContains(c.Metadata, "{\"IsPinned\": true}"))
            .CountAsync(cancellationToken);
    }
    
    public async Task<(IEnumerable<Conversation> conversations, int totalCount)> SearchConversationsAsync(
        Guid userId,
        string? keyword = null,
        List<Guid>? agentIds = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        bool includeArchived = false,
        bool onlyPinned = false,
        bool searchInContent = false,
        string sortBy = "lastMessage",
        bool sortDescending = true,
        int skip = 0,
        int take = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Include(c => c.Messages)
            .Where(c => c.CustomerUserId == userId);

        // Apply filters
        if (!includeArchived)
        {
            query = query.Where(c => !c.IsArchived);
        }

        if (onlyPinned)
        {
            query = query.Where(c => EF.Functions.JsonContains(c.Metadata, "{\"IsPinned\": true}"));
        }

        if (agentIds != null && agentIds.Any())
        {
            query = query.Where(c => agentIds.Contains(c.AgentId));
        }

        if (startDate.HasValue)
        {
            query = query.Where(c => c.CreatedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(c => c.CreatedAt <= endDate.Value);
        }

        if (!string.IsNullOrWhiteSpace(keyword))
        {
            if (searchInContent)
            {
                // Search in conversation title and message content
                query = query.Where(c => 
                    c.Title.Contains(keyword) || 
                    c.Messages.Any(m => m.Content.Contains(keyword)));
            }
            else
            {
                // Search only in title
                query = query.Where(c => c.Title.Contains(keyword));
            }
        }

        // Get total count before pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = sortBy switch
        {
            "created" => sortDescending ? query.OrderByDescending(c => c.CreatedAt) : query.OrderBy(c => c.CreatedAt),
            "messageCount" => sortDescending ? query.OrderByDescending(c => c.MessageCount) : query.OrderBy(c => c.MessageCount),
            "tokenUsage" => sortDescending ? query.OrderByDescending(c => c.TotalTokens) : query.OrderBy(c => c.TotalTokens),
            _ => sortDescending ? query.OrderByDescending(c => c.LastMessageAt) : query.OrderBy(c => c.LastMessageAt)
        };

        // Apply pagination
        var conversations = await query
            .Skip(skip)
            .Take(take)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        return (conversations, totalCount);
    }
    
    public async Task<Dictionary<string, object>> GetConversationStatisticsAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        var conversation = await GetWithFullDetailsAsync(conversationId, cancellationToken);
        if (conversation == null)
        {
            return new Dictionary<string, object>();
        }

        var messages = conversation.Messages.Where(m => !m.IsDeleted).ToList();
        var userMessages = messages.Where(m => m.Role == "user").ToList();
        var assistantMessages = messages.Where(m => m.Role == "assistant").ToList();

        var stats = new Dictionary<string, object>
        {
            ["totalMessages"] = messages.Count,
            ["userMessages"] = userMessages.Count,
            ["assistantMessages"] = assistantMessages.Count,
            ["totalTokens"] = conversation.TotalTokens,
            ["inputTokens"] = conversation.InputTokens,
            ["outputTokens"] = conversation.OutputTokens,
            ["averageMessageLength"] = messages.Any() ? messages.Average(m => m.Content.Length) : 0,
            ["averageTokensPerMessage"] = messages.Any() ? messages.Average(m => m.TokenCount) : 0,
            ["conversationDuration"] = (conversation.LastMessageAt - conversation.CreatedAt).TotalMinutes,
            ["messagesWithAttachments"] = messages.Count(m => m.Attachments.Any()),
            ["messagesWithRatings"] = messages.Count(m => m.Rating != null),
            ["averageRating"] = assistantMessages.Where(m => m.Rating != null).Any() 
                ? assistantMessages.Where(m => m.Rating != null).Average(m => m.Rating!.Score) 
                : 0
        };

        return stats;
    }
    
    public async Task<Dictionary<string, object>> GetUserStatisticsAsync(Guid userId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(c => c.CustomerUserId == userId);

        if (startDate.HasValue)
        {
            query = query.Where(c => c.CreatedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(c => c.CreatedAt <= endDate.Value);
        }

        var conversations = await query
            .Include(c => c.Messages)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        var stats = new Dictionary<string, object>
        {
            ["totalConversations"] = conversations.Count,
            ["activeConversations"] = conversations.Count(c => !c.IsArchived),
            ["archivedConversations"] = conversations.Count(c => c.IsArchived),
            ["pinnedConversations"] = conversations.Count(c => c.Metadata.ContainsKey("IsPinned") && (bool)c.Metadata["IsPinned"]),
            ["totalMessages"] = conversations.Sum(c => c.MessageCount),
            ["totalTokensUsed"] = conversations.Sum(c => c.TotalTokens),
            ["averageMessagesPerConversation"] = conversations.Any() ? conversations.Average(c => c.MessageCount) : 0,
            ["averageTokensPerConversation"] = conversations.Any() ? conversations.Average(c => c.TotalTokens) : 0,
            ["uniqueAgentsUsed"] = conversations.Select(c => c.AgentId).Distinct().Count(),
            ["conversationsByAgent"] = conversations.GroupBy(c => c.AgentId)
                .ToDictionary(g => g.Key.ToString(), g => g.Count()),
            ["conversationsByDay"] = conversations.GroupBy(c => c.CreatedAt.Date)
                .ToDictionary(g => g.Key.ToString("yyyy-MM-dd"), g => g.Count()),
            ["peakHour"] = conversations.GroupBy(c => c.CreatedAt.Hour)
                .OrderByDescending(g => g.Count())
                .FirstOrDefault()?.Key ?? 0
        };

        return stats;
    }
    
    public async Task<IEnumerable<Conversation>> GetConversationsForCleanupAsync(
        int daysOld,
        bool excludePinned = true,
        bool excludeWithAttachments = true,
        int? limit = null,
        CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
        var query = _dbSet
            .Include(c => c.Messages)
                .ThenInclude(m => m.Attachments)
            .Where(c => c.LastMessageAt < cutoffDate);

        if (excludePinned)
        {
            query = query.Where(c => !EF.Functions.JsonContains(c.Metadata, "{\"IsPinned\": true}"));
        }

        if (excludeWithAttachments)
        {
            query = query.Where(c => !c.Messages.Any(m => m.Attachments.Any()));
        }

        if (limit.HasValue)
        {
            query = query.Take(limit.Value);
        }

        return await query.ToListAsync(cancellationToken);
    }
    
    public async Task<int> GetUserStorageUsageAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        // Calculate approximate storage usage based on message content and attachments
        var conversations = await _dbSet
            .Include(c => c.Messages)
                .ThenInclude(m => m.Attachments)
            .Where(c => c.CustomerUserId == userId)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        var totalBytes = 0;
        foreach (var conversation in conversations)
        {
            // Estimate conversation metadata size
            totalBytes += System.Text.Encoding.UTF8.GetByteCount(conversation.Title);
            totalBytes += 200; // Approximate overhead for other fields

            foreach (var message in conversation.Messages)
            {
                // Message content size
                totalBytes += System.Text.Encoding.UTF8.GetByteCount(message.Content);
                
                // Attachment sizes
                totalBytes += message.Attachments.Sum(a => (int)a.FileSize);
            }
        }

        return totalBytes;
    }
    
    public async Task<IEnumerable<Conversation>> GetDeletedConversationsAsync(Guid userId, DateTime? since = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.IgnoreQueryFilters()
            .Where(c => c.CustomerUserId == userId && c.IsDeleted);

        if (since.HasValue)
        {
            query = query.Where(c => c.UpdatedAt >= since.Value);
        }

        return await query
            .OrderByDescending(c => c.UpdatedAt)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }
    
    public async Task<Dictionary<Guid, bool>> BatchUpdateMetadataAsync(List<Guid> conversationIds, string key, object value, CancellationToken cancellationToken = default)
    {
        var results = new Dictionary<Guid, bool>();
        
        var conversations = await _dbSet
            .Where(c => conversationIds.Contains(c.Id))
            .ToListAsync(cancellationToken);

        foreach (var conversation in conversations)
        {
            try
            {
                conversation.SetMetadata(key, value);
                results[conversation.Id] = true;
            }
            catch
            {
                results[conversation.Id] = false;
            }
        }

        await _context.SaveChangesAsync(cancellationToken);
        return results;
    }
    
    public async Task<int> BatchArchiveAsync(List<Guid> conversationIds, Guid userId, CancellationToken cancellationToken = default)
    {
        var conversations = await _dbSet
            .Where(c => conversationIds.Contains(c.Id) && c.CustomerUserId == userId && !c.IsArchived)
            .ToListAsync(cancellationToken);

        foreach (var conversation in conversations)
        {
            conversation.Archive();
        }

        await _context.SaveChangesAsync(cancellationToken);
        return conversations.Count;
    }
    
    public async Task<int> BatchDeleteAsync(List<Guid> conversationIds, Guid userId, CancellationToken cancellationToken = default)
    {
        var conversations = await _dbSet
            .Where(c => conversationIds.Contains(c.Id) && c.CustomerUserId == userId)
            .ToListAsync(cancellationToken);

        _dbSet.RemoveRange(conversations);
        await _context.SaveChangesAsync(cancellationToken);
        return conversations.Count;
    }
}