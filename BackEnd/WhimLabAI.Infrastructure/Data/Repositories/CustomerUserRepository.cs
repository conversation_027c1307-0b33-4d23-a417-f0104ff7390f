using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.ValueObjects;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class CustomerUserRepository : Repository<CustomerUser>, ICustomerUserRepository
{
    public CustomerUserRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public IQueryable<CustomerUser> GetQueryable()
    {
        return _dbSet.AsQueryable();
    }

    public async Task<CustomerUser?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.Profile)
            .Include(u => u.NotificationSetting)
            .FirstOrDefaultAsync(u => u.Username == username, cancellationToken);
    }

    public async Task<CustomerUser?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        // Email is an owned entity, so we need to compare the Value property
        return await _dbSet
            .Include(u => u.Profile)
            .Include(u => u.NotificationSetting)
            .FirstOrDefaultAsync(u => u.Email != null && u.Email.Value == email, cancellationToken);
    }

    public async Task<CustomerUser?> GetByPhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        // Phone is an owned entity, so we need to compare the Value property
        return await _dbSet
            .Include(u => u.Profile)
            .Include(u => u.NotificationSetting)
            .FirstOrDefaultAsync(u => u.Phone != null && u.Phone.Value == phoneNumber, cancellationToken);
    }

    public async Task<bool> IsUsernameExistsAsync(string username, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(u => u.Username == username, cancellationToken);
    }

    public async Task<bool> IsEmailExistsAsync(string email, CancellationToken cancellationToken = default)
    {
        // Email is an owned entity, so we need to compare the Value property
        return await _dbSet.AnyAsync(u => u.Email != null && u.Email.Value == email, cancellationToken);
    }

    public async Task<bool> IsPhoneNumberExistsAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        // Phone is an owned entity, so we need to compare the Value property
        return await _dbSet.AnyAsync(u => u.Phone != null && u.Phone.Value == phoneNumber, cancellationToken);
    }

    public async Task<CustomerUser?> GetWithLoginHistoryAsync(Guid userId, int historyCount = 10, CancellationToken cancellationToken = default)
    {
        // LoginHistories property doesn't exist on CustomerUser
        return await _dbSet
            .Include(u => u.Profile)
            .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
    }

    public async Task<CustomerUser?> GetWithOAuthBindingsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.Profile)
            .Include(u => u.OAuthBindings)
            .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
    }
    
    public async Task<CustomerUser?> GetByOAuthBindingAsync(string provider, string providerUserId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.Profile)
            .Include(u => u.OAuthBindings)
            .Where(u => u.OAuthBindings.Any(b => b.Provider == provider && b.ProviderUserId == providerUserId))
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<CustomerUser?> GetWithDevicesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.DeviceAuthorizations)
            .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
    }

    public async Task<IReadOnlyList<CustomerUser>> GetActiveUsersAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(u => u.IsActive && !u.IsBanned)
            .OrderByDescending(u => u.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public override async Task<CustomerUser?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.Profile)
            .FirstOrDefaultAsync(u => u.Id == id, cancellationToken);
    }
    
    /// <summary>
    /// 根据ID列表批量获取用户
    /// </summary>
    public async Task<List<CustomerUser>> GetByIdsAsync(List<Guid> userIds, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.Profile)
            .Where(u => userIds.Contains(u.Id))
            .ToListAsync(cancellationToken);
    }
    
    /// <summary>
    /// 根据刷新令牌获取用户
    /// </summary>
    public async Task<CustomerUser?> GetByRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        // Use EF.Property to access the shadow property
        return await _dbSet
            .Include(u => u.Profile)
            .Where(u => EF.Property<string>(u, "_refreshToken") == refreshToken)
            .FirstOrDefaultAsync(cancellationToken);
    }
}