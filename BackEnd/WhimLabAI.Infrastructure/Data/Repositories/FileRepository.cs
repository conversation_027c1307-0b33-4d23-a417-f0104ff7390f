using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Storage;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class FileRepository : Repository<FileEntity>, IFileRepository
{
    public FileRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<FileEntity?> GetByFileKeyAsync(
        string fileKey, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(f => f.FileKey == fileKey, cancellationToken);
    }

    public async Task<IEnumerable<FileEntity>> GetUserFilesAsync(
        Guid userId, 
        string? fileType = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Where(f => f.UserId == userId);

        if (!string.IsNullOrEmpty(fileType))
        {
            query = query.Where(f => f.ContentType == fileType);
        }

        return await query
            .OrderByDescending(f => f.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<FileEntity>> GetExpiredTemporaryFilesAsync(
        int hours, 
        CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow.AddHours(-hours);
        return await _dbSet
            .Where(f => f.IsTemporary && f.CreatedAt <= cutoffTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<long> GetUserStorageUsageAsync(
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(f => f.UserId == userId)
            .SumAsync(f => f.FileSize, cancellationToken);
    }

    public async Task<bool> FileExistsAsync(
        string fileKey, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AnyAsync(f => f.FileKey == fileKey, cancellationToken);
    }

    public async Task<int> DeleteExpiredFilesAsync(
        CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        var expiredFiles = await _dbSet
            .Where(f => f.ExpiresAt.HasValue && f.ExpiresAt.Value <= now)
            .ToListAsync(cancellationToken);
            
        _dbSet.RemoveRange(expiredFiles);
        return expiredFiles.Count;
    }
}