using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class SubscriptionRepository : Repository<Subscription>, ISubscriptionRepository
{
    private readonly ILogger<SubscriptionRepository> _logger;

    public SubscriptionRepository(WhimLabAIDbContext context, ILogger<SubscriptionRepository> logger) 
        : base(context)
    {
        _logger = logger;
    }

    public async Task<Subscription?> GetActiveSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(s => s.Plan)
            .Include(s => s.UsageRecords.Where(u => u.UsageTime >= DateTime.UtcNow.AddDays(-30)))
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .FirstOrDefaultAsync(s => s.CustomerUserId == userId && s.Status == SubscriptionStatus.Active, cancellationToken);
    }

    public async Task<Subscription?> GetActiveSubscriptionByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await GetActiveSubscriptionAsync(userId, cancellationToken);
    }

    public async Task<IEnumerable<Subscription>> GetUserSubscriptionHistoryAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(s => s.Plan)
            .Where(s => s.CustomerUserId == userId)
            .OrderByDescending(s => s.StartDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Subscription>> GetExpiringSubscriptionsAsync(int days, CancellationToken cancellationToken = default)
    {
        var beforeDate = DateTime.UtcNow.AddDays(days);
        return await _dbSet
            .Include(s => s.Plan)
            .Where(s => s.Status == SubscriptionStatus.Active && 
                       s.EndDate != null && 
                       s.EndDate <= beforeDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Subscription>> GetExpiredSubscriptionsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(s => s.Plan)
            .Where(s => s.Status == SubscriptionStatus.Active && 
                       s.EndDate != null && 
                       s.EndDate < DateTime.UtcNow)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> HasActiveSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(s => 
            s.CustomerUserId == userId && 
            s.Status == SubscriptionStatus.Active, 
            cancellationToken);
    }

    public async Task<int> GetRemainingTokensAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        var subscription = await _dbSet.FindAsync([subscriptionId], cancellationToken);
        return subscription?.RemainingTokens ?? 0;
    }

    public async Task<bool> ConsumeTokensAsync(Guid subscriptionId, int tokens, CancellationToken cancellationToken = default)
    {
        var subscription = await _dbSet.FindAsync([subscriptionId], cancellationToken);
        if (subscription == null || subscription.RemainingTokens < tokens)
        {
            return false;
        }

        subscription.RemainingTokens -= tokens;
        await _context.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<bool> ResetMonthlyTokensAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        var subscription = await _dbSet
            .Include(s => s.Plan)
            .FirstOrDefaultAsync(s => s.Id == subscriptionId, cancellationToken);
            
        if (subscription == null || subscription.Plan == null)
        {
            return false;
        }

        subscription.RemainingTokens = subscription.Plan.MonthlyTokens;
        subscription.LastResetDate = DateTime.UtcNow;
        await _context.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<IEnumerable<Subscription>> GetSubscriptionsNeedingTokenResetAsync(CancellationToken cancellationToken = default)
    {
        var oneMonthAgo = DateTime.UtcNow.AddMonths(-1);
        
        return await _dbSet
            .Include(s => s.Plan)
            .Where(s => s.Status == SubscriptionStatus.Active &&
                       (s.LastResetDate == null || s.LastResetDate <= oneMonthAgo))
            .ToListAsync(cancellationToken);
    }

    public async Task<object> GetSubscriptionStatisticsAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        // Check if subscription exists
        var subscriptionExists = await _dbSet
            .AnyAsync(s => s.Id == subscriptionId, cancellationToken);

        if (!subscriptionExists)
        {
            return new
            {
                TotalTokensUsed = 0,
                DailyUsage = new Dictionary<DateTime, int>(),
                AverageDaily = 0,
                PeakUsageDay = (DateTime?)null
            };
        }

        // Use database aggregation for statistics
        var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
        
        var dailyUsage = await _context.Set<UsageRecord>()
            .Where(u => u.SubscriptionId == subscriptionId && u.UsageTime >= thirtyDaysAgo)
            .GroupBy(u => u.UsageTime.Date)
            .Select(g => new { Date = g.Key, Tokens = g.Sum(u => u.TokensUsed) })
            .OrderBy(x => x.Date)
            .ToDictionaryAsync(x => x.Date, x => x.Tokens, cancellationToken);

        var totalUsed = dailyUsage.Values.Sum();
        var peakDay = dailyUsage.Count > 0 
            ? dailyUsage.OrderByDescending(kvp => kvp.Value).First()
            : default(KeyValuePair<DateTime, int>);

        return new
        {
            TotalTokensUsed = totalUsed,
            DailyUsage = dailyUsage,
            AverageDaily = dailyUsage.Count > 0 ? totalUsed / dailyUsage.Count : 0,
            PeakUsageDay = peakDay.Key != default ? peakDay.Key : (DateTime?)null
        };
    }

    public async Task<bool> UpgradeSubscriptionAsync(Guid subscriptionId, Guid newPlanId, CancellationToken cancellationToken = default)
    {
        var subscription = await _dbSet
            .Include(s => s.Plan)
            .FirstOrDefaultAsync(s => s.Id == subscriptionId, cancellationToken);

        if (subscription == null || subscription.Status != SubscriptionStatus.Active)
        {
            return false;
        }

        var newPlan = await _context.SubscriptionPlans.FindAsync([newPlanId], cancellationToken);
        if (newPlan == null)
        {
            return false;
        }

        // Calculate prorated tokens if upgrading
        if (newPlan.MonthlyTokens > subscription.Plan!.MonthlyTokens)
        {
            var daysInMonth = DateTime.DaysInMonth(DateTime.UtcNow.Year, DateTime.UtcNow.Month);
            var daysRemaining = daysInMonth - DateTime.UtcNow.Day + 1;
            var proratedTokens = (newPlan.MonthlyTokens - subscription.Plan.MonthlyTokens) * daysRemaining / daysInMonth;
            subscription.RemainingTokens += proratedTokens;
        }

        subscription.PlanId = newPlanId;
        subscription.Plan = newPlan;
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Subscription {SubscriptionId} upgraded to plan {PlanId}", subscriptionId, newPlanId);
        return true;
    }

    public async Task<bool> CancelSubscriptionAsync(Guid subscriptionId, string reason, CancellationToken cancellationToken = default)
    {
        var subscription = await _dbSet.FindAsync([subscriptionId], cancellationToken);
        if (subscription == null)
        {
            return false;
        }

        subscription.Status = SubscriptionStatus.Cancelled;
        subscription.CancellationDate = DateTime.UtcNow;
        subscription.CancellationReason = reason;
        
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Subscription {SubscriptionId} cancelled. Reason: {Reason}", subscriptionId, reason);
        return true;
    }

    // TODO: Pause/Resume functionality is not implemented in the domain model
    // SubscriptionStatus enum doesn't have a Paused value
    // Subscription entity needs domain methods for pause/resume
    /*
    public async Task<bool> PauseSubscriptionAsync(Guid subscriptionId, DateTime resumeDate, CancellationToken cancellationToken = default)
    {
        var subscription = await _dbSet.FindAsync([subscriptionId], cancellationToken);
        if (subscription == null || subscription.Status != SubscriptionStatus.Active)
        {
            return false;
        }

        subscription.Status = SubscriptionStatus.Paused;
        subscription.PauseDate = DateTime.UtcNow;
        subscription.ResumeDate = resumeDate;
        
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Subscription {SubscriptionId} paused until {ResumeDate}", subscriptionId, resumeDate);
        return true;
    }

    public async Task<bool> ResumeSubscriptionAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        var subscription = await _dbSet.FindAsync([subscriptionId], cancellationToken);
        if (subscription == null || subscription.Status != SubscriptionStatus.Paused)
        {
            return false;
        }

        subscription.Status = SubscriptionStatus.Active;
        subscription.PauseDate = null;
        subscription.ResumeDate = null;
        
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Subscription {SubscriptionId} resumed", subscriptionId);
        return true;
    }
    */
    
    public async Task<IEnumerable<Subscription>> GetActiveSubscriptionsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(s => s.Plan)
            .Where(s => s.Status == SubscriptionStatus.Active)
            .ToListAsync(cancellationToken);
    }
}