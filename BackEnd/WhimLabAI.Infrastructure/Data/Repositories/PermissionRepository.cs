using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Auth;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class PermissionRepository : Repository<Permission>, IPermissionRepository
{
    public PermissionRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<Permission?> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(p => p.Code == code, cancellationToken);
    }

    public async Task<IReadOnlyList<Permission>> GetByIdsAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default)
    {
        var idList = ids.ToList();
        return await _dbSet
            .Where(p => idList.Contains(p.Id))
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Permission>> GetAllWithHierarchyAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .OrderBy(p => p.Category)
            .ThenBy(p => p.DisplayOrder)
            .ThenBy(p => p.Code)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Permission>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.Category == category)
            .OrderBy(p => p.DisplayOrder)
            .ThenBy(p => p.Code)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> ExistsAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(p => p.Code == code);
        
        if (excludeId.HasValue)
        {
            query = query.Where(p => p.Id != excludeId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Permission>> GetEnabledPermissionsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.IsEnabled)
            .OrderBy(p => p.Category)
            .ThenBy(p => p.DisplayOrder)
            .ThenBy(p => p.Code)
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Permission>> GetByCodesAsync(IEnumerable<string> codes, CancellationToken cancellationToken = default)
    {
        var codeList = codes.ToList();
        return await _dbSet
            .Where(p => codeList.Contains(p.Code))
            .ToListAsync(cancellationToken);
    }
}
