using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class OrderRepository : Repository<Order>, IOrderRepository
{
    private readonly ILogger<OrderRepository> _logger;

    public OrderRepository(WhimLabAIDbContext context, ILogger<OrderRepository> logger) 
        : base(context)
    {
        _logger = logger;
    }

    public async Task<Order?> GetByOrderNoAsync(string orderNo, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(o => o.Transactions)
            .Include(o => o.Refunds)
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .FirstOrDefaultAsync(o => o.OrderNo == orderNo, cancellationToken);
    }

    public async Task<IEnumerable<Order>> GetUserOrdersAsync(Guid userId, string? status = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Include(o => o.Transactions)
            .Include(o => o.Refunds)
            .Where(o => o.CustomerUserId == userId);
            
        if (!string.IsNullOrEmpty(status) && Enum.TryParse<OrderStatus>(status, out var orderStatus))
        {
            query = query.Where(o => o.Status == orderStatus);
        }
        
        return await query
            .OrderByDescending(o => o.CreatedAt)
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .AsNoTracking() // Read-only query
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Order>> GetOrdersByStatusAsync(OrderStatus status, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(o => o.Transactions)
            .Where(o => o.Status == status)
            .OrderBy(o => o.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Order>> GetPendingPaymentOrdersAsync(TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow.Subtract(timeout);
        
        return await _dbSet
            .Where(o => o.Status == OrderStatus.Pending && o.CreatedAt < cutoffTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> HasUserPurchasedPlanAsync(Guid userId, Guid planId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AnyAsync(o => o.CustomerUserId == userId && 
                          o.Status == OrderStatus.Paid &&
                          o.ProductId == planId && 
                          o.Type == OrderType.Subscription, 
                      cancellationToken);
    }

    public async Task<object> GetOrderStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        // Use database aggregation instead of loading all orders into memory
        var query = _dbSet.Where(o => o.CreatedAt >= startDate && o.CreatedAt <= endDate);
        
        var totalOrders = await query.CountAsync(cancellationToken);
        var completedOrders = await query.CountAsync(o => o.Status == OrderStatus.Paid, cancellationToken);
        var cancelledOrders = await query.CountAsync(o => o.Status == OrderStatus.Cancelled, cancellationToken);
        var refundedOrders = await query.CountAsync(o => o.Status == OrderStatus.Refunded, cancellationToken);
        
        var revenueData = await query
            .Where(o => o.Status == OrderStatus.Paid)
            .GroupBy(o => 1) // Group all into one for aggregate
            .Select(g => new
            {
                TotalRevenue = g.Sum(o => o.FinalAmount.Amount),
                AverageOrderValue = g.Average(o => o.FinalAmount.Amount)
            })
            .FirstOrDefaultAsync(cancellationToken);
        
        var ordersByType = await query
            .GroupBy(o => o.Type)
            .Select(g => new { Type = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Type.ToString(), x => x.Count, cancellationToken);
        
        var dailyRevenue = await query
            .Where(o => o.Status == OrderStatus.Paid)
            .GroupBy(o => o.PaidAt != null ? o.PaidAt.Value.Date : o.CreatedAt.Date)
            .Select(g => new { Date = g.Key, Revenue = g.Sum(o => o.FinalAmount.Amount) })
            .OrderBy(x => x.Date)
            .ToDictionaryAsync(x => x.Date, x => x.Revenue, cancellationToken);
        
        return new
        {
            TotalOrders = totalOrders,
            CompletedOrders = completedOrders,
            CancelledOrders = cancelledOrders,
            RefundedOrders = refundedOrders,
            TotalRevenue = revenueData?.TotalRevenue ?? 0,
            AverageOrderValue = revenueData?.AverageOrderValue ?? 0,
            OrdersByType = ordersByType,
            DailyRevenue = dailyRevenue
        };
    }

    public async Task<IEnumerable<Order>> GetOrdersWithRefundsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(o => o.Refunds)
            .Where(o => o.RefundedAt.HasValue && 
                       o.RefundedAt >= startDate && 
                       o.RefundedAt <= endDate)
            .OrderByDescending(o => o.RefundedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> UpdateOrderStatusAsync(Guid orderId, OrderStatus newStatus, CancellationToken cancellationToken = default)
    {
        var order = await _dbSet.FindAsync([orderId], cancellationToken);
        if (order == null)
        {
            return false;
        }

        var oldStatus = order.Status;
        
        // Use domain methods to update status
        try
        {
            switch (newStatus)
            {
                case OrderStatus.Paid:
                    // Would need transaction ID
                    throw new InvalidOperationException("Use MarkAsPaid method with transaction ID");
                case OrderStatus.Cancelled:
                    order.Cancel();
                    break;
                case OrderStatus.Failed:
                    order.MarkAsFailed();
                    break;
                case OrderStatus.Expired:
                    order.Expire();
                    break;
                case OrderStatus.Refunded:
                    // Would need refund details
                    throw new InvalidOperationException("Use Refund method with refund details");
                default:
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update order {OrderId} status to {NewStatus}", orderId, newStatus);
            return false;
        }

        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Order {OrderId} status changed from {OldStatus} to {NewStatus}", 
            orderId, oldStatus, newStatus);
        
        return true;
    }

    public async Task<Order?> GetLatestOrderByUserAndProductAsync(Guid userId, ProductType productType, Guid productId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(o => o.Transactions)
            .Where(o => o.CustomerUserId == userId && 
                       o.Type == (OrderType)productType && 
                       o.ProductId == productId)
            .OrderByDescending(o => o.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<decimal> GetUserTotalSpentAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(o => o.CustomerUserId == userId && o.Status == OrderStatus.Paid)
            .SumAsync(o => o.FinalAmount.Amount, cancellationToken);
    }

    public async Task<int> GetUserOrderCountAsync(Guid userId, OrderStatus? status = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(o => o.CustomerUserId == userId);
        
        if (status.HasValue)
        {
            query = query.Where(o => o.Status == status.Value);
        }
        
        return await query.CountAsync(cancellationToken);
    }
    
    public async Task<IEnumerable<Order>> GetPendingOrdersAsync(DateTime expiryTime, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(o => o.Status == OrderStatus.Pending && o.CreatedAt < expiryTime)
            .Include(o => o.Transactions)
            .OrderBy(o => o.CreatedAt)
            .ToListAsync(cancellationToken);
    }
    
    public async Task<decimal> GetUserTotalPaymentAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(o => o.CustomerUserId == userId && o.Status == OrderStatus.Paid)
            .SumAsync(o => o.FinalAmount.Amount, cancellationToken);
    }
    
    public async Task<bool> UserOwnsOrderAsync(Guid userId, Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(o => o.Id == orderId && o.CustomerUserId == userId, cancellationToken);
    }
    
    public async Task<string> GenerateOrderNoAsync(CancellationToken cancellationToken = default)
    {
        var prefix = DateTime.UtcNow.ToString("yyMMdd");
        var random = new Random();
        var suffix = random.Next(10000, 99999);
        
        string orderNo;
        do
        {
            orderNo = $"ORD{prefix}{suffix}";
            suffix++;
        } while (await _dbSet.AnyAsync(o => o.OrderNo == orderNo, cancellationToken));
        
        return orderNo;
    }
    
    public async Task<bool> HasPendingRenewalOrderAsync(Guid customerUserId, Guid planId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AnyAsync(o => o.CustomerUserId == customerUserId &&
                          o.ProductId == planId &&
                          o.Type == OrderType.Subscription &&
                          o.Status == OrderStatus.Pending &&
                          o.Remark != null && o.Remark.Contains("自动续费"),
                      cancellationToken);
    }
    
    public async Task<IEnumerable<Order>> GetExpiredPendingOrdersAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(o => o.Status == OrderStatus.Pending && o.ExpireAt < DateTime.UtcNow)
            .ToListAsync(cancellationToken);
    }
    
    public async Task<IEnumerable<Order>> GetOldCancelledOrdersAsync(int daysOld, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
        return await _dbSet
            .Where(o => (o.Status == OrderStatus.Cancelled || o.Status == OrderStatus.Expired) &&
                       o.UpdatedAt < cutoffDate &&
                       o.IsDeleted == false)
            .ToListAsync(cancellationToken);
    }
}