using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.System;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Specifications;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class SystemEventRepository : Repository<SystemEvent>, ISystemEventRepository
{
    public SystemEventRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<SystemEvent>> GetRecentEventsAsync(
        string eventType, 
        int count = 10, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => e.EventType == eventType)
            .OrderByDescending(e => e.CreatedAt)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<SystemEvent>> GetEventsByDateRangeAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => e.CreatedAt >= startDate && e.CreatedAt <= endDate)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}