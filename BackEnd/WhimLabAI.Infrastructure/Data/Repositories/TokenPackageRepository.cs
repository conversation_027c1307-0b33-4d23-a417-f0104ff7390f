using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Finance;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// Token包仓储实现
/// </summary>
public class TokenPackageRepository : Repository<TokenPackage>, ITokenPackageRepository
{
    public TokenPackageRepository(WhimLabAIDbContext context) : base(context)
    {
    }
    
    /// <summary>
    /// 获取所有可用的Token包
    /// </summary>
    public async Task<List<TokenPackage>> GetAvailablePackagesAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        
        return await _context.Set<TokenPackage>()
            .Where(p => p.IsActive)
            .Where(p => !p.IsLimited || 
                       (p.ValidFrom == null || p.ValidFrom <= now) && 
                       (p.ValidTo == null || p.ValidTo >= now))
            .Where(p => !p.TotalStock.HasValue || p.SoldCount < p.TotalStock.Value)
            .OrderBy(p => p.SortOrder)
            .ThenBy(p => p.Price)
            .ToListAsync(cancellationToken);
    }
    
    /// <summary>
    /// 获取用户的购买数量
    /// </summary>
    public async Task<int> GetUserPurchaseCountAsync(Guid userId, Guid packageId, CancellationToken cancellationToken = default)
    {
        return await _context.Set<Order>()
            .Where(o => o.CustomerUserId == userId)
            .Where(o => o.Type == OrderType.TokenPackage)
            .Where(o => o.Status == OrderStatus.Paid)
            .Where(o => o.ProductId == packageId)
            .CountAsync(cancellationToken);
    }
    
    /// <summary>
    /// 按排序获取活动的Token包
    /// </summary>
    public async Task<List<TokenPackage>> GetActivePackagesOrderedAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Set<TokenPackage>()
            .Where(p => p.IsActive)
            .OrderBy(p => p.SortOrder)
            .ThenBy(p => p.Price)
            .ToListAsync(cancellationToken);
    }
}