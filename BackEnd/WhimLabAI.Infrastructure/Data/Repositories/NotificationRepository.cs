using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Notification;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 通知仓储实现
/// </summary>
public class NotificationRepository : Repository<Notification>, INotificationRepository
{
    public NotificationRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<int> GetUnreadCountAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(n => n.UserId == userId && !n.IsRead && !n.IsDeleted)
            .Where(n => n.ExpiresAt == null || n.ExpiresAt > DateTime.UtcNow)
            .CountAsync(cancellationToken);
    }

    public async Task<int> MarkAsReadAsync(Guid userId, List<Guid> notificationIds, CancellationToken cancellationToken = default)
    {
        var notifications = await _dbSet
            .Where(n => n.UserId == userId && notificationIds.Contains(n.Id) && !n.IsRead)
            .ToListAsync(cancellationToken);
        
        foreach (var notification in notifications)
        {
            notification.MarkAsRead();
        }
        
        return notifications.Count;
    }

    public async Task<int> MarkAllAsReadAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var notifications = await _dbSet
            .Where(n => n.UserId == userId && !n.IsRead && !n.IsDeleted)
            .ToListAsync(cancellationToken);
        
        foreach (var notification in notifications)
        {
            notification.MarkAsRead();
        }
        
        return notifications.Count;
    }

    public async Task<int> SoftDeleteAsync(Guid userId, List<Guid> notificationIds, CancellationToken cancellationToken = default)
    {
        var notifications = await _dbSet
            .Where(n => n.UserId == userId && notificationIds.Contains(n.Id) && !n.IsDeleted)
            .ToListAsync(cancellationToken);
        
        foreach (var notification in notifications)
        {
            notification.Delete();
        }
        
        return notifications.Count;
    }

    public async Task<int> CleanupExpiredAsync(CancellationToken cancellationToken = default)
    {
        var expiredNotifications = await _dbSet
            .Where(n => n.ExpiresAt != null && n.ExpiresAt <= DateTime.UtcNow && !n.IsDeleted)
            .ToListAsync(cancellationToken);
        
        foreach (var notification in expiredNotifications)
        {
            notification.Delete();
        }
        
        return expiredNotifications.Count;
    }

    public async Task<(List<Notification> Items, int TotalCount)> GetUserNotificationsAsync(
        Guid userId,
        string? type = null,
        string? level = null,
        bool? isRead = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        string? keyword = null,
        int pageIndex = 0,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Where(n => n.UserId == userId && !n.IsDeleted)
            .Where(n => n.ExpiresAt == null || n.ExpiresAt > DateTime.UtcNow);
        
        // 应用筛选条件
        if (!string.IsNullOrWhiteSpace(type))
        {
            query = query.Where(n => n.Type == type.ToLower());
        }
        
        if (!string.IsNullOrWhiteSpace(level))
        {
            query = query.Where(n => n.Level == level.ToLower());
        }
        
        if (isRead.HasValue)
        {
            query = query.Where(n => n.IsRead == isRead.Value);
        }
        
        if (startDate.HasValue)
        {
            query = query.Where(n => n.CreatedAt >= startDate.Value);
        }
        
        if (endDate.HasValue)
        {
            query = query.Where(n => n.CreatedAt <= endDate.Value);
        }
        
        if (!string.IsNullOrWhiteSpace(keyword))
        {
            query = query.Where(n => 
                EF.Functions.Like(n.Title, $"%{keyword}%") || 
                EF.Functions.Like(n.Content, $"%{keyword}%"));
        }
        
        // 获取总数
        var totalCount = await query.CountAsync(cancellationToken);
        
        // 分页查询
        var items = await query
            .OrderByDescending(n => n.CreatedAt)
            .Skip(pageIndex * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
        
        return (items, totalCount);
    }
    
    public async Task<IEnumerable<Notification>> GetOldReadNotificationsAsync(int daysOld, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
        return await _dbSet
            .Where(n => n.IsRead && n.ReadAt != null && n.ReadAt < cutoffDate && !n.IsDeleted)
            .ToListAsync(cancellationToken);
    }
}