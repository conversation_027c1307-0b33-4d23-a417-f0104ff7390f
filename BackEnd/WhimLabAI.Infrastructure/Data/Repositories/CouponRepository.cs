using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Payment;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class CouponRepository : Repository<Coupon>, ICouponRepository
{
    public CouponRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<Coupon?> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(c => c.Usages)
            .FirstOrDefaultAsync(c => c.Code == code, cancellationToken);
    }

    public async Task<IEnumerable<Coupon>> GetActiveAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await _dbSet
            .Where(c => c.IsActive && c.ValidFrom <= now && c.ValidTo >= now)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Coupon>> GetByUserAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(c => c.Usages)
            .Where(c => c.Usages.Any(u => u.UserId == userId))
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetUserUsageCountAsync(Guid couponId, Guid userId, CancellationToken cancellationToken = default)
    {
        var coupon = await _dbSet
            .Include(c => c.Usages)
            .FirstOrDefaultAsync(c => c.Id == couponId, cancellationToken);
        
        return coupon?.Usages.Count(u => u.UserId == userId) ?? 0;
    }

    public async Task<bool> IsCodeExistsAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(c => c.Code == code, cancellationToken);
    }
}