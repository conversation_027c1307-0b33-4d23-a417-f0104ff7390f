using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// Agent分类仓储实现
/// </summary>
public class AgentCategoryRepository : Repository<AgentCategory>, IAgentCategoryRepository
{
    public AgentCategoryRepository(WhimLabAIDbContext context) : base(context)
    {
    }
    
    /// <summary>
    /// 根据名称获取分类
    /// </summary>
    public async Task<AgentCategory?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.Name == name, cancellationToken);
    }
    
    /// <summary>
    /// 获取所有活跃的分类
    /// </summary>
    public async Task<List<AgentCategory>> GetActiveAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.IsActive)
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }
    
    /// <summary>
    /// 根据父分类获取子分类
    /// </summary>
    public async Task<List<AgentCategory>> GetByParentIdAsync(Guid? parentId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.ParentId == parentId)
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }
    
    /// <summary>
    /// 获取分类树
    /// </summary>
    public async Task<List<AgentCategory>> GetTreeAsync(CancellationToken cancellationToken = default)
    {
        var allCategories = await _dbSet
            .Where(x => x.IsActive)
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
            
        // 构建树形结构
        var rootCategories = allCategories.Where(x => x.ParentId == null).ToList();
        var lookup = allCategories.ToLookup(x => x.ParentId);
        
        foreach (var category in rootCategories)
        {
            BuildSubCategories(category, lookup);
        }
        
        return rootCategories;
    }
    
    /// <summary>
    /// 检查分类是否被使用
    /// </summary>
    public async Task<bool> IsUsedAsync(Guid categoryId, CancellationToken cancellationToken = default)
    {
        // 检查是否有Agent使用此分类
        var hasAgents = await _context.Set<Agent>()
            .AnyAsync(x => x.CategoryId == categoryId, cancellationToken);
            
        if (hasAgents)
            return true;
            
        // 检查是否有子分类
        var hasChildren = await _dbSet
            .AnyAsync(x => x.ParentId == categoryId, cancellationToken);
            
        return hasChildren;
    }
    
    /// <summary>
    /// 获取启用的分类
    /// </summary>
    public async Task<List<AgentCategory>> GetEnabledCategoriesAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.IsActive)
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }
    
    /// <summary>
    /// 检查分类名称是否存在
    /// </summary>
    public async Task<bool> IsNameExistsAsync(string name, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(x => x.Name == name);
        
        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }
        
        return await query.AnyAsync(cancellationToken);
    }
    
    private void BuildSubCategories(AgentCategory parent, ILookup<Guid?, AgentCategory> lookup)
    {
        var children = lookup[parent.Id].ToList();
        if (children.Any())
        {
            // Assign children to the parent category
            // Since Children is a collection property, we can't set it directly
            // The tree structure is built through the Parent-Children relationship
            foreach (var child in children)
            {
                BuildSubCategories(child, lookup);
            }
        }
    }
}