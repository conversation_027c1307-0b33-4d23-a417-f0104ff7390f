using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// Dify对话映射仓储实现
/// </summary>
public class DifyConversationMappingRepository : Repository<DifyConversationMapping>, IDifyConversationMappingRepository
{
    public DifyConversationMappingRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<DifyConversationMapping?> GetByConversationIdAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(x => x.ConversationId == conversationId, cancellationToken);
    }

    public async Task<DifyConversationMapping?> GetByDifyConversationIdAsync(string difyConversationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(x => x.DifyConversationId == difyConversationId, cancellationToken);
    }

    public async Task<IEnumerable<DifyConversationMapping>> GetByAgentIdAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.AgentId == agentId)
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> ExistsAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(x => x.ConversationId == conversationId, cancellationToken);
    }

    public async Task UpdateAsync(DifyConversationMapping mapping, CancellationToken cancellationToken = default)
    {
        Update(mapping);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var mapping = await GetByIdAsync(id, cancellationToken);
        if (mapping != null)
        {
            Remove(mapping);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}