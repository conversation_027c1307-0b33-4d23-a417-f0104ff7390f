using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.System;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class VerificationCodeRepository : Repository<VerificationCode>, IVerificationCodeRepository
{
    public VerificationCodeRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<VerificationCode?> GetLatestCodeAsync(
        string recipient, 
        string type, 
        CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await _dbSet
            .Where(v => 
                v.Recipient == recipient && 
                v.Type == type && 
                !v.IsUsed && 
                v.ExpiresAt > now)
            .OrderByDescending(v => v.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<bool> VerifyCodeAsync(
        string recipient, 
        string type, 
        string code, 
        CancellationToken cancellationToken = default)
    {
        var verificationCode = await GetLatestCodeAsync(recipient, type, cancellationToken);
        if (verificationCode == null || verificationCode.Code != code)
            return false;

        // Verify the code
        var isValid = verificationCode.Verify(code);
        _dbSet.Update(verificationCode);
        return isValid;
    }

    public async Task<bool> InvalidateCodesAsync(
        string recipient, 
        string type, 
        CancellationToken cancellationToken = default)
    {
        var codes = await _dbSet
            .Where(v => v.Recipient == recipient && v.Type == type && !v.IsUsed)
            .ToListAsync(cancellationToken);
        
        if (!codes.Any())
            return false;
            
        foreach (var code in codes)
        {
            code.Invalidate();
        }

        _dbSet.UpdateRange(codes);
        return true;
    }

    public async Task<int> GetRecentCodeCountAsync(
        string recipient, 
        TimeSpan timeWindow, 
        CancellationToken cancellationToken = default)
    {
        var since = DateTime.UtcNow.Subtract(timeWindow);
        return await _dbSet
            .CountAsync(v => 
                v.Recipient == recipient && 
                v.CreatedAt >= since, 
                cancellationToken);
    }

    public async Task<IEnumerable<VerificationCode>> GetExpiredCodesAsync(
        CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await _dbSet
            .Where(v => v.ExpiresAt <= now)
            .ToListAsync(cancellationToken);
    }
}