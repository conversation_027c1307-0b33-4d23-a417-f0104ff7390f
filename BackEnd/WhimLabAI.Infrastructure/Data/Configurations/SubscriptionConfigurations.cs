using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class SubscriptionPlanConfiguration : EntityBaseConfiguration<SubscriptionPlan>
{
    protected override void ConfigureEntity(EntityTypeBuilder<SubscriptionPlan> builder)
    {
        builder.ToTable("SubscriptionPlans");

        builder.Property(p => p.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(p => p.Tier)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(p => p.Description)
            .HasMaxLength(500);

        builder.Property(p => p.BillingCycle)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(p => p.TokenQuota)
            .IsRequired();

        builder.Property(p => p.IsActive)
            .IsRequired();

        builder.Property(p => p.IsPopular)
            .IsRequired();

        builder.Property(p => p.SortOrder)
            .IsRequired();

        builder.Property(p => p.MaxAgents)
            .IsRequired();

        builder.Property(p => p.MaxConversationsPerDay)
            .IsRequired();

        builder.Property(p => p.AllowCustomAgents)
            .IsRequired();

        builder.Property(p => p.AllowPlugins)
            .IsRequired();

        builder.Property(p => p.AllowKnowledgeBase)
            .IsRequired();

        // Configure Money value object
        builder.OwnsOne(p => p.Price, price => ValueObjectConfigurations.ConfigureMoney(price, "price", "currency"));

        // Configure Features as JSON
        builder.Property(p => p.Features)
            .HasColumnType("jsonb")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, new System.Text.Json.JsonSerializerOptions { PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase }),
                v => System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, new System.Text.Json.JsonSerializerOptions { PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase }) ?? new List<string>())
            .Metadata.SetValueComparer(new Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer<IReadOnlyCollection<string>>(
                (c1, c2) => c1 != null && c2 != null && c1.SequenceEqual(c2),
                c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                c => c.ToList()));

        // Configure Limits as JSON with specific types to avoid dynamic JSON requirement
        builder.Property(p => p.Limits)
            .HasColumnType("jsonb")
            .HasColumnType("jsonb");

        // Configure indexes
        builder.HasIndex(p => p.Name).IsUnique();
        builder.HasIndex(p => p.Tier);
        builder.HasIndex(p => p.IsActive);
    }
}

public class SubscriptionConfiguration : EntityBaseConfiguration<Subscription>
{
    protected override void ConfigureEntity(EntityTypeBuilder<Subscription> builder)
    {
        builder.ToTable("Subscriptions");

        builder.Property(s => s.CustomerUserId)
            .IsRequired();

        builder.Property(s => s.PlanId)
            .IsRequired();

        builder.Property(s => s.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(s => s.StartDate)
            .IsRequired();

        builder.Property(s => s.EndDate)
            .IsRequired();

        builder.Property(s => s.AutoRenew)
            .IsRequired();

        // Configure TokenQuota value object
        builder.OwnsOne(s => s.TokenQuota, quota => ValueObjectConfigurations.ConfigureTokenQuota(quota));

        // Configure relationships
        builder.HasOne(s => s.Plan)
            .WithMany()
            .HasForeignKey(s => s.PlanId)
            .OnDelete(DeleteBehavior.Restrict);

        // User navigation property doesn't exist - just configure the UserId foreign key

        // Configure indexes
        builder.HasIndex(s => s.CustomerUserId);
        builder.HasIndex(s => s.Status);
        builder.HasIndex(s => s.EndDate);
        builder.HasIndex(s => new { s.CustomerUserId, s.Status });
    }
}

public class UsageRecordConfiguration : EntityBaseConfiguration<UsageRecord>
{
    protected override void ConfigureEntity(EntityTypeBuilder<UsageRecord> builder)
    {
        builder.ToTable("UsageRecords");

        builder.Property(u => u.UserId)
            .IsRequired();

        builder.Property(u => u.SubscriptionId)
            .IsRequired();

        builder.Property(u => u.AgentId)
            .IsRequired();

        builder.Property(u => u.ConversationId)
            .IsRequired();

        builder.Property(u => u.ModelName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(u => u.ModelProvider)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(u => u.TokensUsed)
            .IsRequired();

        builder.Property(u => u.CostAmount)
            .HasPrecision(18, 6)
            .IsRequired();
            
        builder.Property(u => u.UsageTime)
            .IsRequired();
            
        builder.Property(u => u.RequestId)
            .HasMaxLength(100);
            
        builder.Property(u => u.SessionId)
            .HasMaxLength(100);

        builder.Property(u => u.Metadata)
            .HasColumnType("jsonb");

        // Configure relationships
        builder.HasOne(u => u.Subscription)
            .WithMany(s => s.UsageRecords)
            .HasForeignKey(u => u.SubscriptionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne<CustomerUser>()
            .WithMany()
            .HasForeignKey(u => u.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(u => u.UserId);
        builder.HasIndex(u => u.SubscriptionId);
        builder.HasIndex(u => u.CreatedAt);
        builder.HasIndex(u => new { u.UserId, u.CreatedAt });
        builder.HasIndex(u => new { u.SubscriptionId, u.CreatedAt });
    }
}