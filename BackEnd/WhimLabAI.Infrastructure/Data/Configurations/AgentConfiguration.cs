using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class AgentConfiguration : IEntityTypeConfiguration<WhimLabAI.Domain.Entities.Agent.Agent>
{
    public void Configure(EntityTypeBuilder<WhimLabAI.Domain.Entities.Agent.Agent> builder)
    {
        builder.ToTable("agents");

        builder.HasKey(a => a.Id);

        builder.Property(a => a.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(a => a.UniqueKey)
            .HasColumnName("unique_key")
            .HasMaxLength(100)
            .IsRequired();

        builder.HasIndex(a => a.UniqueKey)
            .IsUnique();

        builder.Property(a => a.Name)
            .HasColumnName("name")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(a => a.Description)
            .HasColumnName("description")
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(a => a.Icon)
            .HasColumnName("icon")
            .HasMaxLength(500);

        builder.Property(a => a.Cover)
            .HasColumnName("cover")
            .HasMaxLength(500);

        builder.Property(a => a.DetailedIntro)
            .HasColumnName("detailed_intro")
            .HasColumnType("text");

        builder.Property(a => a.CurrentVersionId)
            .HasColumnName("current_version_id");

        builder.Property(a => a.ViewCount)
            .HasColumnName("view_count")
            .IsRequired();

        builder.Property(a => a.AverageRating)
            .HasColumnName("average_rating")
            .HasPrecision(3, 1);

        builder.Property(a => a.RatingCount)
            .HasColumnName("rating_count")
            .IsRequired();

        builder.Property(a => a.ArchivedAt)
            .HasColumnName("archived_at");

        builder.Property(a => a.Metadata)
            .HasColumnName("metadata")
            .HasColumnType("jsonb");

        builder.Property(a => a.CreatorId)
            .HasColumnName("creator_id")
            .IsRequired();

        builder.Property(a => a.CategoryId)
            .HasColumnName("category_id");

        builder.Property(a => a.Status)
            .HasColumnName("status")
            .HasConversion<string>()
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(a => a.PublishedAt)
            .HasColumnName("published_at");

        builder.Property(a => a.UsageCount)
            .HasColumnName("usage_count")
            .IsRequired();

        builder.Property(a => a.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(a => a.UpdatedAt)
            .HasColumnName("updated_at");

        // Relationships
        builder.HasOne(a => a.Category)
            .WithMany()
            .HasForeignKey(a => a.CategoryId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure Tags as owned collection stored as JSON
        builder.OwnsMany(a => a.Tags, tags =>
        {
            tags.ToJson("tags");
            tags.Property(t => t.Name).HasMaxLength(50);
        });

        builder.HasMany(a => a.Versions)
            .WithOne()
            .HasForeignKey(v => v.AgentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(a => a.Ratings)
            .WithOne()
            .HasForeignKey("AgentId")
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(a => a.ApiKeys)
            .WithOne()
            .HasForeignKey(k => k.AgentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(a => a.CurrentVersion)
            .WithMany()
            .HasForeignKey(a => a.CurrentVersionId)
            .OnDelete(DeleteBehavior.Restrict);

        // Seed data removed - Agent has complex JSON property (Metadata) 
        // which cannot be properly seeded using HasData. Use DataSeeder instead.
    }
}