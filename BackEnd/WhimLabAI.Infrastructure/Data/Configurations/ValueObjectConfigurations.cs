using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public static class ValueObjectConfigurations
{
    public static void ConfigureMoney<TEntity>(
        OwnedNavigationBuilder<TEntity, Money> moneyBuilder,
        string amountColumnName = "Amount",
        string currencyColumnName = "Currency") where TEntity : class
    {
        moneyBuilder.Property(m => m.Amount)
            .HasColumnName(amountColumnName)
            .HasPrecision(18, 2)
            .IsRequired();

        moneyBuilder.Property(m => m.Currency)
            .HasColumnName(currencyColumnName)
            .HasMaxLength(3)
            .IsRequired();
    }

    public static void ConfigureEmail<TEntity>(
        OwnedNavigationBuilder<TEntity, WhimLabAI.Domain.ValueObjects.Email> emailBuilder,
        string columnName = "Email") where TEntity : class
    {
        emailBuilder.Property(e => e.Value)
            .HasColumnName(columnName)
            .HasMaxLength(255)
            .IsRequired();
    }

    public static void ConfigurePassword<TEntity>(
        OwnedNavigationBuilder<TEntity, Password> passwordBuilder,
        string columnName = "PasswordHash") where TEntity : class
    {
        passwordBuilder.Property(p => p.HashedValue)
            .HasColumnName(columnName)
            .HasMaxLength(255)
            .IsRequired();
    }

    public static void ConfigurePhoneNumber<TEntity>(
        OwnedNavigationBuilder<TEntity, PhoneNumber> phoneBuilder,
        string columnName = "Phone") where TEntity : class
    {
        phoneBuilder.Property(p => p.Value)
            .HasColumnName(columnName)
            .HasMaxLength(20);
    }

    public static void ConfigureTokenQuota<TEntity>(
        OwnedNavigationBuilder<TEntity, TokenQuota> tokenQuotaBuilder,
        string monthlyTokensColumn = "MonthlyTokens",
        string usedTokensColumn = "UsedTokens",
        string remainingTokensColumn = "RemainingTokens") where TEntity : class
    {
        tokenQuotaBuilder.Property(t => t.Total)
            .HasColumnName(monthlyTokensColumn)
            .IsRequired();

        tokenQuotaBuilder.Property(t => t.Used)
            .HasColumnName(usedTokensColumn)
            .IsRequired();

        // Remaining is a computed property, no need to map it to database
        tokenQuotaBuilder.Ignore(t => t.Remaining);
    }

    public static void ConfigureModelConfiguration(EntityTypeBuilder<Domain.Entities.Agent.AgentVersion> builder)
    {
        builder.OwnsOne(v => v.ModelConfig, mc =>
        {
            mc.Property(m => m.ModelType).HasColumnName("ModelType").HasMaxLength(50);
            mc.Property(m => m.ModelName).HasColumnName("ModelName").HasMaxLength(100);
            mc.Property(m => m.Temperature).HasColumnName("Temperature");
            mc.Property(m => m.MaxTokens).HasColumnName("MaxTokens");
            mc.Property(m => m.TopP).HasColumnName("TopP");
            mc.Property(m => m.FrequencyPenalty).HasColumnName("FrequencyPenalty");
            mc.Property(m => m.PresencePenalty).HasColumnName("PresencePenalty");
            mc.Property(m => m.AdditionalSettings)
                .HasColumnName("AdditionalSettings")
                .HasColumnType("jsonb");
        });
    }
}