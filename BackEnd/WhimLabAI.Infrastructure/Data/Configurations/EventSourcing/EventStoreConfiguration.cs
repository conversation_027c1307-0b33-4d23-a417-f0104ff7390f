using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.EventSourcing;

namespace WhimLabAI.Infrastructure.Data.Configurations.EventSourcing;

public class EventStoreConfiguration : IEntityTypeConfiguration<EventStore>
{
    public void Configure(EntityTypeBuilder<EventStore> builder)
    {
        builder.ToTable("EventStores");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.AggregateId)
            .IsRequired();

        builder.Property(e => e.AggregateType)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(e => e.EventType)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(e => e.EventData)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(e => e.Metadata)
            .HasColumnType("jsonb");

        builder.Property(e => e.Version)
            .IsRequired();

        builder.Property(e => e.OccurredOn)
            .IsRequired();

        builder.Property(e => e.UserId)
            .HasMaxLength(100);

        builder.Property(e => e.CorrelationId)
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(e => e.AggregateId);
        builder.HasIndex(e => new { e.AggregateId, e.Version })
            .IsUnique();
        builder.HasIndex(e => e.OccurredOn);
        builder.HasIndex(e => e.EventType);
        builder.HasIndex(e => e.CorrelationId);
    }
}