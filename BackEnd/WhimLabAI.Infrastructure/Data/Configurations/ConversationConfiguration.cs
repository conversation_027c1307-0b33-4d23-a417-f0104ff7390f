using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Conversation;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class ConversationConfiguration : IEntityTypeConfiguration<Conversation>
{
    public void Configure(EntityTypeBuilder<Conversation> builder)
    {
        builder.ToTable("conversations");

        builder.HasKey(c => c.Id);

        builder.Property(c => c.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(c => c.CustomerUserId)
            .HasColumnName("user_id")
            .IsRequired();

        builder.Property(c => c.AgentId)
            .HasColumnName("agent_id")
            .IsRequired();

        builder.Property(c => c.Title)
            .HasColumnName("title")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(c => c.StartedAt)
            .HasColumnName("started_at")
            .IsRequired();

        builder.Property(c => c.Model)
            .HasColumnName("model")
            .HasMaxLength(50);

        builder.Property(c => c.IsArchived)
            .HasColumnName("is_archived")
            .IsRequired();

        // IsPinned property removed - doesn't exist in entity

        builder.Property(c => c.LastMessageAt)
            .HasColumnName("last_message_at");

        builder.Property(c => c.MessageCount)
            .HasColumnName("message_count")
            .IsRequired();

        builder.Property(c => c.TotalTokens)
            .HasColumnName("total_tokens")
            .IsRequired();

        builder.Property(c => c.InputTokens)
            .HasColumnName("input_tokens")
            .IsRequired();

        builder.Property(c => c.OutputTokens)
            .HasColumnName("output_tokens")
            .IsRequired();

        builder.Property(c => c.Metadata)
            .HasColumnName("metadata")
            .HasColumnType("jsonb");

        builder.Property(c => c.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(c => c.UpdatedAt)
            .HasColumnName("updated_at");

        // Relationships
        builder.HasMany(c => c.Messages)
            .WithOne()
            .HasForeignKey(m => m.ConversationId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(c => c.CustomerUserId);
        builder.HasIndex(c => c.AgentId);
        builder.HasIndex(c => new { c.CustomerUserId, c.IsArchived });
        builder.HasIndex(c => c.LastMessageAt);
    }
}