using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Notification;

namespace WhimLabAI.Infrastructure.Data.Configurations;

/// <summary>
/// 通知实体配置
/// </summary>
public class NotificationConfiguration : IEntityTypeConfiguration<Notification>
{
    public void Configure(EntityTypeBuilder<Notification> builder)
    {
        builder.ToTable("notifications");
        
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.Id)
            .HasColumnName("id")
            .IsRequired();
        
        builder.Property(e => e.UserId)
            .HasColumnName("user_id")
            .IsRequired();
        
        builder.Property(e => e.Title)
            .HasColumnName("title")
            .HasMaxLength(200)
            .IsRequired();
        
        builder.Property(e => e.Content)
            .HasColumnName("content")
            .HasMaxLength(1000)
            .IsRequired();
        
        builder.Property(e => e.Type)
            .HasColumnName("type")
            .HasMaxLength(50)
            .IsRequired();
        
        builder.Property(e => e.Level)
            .HasColumnName("level")
            .HasMaxLength(20)
            .IsRequired();
        
        builder.Property(e => e.IsRead)
            .HasColumnName("is_read")
            .IsRequired();
        
        builder.Property(e => e.ReadAt)
            .HasColumnName("read_at");
        
        builder.Property(e => e.MetadataJson)
            .HasColumnName("metadata_json")
            .HasColumnType("jsonb");
        
        builder.Property(e => e.ExpiresAt)
            .HasColumnName("expires_at");
        
        builder.Property(e => e.IsDeleted)
            .HasColumnName("is_deleted")
            .IsRequired();
        
        builder.Property(e => e.DeletedAt)
            .HasColumnName("deleted_at");
        
        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();
        
        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at");
        
        builder.Property(e => e.UpdatedBy)
            .HasColumnName("updated_by")
            .HasMaxLength(100);
        
        // 索引
        builder.HasIndex(e => e.UserId)
            .HasDatabaseName("idx_notifications_user_id");
        
        builder.HasIndex(e => new { e.UserId, e.IsRead, e.IsDeleted })
            .HasDatabaseName("idx_notifications_user_read_deleted");
        
        builder.HasIndex(e => e.Type)
            .HasDatabaseName("idx_notifications_type");
        
        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("idx_notifications_created_at");
        
        builder.HasIndex(e => e.ExpiresAt)
            .HasDatabaseName("idx_notifications_expires_at");
        
        // 全局查询过滤器（排除已删除的）
        builder.HasQueryFilter(e => !e.IsDeleted);
    }
}