using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class AdminUserRoleConfiguration : IEntityTypeConfiguration<AdminUserRole>
{
    public void Configure(EntityTypeBuilder<AdminUserRole> builder)
    {
        builder.ToTable("admin_user_roles");

        // Temporarily use composite key to match actual database
        builder.HasKey(ur => new { ur.AdminUserId, ur.RoleId });
        builder.Ignore(ur => ur.Id); // Ignore the Id property for now
        
        builder.Property(ur => ur.AdminUserId)
            .HasColumnName("user_id") // actual column name in database
            .IsRequired();
            
        builder.Property(ur => ur.RoleId)
            .HasColumnName("role_id")
            .IsRequired();
            
        // Ignore properties that don't exist in current database
        builder.Ignore(ur => ur.AssignedAt);
        builder.Ignore(ur => ur.AssignedBy);
        builder.Ignore(ur => ur.UpdatedAt);
        builder.Ignore(ur => ur.UpdatedBy);
            
        builder.Property(ur => ur.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        // Create unique index on admin_user_id and role_id
        builder.HasIndex(ur => new { ur.AdminUserId, ur.RoleId })
            .IsUnique()
            .HasDatabaseName("ix_admin_user_roles_admin_user_role");

        // Configure relationships
        builder.HasOne(ur => ur.AdminUser)
            .WithMany(u => u.UserRoles)
            .HasForeignKey(ur => ur.AdminUserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ur => ur.Role)
            .WithMany()
            .HasForeignKey(ur => ur.RoleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}