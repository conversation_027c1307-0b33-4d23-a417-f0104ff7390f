using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Agent;

namespace WhimLabAI.Infrastructure.Data.Configurations.Agent;

public class AgentVersionUsageStatsConfiguration : IEntityTypeConfiguration<AgentVersionUsageStats>
{
    public void Configure(EntityTypeBuilder<AgentVersionUsageStats> builder)
    {
        builder.ToTable("AgentVersionUsageStats");

        builder.HasKey(s => s.Id);

        builder.Property(s => s.AgentVersionId)
            .IsRequired();

        builder.Property(s => s.Date)
            .IsRequired()
            .HasColumnType("date");

        builder.Property(s => s.ConversationCount)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(s => s.MessageCount)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(s => s.TotalTokensConsumed)
            .IsRequired()
            .HasDefaultValue(0L);

        builder.Property(s => s.UniqueUserCount)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(s => s.AverageResponseTime)
            .IsRequired()
            .HasDefaultValue(0.0);

        builder.Property(s => s.ErrorCount)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(s => s.SuccessRate)
            .IsRequired()
            .HasDefaultValue(100.0);

        // Relationship
        builder.HasOne(s => s.AgentVersion)
            .WithMany(v => v.UsageStats)
            .HasForeignKey(s => s.AgentVersionId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(s => new { s.AgentVersionId, s.Date })
            .IsUnique();
        
        builder.HasIndex(s => s.Date);
    }
}