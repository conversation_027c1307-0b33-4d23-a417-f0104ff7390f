using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Agent;

namespace WhimLabAI.Infrastructure.Data.Configurations.Agent;

public class AgentVersionReviewConfiguration : IEntityTypeConfiguration<AgentVersionReview>
{
    public void Configure(EntityTypeBuilder<AgentVersionReview> builder)
    {
        builder.ToTable("AgentVersionReviews");

        builder.HasKey(r => r.Id);

        builder.Property(r => r.AgentVersionId)
            .IsRequired();

        builder.Property(r => r.ReviewerId)
            .IsRequired();

        builder.Property(r => r.Status)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(r => r.Comments)
            .HasMaxLength(1000);

        builder.Property(r => r.ReviewedAt)
            .IsRequired();

        builder.Property(r => r.ReviewDetails)
            .HasColumnType("jsonb");

        // Relationship
        builder.HasOne(r => r.AgentVersion)
            .WithMany(v => v.Reviews)
            .HasForeignKey(r => r.AgentVersionId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(r => r.AgentVersionId);
        builder.HasIndex(r => r.ReviewerId);
        builder.HasIndex(r => r.ReviewedAt);
    }
}