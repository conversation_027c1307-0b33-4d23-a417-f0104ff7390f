using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Agent;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class AgentRatingConfiguration : IEntityTypeConfiguration<AgentRating>
{
    public void Configure(EntityTypeBuilder<AgentRating> builder)
    {
        builder.ToTable("agent_ratings");

        builder.<PERSON><PERSON>ey(r => r.Id);

        builder.Property(r => r.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(r => r.UserId)
            .HasColumnName("user_id")
            .IsRequired();

        builder.Property(r => r.Score)
            .HasColumnName("score")
            .IsRequired();

        builder.Property(r => r.Feedback)
            .HasColumnName("feedback")
            .HasMaxLength(500);

        builder.Property(r => r.IsVerifiedPurchase)
            .HasColumnName("is_verified_purchase")
            .IsRequired();

        builder.Property(r => r.HelpfulCount)
            .HasColumnName("helpful_count")
            .IsRequired();

        builder.Property(r => r.UnhelpfulCount)
            .HasColumnName("unhelpful_count")
            .IsRequired();

        builder.Property(r => r.RatedAt)
            .HasColumnName("rated_at")
            .IsRequired();

        builder.Property(r => r.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(r => r.UpdatedAt)
            .HasColumnName("updated_at");

        // Index for filtering by user
        builder.HasIndex(r => r.UserId);

        // Index for filtering by score
        builder.HasIndex(r => r.Score);
    }
}