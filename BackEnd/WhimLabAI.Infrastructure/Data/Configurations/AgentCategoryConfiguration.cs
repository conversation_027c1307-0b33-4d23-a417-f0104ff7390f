using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Agent;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class AgentCategoryConfiguration : IEntityTypeConfiguration<AgentCategory>
{
    public void Configure(EntityTypeBuilder<AgentCategory> builder)
    {
        builder.ToTable("agent_categories");

        builder.HasKey(c => c.Id);

        builder.Property(c => c.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(c => c.Name)
            .HasColumnName("name")
            .HasMaxLength(50)
            .IsRequired();

        builder.HasIndex(c => c.Name)
            .IsUnique();

        builder.Property(c => c.DisplayName)
            .HasColumnName("display_name")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(c => c.Description)
            .HasColumnName("description")
            .HasMaxLength(200);

        builder.Property(c => c.Icon)
            .HasColumnName("icon")
            .HasMaxLength(50);

        builder.Property(c => c.SortOrder)
            .HasColumnName("sort_order")
            .IsRequired();

        builder.Property(c => c.IsActive)
            .HasColumnName("is_active")
            .IsRequired();

        builder.Property(c => c.AgentCount)
            .HasColumnName("agent_count")
            .IsRequired();

        builder.Property(c => c.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(c => c.UpdatedAt)
            .HasColumnName("updated_at");

        // Seed data - Agent categories
        builder.HasData(
            new
            {
                Id = Guid.Parse("10000000-0000-0000-0000-000000000001"),
                Name = "programming-development",
                DisplayName = "编程开发",
                Description = "编程、开发、调试相关的AI助手",
                Icon = "code",
                SortOrder = 1,
                IsActive = true,
                AgentCount = 0,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
            },
            new
            {
                Id = Guid.Parse("10000000-0000-0000-0000-000000000002"),
                Name = "creative-writing",
                DisplayName = "写作创作",
                Description = "文章写作、内容创作相关的AI助手",
                Icon = "edit",
                SortOrder = 2,
                IsActive = true,
                AgentCount = 0,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
            },
            new
            {
                Id = Guid.Parse("10000000-0000-0000-0000-000000000003"),
                Name = "translation-language",
                DisplayName = "翻译语言",
                Description = "多语言翻译、语言学习相关的AI助手",
                Icon = "translate",
                SortOrder = 3,
                IsActive = true,
                AgentCount = 0,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
            },
            new
            {
                Id = Guid.Parse("10000000-0000-0000-0000-000000000004"),
                Name = "data-analysis",
                DisplayName = "数据分析",
                Description = "数据处理、分析、可视化相关的AI助手",
                Icon = "analytics",
                SortOrder = 4,
                IsActive = true,
                AgentCount = 0,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
            },
            new
            {
                Id = Guid.Parse("10000000-0000-0000-0000-000000000005"),
                Name = "education-learning",
                DisplayName = "教育学习",
                Description = "教育、培训、学习辅导相关的AI助手",
                Icon = "school",
                SortOrder = 5,
                IsActive = true,
                AgentCount = 0,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
            },
            new
            {
                Id = Guid.Parse("10000000-0000-0000-0000-000000000006"),
                Name = "life-assistant",
                DisplayName = "生活助理",
                Description = "日常生活、个人助理相关的AI助手",
                Icon = "assistant",
                SortOrder = 6,
                IsActive = true,
                AgentCount = 0,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
            },
            new
            {
                Id = Guid.Parse("10000000-0000-0000-0000-000000000007"),
                Name = "business-finance",
                DisplayName = "商业金融",
                Description = "商业分析、金融咨询相关的AI助手",
                Icon = "business",
                SortOrder = 7,
                IsActive = true,
                AgentCount = 0,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
            },
            new
            {
                Id = Guid.Parse("10000000-0000-0000-0000-000000000008"),
                Name = "others",
                DisplayName = "其他",
                Description = "其他类型的AI助手",
                Icon = "more",
                SortOrder = 99,
                IsActive = true,
                AgentCount = 0,
                CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
            }
        );
    }
}