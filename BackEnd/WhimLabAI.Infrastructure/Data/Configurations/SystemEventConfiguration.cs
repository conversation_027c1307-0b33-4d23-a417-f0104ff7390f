using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.System;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class SystemEventConfiguration : IEntityTypeConfiguration<SystemEvent>
{
    public void Configure(EntityTypeBuilder<SystemEvent> builder)
    {
        builder.ToTable("system_events");
        
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.EventType)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(e => e.EventName)
            .IsRequired()
            .HasMaxLength(255);
            
        builder.Property(e => e.EventData)
            .HasColumnType("jsonb")
            .IsRequired();
            
        builder.Property(e => e.OccurredAt)
            .IsRequired();
            
        builder.Property(e => e.SourceSystem)
            .HasMaxLength(100);
            
        builder.Property(e => e.CorrelationId)
            .HasMaxLength(255);
        
        // Indexes for better query performance
        builder.HasIndex(e => e.EventType);
        builder.HasIndex(e => e.EventName);
        builder.HasIndex(e => e.OccurredAt);
        builder.HasIndex(e => e.CorrelationId)
            .HasFilter("\"CorrelationId\" IS NOT NULL");
    }
}