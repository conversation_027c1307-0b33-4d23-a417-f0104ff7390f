using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class CustomerUserConfiguration : IEntityTypeConfiguration<CustomerUser>
{
    public void Configure(EntityTypeBuilder<CustomerUser> builder)
    {
        builder.ToTable("customer_users");

        builder.<PERSON><PERSON><PERSON>(u => u.Id);
        builder.Property(u => u.Id).HasColumnName("id");

        builder.Property(u => u.Username)
            .HasColumnName("username")
            .HasMaxLength(50)
            .IsRequired();

        builder.HasIndex(u => u.Username)
            .IsUnique()
            .HasDatabaseName("ix_customer_users_username");

        builder.OwnsOne(u => u.Email, email =>
        {
            email.Property(e => e.Value)
                .HasColumnName("email")
                .HasMaxLength(255);
            
            email.HasIndex(e => e.Value)
                .HasDatabaseName("ix_customer_users_email");
        });

        builder.OwnsOne(u => u.Phone, phone =>
        {
            phone.Property(p => p.Value)
                .HasColumnName("phone_number")
                .HasMaxLength(20);
            phone.Property(p => p.CountryCode)
                .HasColumnName("country_code")
                .HasMaxLength(5)
                .HasDefaultValue("+86");
                
            phone.HasIndex(p => p.Value)
                .HasDatabaseName("ix_customer_users_phone_number");
        });

        builder.OwnsOne(u => u.PasswordHash, password =>
        {
            password.Property(p => p.HashedValue)
                .HasColumnName("password_hash")
                .HasMaxLength(255)
                .IsRequired();
        });

        builder.Property(u => u.IsActive)
            .HasColumnName("is_active")
            .IsRequired();

        builder.Property(u => u.IsBanned)
            .HasColumnName("is_banned")
            .IsRequired();

        builder.Property(u => u.IsEmailVerified)
            .HasColumnName("is_email_verified")
            .IsRequired();

        builder.Property(u => u.IsPhoneVerified)
            .HasColumnName("is_phone_verified")
            .IsRequired();

        builder.Property(u => u.LastLoginAt)
            .HasColumnName("last_login_at");

        builder.Property(u => u.LastLoginIp)
            .HasColumnName("last_login_ip")
            .HasMaxLength(45);

        builder.Property<int>("_loginFailedCount")
            .HasColumnName("login_failed_count")
            .HasField("_loginFailedCount")
            .IsRequired();

        builder.Property<DateTime?>("_lockedUntil")
            .HasColumnName("locked_until")
            .HasField("_lockedUntil");

        builder.Property<string?>("_refreshToken")
            .HasColumnName("refresh_token")
            .HasMaxLength(500)
            .HasField("_refreshToken");

        builder.Property<DateTime?>("_refreshTokenExpiry")
            .HasColumnName("refresh_token_expiry")
            .HasField("_refreshTokenExpiry");

        builder.Property(u => u.TwoFactorEnabled)
            .HasColumnName("two_factor_enabled")
            .IsRequired();

        builder.Property<string?>("_twoFactorSecret")
            .HasColumnName("two_factor_secret")
            .HasField("_twoFactorSecret")
            .HasMaxLength(100);

        builder.Property(u => u.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(u => u.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        // Configure relationships
        builder.HasOne(u => u.Profile)
            .WithOne(p => p.User)
            .HasForeignKey<CustomerProfile>(p => p.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // LoginHistories doesn't exist on CustomerUser entity

        builder.HasMany(u => u.DeviceAuthorizations)
            .WithOne()
            .OnDelete(DeleteBehavior.Cascade);

        // NotificationSetting is a single value object
        builder.OwnsOne(u => u.NotificationSetting, ns =>
        {
            ns.Property(n => n.EmailNotification)
                .HasColumnName("notification_email")
                .HasDefaultValue(true);
            ns.Property(n => n.SmsNotification)
                .HasColumnName("notification_sms")
                .HasDefaultValue(false);
            ns.Property(n => n.SystemNotification)
                .HasColumnName("notification_system")
                .HasDefaultValue(true);
            ns.Property(n => n.PromotionNotification)
                .HasColumnName("notification_promotion")
                .HasDefaultValue(false);
            ns.Property(n => n.SecurityAlert)
                .HasColumnName("notification_security")
                .HasDefaultValue(true);
            ns.Property(n => n.QuotaAlert)
                .HasColumnName("notification_quota")
                .HasDefaultValue(true);
            ns.Property(n => n.NewFeatureNotification)
                .HasColumnName("notification_features")
                .HasDefaultValue(true);
            ns.Property(n => n.NewsletterSubscription)
                .HasColumnName("notification_newsletter")
                .HasDefaultValue(false);
        });

        builder.HasMany(u => u.OAuthBindings)
            .WithOne()
            .OnDelete(DeleteBehavior.Cascade);

        // Seed data removed - CustomerUser has owned types (Email, Phone, PasswordHash) 
        // which cannot be seeded using HasData. Use DataSeeder instead.
    }
}