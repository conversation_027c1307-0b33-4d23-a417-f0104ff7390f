using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class AgentVersionConfiguration : IEntityTypeConfiguration<AgentVersion>
{
    public void Configure(EntityTypeBuilder<AgentVersion> builder)
    {
        builder.ToTable("agent_versions");

        builder.<PERSON><PERSON>ey(v => v.Id);

        builder.Property(v => v.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(v => v.AgentId)
            .HasColumnName("agent_id")
            .IsRequired();

        builder.Property(v => v.VersionNumber)
            .HasColumnName("version_number")
            .IsRequired();

        builder.Property(v => v.SystemPrompt)
            .HasColumnName("system_prompt")
            .HasColumnType("text");

        builder.Property(v => v.UserPrompt)
            .HasColumnName("user_prompt")
            .HasColumnType("text");

        builder.Property(v => v.ChangeLog)
            .HasColumnName("change_log")
            .HasMaxLength(1000);

        builder.Property(v => v.ModelConfig)
            .HasColumnName("model_config")
            .HasColumnType("jsonb")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Domain.ValueObjects.ModelConfiguration>(v, (System.Text.Json.JsonSerializerOptions?)null));

        builder.Property(v => v.Plugins)
            .HasColumnName("plugins")
            .HasColumnType("jsonb")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, new System.Text.Json.JsonSerializerOptions { PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase }),
                v => System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, new System.Text.Json.JsonSerializerOptions { PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase }) ?? new List<string>())
            .Metadata.SetValueComparer(new Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer<IReadOnlyCollection<string>>(
                (c1, c2) => c1 != null && c2 != null && c1.SequenceEqual(c2),
                c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                c => c.ToList()));

        builder.Property(v => v.KnowledgeBases)
            .HasColumnName("knowledge_bases")
            .HasColumnType("jsonb")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, new System.Text.Json.JsonSerializerOptions { PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase }),
                v => System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, new System.Text.Json.JsonSerializerOptions { PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase }) ?? new List<string>())
            .Metadata.SetValueComparer(new Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer<IReadOnlyCollection<string>>(
                (c1, c2) => c1 != null && c2 != null && c1.SequenceEqual(c2),
                c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                c => c.ToList()));

        builder.Property(v => v.Status)
            .HasColumnName("status")
            .HasConversion<string>()
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(v => v.PublishedAt)
            .HasColumnName("published_at");

        builder.Property(v => v.PublishedBy)
            .HasColumnName("published_by")
            .HasMaxLength(100);

        builder.Property(v => v.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(v => v.UpdatedAt)
            .HasColumnName("updated_at");

        // New approval workflow fields
        builder.Property(v => v.ReviewStatus)
            .HasColumnName("review_status")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired()
            .HasDefaultValue(ReviewStatus.Pending);

        builder.Property(v => v.SubmittedForReviewAt)
            .HasColumnName("submitted_for_review_at");

        builder.Property(v => v.LastPublishAttemptAt)
            .HasColumnName("last_publish_attempt_at");

        builder.Property(v => v.PublishAttemptCount)
            .HasColumnName("publish_attempt_count")
            .IsRequired()
            .HasDefaultValue(0);

        // Relationship
        builder.HasOne(v => v.Agent)
            .WithMany(a => a.Versions)
            .HasForeignKey(v => v.AgentId)
            .OnDelete(DeleteBehavior.Cascade);

        // Index for agent_id and version_number
        builder.HasIndex(v => new { v.AgentId, v.VersionNumber })
            .IsUnique();

        // Index for status queries
        builder.HasIndex(v => v.Status);
        
        // Index for review status queries
        builder.HasIndex(v => v.ReviewStatus);
        
        // Index for submitted for review date
        builder.HasIndex(v => v.SubmittedForReviewAt);
    }
}