using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class QRCodeSessionConfiguration : IEntityTypeConfiguration<QRCodeSession>
{
    public void Configure(EntityTypeBuilder<QRCodeSession> builder)
    {
        builder.ToTable("QRCodeSessions");
        
        builder.HasKey(q => q.Id);
        
        builder.Property(q => q.SessionId)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.HasIndex(q => q.SessionId)
            .IsUnique();
            
        builder.Property(q => q.QRCodeData)
            .IsRequired()
            .HasMaxLength(1000);
            
        builder.Property(q => q.Status)
            .IsRequired()
            .HasConversion<string>();
            
        builder.Property(q => q.ExpiresAt)
            .IsRequired();
            
        builder.Property(q => q.ClientIp)
            .HasMaxLength(45); // IPv6 length
            
        builder.Property(q => q.UserAgent)
            .HasMaxLength(500);
            
        builder.Property(q => q.DeviceId)
            .HasMaxLength(100);
            
        builder.Property(q => q.DeviceName)
            .HasMaxLength(200);
            
        builder.Property(q => q.DeviceType)
            .HasMaxLength(50);
            
        builder.Property(q => q.ScannedByDeviceId)
            .HasMaxLength(100);
            
        builder.Property(q => q.ScannedByIp)
            .HasMaxLength(45);
            
        builder.Property(q => q.CreatedAt)
            .IsRequired();
            
        builder.Property(q => q.UpdatedAt)
            .IsRequired();
            
        // Indexes for performance
        builder.HasIndex(q => q.Status);
        builder.HasIndex(q => q.ExpiresAt);
        builder.HasIndex(q => q.UserId);
        builder.HasIndex(q => new { q.Status, q.ExpiresAt });
        
        // Relationships
        builder.HasOne(q => q.User)
            .WithMany()
            .HasForeignKey(q => q.UserId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}