using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Entities.Auth;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class AdminUserConfiguration : IEntityTypeConfiguration<AdminUser>
{
    public void Configure(EntityTypeBuilder<AdminUser> builder)
    {
        builder.ToTable("admin_users");

        builder.<PERSON><PERSON>ey(u => u.Id);
        builder.Property(u => u.Id).HasColumnName("id");

        builder.Property(u => u.Username)
            .HasColumnName("username")
            .HasMaxLength(50)
            .IsRequired();

        builder.HasIndex(u => u.Username)
            .IsUnique()
            .HasDatabaseName("ix_admin_users_username");

        builder.Property(u => u.Email)
            .HasColumnName("email")
            .HasMaxLength(255)
            .IsRequired();

        builder.HasIndex(u => u.Email)
            .HasDatabaseName("ix_admin_users_email");

        // Configure Phone value object
        builder.OwnsOne(u => u.Phone, phone =>
        {
            phone.Property(p => p.Value)
                .HasColumnName("phone_number")
                .HasMaxLength(20);
                
            phone.Property(p => p.CountryCode)
                .HasColumnName("phone_country_code")
                .HasMaxLength(5);
        });

        builder.Property(u => u.PasswordHash)
            .HasColumnName("password_hash")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(u => u.Nickname)
            .HasColumnName("nickname")
            .HasMaxLength(50);

        builder.Property(u => u.Avatar)
            .HasColumnName("avatar")
            .HasMaxLength(500);

        builder.Property(u => u.Status)
            .HasColumnName("status")
            .HasConversion<string>()
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(u => u.IsSuperAdmin)
            .HasColumnName("is_super")
            .IsRequired();

        builder.Property(u => u.LastLoginAt)
            .HasColumnName("last_login_at");

        builder.Property(u => u.LastLoginIp)
            .HasColumnName("last_login_ip")
            .HasMaxLength(45);

        builder.Property(u => u.FailedLoginAttempts)
            .HasColumnName("login_failed_count")
            .IsRequired();

        builder.Property(u => u.LockedUntil)
            .HasColumnName("locked_until");

        builder.Property(u => u.PasswordExpiredAt)
            .HasColumnName("password_expired_at");

        builder.Property(u => u.TwoFactorEnabled)
            .HasColumnName("two_factor_enabled")
            .IsRequired();

        builder.Property(u => u.TwoFactorSecret)
            .HasColumnName("two_factor_secret")
            .HasMaxLength(100);

        // IP白名单配置
        builder.Property(u => u.EnableIpWhitelist)
            .HasColumnName("enable_ip_whitelist")
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(u => u.IpWhitelist)
            .HasColumnName("ip_whitelist")
            .HasColumnType("jsonb");

        builder.Property(u => u.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(u => u.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        // CreatedBy and UpdatedBy don't exist in AdminUser entity

        // 配置与AdminUserRole的关系
        builder.HasMany(u => u.UserRoles)
            .WithOne(ur => ur.AdminUser)
            .HasForeignKey(ur => ur.AdminUserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}