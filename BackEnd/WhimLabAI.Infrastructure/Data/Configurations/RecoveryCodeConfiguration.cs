using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class RecoveryCodeConfiguration : IEntityTypeConfiguration<RecoveryCode>
{
    public void Configure(EntityTypeBuilder<RecoveryCode> builder)
    {
        builder.ToTable("recovery_codes");

        builder.HasKey(rc => rc.Id);
        builder.Property(rc => rc.Id).HasColumnName("id");

        builder.Property(rc => rc.UserId)
            .HasColumnName("user_id")
            .IsRequired();

        builder.Property(rc => rc.Code)
            .HasColumnName("code")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(rc => rc.IsUsed)
            .HasColumnName("is_used")
            .IsRequired();

        builder.Property(rc => rc.UsedAt)
            .HasColumnName("used_at");

        builder.Property(rc => rc.UsedByIp)
            .HasColumnName("used_by_ip")
            .HasMaxLength(45);

        builder.Property(rc => rc.ExpiresAt)
            .HasColumnName("expires_at")
            .IsRequired();

        builder.Property(rc => rc.UserType)
            .HasColumnName("user_type")
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(rc => rc.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(rc => rc.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        // Indexes
        builder.HasIndex(rc => rc.Code)
            .IsUnique()
            .HasDatabaseName("ix_recovery_codes_code");

        builder.HasIndex(rc => new { rc.UserId, rc.UserType })
            .HasDatabaseName("ix_recovery_codes_user_id_type");

        builder.HasIndex(rc => new { rc.UserId, rc.UserType, rc.IsUsed })
            .HasDatabaseName("ix_recovery_codes_user_id_type_is_used");

        // Relationships
        builder.HasOne(rc => rc.CustomerUser)
            .WithMany(u => u.RecoveryCodes)
            .HasForeignKey(rc => rc.UserId)
            .HasPrincipalKey(u => u.Id)
            .OnDelete(DeleteBehavior.Cascade)
            .HasConstraintName("fk_recovery_codes_customer_users");

        builder.HasOne(rc => rc.AdminUser)
            .WithMany(u => u.RecoveryCodes)
            .HasForeignKey(rc => rc.UserId)
            .HasPrincipalKey(u => u.Id)
            .OnDelete(DeleteBehavior.Cascade)
            .HasConstraintName("fk_recovery_codes_admin_users");
    }
}