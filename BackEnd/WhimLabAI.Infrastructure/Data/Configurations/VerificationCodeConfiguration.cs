using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.System;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class VerificationCodeConfiguration : IEntityTypeConfiguration<VerificationCode>
{
    public void Configure(EntityTypeBuilder<VerificationCode> builder)
    {
        builder.ToTable("verification_codes");
        
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.Recipient)
            .IsRequired()
            .HasMaxLength(255);
            
        builder.Property(e => e.Code)
            .IsRequired()
            .HasMaxLength(10);
            
        builder.Property(e => e.Type)
            .IsRequired()
            .HasMaxLength(50);
            
        builder.Property(e => e.ExpiresAt)
            .IsRequired();
            
        builder.Property(e => e.IsUsed)
            .IsRequired()
            .HasDefaultValue(false);
            
        builder.Property(e => e.UsedAt);
        
        builder.Property(e => e.AttemptCount)
            .IsRequired()
            .HasDefaultValue(0);
            
        builder.Property(e => e.IpAddress)
            .HasMaxLength(45); // Support IPv6
            
        builder.Property(e => e.UserAgent)
            .HasMaxLength(500);
        
        // Indexes for better query performance
        builder.HasIndex(e => new { e.Recipient, e.Type, e.IsUsed });
        builder.HasIndex(e => e.ExpiresAt);
        builder.HasIndex(e => new { e.Code, e.Recipient, e.Type })
            .HasFilter("\"IsUsed\" = false");
    }
}