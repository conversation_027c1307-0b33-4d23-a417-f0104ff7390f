using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Payment;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class CouponUsageConfiguration : IEntityTypeConfiguration<CouponUsage>
{
    public void Configure(EntityTypeBuilder<CouponUsage> builder)
    {
        builder.ToTable("CouponUsages");
        
        builder.<PERSON><PERSON><PERSON>(cu => cu.Id);
        
        builder.Property(cu => cu.UserId)
            .IsRequired();
            
        builder.HasIndex(cu => cu.UserId);
            
        builder.Property(cu => cu.OrderId)
            .IsRequired();
            
        builder.HasIndex(cu => cu.OrderId);
            
        builder.OwnsOne(cu => cu.DiscountAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("DiscountAmount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();
            money.Property(m => m.Currency)
                .HasColumnName("DiscountCurrency")
                .HasMaxLength(3)
                .IsRequired();
        });
        
        builder.Property(cu => cu.UsedAt)
            .IsRequired();
            
        builder.HasIndex(cu => new { cu.UserId, cu.UsedAt });
        
        builder.Property(cu => cu.CreatedAt)
            .IsRequired();
            
        builder.Property(cu => cu.UpdatedAt)
            .IsRequired();
    }
}