using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.User;
using System.Linq;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class CustomerProfileConfiguration : IEntityTypeConfiguration<CustomerProfile>
{
    public void Configure(EntityTypeBuilder<CustomerProfile> builder)
    {
        builder.ToTable("customer_profiles");

        builder.HasKey(p => p.Id);
        builder.Property(p => p.Id).HasColumnName("id");

        builder.Property(p => p.UserId)
            .HasColumnName("user_id")
            .IsRequired();

        builder.HasIndex(p => p.UserId)
            .IsUnique()
            .HasDatabaseName("ix_customer_profiles_user_id");

        builder.Property(p => p.Company)
            .HasColumnName("company")
            .HasMaxLength(100);
            
        builder.Property(p => p.Department)
            .HasColumnName("department")
            .HasMaxLength(100);
            
        builder.Property(p => p.JobTitle)
            .HasColumnName("job_title")
            .HasMaxLength(100);
            
        builder.Property(p => p.WorkEmail)
            .HasColumnName("work_email")
            .HasMaxLength(255);
            
        builder.Property(p => p.WorkPhone)
            .HasColumnName("work_phone")
            .HasMaxLength(20);

        builder.Property(p => p.Country)
            .HasColumnName("country")
            .HasMaxLength(50);

        builder.Property(p => p.Province)
            .HasColumnName("province")
            .HasMaxLength(50);

        builder.Property(p => p.City)
            .HasColumnName("city")
            .HasMaxLength(50);

        builder.Property(p => p.Address)
            .HasColumnName("address")
            .HasMaxLength(200);

        builder.Property(p => p.PostalCode)
            .HasColumnName("postal_code")
            .HasMaxLength(20);
            
        builder.Property(p => p.Website)
            .HasColumnName("website")
            .HasMaxLength(255);
            
        builder.Property(p => p.LinkedIn)
            .HasColumnName("linkedin")
            .HasMaxLength(255);
            
        builder.Property(p => p.Twitter)
            .HasColumnName("twitter")
            .HasMaxLength(255);
            
        builder.Property(p => p.GitHub)
            .HasColumnName("github")
            .HasMaxLength(255);
            
        builder.Property(p => p.CustomFields)
            .HasColumnName("custom_fields")
            .HasColumnType("jsonb")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, new System.Text.Json.JsonSerializerOptions { PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase }),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, new System.Text.Json.JsonSerializerOptions { PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase }) ?? new Dictionary<string, string>())
            .Metadata.SetValueComparer(new Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer<Dictionary<string, string>>(
                (c1, c2) => c1.SequenceEqual(c2),
                c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                c => new Dictionary<string, string>(c)));

        builder.Property(p => p.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(p => p.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        // Seed data removed - CustomerProfile depends on CustomerUser which has owned types
        // and cannot be seeded using HasData. Use DataSeeder instead.
    }
}