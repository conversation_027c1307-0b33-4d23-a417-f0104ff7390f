using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using System.Threading.RateLimiting;
using Microsoft.AspNetCore.RateLimiting;

namespace WhimLabAI.Infrastructure.ApiSecurity;

/// <summary>
/// API速率限制服务
/// </summary>
public class RateLimitingService : IRateLimitingService
{
    private readonly IDistributedCache _cache;
    private readonly ILogger<RateLimitingService> _logger;
    private readonly RateLimitOptions _options;

    public RateLimitingService(
        IDistributedCache cache,
        ILogger<RateLimitingService> logger,
        IOptions<RateLimitOptions> options)
    {
        _cache = cache;
        _logger = logger;
        _options = options.Value;
    }

    /// <summary>
    /// 检查速率限制
    /// </summary>
    public async Task<RateLimitResult> CheckRateLimitAsync(string key, RateLimitRule rule)
    {
        var windowKey = GetWindowKey(key, rule.Window);
        var countKey = $"ratelimit:{windowKey}:count";
        
        // 获取当前计数
        var countStr = await _cache.GetStringAsync(countKey);
        var currentCount = string.IsNullOrEmpty(countStr) ? 0 : int.Parse(countStr);
        
        if (currentCount >= rule.Limit)
        {
            // 获取窗口重置时间
            var resetTime = GetWindowResetTime(rule.Window);
            
            _logger.LogWarning("Rate limit exceeded for key: {Key}, Count: {Count}/{Limit}", 
                key, currentCount, rule.Limit);
            
            return new RateLimitResult
            {
                IsAllowed = false,
                Limit = rule.Limit,
                Remaining = 0,
                ResetTime = resetTime,
                RetryAfter = (int)(resetTime - DateTime.UtcNow).TotalSeconds
            };
        }
        
        // 增加计数
        currentCount++;
        await _cache.SetStringAsync(countKey, currentCount.ToString(), new DistributedCacheEntryOptions
        {
            AbsoluteExpiration = GetWindowResetTime(rule.Window)
        });
        
        return new RateLimitResult
        {
            IsAllowed = true,
            Limit = rule.Limit,
            Remaining = rule.Limit - currentCount,
            ResetTime = GetWindowResetTime(rule.Window)
        };
    }

    /// <summary>
    /// 检查API密钥速率限制
    /// </summary>
    public async Task<RateLimitResult> CheckApiKeyRateLimitAsync(
        Guid apiKeyId, 
        int rateLimit,
        int? dailyQuota = null,
        int? monthlyQuota = null)
    {
        // 检查每分钟限制
        var minuteRule = new RateLimitRule
        {
            Limit = rateLimit,
            Window = TimeSpan.FromMinutes(1)
        };
        
        var minuteResult = await CheckRateLimitAsync($"apikey:{apiKeyId}:minute", minuteRule);
        if (!minuteResult.IsAllowed)
        {
            return minuteResult;
        }
        
        // 检查每日配额
        if (dailyQuota.HasValue)
        {
            var dailyRule = new RateLimitRule
            {
                Limit = dailyQuota.Value,
                Window = TimeSpan.FromDays(1)
            };
            
            var dailyResult = await CheckRateLimitAsync($"apikey:{apiKeyId}:daily", dailyRule);
            if (!dailyResult.IsAllowed)
            {
                return dailyResult;
            }
        }
        
        // 检查每月配额
        if (monthlyQuota.HasValue)
        {
            var monthlyKey = $"apikey:{apiKeyId}:monthly:{DateTime.UtcNow:yyyy-MM}";
            var monthlyCountStr = await _cache.GetStringAsync(monthlyKey);
            var monthlyCount = string.IsNullOrEmpty(monthlyCountStr) ? 0 : int.Parse(monthlyCountStr);
            
            if (monthlyCount >= monthlyQuota.Value)
            {
                return new RateLimitResult
                {
                    IsAllowed = false,
                    Limit = monthlyQuota.Value,
                    Remaining = 0,
                    ResetTime = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).AddMonths(1),
                    RetryAfter = (int)(new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).AddMonths(1) - DateTime.UtcNow).TotalSeconds
                };
            }
            
            // 增加月度计数
            await _cache.SetStringAsync(monthlyKey, (monthlyCount + 1).ToString(), new DistributedCacheEntryOptions
            {
                AbsoluteExpiration = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).AddMonths(1)
            });
        }
        
        return minuteResult;
    }

    /// <summary>
    /// 检查IP速率限制
    /// </summary>
    public async Task<RateLimitResult> CheckIpRateLimitAsync(string ipAddress, string endpoint)
    {
        // 全局IP限制
        var globalRule = _options.GlobalIpRateLimit;
        var globalResult = await CheckRateLimitAsync($"ip:{ipAddress}:global", globalRule);
        if (!globalResult.IsAllowed)
        {
            return globalResult;
        }
        
        // 端点特定限制
        if (_options.EndpointRateLimits.TryGetValue(endpoint, out var endpointRule))
        {
            var endpointResult = await CheckRateLimitAsync($"ip:{ipAddress}:endpoint:{endpoint}", endpointRule);
            if (!endpointResult.IsAllowed)
            {
                return endpointResult;
            }
        }
        
        return globalResult;
    }

    /// <summary>
    /// 获取使用统计
    /// </summary>
    public async Task<ApiKeyUsageStats> GetUsageStatsAsync(Guid apiKeyId)
    {
        var stats = new ApiKeyUsageStats
        {
            ApiKeyId = apiKeyId
        };
        
        // 获取当前分钟使用量
        var minuteKey = $"ratelimit:apikey:{apiKeyId}:minute:{GetWindowKey(DateTime.UtcNow, TimeSpan.FromMinutes(1))}:count";
        var minuteCountStr = await _cache.GetStringAsync(minuteKey);
        stats.CurrentMinuteUsage = string.IsNullOrEmpty(minuteCountStr) ? 0 : int.Parse(minuteCountStr);
        
        // 获取今日使用量
        var dailyKey = $"ratelimit:apikey:{apiKeyId}:daily:{GetWindowKey(DateTime.UtcNow, TimeSpan.FromDays(1))}:count";
        var dailyCountStr = await _cache.GetStringAsync(dailyKey);
        stats.TodayUsage = string.IsNullOrEmpty(dailyCountStr) ? 0 : int.Parse(dailyCountStr);
        
        // 获取本月使用量
        var monthlyKey = $"apikey:{apiKeyId}:monthly:{DateTime.UtcNow:yyyy-MM}";
        var monthlyCountStr = await _cache.GetStringAsync(monthlyKey);
        stats.MonthlyUsage = string.IsNullOrEmpty(monthlyCountStr) ? 0 : int.Parse(monthlyCountStr);
        
        return stats;
    }

    private string GetWindowKey(string baseKey, TimeSpan window)
    {
        var windowStart = GetWindowStart(DateTime.UtcNow, window);
        return $"{baseKey}:{windowStart.Ticks}";
    }

    private string GetWindowKey(DateTime time, TimeSpan window)
    {
        var windowStart = GetWindowStart(time, window);
        return windowStart.Ticks.ToString();
    }

    private DateTime GetWindowStart(DateTime time, TimeSpan window)
    {
        var ticks = time.Ticks / window.Ticks * window.Ticks;
        return new DateTime(ticks, DateTimeKind.Utc);
    }

    private DateTime GetWindowResetTime(TimeSpan window)
    {
        var now = DateTime.UtcNow;
        var windowStart = GetWindowStart(now, window);
        return windowStart.Add(window);
    }
}

/// <summary>
/// 速率限制策略提供者
/// </summary>
public class CustomRateLimiterPolicy : IRateLimiterPolicy<HttpContext>
{
    private readonly IRateLimitingService _rateLimitingService;
    private readonly ILogger<CustomRateLimiterPolicy> _logger;

    public CustomRateLimiterPolicy(
        IRateLimitingService rateLimitingService,
        ILogger<CustomRateLimiterPolicy> logger)
    {
        _rateLimitingService = rateLimitingService;
        _logger = logger;
    }

    public Func<OnRejectedContext, CancellationToken, ValueTask>? OnRejected { get; } = async (context, token) =>
    {
        context.HttpContext.Response.StatusCode = StatusCodes.Status429TooManyRequests;
        context.HttpContext.Response.Headers.Add("Retry-After", context.Lease.TryGetMetadata(MetadataName.RetryAfter, out var retryAfter) ? retryAfter.TotalSeconds.ToString() : "60");
        
        await context.HttpContext.Response.WriteAsJsonAsync(new
        {
            error = "Rate limit exceeded",
            message = "Too many requests. Please retry after some time."
        }, token);
    };

    public RateLimitPartition<HttpContext> GetPartition(HttpContext httpContext)
    {
        // 获取客户端标识
        var clientId = GetClientIdentifier(httpContext);
        
        return RateLimitPartition.GetFixedWindowLimiter(
            httpContext,
            context => new FixedWindowRateLimiterOptions
            {
                PermitLimit = 100,
                Window = TimeSpan.FromMinutes(1),
                QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                QueueLimit = 10
            });
    }

    private string GetClientIdentifier(HttpContext context)
    {
        // 优先使用API密钥
        if (context.Request.Headers.TryGetValue("X-API-Key", out var apiKey))
        {
            return $"apikey:{apiKey}";
        }
        
        // 使用认证用户
        if (context.User.Identity?.IsAuthenticated == true)
        {
            return $"user:{context.User.Identity.Name}";
        }
        
        // 使用IP地址
        var ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        return $"ip:{ipAddress}";
    }
}

/// <summary>
/// 速率限制选项
/// </summary>
public class RateLimitOptions
{
    public RateLimitRule GlobalIpRateLimit { get; set; } = new()
    {
        Limit = 100,
        Window = TimeSpan.FromMinutes(1)
    };
    
    public Dictionary<string, RateLimitRule> EndpointRateLimits { get; set; } = new()
    {
        ["/api/auth/login"] = new RateLimitRule { Limit = 5, Window = TimeSpan.FromMinutes(5) },
        ["/api/auth/register"] = new RateLimitRule { Limit = 3, Window = TimeSpan.FromMinutes(10) },
        ["/api/ai/chat"] = new RateLimitRule { Limit = 30, Window = TimeSpan.FromMinutes(1) }
    };
}

/// <summary>
/// 速率限制规则
/// </summary>
public class RateLimitRule
{
    public int Limit { get; set; }
    public TimeSpan Window { get; set; }
}

/// <summary>
/// 速率限制结果
/// </summary>
public class RateLimitResult
{
    public bool IsAllowed { get; set; }
    public int Limit { get; set; }
    public int Remaining { get; set; }
    public DateTime ResetTime { get; set; }
    public int? RetryAfter { get; set; }
}

/// <summary>
/// API密钥使用统计
/// </summary>
public class ApiKeyUsageStats
{
    public Guid ApiKeyId { get; set; }
    public int CurrentMinuteUsage { get; set; }
    public int TodayUsage { get; set; }
    public int MonthlyUsage { get; set; }
}

/// <summary>
/// 速率限制服务接口
/// </summary>
public interface IRateLimitingService
{
    Task<RateLimitResult> CheckRateLimitAsync(string key, RateLimitRule rule);
    Task<RateLimitResult> CheckApiKeyRateLimitAsync(Guid apiKeyId, int rateLimit, int? dailyQuota = null, int? monthlyQuota = null);
    Task<RateLimitResult> CheckIpRateLimitAsync(string ipAddress, string endpoint);
    Task<ApiKeyUsageStats> GetUsageStatsAsync(Guid apiKeyId);
}