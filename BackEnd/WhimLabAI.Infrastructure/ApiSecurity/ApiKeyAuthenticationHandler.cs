using System;
using System.Linq;
using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Infrastructure.Security;
using WhimLabAI.Domain.Entities.ApiKey;

namespace WhimLabAI.Infrastructure.ApiSecurity;

/// <summary>
/// API密钥认证处理器
/// </summary>
public class ApiKeyAuthenticationHandler : AuthenticationHandler<ApiKeyAuthenticationOptions>
{
    private readonly WhimLabAIDbContext _dbContext;
    private readonly IRateLimitingService _rateLimitingService;
    private readonly ILogger<ApiKeyAuthenticationHandler> _logger;
    private const string ApiKeyHeaderName = "X-API-Key";
    private const string ApiKeyQueryName = "api_key";

    public ApiKeyAuthenticationHandler(
        IOptionsMonitor<ApiKeyAuthenticationOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        ISystemClock clock,
        WhimLabAIDbContext dbContext,
        IRateLimitingService rateLimitingService)
        : base(options, logger, encoder, clock)
    {
        _dbContext = dbContext;
        _rateLimitingService = rateLimitingService;
        _logger = logger.CreateLogger<ApiKeyAuthenticationHandler>();
    }

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        try
        {
            // 获取API密钥
            var apiKeyValue = GetApiKey();
            if (string.IsNullOrEmpty(apiKeyValue))
            {
                return AuthenticateResult.NoResult();
            }

            // 验证密钥格式
            if (!ApiKeyCryptoHelper.IsValidApiKeyFormat(apiKeyValue))
            {
                _logger.LogWarning("Invalid API key format: {KeyPrefix}", 
                    apiKeyValue.Length > 8 ? apiKeyValue.Substring(0, 8) + "..." : "***");
                return AuthenticateResult.Fail("Invalid API key format");
            }

            // 提取密钥后缀用于查找
            var keySuffix = ApiKeyCryptoHelper.GetKeySuffix(apiKeyValue);
            
            // 查找API密钥（通过后缀）
            var apiKey = await _dbContext.ApiKeys
                .FirstOrDefaultAsync(k => k.KeySuffix == keySuffix && k.IsActive);

            if (apiKey == null)
            {
                _logger.LogWarning("API key not found or inactive");
                return AuthenticateResult.Fail("Invalid API key");
            }

            // 验证密钥哈希
            bool isValid;
            if (!string.IsNullOrEmpty(apiKey.KeySalt))
            {
                // 使用PBKDF2验证（新密钥）
                isValid = ApiKeyCryptoHelper.VerifyApiKey(apiKeyValue, apiKey.KeyHash);
            }
            else
            {
                // 向后兼容：使用旧的SHA256验证
                isValid = ApiKey.VerifyApiKey(apiKeyValue, apiKey.KeyHash);
            }

            if (!isValid)
            {
                _logger.LogWarning("API key hash verification failed for key ID: {KeyId}", apiKey.Id);
                return AuthenticateResult.Fail("Invalid API key");
            }

            // 检查过期
            if (apiKey.IsExpired())
            {
                _logger.LogWarning("API key expired: {KeyId}", apiKey.Id);
                return AuthenticateResult.Fail("API key expired");
            }

            // 验证IP白名单
            var clientIp = GetClientIpAddress();
            if (!apiKey.IsIpAllowed(clientIp))
            {
                _logger.LogWarning("IP not allowed for API key: {KeyId}, IP: {IP}", apiKey.Id, clientIp);
                return AuthenticateResult.Fail("IP address not allowed");
            }

            // 验证域名
            var origin = Context.Request.Headers["Origin"].FirstOrDefault();
            if (!string.IsNullOrEmpty(origin))
            {
                var uri = new Uri(origin);
                if (!apiKey.IsDomainAllowed(uri.Host))
                {
                    _logger.LogWarning("Domain not allowed for API key: {KeyId}, Domain: {Domain}", 
                        apiKey.Id, uri.Host);
                    return AuthenticateResult.Fail("Domain not allowed");
                }
            }

            // 检查速率限制
            var rateLimitResult = await _rateLimitingService.CheckApiKeyRateLimitAsync(
                apiKey.Id, 
                apiKey.RateLimit,
                apiKey.DailyQuota,
                apiKey.MonthlyQuota);

            if (!rateLimitResult.IsAllowed)
            {
                Context.Response.Headers.Add("X-RateLimit-Limit", rateLimitResult.Limit.ToString());
                Context.Response.Headers.Add("X-RateLimit-Remaining", rateLimitResult.Remaining.ToString());
                Context.Response.Headers.Add("X-RateLimit-Reset", rateLimitResult.ResetTime.ToUnixTimeSeconds().ToString());
                
                if (rateLimitResult.RetryAfter.HasValue)
                {
                    Context.Response.Headers.Add("Retry-After", rateLimitResult.RetryAfter.Value.ToString());
                }

                _logger.LogWarning("Rate limit exceeded for API key: {KeyId}", apiKey.Id);
                return AuthenticateResult.Fail("Rate limit exceeded");
            }

            // 记录使用
            apiKey.RecordUsage(clientIp);
            await _dbContext.SaveChangesAsync();

            // 创建认证票据
            var userId = apiKey.UserType switch
            {
                "Customer" => apiKey.CustomerUserId?.ToString() ?? string.Empty,
                "Admin" => apiKey.AdminUserId?.ToString() ?? string.Empty,
                _ => string.Empty
            };
            
            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, userId),
                new Claim("ApiKeyId", apiKey.Id.ToString()),
                new Claim("UserType", apiKey.UserType),
                new Claim("KeyType", apiKey.KeyType),
                new Claim(ClaimTypes.Name, apiKey.Name)
            };

            // 添加作用域声明
            foreach (var scope in apiKey.Scopes)
            {
                claims = claims.Append(new Claim("scope", scope)).ToArray();
            }

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            // 添加速率限制响应头
            Context.Response.Headers.Add("X-RateLimit-Limit", rateLimitResult.Limit.ToString());
            Context.Response.Headers.Add("X-RateLimit-Remaining", rateLimitResult.Remaining.ToString());
            Context.Response.Headers.Add("X-RateLimit-Reset", rateLimitResult.ResetTime.ToUnixTimeSeconds().ToString());

            return AuthenticateResult.Success(ticket);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during API key authentication");
            return AuthenticateResult.Fail($"Authentication error: {ex.Message}");
        }
    }

    protected override Task HandleChallengeAsync(AuthenticationProperties properties)
    {
        Response.Headers["WWW-Authenticate"] = $"ApiKey realm=\"{Options.Realm}\", charset=\"UTF-8\"";
        return base.HandleChallengeAsync(properties);
    }

    private string? GetApiKey()
    {
        // 优先从请求头获取
        if (Request.Headers.TryGetValue(ApiKeyHeaderName, out var headerValue))
        {
            return headerValue.FirstOrDefault();
        }

        // 从查询参数获取
        if (Request.Query.TryGetValue(ApiKeyQueryName, out var queryValue))
        {
            return queryValue.FirstOrDefault();
        }

        // 从Authorization头获取
        var authHeader = Request.Headers["Authorization"].FirstOrDefault();
        if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("ApiKey ", StringComparison.OrdinalIgnoreCase))
        {
            return authHeader.Substring(7);
        }

        return null;
    }

    private string GetClientIpAddress()
    {
        // 检查代理头
        var forwardedFor = Context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (ips.Length > 0)
            {
                return ips[0].Trim();
            }
        }

        var realIp = Context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return Context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}

/// <summary>
/// API密钥认证选项
/// </summary>
public class ApiKeyAuthenticationOptions : AuthenticationSchemeOptions
{
    public string Realm { get; set; } = "WhimLabAI API";
    public bool RequireHttps { get; set; } = true;
}

/// <summary>
/// Unix时间戳扩展
/// </summary>
public static class DateTimeExtensions
{
    private static readonly DateTime UnixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

    public static long ToUnixTimeSeconds(this DateTime dateTime)
    {
        return (long)(dateTime.ToUniversalTime() - UnixEpoch).TotalSeconds;
    }
}