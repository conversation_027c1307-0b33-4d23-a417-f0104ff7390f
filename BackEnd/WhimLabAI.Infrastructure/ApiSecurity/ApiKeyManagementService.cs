using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.ApiKey;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.ApiSecurity;

/// <summary>
/// API密钥管理服务
/// </summary>
public class ApiKeyManagementService : IApiKeyManagementService
{
    private readonly WhimLabAIDbContext _dbContext;
    private readonly ILogger<ApiKeyManagementService> _logger;
    private readonly IRateLimitingService _rateLimitingService;

    public ApiKeyManagementService(
        WhimLabAIDbContext dbContext,
        ILogger<ApiKeyManagementService> logger,
        IRateLimitingService rateLimitingService)
    {
        _dbContext = dbContext;
        _logger = logger;
        _rateLimitingService = rateLimitingService;
    }

    /// <summary>
    /// 创廽API密钥
    /// </summary>
    public async Task<Result<ApiKeyCreationResult>> CreateApiKeyAsync(CreateApiKeyRequest request)
    {
        try
        {
            // 验证用户API密钥数量限制
            var existingKeysCount = request.UserType switch
            {
                "Customer" => await _dbContext.ApiKeys
                    .CountAsync(k => k.CustomerUserId == request.UserId && k.IsActive),
                "Admin" => await _dbContext.ApiKeys
                    .CountAsync(k => k.AdminUserId == request.UserId && k.IsActive),
                _ => await _dbContext.ApiKeys
                    .CountAsync(k => k.UserType == request.UserType && k.IsActive)
            };

            var maxKeys = request.UserType switch
            {
                "Customer" => request.KeyType == "Development" ? 5 : 2,
                "Admin" => 10,
                "System" => 100,
                _ => 3
            };

            if (existingKeysCount >= maxKeys)
            {
                return Result<ApiKeyCreationResult>.Failure(
                    $"Maximum number of API keys ({maxKeys}) reached for this user");
            }

            // 创建密钥
            var (apiKey, rawKey) = request.UserType switch
            {
                "Customer" => ApiKey.CreateForCustomerUser(
                    request.Name,
                    request.UserId,
                    request.KeyType,
                    request.ExpiresAt),
                "Admin" => ApiKey.CreateForAdminUser(
                    request.Name,
                    request.UserId,
                    request.KeyType,
                    request.ExpiresAt),
                "System" => ApiKey.CreateForSystem(
                    request.Name,
                    request.KeyType,
                    request.ExpiresAt),
                _ => throw new ArgumentException($"Invalid user type: {request.UserType}")
            };

            // 设置作用域
            if (request.Scopes?.Any() == true)
            {
                apiKey.SetScopes(request.Scopes.ToArray());
            }

            // 设置IP白名单
            if (request.IpWhitelist?.Any() == true)
            {
                apiKey.SetIpWhitelist(request.IpWhitelist.ToArray());
            }

            // 设置允许的域名
            if (request.AllowedDomains?.Any() == true)
            {
                apiKey.SetAllowedDomains(request.AllowedDomains.ToArray());
            }

            // 设置速率限制
            if (request.RateLimit.HasValue)
            {
                apiKey.SetRateLimit(
                    request.RateLimit.Value,
                    request.DailyQuota,
                    request.MonthlyQuota);
            }

            // 保存到数据库
            _dbContext.ApiKeys.Add(apiKey);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("API key created: {KeyId} for user: {UserId}", 
                apiKey.Id, request.UserId);

            return Result<ApiKeyCreationResult>.Success(new ApiKeyCreationResult
            {
                ApiKeyId = apiKey.Id,
                Key = rawKey,
                Name = apiKey.Name,
                ExpiresAt = apiKey.ExpiresAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create API key for user: {UserId}", request.UserId);
            return Result<ApiKeyCreationResult>.Failure($"Failed to create API key: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取API密钥列表
    /// </summary>
    public async Task<Result<List<ApiKeyInfo>>> GetApiKeysAsync(Guid userId, string userType)
    {
        try
        {
            var query = userType switch
            {
                "Customer" => _dbContext.ApiKeys
                    .Where(k => k.CustomerUserId == userId && k.UserType == userType),
                "Admin" => _dbContext.ApiKeys
                    .Where(k => k.AdminUserId == userId && k.UserType == userType),
                _ => _dbContext.ApiKeys
                    .Where(k => k.UserType == userType)
            };

            var apiKeys = await query
                .OrderByDescending(k => k.CreatedAt)
                .Select(k => new ApiKeyInfo
                {
                    Id = k.Id,
                    Name = k.Name,
                    KeyPrefix = k.KeyPrefix,
                    KeySuffix = k.KeySuffix,
                    KeyType = k.KeyType,
                    Scopes = k.Scopes,
                    IpWhitelist = k.IpWhitelist,
                    AllowedDomains = k.AllowedDomains,
                    RateLimit = k.RateLimit,
                    DailyQuota = k.DailyQuota,
                    MonthlyQuota = k.MonthlyQuota,
                    IsActive = k.IsActive,
                    ExpiresAt = k.ExpiresAt,
                    LastUsedAt = k.LastUsedAt,
                    LastUsedIp = k.LastUsedIp,
                    TotalUsageCount = k.TotalUsageCount,
                    CreatedAt = k.CreatedAt
                })
                .ToListAsync();

            // 获取使用统计
            foreach (var apiKey in apiKeys)
            {
                var stats = await _rateLimitingService.GetUsageStatsAsync(apiKey.Id);
                apiKey.CurrentMinuteUsage = stats.CurrentMinuteUsage;
                apiKey.TodayUsage = stats.TodayUsage;
                apiKey.MonthlyUsage = stats.MonthlyUsage;
            }

            return Result<List<ApiKeyInfo>>.Success(apiKeys);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get API keys for user: {UserId}", userId);
            return Result<List<ApiKeyInfo>>.Failure($"Failed to get API keys: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取API密钥详情
    /// </summary>
    public async Task<Result<ApiKeyInfo>> GetApiKeyAsync(Guid apiKeyId, Guid userId)
    {
        try
        {
            var apiKey = await _dbContext.ApiKeys
                .Where(k => k.Id == apiKeyId && 
                       ((k.UserType == "Customer" && k.CustomerUserId == userId) ||
                        (k.UserType == "Admin" && k.AdminUserId == userId)))
                .Select(k => new ApiKeyInfo
                {
                    Id = k.Id,
                    Name = k.Name,
                    KeyPrefix = k.KeyPrefix,
                    KeySuffix = k.KeySuffix,
                    KeyType = k.KeyType,
                    Scopes = k.Scopes,
                    IpWhitelist = k.IpWhitelist,
                    AllowedDomains = k.AllowedDomains,
                    RateLimit = k.RateLimit,
                    DailyQuota = k.DailyQuota,
                    MonthlyQuota = k.MonthlyQuota,
                    IsActive = k.IsActive,
                    ExpiresAt = k.ExpiresAt,
                    LastUsedAt = k.LastUsedAt,
                    LastUsedIp = k.LastUsedIp,
                    TotalUsageCount = k.TotalUsageCount,
                    CreatedAt = k.CreatedAt,
                    Description = k.Description
                })
                .FirstOrDefaultAsync();

            if (apiKey == null)
            {
                return Result<ApiKeyInfo>.Failure("API key not found");
            }

            // 获取使用统计
            var stats = await _rateLimitingService.GetUsageStatsAsync(apiKey.Id);
            apiKey.CurrentMinuteUsage = stats.CurrentMinuteUsage;
            apiKey.TodayUsage = stats.TodayUsage;
            apiKey.MonthlyUsage = stats.MonthlyUsage;

            return Result<ApiKeyInfo>.Success(apiKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get API key: {ApiKeyId}", apiKeyId);
            return Result<ApiKeyInfo>.Failure($"Failed to get API key: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新API密钥
    /// </summary>
    public async Task<Result> UpdateApiKeyAsync(UpdateApiKeyRequest request)
    {
        try
        {
            var apiKey = await _dbContext.ApiKeys
                .FirstOrDefaultAsync(k => k.Id == request.ApiKeyId && 
                    ((k.UserType == "Customer" && k.CustomerUserId == request.UserId) ||
                     (k.UserType == "Admin" && k.AdminUserId == request.UserId)));

            if (apiKey == null)
            {
                return Result.Failure("API key not found");
            }

            // 更新基本信息
            apiKey.UpdateInfo(request.Name, request.Description);

            // 更新作用域
            if (request.Scopes != null)
            {
                apiKey.SetScopes(request.Scopes.ToArray());
            }

            // 更新IP白名单
            if (request.IpWhitelist != null)
            {
                apiKey.SetIpWhitelist(request.IpWhitelist.ToArray());
            }

            // 更新允许的域名
            if (request.AllowedDomains != null)
            {
                apiKey.SetAllowedDomains(request.AllowedDomains.ToArray());
            }

            // 更新速率限制
            if (request.RateLimit.HasValue)
            {
                apiKey.SetRateLimit(
                    request.RateLimit.Value,
                    request.DailyQuota,
                    request.MonthlyQuota);
            }

            // 更新过期时间
            if (request.ExpiresAt.HasValue)
            {
                apiKey.UpdateExpiration(request.ExpiresAt);
            }

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("API key updated: {ApiKeyId}", request.ApiKeyId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update API key: {ApiKeyId}", request.ApiKeyId);
            return Result.Failure($"Failed to update API key: {ex.Message}");
        }
    }

    /// <summary>
    /// 停用API密钥
    /// </summary>
    public async Task<Result> DeactivateApiKeyAsync(Guid apiKeyId, Guid userId)
    {
        try
        {
            var apiKey = await _dbContext.ApiKeys
                .FirstOrDefaultAsync(k => k.Id == apiKeyId && 
                    ((k.UserType == "Customer" && k.CustomerUserId == userId) ||
                     (k.UserType == "Admin" && k.AdminUserId == userId)));

            if (apiKey == null)
            {
                return Result.Failure("API key not found");
            }

            apiKey.Deactivate();
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("API key deactivated: {ApiKeyId}", apiKeyId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to deactivate API key: {ApiKeyId}", apiKeyId);
            return Result.Failure($"Failed to deactivate API key: {ex.Message}");
        }
    }

    /// <summary>
    /// 激活API密钥
    /// </summary>
    public async Task<Result> ActivateApiKeyAsync(Guid apiKeyId, Guid userId)
    {
        try
        {
            var apiKey = await _dbContext.ApiKeys
                .FirstOrDefaultAsync(k => k.Id == apiKeyId && 
                    ((k.UserType == "Customer" && k.CustomerUserId == userId) ||
                     (k.UserType == "Admin" && k.AdminUserId == userId)));

            if (apiKey == null)
            {
                return Result.Failure("API key not found");
            }

            apiKey.Activate();
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("API key activated: {ApiKeyId}", apiKeyId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to activate API key: {ApiKeyId}", apiKeyId);
            return Result.Failure($"Failed to activate API key: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除API密钥
    /// </summary>
    public async Task<Result> DeleteApiKeyAsync(Guid apiKeyId, Guid userId)
    {
        try
        {
            var apiKey = await _dbContext.ApiKeys
                .FirstOrDefaultAsync(k => k.Id == apiKeyId && 
                    ((k.UserType == "Customer" && k.CustomerUserId == userId) ||
                     (k.UserType == "Admin" && k.AdminUserId == userId)));

            if (apiKey == null)
            {
                return Result.Failure("API key not found");
            }

            _dbContext.ApiKeys.Remove(apiKey);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("API key deleted: {ApiKeyId}", apiKeyId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete API key: {ApiKeyId}", apiKeyId);
            return Result.Failure($"Failed to delete API key: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取API密钥使用历史
    /// </summary>
    public async Task<Result<List<ApiKeyUsageInfo>>> GetApiKeyUsageHistoryAsync(
        Guid apiKeyId, 
        Guid userId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageSize = 100,
        int pageNumber = 1)
    {
        try
        {
            // 验证API密钥归属
            var keyExists = await _dbContext.ApiKeys
                .AnyAsync(k => k.Id == apiKeyId && 
                    ((k.UserType == "Customer" && k.CustomerUserId == userId) ||
                     (k.UserType == "Admin" && k.AdminUserId == userId)));

            if (!keyExists)
            {
                return Result<List<ApiKeyUsageInfo>>.Failure("API key not found");
            }

            var query = _dbContext.ApiKeyUsages
                .Where(u => u.ApiKeyId == apiKeyId);

            if (startDate.HasValue)
            {
                query = query.Where(u => u.UsedAt >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(u => u.UsedAt <= endDate.Value);
            }

            var totalCount = await query.CountAsync();
            
            var usages = await query
                .OrderByDescending(u => u.UsedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Select(u => new ApiKeyUsageInfo
                {
                    Id = u.Id,
                    RequestPath = u.RequestPath,
                    RequestMethod = u.RequestMethod,
                    IpAddress = u.IpAddress,
                    UserAgent = u.UserAgent,
                    Origin = u.Origin,
                    ResponseStatusCode = u.ResponseStatusCode,
                    ResponseTime = u.ResponseTime,
                    RequestSize = u.RequestSize,
                    ResponseSize = u.ResponseSize,
                    IsSuccess = u.IsSuccess,
                    ErrorMessage = u.ErrorMessage,
                    UsedAt = u.UsedAt
                })
                .ToListAsync();

            return Result<List<ApiKeyUsageInfo>>.Success(usages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get API key usage history: {ApiKeyId}", apiKeyId);
            return Result<List<ApiKeyUsageInfo>>.Failure($"Failed to get usage history: {ex.Message}");
        }
    }
}

/// <summary>
/// API密钥管理服务接口
/// </summary>
public interface IApiKeyManagementService
{
    Task<Result<ApiKeyCreationResult>> CreateApiKeyAsync(CreateApiKeyRequest request);
    Task<Result<List<ApiKeyInfo>>> GetApiKeysAsync(Guid userId, string userType);
    Task<Result<ApiKeyInfo>> GetApiKeyAsync(Guid apiKeyId, Guid userId);
    Task<Result> UpdateApiKeyAsync(UpdateApiKeyRequest request);
    Task<Result> DeactivateApiKeyAsync(Guid apiKeyId, Guid userId);
    Task<Result> ActivateApiKeyAsync(Guid apiKeyId, Guid userId);
    Task<Result> DeleteApiKeyAsync(Guid apiKeyId, Guid userId);
    Task<Result<List<ApiKeyUsageInfo>>> GetApiKeyUsageHistoryAsync(
        Guid apiKeyId, 
        Guid userId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageSize = 100,
        int pageNumber = 1);
}

/// <summary>
/// 创廽API密钥请求
/// </summary>
public class CreateApiKeyRequest
{
    public string Name { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string UserType { get; set; } = string.Empty;
    public string KeyType { get; set; } = "Development";
    public List<string>? Scopes { get; set; }
    public List<string>? IpWhitelist { get; set; }
    public List<string>? AllowedDomains { get; set; }
    public int? RateLimit { get; set; }
    public int? DailyQuota { get; set; }
    public int? MonthlyQuota { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// API密钥创建结果
/// </summary>
public class ApiKeyCreationResult
{
    public Guid ApiKeyId { get; set; }
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// 更新API密钥请求
/// </summary>
public class UpdateApiKeyRequest
{
    public Guid ApiKeyId { get; set; }
    public Guid UserId { get; set; }
    public string? Name { get; set; }
    public List<string>? Scopes { get; set; }
    public List<string>? IpWhitelist { get; set; }
    public List<string>? AllowedDomains { get; set; }
    public int? RateLimit { get; set; }
    public int? DailyQuota { get; set; }
    public int? MonthlyQuota { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// API密钥信息
/// </summary>
public class ApiKeyInfo
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string KeyPrefix { get; set; } = string.Empty;
    public string KeySuffix { get; set; } = string.Empty;
    public string KeyType { get; set; } = string.Empty;
    public List<string> Scopes { get; set; } = new();
    public List<string> IpWhitelist { get; set; } = new();
    public List<string> AllowedDomains { get; set; } = new();
    public int RateLimit { get; set; }
    public int? DailyQuota { get; set; }
    public int? MonthlyQuota { get; set; }
    public bool IsActive { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public string? LastUsedIp { get; set; }
    public long TotalUsageCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? Description { get; set; }
    
    // 使用统计
    public int CurrentMinuteUsage { get; set; }
    public int TodayUsage { get; set; }
    public int MonthlyUsage { get; set; }
}

/// <summary>
/// API密钥使用信息
/// </summary>
public class ApiKeyUsageInfo
{
    public Guid Id { get; set; }
    public string RequestPath { get; set; } = string.Empty;
    public string RequestMethod { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string? UserAgent { get; set; }
    public string? Origin { get; set; }
    public int ResponseStatusCode { get; set; }
    public long ResponseTime { get; set; }
    public long? RequestSize { get; set; }
    public long? ResponseSize { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UsedAt { get; set; }
}