using System;
using System.Threading.RateLimiting;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.IO;

namespace WhimLabAI.Infrastructure.ApiSecurity;

/// <summary>
/// API安全服务扩展
/// </summary>
public static class ApiSecurityServiceExtensions
{
    /// <summary>
    /// 添加API安全服务
    /// </summary>
    public static IServiceCollection AddApiSecurityServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册RecyclableMemoryStreamManager
        services.AddSingleton<RecyclableMemoryStreamManager>(provider =>
        {
            var manager = new RecyclableMemoryStreamManager();
            // RecyclableMemoryStreamManager configuration
            // Note: The configuration approach may vary by version
            return manager;
        });

        // 注册速率限制服务
        services.AddSingleton<IRateLimitingService, RateLimitingService>();
        services.Configure<RateLimitOptions>(configuration.GetSection("Security:RateLimiting"));

        // 注册API密钥管理服务
        services.AddScoped<IApiKeyManagementService, ApiKeyManagementService>();

        // 添加API密钥认证
        services.AddAuthentication()
            .AddScheme<ApiKeyAuthenticationOptions, ApiKeyAuthenticationHandler>("ApiKey", options =>
            {
                options.Realm = "WhimLabAI API";
                options.RequireHttps = !services.BuildServiceProvider().GetRequiredService<IWebHostEnvironment>().IsDevelopment();
            });

        // 添加授权处理器
        services.AddSingleton<IAuthorizationHandler, ApiKeyScopeAuthorizationHandler>();
        services.AddSingleton<IAuthorizationHandler, ApiKeyTypeAuthorizationHandler>();

        // 配置速率限制
        services.AddRateLimiter(options =>
        {
            options.RejectionStatusCode = StatusCodes.Status429TooManyRequests;
            
            // 全局限制策略
            options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(httpContext =>
            {
                var clientId = GetClientIdentifier(httpContext);
                
                return RateLimitPartition.GetFixedWindowLimiter(
                    clientId,
                    partition => new FixedWindowRateLimiterOptions
                    {
                        PermitLimit = 1000,
                        Window = TimeSpan.FromMinutes(1),
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 50
                    });
            });

            // API密钥限制策略
            options.AddPolicy("ApiKeyPolicy", httpContext =>
            {
                var apiKeyId = httpContext.User.FindFirst("ApiKeyId")?.Value;
                if (string.IsNullOrEmpty(apiKeyId))
                {
                    return RateLimitPartition.GetNoLimiter("anonymous");
                }

                return RateLimitPartition.GetFixedWindowLimiter(
                    $"apikey:{apiKeyId}",
                    partition => new FixedWindowRateLimiterOptions
                    {
                        PermitLimit = 100,
                        Window = TimeSpan.FromMinutes(1),
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 10
                    });
            });

            // 登录端点限制
            options.AddPolicy("LoginPolicy", httpContext =>
            {
                var ipAddress = GetClientIpAddress(httpContext);
                
                return RateLimitPartition.GetFixedWindowLimiter(
                    $"login:{ipAddress}",
                    partition => new FixedWindowRateLimiterOptions
                    {
                        PermitLimit = 5,
                        Window = TimeSpan.FromMinutes(5),
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 0
                    });
            });

            // 注册端点限制
            options.AddPolicy("RegisterPolicy", httpContext =>
            {
                var ipAddress = GetClientIpAddress(httpContext);
                
                return RateLimitPartition.GetFixedWindowLimiter(
                    $"register:{ipAddress}",
                    partition => new FixedWindowRateLimiterOptions
                    {
                        PermitLimit = 3,
                        Window = TimeSpan.FromMinutes(10),
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 0
                    });
            });

            // AI聊天端点限制
            options.AddPolicy("ChatPolicy", httpContext =>
            {
                var userId = httpContext.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return RateLimitPartition.GetNoLimiter("anonymous");
                }

                return RateLimitPartition.GetSlidingWindowLimiter(
                    $"chat:{userId}",
                    partition => new SlidingWindowRateLimiterOptions
                    {
                        PermitLimit = 30,
                        Window = TimeSpan.FromMinutes(1),
                        SegmentsPerWindow = 6,
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 5
                    });
            });

            // 错误响应
            options.OnRejected = async (context, token) =>
            {
                context.HttpContext.Response.StatusCode = StatusCodes.Status429TooManyRequests;
                
                if (context.Lease.TryGetMetadata(MetadataName.RetryAfter, out var retryAfter))
                {
                    context.HttpContext.Response.Headers.RetryAfter = ((int)retryAfter.TotalSeconds).ToString();
                }

                await context.HttpContext.Response.WriteAsJsonAsync(new
                {
                    error = "RateLimitExceeded",
                    message = "Too many requests. Please retry later.",
                    retryAfter = context.Lease.TryGetMetadata(MetadataName.RetryAfter, out var retry) ? (int)retry.TotalSeconds : 60
                }, cancellationToken: token);
            };
        });

        return services;
    }

    /// <summary>
    /// 使用API安全中间件
    /// </summary>
    public static IApplicationBuilder UseApiSecurity(this IApplicationBuilder app)
    {
        // 使用速率限制
        app.UseRateLimiter();

        // 使用API密钥使用记录
        app.UseApiKeyUsageRecording();

        return app;
    }

    private static string GetClientIdentifier(HttpContext context)
    {
        // 优先使用API密钥
        var apiKeyId = context.User.FindFirst("ApiKeyId")?.Value;
        if (!string.IsNullOrEmpty(apiKeyId))
        {
            return $"apikey:{apiKeyId}";
        }

        // 使用认证用户
        if (context.User.Identity?.IsAuthenticated == true)
        {
            var userId = context.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (!string.IsNullOrEmpty(userId))
            {
                return $"user:{userId}";
            }
        }

        // 使用IP地址
        return $"ip:{GetClientIpAddress(context)}";
    }

    private static string GetClientIpAddress(HttpContext context)
    {
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (ips.Length > 0)
            {
                return ips[0].Trim();
            }
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}