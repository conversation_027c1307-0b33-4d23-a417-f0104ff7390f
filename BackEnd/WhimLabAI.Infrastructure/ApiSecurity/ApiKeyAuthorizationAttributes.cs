using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace WhimLabAI.Infrastructure.ApiSecurity;

/// <summary>
/// API密钥授权属性
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public class ApiKeyAuthorizeAttribute : Attribute, IAsyncAuthorizationFilter
{
    private readonly string[] _requiredScopes;
    private readonly string[] _allowedKeyTypes;
    private readonly bool _requireActiveUser;

    public ApiKeyAuthorizeAttribute()
    {
        _requiredScopes = Array.Empty<string>();
        _allowedKeyTypes = Array.Empty<string>();
        _requireActiveUser = false;
    }

    public ApiKeyAuthorizeAttribute(params string[] requiredScopes)
    {
        _requiredScopes = requiredScopes ?? Array.Empty<string>();
        _allowedKeyTypes = Array.Empty<string>();
        _requireActiveUser = false;
    }

    /// <summary>
    /// 允许的密钥类型
    /// </summary>
    public string[] AllowedKeyTypes
    {
        get => _allowedKeyTypes;
        init => _allowedKeyTypes = value ?? Array.Empty<string>();
    }

    /// <summary>
    /// 是否需要活跃用户
    /// </summary>
    public bool RequireActiveUser
    {
        get => _requireActiveUser;
        init => _requireActiveUser = value;
    }

    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        // 检查是否有[AllowAnonymous]属性
        var allowAnonymous = context.ActionDescriptor.EndpointMetadata
            .Any(m => m is AllowAnonymousAttribute);
        
        if (allowAnonymous)
        {
            return;
        }

        // 检查认证
        if (!context.HttpContext.User.Identity?.IsAuthenticated ?? true)
        {
            context.Result = new UnauthorizedObjectResult(new
            {
                error = "Unauthorized",
                message = "API key authentication required"
            });
            return;
        }

        // 检查是否是API密钥认证
        var isApiKeyAuth = context.HttpContext.User.Identity.AuthenticationType == "ApiKey";
        if (!isApiKeyAuth)
        {
            context.Result = new UnauthorizedObjectResult(new
            {
                error = "Invalid authentication",
                message = "API key authentication required"
            });
            return;
        }

        // 检查密钥类型
        if (_allowedKeyTypes.Any())
        {
            var keyType = context.HttpContext.User.FindFirst("KeyType")?.Value;
            if (string.IsNullOrEmpty(keyType) || !_allowedKeyTypes.Contains(keyType))
            {
                context.Result = new ForbiddenObjectResult(new
                {
                    error = "Forbidden",
                    message = $"API key type '{keyType}' is not allowed for this endpoint"
                });
                return;
            }
        }

        // 检查作用域
        if (_requiredScopes.Any())
        {
            var userScopes = context.HttpContext.User.FindAll("scope")
                .Select(c => c.Value)
                .ToList();

            var missingScopes = _requiredScopes
                .Where(s => !userScopes.Contains(s, StringComparer.OrdinalIgnoreCase))
                .ToList();

            if (missingScopes.Any())
            {
                context.Result = new ForbiddenObjectResult(new
                {
                    error = "Insufficient scope",
                    message = $"Required scopes: {string.Join(", ", missingScopes)}"
                });
                return;
            }
        }

        // 如果需要活跃用户，还需要检查用户状态
        if (_requireActiveUser)
        {
            // 这里可以添加额外的用户状态检查
            // 例如查询数据库确认用户是否被禁用
            await Task.CompletedTask;
        }
    }
}

/// <summary>
/// 禁止结果
/// </summary>
public class ForbiddenObjectResult : ObjectResult
{
    public ForbiddenObjectResult(object? value) : base(value)
    {
        StatusCode = StatusCodes.Status403Forbidden;
    }
}

/// <summary>
/// API密钥作用域常量
/// </summary>
public static class ApiScopes
{
    // 基础作用域
    public const string Read = "read";
    public const string Write = "write";
    public const string Delete = "delete";
    
    // 用户相关
    public const string UserProfile = "user.profile";
    public const string UserProfileWrite = "user.profile.write";
    
    // AI代理相关
    public const string AgentRead = "agent.read";
    public const string AgentWrite = "agent.write";
    public const string AgentChat = "agent.chat";
    public const string AgentExecute = "agent.execute";
    
    // 对话相关
    public const string ConversationRead = "conversation.read";
    public const string ConversationWrite = "conversation.write";
    public const string ConversationDelete = "conversation.delete";
    
    // 订阅相关
    public const string SubscriptionRead = "subscription.read";
    public const string SubscriptionManage = "subscription.manage";
    
    // 支付相关
    public const string PaymentRead = "payment.read";
    public const string PaymentCreate = "payment.create";
    
    // 管理员作用域
    public const string AdminAccess = "admin.access";
    public const string AdminUserManage = "admin.user.manage";
    public const string AdminAgentManage = "admin.agent.manage";
    public const string AdminSystemConfig = "admin.system.config";
    public const string AdminAnalytics = "admin.analytics";
    
    // 系统作用域
    public const string SystemFull = "system.full";
    public const string SystemWebhook = "system.webhook";
    public const string SystemIntegration = "system.integration";
}

/// <summary>
/// API密钥类型常量
/// </summary>
public static class ApiKeyTypes
{
    public const string Development = "Development";
    public const string Production = "Production";
    public const string Test = "Test";
    public const string Webhook = "Webhook";
    public const string Integration = "Integration";
    public const string System = "System";
}

/// <summary>
/// 需要特定作用域的授权策略
/// </summary>
public class ApiKeyScopeRequirement : IAuthorizationRequirement
{
    public string[] RequiredScopes { get; }

    public ApiKeyScopeRequirement(params string[] requiredScopes)
    {
        RequiredScopes = requiredScopes ?? Array.Empty<string>();
    }
}

/// <summary>
/// API密钥作用域授权处理器
/// </summary>
public class ApiKeyScopeAuthorizationHandler : AuthorizationHandler<ApiKeyScopeRequirement>
{
    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        ApiKeyScopeRequirement requirement)
    {
        if (!context.User.Identity?.IsAuthenticated ?? true)
        {
            return Task.CompletedTask;
        }

        var userScopes = context.User.FindAll("scope")
            .Select(c => c.Value)
            .ToList();

        var hasAllRequiredScopes = requirement.RequiredScopes
            .All(s => userScopes.Contains(s, StringComparer.OrdinalIgnoreCase));

        if (hasAllRequiredScopes)
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}

/// <summary>
/// API密钥类型授权要求
/// </summary>
public class ApiKeyTypeRequirement : IAuthorizationRequirement
{
    public string[] AllowedTypes { get; }

    public ApiKeyTypeRequirement(params string[] allowedTypes)
    {
        AllowedTypes = allowedTypes ?? Array.Empty<string>();
    }
}

/// <summary>
/// API密钥类型授权处理器
/// </summary>
public class ApiKeyTypeAuthorizationHandler : AuthorizationHandler<ApiKeyTypeRequirement>
{
    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        ApiKeyTypeRequirement requirement)
    {
        if (!context.User.Identity?.IsAuthenticated ?? true)
        {
            return Task.CompletedTask;
        }

        var keyType = context.User.FindFirst("KeyType")?.Value;
        if (!string.IsNullOrEmpty(keyType) && requirement.AllowedTypes.Contains(keyType))
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}