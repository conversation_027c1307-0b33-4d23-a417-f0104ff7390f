using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IO;
using WhimLabAI.Domain.Entities.ApiKey;
using WhimLabAI.Infrastructure.Data;

namespace WhimLabAI.Infrastructure.ApiSecurity;

/// <summary>
/// API密钥使用记录中间件
/// </summary>
public class ApiKeyUsageMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ApiKeyUsageMiddleware> _logger;
    private readonly RecyclableMemoryStreamManager _memoryStreamManager;
    private readonly HashSet<string> _excludedPaths;

    public ApiKeyUsageMiddleware(
        RequestDelegate next,
        ILogger<ApiKeyUsageMiddleware> logger,
        RecyclableMemoryStreamManager memoryStreamManager)
    {
        _next = next;
        _logger = logger;
        _memoryStreamManager = memoryStreamManager;
        _excludedPaths = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "/health",
            "/health/ready",
            "/health/live",
            "/swagger",
            "/api/audit/logs"
        };
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 检查是否需要记录
        if (!ShouldRecordUsage(context))
        {
            await _next(context);
            return;
        }

        // 获取API密钥ID
        var apiKeyIdClaim = context.User.FindFirst("ApiKeyId")?.Value;
        if (string.IsNullOrEmpty(apiKeyIdClaim) || !Guid.TryParse(apiKeyIdClaim, out var apiKeyId))
        {
            await _next(context);
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        var originalBodyStream = context.Response.Body;
        long? requestSize = null;
        long? responseSize = null;
        string? errorMessage = null;

        try
        {
            // 记录请求大小
            if (context.Request.ContentLength.HasValue)
            {
                requestSize = context.Request.ContentLength.Value;
            }

            // 使用内存流捕获响应
            await using var responseBody = _memoryStreamManager.GetStream();
            context.Response.Body = responseBody;

            // 执行下一个中间件
            await _next(context);

            // 记录响应大小
            responseSize = responseBody.Length;

            // 将响应写回原始流
            responseBody.Seek(0, SeekOrigin.Begin);
            await responseBody.CopyToAsync(originalBodyStream);
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
            _logger.LogError(ex, "Error processing request for API key: {ApiKeyId}", apiKeyId);
            throw;
        }
        finally
        {
            stopwatch.Stop();
            context.Response.Body = originalBodyStream;

            // 异步记录使用情况
            _ = Task.Run(async () =>
            {
                try
                {
                    await RecordApiKeyUsageAsync(
                        context,
                        apiKeyId,
                        stopwatch.ElapsedMilliseconds,
                        requestSize,
                        responseSize,
                        errorMessage);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to record API key usage for key: {ApiKeyId}", apiKeyId);
                }
            });
        }
    }

    private bool ShouldRecordUsage(HttpContext context)
    {
        // 检查是否在排除路径中
        if (_excludedPaths.Contains(context.Request.Path.Value ?? string.Empty))
        {
            return false;
        }

        // 检查是否是API密钥认证
        return context.User.Identity?.AuthenticationType == "ApiKey";
    }

    private async Task RecordApiKeyUsageAsync(
        HttpContext context,
        Guid apiKeyId,
        long responseTime,
        long? requestSize,
        long? responseSize,
        string? errorMessage)
    {
        using var scope = context.RequestServices.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<WhimLabAIDbContext>();

        var usage = ApiKeyUsage.Create(
            apiKeyId,
            context.Request.Path.Value ?? string.Empty,
            context.Request.Method,
            GetClientIpAddress(context),
            context.Response.StatusCode,
            responseTime);

        // 设置请求详情
        var userAgent = context.Request.Headers["User-Agent"].FirstOrDefault();
        var origin = context.Request.Headers["Origin"].FirstOrDefault();
        usage.SetRequestDetails(userAgent, origin, requestSize);

        // 设置响应详情
        usage.SetResponseDetails(responseSize, errorMessage);

        dbContext.ApiKeyUsages.Add(usage);
        await dbContext.SaveChangesAsync();
    }

    private string GetClientIpAddress(HttpContext context)
    {
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (ips.Length > 0)
            {
                return ips[0].Trim();
            }
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}

/// <summary>
/// API密钥使用记录中间件扩展
/// </summary>
public static class ApiKeyUsageMiddlewareExtensions
{
    public static IApplicationBuilder UseApiKeyUsageRecording(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ApiKeyUsageMiddleware>();
    }
}