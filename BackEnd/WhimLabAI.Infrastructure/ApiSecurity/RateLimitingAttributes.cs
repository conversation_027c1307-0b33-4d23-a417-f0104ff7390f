using System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.RateLimiting;

namespace WhimLabAI.Infrastructure.ApiSecurity;

/// <summary>
/// 通用速率限制属性 - 使用时配合 [EnableRateLimiting("ApiKeyPolicy")]
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false, Inherited = true)]
public class RateLimitAttribute : Attribute
{
    public string PolicyName { get; }
    
    public RateLimitAttribute(string policyName = "ApiKeyPolicy")
    {
        PolicyName = policyName;
    }
}

/// <summary>
/// 登录端点速率限制 - 使用时配合 [EnableRateLimiting("LoginPolicy")]
/// </summary>
[AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
public class LoginRateLimitAttribute : Attribute
{
    public string PolicyName { get; } = "LoginPolicy";
}

/// <summary>
/// 注册端点速率限制 - 使用时配合 [EnableRateLimiting("RegisterPolicy")]
/// </summary>
[AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
public class RegisterRateLimitAttribute : Attribute
{
    public string PolicyName { get; } = "RegisterPolicy";
}

/// <summary>
/// AI聊天端点速率限制 - 使用时配合 [EnableRateLimiting("ChatPolicy")]
/// </summary>
[AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
public class ChatRateLimitAttribute : Attribute
{
    public string PolicyName { get; } = "ChatPolicy";
}

/// <summary>
/// 自定义速率限制属性
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public class CustomRateLimitAttribute : ActionFilterAttribute
{
    private readonly int _requestsPerMinute;
    private readonly int _requestsPerHour;
    private readonly int _requestsPerDay;

    public CustomRateLimitAttribute(int requestsPerMinute = 60, int requestsPerHour = 600, int requestsPerDay = 10000)
    {
        _requestsPerMinute = requestsPerMinute;
        _requestsPerHour = requestsPerHour;
        _requestsPerDay = requestsPerDay;
    }

    public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var rateLimitingService = context.HttpContext.RequestServices.GetService(typeof(IRateLimitingService)) as IRateLimitingService;
        if (rateLimitingService == null)
        {
            await next();
            return;
        }

        var clientId = GetClientIdentifier(context.HttpContext);
        var endpoint = context.HttpContext.Request.Path.Value ?? "unknown";

        // 检查每分钟限制
        var minuteRule = new RateLimitRule
        {
            Limit = _requestsPerMinute,
            Window = TimeSpan.FromMinutes(1)
        };
        var minuteResult = await rateLimitingService.CheckRateLimitAsync($"{clientId}:minute:{endpoint}", minuteRule);
        
        if (!minuteResult.IsAllowed)
        {
            context.Result = new ObjectResult(new
            {
                error = "RateLimitExceeded",
                message = "Too many requests per minute",
                limit = minuteResult.Limit,
                retryAfter = minuteResult.RetryAfter
            })
            {
                StatusCode = 429
            };
            
            context.HttpContext.Response.Headers.Add("X-RateLimit-Limit", minuteResult.Limit.ToString());
            context.HttpContext.Response.Headers.Add("X-RateLimit-Remaining", minuteResult.Remaining.ToString());
            context.HttpContext.Response.Headers.Add("X-RateLimit-Reset", minuteResult.ResetTime.ToUnixTimeSeconds().ToString());
            if (minuteResult.RetryAfter.HasValue)
            {
                context.HttpContext.Response.Headers.Add("Retry-After", minuteResult.RetryAfter.Value.ToString());
            }
            return;
        }

        // 检查每小时限制
        var hourRule = new RateLimitRule
        {
            Limit = _requestsPerHour,
            Window = TimeSpan.FromHours(1)
        };
        var hourResult = await rateLimitingService.CheckRateLimitAsync($"{clientId}:hour:{endpoint}", hourRule);
        
        if (!hourResult.IsAllowed)
        {
            context.Result = new ObjectResult(new
            {
                error = "RateLimitExceeded",
                message = "Too many requests per hour",
                limit = hourResult.Limit,
                retryAfter = hourResult.RetryAfter
            })
            {
                StatusCode = 429
            };
            
            context.HttpContext.Response.Headers.Add("X-RateLimit-Limit-Hour", hourResult.Limit.ToString());
            context.HttpContext.Response.Headers.Add("X-RateLimit-Remaining-Hour", hourResult.Remaining.ToString());
            context.HttpContext.Response.Headers.Add("X-RateLimit-Reset-Hour", hourResult.ResetTime.ToUnixTimeSeconds().ToString());
            if (hourResult.RetryAfter.HasValue)
            {
                context.HttpContext.Response.Headers.Add("Retry-After", hourResult.RetryAfter.Value.ToString());
            }
            return;
        }

        // 检查每天限制
        var dayRule = new RateLimitRule
        {
            Limit = _requestsPerDay,
            Window = TimeSpan.FromDays(1)
        };
        var dayResult = await rateLimitingService.CheckRateLimitAsync($"{clientId}:day:{endpoint}", dayRule);
        
        if (!dayResult.IsAllowed)
        {
            context.Result = new ObjectResult(new
            {
                error = "RateLimitExceeded",
                message = "Too many requests per day",
                limit = dayResult.Limit,
                retryAfter = dayResult.RetryAfter
            })
            {
                StatusCode = 429
            };
            
            context.HttpContext.Response.Headers.Add("X-RateLimit-Limit-Day", dayResult.Limit.ToString());
            context.HttpContext.Response.Headers.Add("X-RateLimit-Remaining-Day", dayResult.Remaining.ToString());
            context.HttpContext.Response.Headers.Add("X-RateLimit-Reset-Day", dayResult.ResetTime.ToUnixTimeSeconds().ToString());
            if (dayResult.RetryAfter.HasValue)
            {
                context.HttpContext.Response.Headers.Add("Retry-After", dayResult.RetryAfter.Value.ToString());
            }
            return;
        }

        // 添加速率限制响应头
        context.HttpContext.Response.Headers.Add("X-RateLimit-Limit", minuteResult.Limit.ToString());
        context.HttpContext.Response.Headers.Add("X-RateLimit-Remaining", minuteResult.Remaining.ToString());
        context.HttpContext.Response.Headers.Add("X-RateLimit-Reset", minuteResult.ResetTime.ToUnixTimeSeconds().ToString());

        await next();
    }

    private string GetClientIdentifier(HttpContext context)
    {
        // 优先使用API密钥
        var apiKeyId = context.User.FindFirst("ApiKeyId")?.Value;
        if (!string.IsNullOrEmpty(apiKeyId))
        {
            return $"apikey:{apiKeyId}";
        }

        // 使用认证用户
        if (context.User.Identity?.IsAuthenticated == true)
        {
            var userId = context.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (!string.IsNullOrEmpty(userId))
            {
                return $"user:{userId}";
            }
        }

        // 使用IP地址
        return $"ip:{GetClientIpAddress(context)}";
    }

    private string GetClientIpAddress(HttpContext context)
    {
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (ips.Length > 0)
            {
                return ips[0].Trim();
            }
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}