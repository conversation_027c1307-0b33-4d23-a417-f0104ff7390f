using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Infrastructure.AI;

/// <summary>
/// AI提供商管理器实现
/// </summary>
public class AIProviderManager : IAIProviderManager
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AIProviderManager> _logger;
    private readonly Dictionary<AIProviderType, Type> _providerTypes;
    private readonly Dictionary<string, AIProviderType> _modelToProviderMap;

    public AIProviderManager(
        IServiceProvider serviceProvider,
        ILogger<AIProviderManager> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        
        // 注册提供商类型
        _providerTypes = new Dictionary<AIProviderType, Type>
        {
            { AIProviderType.SemanticKernel, typeof(Providers.SemanticKernelProvider) },
            { AIProviderType.Dify, typeof(Providers.DifyProvider) },
            { AIProviderType.Mock, typeof(Providers.MockAIProvider) }
        };
        
        // 模型到提供商的映射
        _modelToProviderMap = new Dictionary<string, AIProviderType>
        {
            // Semantic Kernel models
            { "gpt-4-turbo-preview", AIProviderType.SemanticKernel },
            { "gpt-4", AIProviderType.SemanticKernel },
            { "gpt-3.5-turbo", AIProviderType.SemanticKernel },
            { "gpt-3.5-turbo-16k", AIProviderType.SemanticKernel },
            { "text-embedding-ada-002", AIProviderType.SemanticKernel },
            { "text-embedding-3-small", AIProviderType.SemanticKernel },
            { "text-embedding-3-large", AIProviderType.SemanticKernel },
            
            // Dify models
            { "dify-chat-app", AIProviderType.Dify },
            { "dify-completion-app", AIProviderType.Dify },
            { "dify-workflow-app", AIProviderType.Dify },
            
            // Mock models (for testing)
            { "mock-gpt-3.5", AIProviderType.Mock },
            { "mock-gpt-4", AIProviderType.Mock },
            { "mock-embedding-small", AIProviderType.Mock },
            { "mock-embedding-large", AIProviderType.Mock }
        };
    }

    public IAIProvider GetProvider(AIProviderType type)
    {
        if (!_providerTypes.TryGetValue(type, out var providerType))
        {
            throw new BusinessException($"不支持的AI提供商类型: {type}");
        }

        var provider = _serviceProvider.GetService(providerType) as IAIProvider;
        if (provider == null)
        {
            throw new InvalidOperationException($"AI提供商 {type} 未正确配置");
        }

        _logger.LogDebug("Retrieved AI provider for type {ProviderType}", type);
        return provider;
    }

    public IAIProvider GetProviderByModel(string model)
    {
        if (!_modelToProviderMap.TryGetValue(model, out var providerType))
        {
            throw new BusinessException($"未找到支持模型 {model} 的AI提供商");
        }

        return GetProvider(providerType);
    }

    public IEnumerable<IAIProvider> GetAvailableProviders()
    {
        var providers = new List<IAIProvider>();
        
        foreach (var providerType in _providerTypes.Keys)
        {
            try
            {
                var provider = GetProvider(providerType);
                providers.Add(provider);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get provider {ProviderType}", providerType);
            }
        }
        
        return providers;
    }

    public async Task<bool> IsProviderAvailableAsync(AIProviderType type, CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = GetProvider(type);
            return await provider.IsAvailableAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking provider availability for {ProviderType}", type);
            return false;
        }
    }
}