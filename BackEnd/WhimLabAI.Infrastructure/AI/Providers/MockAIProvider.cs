using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.AI.Providers;

/// <summary>
/// Mock AI provider for testing purposes
/// </summary>
public class MockAIProvider : IAIProvider
{
    private readonly Random _random = new();
    
    public string ProviderName => "Mock";
    public AIProviderType ProviderType => AIProviderType.Mock;
    
    public IReadOnlyList<string> SupportedModels => new[]
    {
        "mock-gpt-3.5",
        "mock-gpt-4",
        "mock-embedding-small",
        "mock-embedding-large"
    };

    public async Task<Result<AIResponse>> SendMessageAsync(AIRequest request, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken); // Simulate network delay

        var userMessage = request.Messages.LastOrDefault(m => m.Role == "user")?.Content ?? "Hello";
        var responseText = GenerateMockResponse(userMessage);
        
        var response = new AIResponse
        {
            Content = responseText,
            TokensUsed = CountTokens(responseText) + CountTokens(userMessage),
            PromptTokens = CountTokens(userMessage),
            CompletionTokens = CountTokens(responseText),
            FinishReason = "stop",
            Metadata = new Dictionary<string, object>
            {
                ["model"] = request.Model,
                ["provider"] = "mock"
            }
        };

        return Result<AIResponse>.Success(response);
    }

    public async IAsyncEnumerable<Result<AIStreamChunk>> StreamMessageAsync(
        AIRequest request,
        [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var userMessage = request.Messages.LastOrDefault(m => m.Role == "user")?.Content ?? "Hello";
        var fullResponse = GenerateMockResponse(userMessage);
        var words = fullResponse.Split(' ');

        foreach (var word in words)
        {
            if (cancellationToken.IsCancellationRequested)
                yield break;

            await Task.Delay(50, cancellationToken); // Simulate streaming delay

            yield return Result<AIStreamChunk>.Success(new AIStreamChunk
            {
                Content = word + " ",
                IsComplete = false,
                TokenCount = CountTokens(word)
            });
        }

        yield return Result<AIStreamChunk>.Success(new AIStreamChunk
        {
            Content = null,
            IsComplete = true,
            TokenCount = CountTokens(fullResponse),
            Metadata = new Dictionary<string, object>
            {
                ["model"] = request.Model,
                ["totalTokens"] = CountTokens(fullResponse)
            }
        });
    }

    public Task<Result<int>> GetTokenCountAsync(string text, string? model = null, CancellationToken cancellationToken = default)
    {
        var count = CountTokens(text);
        return Task.FromResult(Result<int>.Success(count));
    }

    public Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(true);
    }

    public Task<Result<AIModelInfo>> GetModelInfoAsync(string model, CancellationToken cancellationToken = default)
    {
        var info = model switch
        {
            "mock-gpt-3.5" => new AIModelInfo
            {
                ModelId = "mock-gpt-3.5",
                DisplayName = "Mock GPT-3.5",
                MaxTokens = 4096,
                ContextLength = 4096,
                SupportsStreaming = true,
                SupportsVision = false,
                SupportsEmbedding = false,
                SupportedLanguages = new List<string> { "en", "zh", "ja", "es", "fr" },
                PricePerMillionTokens = 0.5m
            },
            "mock-gpt-4" => new AIModelInfo
            {
                ModelId = "mock-gpt-4",
                DisplayName = "Mock GPT-4",
                MaxTokens = 8192,
                ContextLength = 8192,
                SupportsStreaming = true,
                SupportsVision = true,
                SupportsEmbedding = false,
                SupportedLanguages = new List<string> { "en", "zh", "ja", "es", "fr", "de", "ru" },
                PricePerMillionTokens = 30m
            },
            "mock-embedding-small" => new AIModelInfo
            {
                ModelId = "mock-embedding-small",
                DisplayName = "Mock Embedding Small",
                MaxTokens = 8191,
                ContextLength = 8191,
                SupportsStreaming = false,
                SupportsVision = false,
                SupportsEmbedding = true,
                SupportedLanguages = new List<string> { "en", "zh" },
                PricePerMillionTokens = 0.1m,
                Capabilities = new Dictionary<string, object> { ["dimensions"] = 1536 }
            },
            _ => new AIModelInfo
            {
                ModelId = model,
                DisplayName = $"Mock {model}",
                MaxTokens = 4096,
                ContextLength = 4096,
                SupportsStreaming = true,
                SupportsVision = false,
                SupportsEmbedding = false,
                PricePerMillionTokens = 1m
            }
        };

        return Task.FromResult(Result<AIModelInfo>.Success(info));
    }

    public async Task<Result<AIEmbeddingResponse>> GetEmbeddingAsync(AIEmbeddingRequest request, CancellationToken cancellationToken = default)
    {
        await Task.Delay(50, cancellationToken); // Simulate processing

        var embeddings = new List<float[]>();
        var dimensions = request.Dimensions ?? 1536;

        foreach (var text in request.Texts)
        {
            var embedding = GenerateMockEmbedding(text, dimensions);
            embeddings.Add(embedding);
        }

        var response = new AIEmbeddingResponse
        {
            Embeddings = embeddings,
            TokensUsed = request.Texts.Sum(CountTokens),
            Model = request.Model
        };

        return Result<AIEmbeddingResponse>.Success(response);
    }

    private string GenerateMockResponse(string userMessage)
    {
        var responses = new[]
        {
            $"This is a mock response to your message: '{userMessage}'. The mock AI provider is working correctly!",
            $"I received your message: '{userMessage}'. This is a simulated response for testing purposes.",
            $"Mock AI acknowledges: '{userMessage}'. All systems are functioning normally.",
            $"Test response generated for: '{userMessage}'. The integration is working as expected.",
            $"Hello! I'm the mock AI responding to: '{userMessage}'. Everything looks good!"
        };

        return responses[_random.Next(responses.Length)];
    }

    private float[] GenerateMockEmbedding(string text, int dimensions)
    {
        var embedding = new float[dimensions];
        var seed = text.GetHashCode();
        var rng = new Random(seed);

        for (int i = 0; i < dimensions; i++)
        {
            // Generate normalized values between -1 and 1
            embedding[i] = (float)(rng.NextDouble() * 2 - 1);
        }

        // Normalize the vector
        var magnitude = (float)Math.Sqrt(embedding.Sum(x => x * x));
        for (int i = 0; i < dimensions; i++)
        {
            embedding[i] /= magnitude;
        }

        return embedding;
    }

    private int CountTokens(string text)
    {
        // Simple approximation: ~4 characters per token
        return Math.Max(1, text.Length / 4);
    }
}