# DIFY Provider Configuration

## Overview
The DIFY integration has been restructured to support multiple agent instances, each with its own API key.

## Key Changes

### 1. Configuration Structure
Previously, API keys were stored in appsettings.json:
```json
"ApiKeys": {
  "dify-chat-app": "app-chat-key",
  "dify-completion-app": "app-completion-key",
  "dify-workflow-app": "app-workflow-key"
}
```

Now, only app type endpoints are configured:
```json
"AppTypes": {
  "Chat": "/chat-messages",
  "Completion": "/completion-messages",
  "Workflow": "/workflows/run"
}
```

### 2. API Key Management
- Each Agent entity stores its own DIFY API key using the `Agent<PERSON>piKey` entity
- API keys are encrypted and stored in the database
- Each DIFY agent instance can have a unique API key

### 3. Usage Pattern
When calling DIFY services, the API key must be provided:
```csharp
// Get the agent's DIFY API key
var apiKey = agent.GetDifyApiKey()?.DecryptApiKey();

// Pass it to the DIFY provider
var response = await difyProvider.RunAppAsync(new DifyAppRequest
{
    AppId = agent.CurrentVersion.ModelConfig.AdditionalSettings["DifyAppId"],
    AppType = "Chat", // or "Completion", "Workflow"
    Query = userMessage,
    User = userId.ToString()
}, apiKey, cancellationToken);
```

### 4. Benefits
- Unlimited DIFY agent instances
- Each agent has its own secure API key
- No hardcoded credentials in configuration files
- Better security and flexibility