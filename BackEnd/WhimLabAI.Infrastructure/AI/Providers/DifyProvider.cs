using System.Net.Http.Headers;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Infrastructure.AI.Configuration;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.AI.Providers;

/// <summary>
/// Dify平台AI提供商实现
/// </summary>
public class DifyProvider : IDifyProvider
{
    private readonly DifyProviderSettings _settings;
    private readonly ILogger<DifyProvider> _logger;
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public string ProviderName => "Dify";
    public AIProviderType ProviderType => AIProviderType.Dify;
    
    public IReadOnlyList<string> SupportedModels { get; } = new List<string>
    {
        "dify-chat-app",
        "dify-completion-app",
        "dify-workflow-app"
    };

    public DifyProvider(
        IOptions<DifyProviderSettings> settings,
        ILogger<DifyProvider> logger,
        IHttpClientFactory httpClientFactory)
    {
        _settings = settings.Value;
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient("Dify");
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<Result<AIResponse>> SendMessageAsync(AIRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // API key should be provided in request.AdditionalParameters
            var apiKey = request.AdditionalParameters?.GetValueOrDefault("ApiKey")?.ToString();
            if (string.IsNullOrEmpty(apiKey))
            {
                return Result<AIResponse>.Failure("API_KEY_MISSING", "未提供API密钥");
            }

            var appType = request.AdditionalParameters?.GetValueOrDefault("AppType")?.ToString() ?? "Chat";
            var endpoint = GetEndpointForAppType(appType);
            var requestBody = BuildDifyRequest(request);
            
            using var httpRequest = new HttpRequestMessage(HttpMethod.Post, $"{_settings.BaseUrl}{endpoint}");
            httpRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
            httpRequest.Content = new StringContent(
                JsonSerializer.Serialize(requestBody, _jsonOptions),
                Encoding.UTF8,
                "application/json");
            
            var startTime = DateTime.UtcNow;
            var response = await _httpClient.SendAsync(httpRequest, cancellationToken);
            var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Dify API error: {StatusCode} - {Error}", response.StatusCode, errorContent);
                return Result<AIResponse>.Failure("DIFY_API_ERROR", $"Dify API返回错误: {response.StatusCode}");
            }
            
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var difyResponse = JsonSerializer.Deserialize<DifyResponse>(responseContent, _jsonOptions);
            
            if (difyResponse == null)
            {
                return Result<AIResponse>.Failure("RESPONSE_PARSE_ERROR", "无法解析Dify响应");
            }
            
            return Result<AIResponse>.Success(new AIResponse
            {
                Content = difyResponse.Answer ?? string.Empty,
                TokensUsed = difyResponse.Metadata?.Usage?.TotalTokens ?? 0,
                PromptTokens = difyResponse.Metadata?.Usage?.PromptTokens ?? 0,
                CompletionTokens = difyResponse.Metadata?.Usage?.CompletionTokens ?? 0,
                FinishReason = "stop",
                Metadata = new Dictionary<string, object>
                {
                    ["ResponseTimeMs"] = responseTime,
                    ["ConversationId"] = difyResponse.ConversationId ?? string.Empty,
                    ["MessageId"] = difyResponse.MessageId ?? string.Empty
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Dify SendMessageAsync");
            return Result<AIResponse>.Failure("DIFY_ERROR", $"Dify处理失败: {ex.Message}");
        }
    }

    public async IAsyncEnumerable<Result<AIStreamChunk>> StreamMessageAsync(
        AIRequest request,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        HttpResponseMessage? response = null;
        Stream? stream = null;
        StreamReader? reader = null;
        
        try
        {
            var apiKey = request.AdditionalParameters?.GetValueOrDefault("ApiKey")?.ToString();
            if (string.IsNullOrEmpty(apiKey))
            {
                yield return Result<AIStreamChunk>.Failure("API_KEY_MISSING", "未提供API密钥");
                yield break;
            }

            var appType = request.AdditionalParameters?.GetValueOrDefault("AppType")?.ToString() ?? "Chat";
            var endpoint = GetEndpointForAppType(appType);
            var requestBody = BuildDifyRequest(request);
            requestBody["response_mode"] = "streaming";
            
            using var httpRequest = new HttpRequestMessage(HttpMethod.Post, $"{_settings.BaseUrl}{endpoint}");
            httpRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
            httpRequest.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("text/event-stream"));
            httpRequest.Content = new StringContent(
                JsonSerializer.Serialize(requestBody, _jsonOptions),
                Encoding.UTF8,
                "application/json");
            
            response = await _httpClient.SendAsync(httpRequest, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                yield return Result<AIStreamChunk>.Failure("DIFY_STREAM_ERROR", $"Dify流式API错误: {response.StatusCode}");
                yield break;
            }
            
            stream = await response.Content.ReadAsStreamAsync(cancellationToken);
            reader = new StreamReader(stream);
            
            var totalContent = new StringBuilder();
            var tokenCount = 0;
            
            while (!reader.EndOfStream && !cancellationToken.IsCancellationRequested)
            {
                var line = await reader.ReadLineAsync(cancellationToken);
                if (string.IsNullOrEmpty(line))
                    continue;
                
                if (line.StartsWith("data: "))
                {
                    var data = line.Substring(6);
                    if (data == "[DONE]")
                    {
                        yield return Result<AIStreamChunk>.Success(new AIStreamChunk
                        {
                            Content = null,
                            IsComplete = true,
                            TokenCount = tokenCount,
                            Metadata = new Dictionary<string, object>
                            {
                                ["TotalContent"] = totalContent.ToString()
                            }
                        });
                        break;
                    }
                    
                    DifyStreamEvent? eventData = null;
                    Exception? parseException = null;
                    
                    try
                    {
                        eventData = JsonSerializer.Deserialize<DifyStreamEvent>(data, _jsonOptions);
                    }
                    catch (JsonException ex)
                    {
                        parseException = ex;
                    }
                    
                    if (parseException != null)
                    {
                        _logger.LogWarning(parseException, "Failed to parse Dify stream event: {Data}", data);
                    }
                    else if (eventData?.Answer != null)
                    {
                        totalContent.Append(eventData.Answer);
                        tokenCount++;
                        
                        yield return Result<AIStreamChunk>.Success(new AIStreamChunk
                        {
                            Content = eventData.Answer,
                            IsComplete = false,
                            TokenCount = tokenCount
                        });
                    }
                }
            }
        }
        finally
        {
            reader?.Dispose();
            stream?.Dispose();
            response?.Dispose();
        }
    }

    public async Task<Result<int>> GetTokenCountAsync(string text, string? model = null, CancellationToken cancellationToken = default)
    {
        // Dify doesn't provide a direct token counting API
        // Use approximation
        await Task.CompletedTask;
        var approximateTokens = text.Length / 4;
        return Result<int>.Success(approximateTokens);
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Basic connectivity check without API key
            using var request = new HttpRequestMessage(HttpMethod.Get, _settings.BaseUrl);
            var response = await _httpClient.SendAsync(request, cancellationToken);
            return true; // If we can reach the server, consider it available
        }
        catch
        {
            return false;
        }
    }

    public async Task<Result<AIModelInfo>> GetModelInfoAsync(string model, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        
        var modelInfo = model switch
        {
            "dify-chat-app" => new AIModelInfo
            {
                ModelId = model,
                DisplayName = "Dify Chat Application",
                MaxTokens = 4096,
                ContextLength = 32000,
                SupportsStreaming = true,
                SupportsVision = true,
                SupportsEmbedding = false,
                PricePerMillionTokens = 0 // Pricing depends on underlying model
            },
            "dify-completion-app" => new AIModelInfo
            {
                ModelId = model,
                DisplayName = "Dify Completion Application",
                MaxTokens = 4096,
                ContextLength = 16000,
                SupportsStreaming = true,
                SupportsVision = false,
                SupportsEmbedding = false,
                PricePerMillionTokens = 0
            },
            "dify-workflow-app" => new AIModelInfo
            {
                ModelId = model,
                DisplayName = "Dify Workflow Application",
                MaxTokens = 8192,
                ContextLength = 64000,
                SupportsStreaming = false,
                SupportsVision = true,
                SupportsEmbedding = false,
                PricePerMillionTokens = 0
            },
            _ => null
        };
        
        if (modelInfo == null)
        {
            return Result<AIModelInfo>.Failure("MODEL_NOT_FOUND", $"模型 {model} 不存在");
        }
        
        return Result<AIModelInfo>.Success(modelInfo);
    }

    public Task<Result<AIEmbeddingResponse>> GetEmbeddingAsync(AIEmbeddingRequest request, CancellationToken cancellationToken = default)
    {
        // Dify doesn't directly support embeddings
        return Task.FromResult(Result<AIEmbeddingResponse>.Failure("NOT_SUPPORTED", "Dify不支持嵌入生成"));
    }

    private string GetEndpointForAppType(string appType)
    {
        return _settings.AppTypes.GetValueOrDefault(appType, "/chat-messages");
    }

    private Dictionary<string, object> BuildDifyRequest(AIRequest request)
    {
        var requestBody = new Dictionary<string, object>
        {
            ["query"] = request.Messages.LastOrDefault(m => m.Role == "user")?.Content ?? string.Empty,
            ["response_mode"] = "blocking",
            ["user"] = request.AdditionalParameters?.GetValueOrDefault("UserId")?.ToString() ?? Guid.NewGuid().ToString()
        };
        
        // Add conversation history
        var conversationId = request.AdditionalParameters?.GetValueOrDefault("ConversationId")?.ToString();
        if (!string.IsNullOrEmpty(conversationId))
        {
            requestBody["conversation_id"] = conversationId;
        }
        
        // Add inputs for workflow apps
        var appType = request.AdditionalParameters?.GetValueOrDefault("AppType")?.ToString() ?? "Chat";
        if (appType == "Workflow" && request.AdditionalParameters?.ContainsKey("Inputs") == true)
        {
            requestBody["inputs"] = request.AdditionalParameters["Inputs"];
        }
        
        return requestBody;
    }

    // IDifyProvider Implementation
    public async Task<DifyAppResponse> RunAppAsync(DifyAppRequest request, string apiKey, CancellationToken cancellationToken = default)
    {
        var aiRequest = new AIRequest
        {
            Messages = new List<AIMessage>
            {
                new AIMessage { Role = "user", Content = request.Query }
            },
            AdditionalParameters = new Dictionary<string, object>
            {
                ["ApiKey"] = apiKey,
                ["AppType"] = request.AppType,
                ["UserId"] = request.User ?? Guid.NewGuid().ToString(),
                ["ConversationId"] = request.ConversationId ?? string.Empty
            }
        };

        if (request.Inputs != null)
        {
            aiRequest.AdditionalParameters["Inputs"] = request.Inputs;
        }

        var result = await SendMessageAsync(aiRequest, cancellationToken);
        
        if (!result.IsSuccess)
        {
            throw new InvalidOperationException($"Dify API error: {result.Error}");
        }

        return new DifyAppResponse
        {
            Answer = result.Value!.Content,
            ConversationId = result.Value.Metadata?.GetValueOrDefault("ConversationId")?.ToString() ?? string.Empty,
            MessageId = result.Value.Metadata?.GetValueOrDefault("MessageId")?.ToString() ?? string.Empty,
            TotalTokens = result.Value.TokensUsed,
            Metadata = result.Value.Metadata
        };
    }

    public async Task<DifyWorkflowResponse> RunWorkflowAsync(DifyWorkflowRequest request, string apiKey, CancellationToken cancellationToken = default)
    {
        var endpoint = GetEndpointForAppType("Workflow");
        var requestBody = new Dictionary<string, object>
        {
            ["inputs"] = request.Inputs,
            ["user"] = request.User ?? Guid.NewGuid().ToString()
        };

        using var httpRequest = new HttpRequestMessage(HttpMethod.Post, $"{_settings.BaseUrl}{endpoint}");
        httpRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
        httpRequest.Content = new StringContent(
            JsonSerializer.Serialize(requestBody, _jsonOptions),
            Encoding.UTF8,
            "application/json");

        var response = await _httpClient.SendAsync(httpRequest, cancellationToken);
        
        if (!response.IsSuccessStatusCode)
        {
            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
            throw new InvalidOperationException($"Dify Workflow API error: {response.StatusCode} - {errorContent}");
        }

        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        var workflowData = JsonSerializer.Deserialize<DifyWorkflowResponse>(responseContent, _jsonOptions);
        
        return workflowData ?? new DifyWorkflowResponse();
    }

    public async Task<List<DifyAppInfo>> GetAppsAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        using var httpRequest = new HttpRequestMessage(HttpMethod.Get, $"{_settings.BaseUrl}/apps");
        httpRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);

        var response = await _httpClient.SendAsync(httpRequest, cancellationToken);
        
        if (!response.IsSuccessStatusCode)
        {
            return new List<DifyAppInfo>();
        }

        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        var apps = JsonSerializer.Deserialize<List<DifyAppInfo>>(responseContent, _jsonOptions);
        
        return apps ?? new List<DifyAppInfo>();
    }

    public async Task<DifyConversationHistory> GetConversationHistoryAsync(string conversationId, string apiKey, CancellationToken cancellationToken = default)
    {
        using var httpRequest = new HttpRequestMessage(HttpMethod.Get, $"{_settings.BaseUrl}/conversations/{conversationId}/messages");
        httpRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);

        var response = await _httpClient.SendAsync(httpRequest, cancellationToken);
        
        if (!response.IsSuccessStatusCode)
        {
            return new DifyConversationHistory { ConversationId = conversationId };
        }

        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        var history = JsonSerializer.Deserialize<DifyConversationHistory>(responseContent, _jsonOptions);
        
        return history ?? new DifyConversationHistory { ConversationId = conversationId };
    }
}

// Dify response models
internal class DifyResponse
{
    public string? Answer { get; set; }
    public string? ConversationId { get; set; }
    public string? MessageId { get; set; }
    public DifyMetadata? Metadata { get; set; }
}

internal class DifyMetadata
{
    public DifyUsage? Usage { get; set; }
}

internal class DifyUsage
{
    public int PromptTokens { get; set; }
    public int CompletionTokens { get; set; }
    public int TotalTokens { get; set; }
}

internal class DifyStreamEvent
{
    public string? Event { get; set; }
    public string? Answer { get; set; }
    public string? ConversationId { get; set; }
    public string? MessageId { get; set; }
}