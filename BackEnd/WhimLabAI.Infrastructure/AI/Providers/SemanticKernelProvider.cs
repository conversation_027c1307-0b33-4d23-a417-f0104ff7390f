using System.Runtime.CompilerServices;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
#pragma warning disable SKEXP0001
using Microsoft.SemanticKernel.Embeddings;
#pragma warning restore SKEXP0001
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.AI.Providers;

/// <summary>
/// Semantic Kernel AI提供商实现
/// </summary>
public class SemanticKernelProvider : IAIProvider
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<SemanticKernelProvider> _logger;
    private readonly Dictionary<string, Kernel> _kernels = new();
    private readonly SemaphoreSlim _kernelLock = new(1, 1);

    public string ProviderName => "Semantic Kernel";
    public AIProviderType ProviderType => AIProviderType.SemanticKernel;
    
    public IReadOnlyList<string> SupportedModels { get; } = new List<string>
    {
        "gpt-4-turbo-preview",
        "gpt-4",
        "gpt-3.5-turbo",
        "gpt-3.5-turbo-16k",
        "text-embedding-ada-002",
        "text-embedding-3-small",
        "text-embedding-3-large"
    };

    public SemanticKernelProvider(
        IConfiguration configuration,
        ILogger<SemanticKernelProvider> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<Result<AIResponse>> SendMessageAsync(AIRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var kernel = await GetOrCreateKernelAsync(request.Model, cancellationToken);
            var chatService = kernel.GetRequiredService<IChatCompletionService>();
            
            var chatHistory = new ChatHistory();
            
            // Add system message if present
            var systemMessage = request.Messages.FirstOrDefault(m => m.Role == "system");
            if (systemMessage != null)
            {
                chatHistory.AddSystemMessage(systemMessage.Content);
            }
            
            // Add conversation messages
            foreach (var message in request.Messages.Where(m => m.Role != "system"))
            {
                switch (message.Role.ToLower())
                {
                    case "user":
                        chatHistory.AddUserMessage(message.Content);
                        break;
                    case "assistant":
                        chatHistory.AddAssistantMessage(message.Content);
                        break;
                }
            }
            
            // Create execution settings
            var executionSettings = new OpenAIPromptExecutionSettings
            {
                Temperature = request.Temperature,
                MaxTokens = request.MaxTokens,
                TopP = request.TopP ?? 1.0,
                StopSequences = request.StopSequence != null ? new[] { request.StopSequence } : null
            };
            
            // Get completion
            var startTime = DateTime.UtcNow;
            var result = await chatService.GetChatMessageContentAsync(
                chatHistory,
                executionSettings,
                kernel,
                cancellationToken);
            
            var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            
            // Extract token usage from metadata
            var usage = GetTokenUsage(result);
            
            return Result<AIResponse>.Success(new AIResponse
            {
                Content = result.Content ?? string.Empty,
                TokensUsed = usage.TotalTokens,
                PromptTokens = usage.PromptTokens,
                CompletionTokens = usage.CompletionTokens,
                FinishReason = result.Metadata?["FinishReason"]?.ToString(),
                Metadata = new Dictionary<string, object>
                {
                    ["ResponseTimeMs"] = responseTime,
                    ["Model"] = request.Model
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SendMessageAsync for model {Model}", request.Model);
            return Result<AIResponse>.Failure("AI_ERROR", $"处理请求失败: {ex.Message}");
        }
    }

    public async IAsyncEnumerable<Result<AIStreamChunk>> StreamMessageAsync(
        AIRequest request,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        Kernel? kernel = null;
        IChatCompletionService? chatService = null;
        Exception? caughtException = null;
        
        // Initialize outside of enumeration
        try
        {
            kernel = await GetOrCreateKernelAsync(request.Model, cancellationToken);
            chatService = kernel.GetRequiredService<IChatCompletionService>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing kernel for model {Model}", request.Model);
            caughtException = ex;
        }
        
        if (caughtException != null)
        {
            yield return Result<AIStreamChunk>.Failure("INIT_ERROR", $"初始化失败: {caughtException.Message}");
            yield break;
        }
        
        var chatHistory = new ChatHistory();
        
        // Add messages to history
        foreach (var message in request.Messages)
        {
            switch (message.Role.ToLower())
            {
                case "system":
                    chatHistory.AddSystemMessage(message.Content);
                    break;
                case "user":
                    chatHistory.AddUserMessage(message.Content);
                    break;
                case "assistant":
                    chatHistory.AddAssistantMessage(message.Content);
                    break;
            }
        }
        
        var executionSettings = new OpenAIPromptExecutionSettings
        {
            Temperature = request.Temperature,
            MaxTokens = request.MaxTokens,
            TopP = request.TopP ?? 1.0
        };
        
        var responseBuilder = new StringBuilder();
        var tokenCount = 0;
        var hasError = false;
        
        // Stream messages without try-catch around yield
        await foreach (var streamingContent in chatService.GetStreamingChatMessageContentsAsync(
            chatHistory,
            executionSettings,
            kernel,
            cancellationToken).ConfigureAwait(false))
        {
            try
            {
                if (!string.IsNullOrEmpty(streamingContent.Content))
                {
                    responseBuilder.Append(streamingContent.Content);
                    tokenCount++;
                }
            }
            catch (Exception ex)
            {
                caughtException = ex;
                hasError = true;
                break;
            }
            
            if (!hasError && !string.IsNullOrEmpty(streamingContent.Content))
            {
                yield return Result<AIStreamChunk>.Success(new AIStreamChunk
                {
                    Content = streamingContent.Content,
                    IsComplete = false,
                    TokenCount = tokenCount
                });
            }
        }
        
        if (hasError && caughtException != null)
        {
            _logger.LogError(caughtException, "Error in StreamMessageAsync for model {Model}", request.Model);
            yield return Result<AIStreamChunk>.Failure("STREAM_ERROR", $"流式处理失败: {caughtException.Message}");
        }
        else
        {
            // Send completion chunk
            yield return Result<AIStreamChunk>.Success(new AIStreamChunk
            {
                Content = null,
                IsComplete = true,
                TokenCount = tokenCount,
                Metadata = new Dictionary<string, object>
                {
                    ["TotalContent"] = responseBuilder.ToString(),
                    ["Model"] = request.Model
                }
            });
        }
    }

    public async Task<Result<int>> GetTokenCountAsync(string text, string? model = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // 简化的Token计数实现
            // 实际应该使用tiktoken或其他准确的tokenizer
            var approximateTokens = text.Length / 4; // 粗略估计：平均每4个字符一个token
            
            return Result<int>.Success(approximateTokens);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error counting tokens");
            return Result<int>.Failure("TOKEN_COUNT_ERROR", "计算Token数量失败");
        }
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var testKernel = await GetOrCreateKernelAsync("gpt-3.5-turbo", cancellationToken);
            return testKernel != null;
        }
        catch
        {
            return false;
        }
    }

    public async Task<Result<AIModelInfo>> GetModelInfoAsync(string model, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // 避免编译警告
        
        var modelInfo = model switch
        {
            "gpt-4-turbo-preview" => new AIModelInfo
            {
                ModelId = model,
                DisplayName = "GPT-4 Turbo",
                MaxTokens = 4096,
                ContextLength = 128000,
                SupportsStreaming = true,
                SupportsVision = true,
                SupportsEmbedding = false,
                PricePerMillionTokens = 10m
            },
            "gpt-4" => new AIModelInfo
            {
                ModelId = model,
                DisplayName = "GPT-4",
                MaxTokens = 4096,
                ContextLength = 8192,
                SupportsStreaming = true,
                SupportsVision = false,
                SupportsEmbedding = false,
                PricePerMillionTokens = 30m
            },
            "gpt-3.5-turbo" => new AIModelInfo
            {
                ModelId = model,
                DisplayName = "GPT-3.5 Turbo",
                MaxTokens = 4096,
                ContextLength = 4096,
                SupportsStreaming = true,
                SupportsVision = false,
                SupportsEmbedding = false,
                PricePerMillionTokens = 0.5m
            },
            "text-embedding-ada-002" => new AIModelInfo
            {
                ModelId = model,
                DisplayName = "Text Embedding Ada v2",
                MaxTokens = 8191,
                ContextLength = 8191,
                SupportsStreaming = false,
                SupportsVision = false,
                SupportsEmbedding = true,
                PricePerMillionTokens = 0.1m
            },
            _ => null
        };
        
        if (modelInfo == null)
        {
            return Result<AIModelInfo>.Failure("MODEL_NOT_FOUND", $"模型 {model} 不存在");
        }
        
        return Result<AIModelInfo>.Success(modelInfo);
    }

    public async Task<Result<AIEmbeddingResponse>> GetEmbeddingAsync(AIEmbeddingRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var kernel = await GetOrCreateKernelAsync(request.Model, cancellationToken);
#pragma warning disable SKEXP0001
            var embeddingService = kernel.GetRequiredService<ITextEmbeddingGenerationService>();
#pragma warning restore SKEXP0001
            
            var embeddings = new List<float[]>();
            var totalTokens = 0;
            
            foreach (var text in request.Texts)
            {
                var embedding = await embeddingService.GenerateEmbeddingAsync(text, kernel, cancellationToken);
                embeddings.Add(embedding.ToArray());
                totalTokens += text.Length / 4; // 粗略估计
            }
            
            return Result<AIEmbeddingResponse>.Success(new AIEmbeddingResponse
            {
                Embeddings = embeddings,
                TokensUsed = totalTokens,
                Model = request.Model
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embeddings");
            return Result<AIEmbeddingResponse>.Failure("EMBEDDING_ERROR", $"生成嵌入失败: {ex.Message}");
        }
    }

    private async Task<Kernel> GetOrCreateKernelAsync(string model, CancellationToken cancellationToken)
    {
        await _kernelLock.WaitAsync(cancellationToken);
        try
        {
            if (_kernels.TryGetValue(model, out var existingKernel))
            {
                return existingKernel;
            }
            
            var builder = Kernel.CreateBuilder();
            
            // 配置OpenAI服务
            var apiKey = _configuration[$"AI:SemanticKernel:ApiKey"];
            var endpoint = _configuration[$"AI:SemanticKernel:Endpoint"];
            
            if (string.IsNullOrEmpty(apiKey))
            {
                throw new InvalidOperationException("Semantic Kernel API key not configured");
            }
            
            if (model.Contains("embedding"))
            {
#pragma warning disable SKEXP0010
                builder.AddOpenAITextEmbeddingGeneration(model, apiKey);
#pragma warning restore SKEXP0010
            }
            else
            {
                if (!string.IsNullOrEmpty(endpoint))
                {
                    // 使用自定义端点（如Azure OpenAI）
                    builder.AddOpenAIChatCompletion(model, endpoint, apiKey);
                }
                else
                {
                    // 使用默认OpenAI端点
                    builder.AddOpenAIChatCompletion(model, apiKey);
                }
            }
            
            var kernel = builder.Build();
            _kernels[model] = kernel;
            
            return kernel;
        }
        finally
        {
            _kernelLock.Release();
        }
    }
    
    private static (int PromptTokens, int CompletionTokens, int TotalTokens) GetTokenUsage(ChatMessageContent content)
    {
        // 尝试从元数据中提取token使用信息
        if (content.Metadata != null)
        {
            var promptTokens = 0;
            var completionTokens = 0;
            
            if (content.Metadata.TryGetValue("PromptTokens", out var pt) && int.TryParse(pt.ToString(), out var ptValue))
            {
                promptTokens = ptValue;
            }
            
            if (content.Metadata.TryGetValue("CompletionTokens", out var ct) && int.TryParse(ct.ToString(), out var ctValue))
            {
                completionTokens = ctValue;
            }
            
            return (promptTokens, completionTokens, promptTokens + completionTokens);
        }
        
        // 如果没有元数据，返回估计值
        var estimatedTokens = (content.Content?.Length ?? 0) / 4;
        return (estimatedTokens / 2, estimatedTokens / 2, estimatedTokens);
    }
}