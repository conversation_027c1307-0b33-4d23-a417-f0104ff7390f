using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Results;

#pragma warning disable SKEXP0001 // Suppress experimental API warning
#pragma warning disable SKEXP0010 // Suppress experimental API warning

namespace WhimLabAI.Infrastructure.AI;

/// <summary>
/// 嵌入服务实现
/// </summary>
public class EmbeddingService : IEmbeddingService
{
    private readonly ILogger<EmbeddingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly Dictionary<string, Kernel> _kernelCache = new();
    private readonly Dictionary<string, EmbeddingModelInfo> _modelInfoCache = new();

    public EmbeddingService(
        ILogger<EmbeddingService> logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        InitializeModelInfo();
    }

    public async Task<Result<float[]>> GenerateEmbeddingAsync(
        string text,
        string? model = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return Result<float[]>.Failure("Text cannot be empty");
            }

            model ??= _configuration["AI:DefaultEmbeddingModel"] ?? "text-embedding-3-small";
            
            var kernel = GetOrCreateKernel(model);
            var embeddingService = kernel.GetRequiredService<ITextEmbeddingGenerationService>();
            
            var embeddings = await embeddingService.GenerateEmbeddingsAsync(
                new[] { text }, 
                kernel: kernel,
                cancellationToken: cancellationToken);
            
            var embeddingsList = embeddings.ToList();
            if (embeddingsList.Count == 0)
            {
                return Result<float[]>.Failure("Failed to generate embedding");
            }
            
            var floatArray = embeddingsList[0].ToArray();
            
            _logger.LogInformation("Generated embedding for text with {Length} dimensions using model {Model}", 
                floatArray.Length, model);
            
            return Result<float[]>.Success(floatArray);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate embedding for text");
            return Result<float[]>.Failure($"Failed to generate embedding: {ex.Message}");
        }
    }

    public async Task<Result<IList<float[]>>> GenerateEmbeddingsAsync(
        IList<string> texts,
        string? model = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (texts == null || texts.Count == 0)
            {
                return Result<IList<float[]>>.Failure("Texts cannot be empty");
            }

            if (texts.Any(string.IsNullOrWhiteSpace))
            {
                return Result<IList<float[]>>.Failure("All texts must be non-empty");
            }

            model ??= _configuration["AI:DefaultEmbeddingModel"] ?? "text-embedding-3-small";
            
            var kernel = GetOrCreateKernel(model);
            var embeddingService = kernel.GetRequiredService<ITextEmbeddingGenerationService>();
            
            var embeddings = await embeddingService.GenerateEmbeddingsAsync(
                texts, 
                kernel: kernel,
                cancellationToken: cancellationToken);
            
            var floatArrays = embeddings.Select(e => e.ToArray()).ToList();
            
            _logger.LogInformation("Generated {Count} embeddings with {Length} dimensions using model {Model}", 
                floatArrays.Count, floatArrays.FirstOrDefault()?.Length ?? 0, model);
            
            return Result<IList<float[]>>.Success(floatArrays);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate embeddings for batch");
            return Result<IList<float[]>>.Failure($"Failed to generate embeddings: {ex.Message}");
        }
    }

    public Task<Result<EmbeddingModelInfo>> GetModelInfoAsync(string model)
    {
        if (_modelInfoCache.TryGetValue(model, out var modelInfo))
        {
            return Task.FromResult(Result<EmbeddingModelInfo>.Success(modelInfo));
        }
        
        return Task.FromResult(Result<EmbeddingModelInfo>.Failure($"Model {model} not found"));
    }

    public Task<Result<IList<EmbeddingModelInfo>>> GetSupportedModelsAsync()
    {
        var models = _modelInfoCache.Values.Where(m => m.IsAvailable).ToList();
        return Task.FromResult(Result<IList<EmbeddingModelInfo>>.Success(models));
    }

    private void InitializeModelInfo()
    {
        // OpenAI models
        _modelInfoCache["text-embedding-3-small"] = new EmbeddingModelInfo
        {
            Name = "text-embedding-3-small",
            Dimension = 1536,
            MaxInputLength = 8191,
            Provider = "OpenAI",
            IsAvailable = !string.IsNullOrEmpty(_configuration["AI:OpenAI:ApiKey"])
        };
        
        _modelInfoCache["text-embedding-3-large"] = new EmbeddingModelInfo
        {
            Name = "text-embedding-3-large",
            Dimension = 3072,
            MaxInputLength = 8191,
            Provider = "OpenAI",
            IsAvailable = !string.IsNullOrEmpty(_configuration["AI:OpenAI:ApiKey"])
        };
        
        _modelInfoCache["text-embedding-ada-002"] = new EmbeddingModelInfo
        {
            Name = "text-embedding-ada-002",
            Dimension = 1536,
            MaxInputLength = 8191,
            Provider = "OpenAI",
            IsAvailable = !string.IsNullOrEmpty(_configuration["AI:OpenAI:ApiKey"])
        };
        
        // Azure OpenAI models
        var azureApiKey = _configuration["AI:Azure:ApiKey"];
        var azureEndpoint = _configuration["AI:Azure:Endpoint"];
        var azureDeploymentName = _configuration["AI:Azure:EmbeddingDeploymentName"];
        
        if (!string.IsNullOrEmpty(azureApiKey) && !string.IsNullOrEmpty(azureEndpoint) && !string.IsNullOrEmpty(azureDeploymentName))
        {
            _modelInfoCache[$"azure-{azureDeploymentName}"] = new EmbeddingModelInfo
            {
                Name = $"azure-{azureDeploymentName}",
                Dimension = 1536, // Assuming text-embedding-ada-002
                MaxInputLength = 8191,
                Provider = "Azure OpenAI",
                IsAvailable = true,
                Metadata = new Dictionary<string, object>
                {
                    ["DeploymentName"] = azureDeploymentName
                }
            };
        }
    }

    private Kernel GetOrCreateKernel(string model)
    {
        if (_kernelCache.TryGetValue(model, out var kernel))
        {
            return kernel;
        }

        var builder = Kernel.CreateBuilder();

        if (model.StartsWith("azure-"))
        {
            // Azure OpenAI
            var apiKey = _configuration["AI:Azure:ApiKey"] ?? 
                throw new InvalidOperationException("Azure OpenAI API key not configured");
            var endpoint = _configuration["AI:Azure:Endpoint"] ?? 
                throw new InvalidOperationException("Azure OpenAI endpoint not configured");
            var deploymentName = _configuration["AI:Azure:EmbeddingDeploymentName"] ?? 
                throw new InvalidOperationException("Azure OpenAI embedding deployment name not configured");

            builder.AddAzureOpenAITextEmbeddingGeneration(
                deploymentName: deploymentName,
                endpoint: endpoint,
                apiKey: apiKey);
        }
        else
        {
            // OpenAI
            var apiKey = _configuration["AI:OpenAI:ApiKey"] ?? 
                throw new InvalidOperationException("OpenAI API key not configured");

            builder.AddOpenAITextEmbeddingGeneration(
                modelId: model,
                apiKey: apiKey);
        }

        kernel = builder.Build();
        _kernelCache[model] = kernel;
        
        return kernel;
    }
}