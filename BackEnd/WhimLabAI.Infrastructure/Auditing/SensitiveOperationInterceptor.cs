using System.Reflection;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Audit;

namespace WhimLabAI.Infrastructure.Auditing;

/// <summary>
/// 敏感操作拦截器 - 自动审计敏感操作
/// </summary>
public class SensitiveOperationInterceptor : IAsyncActionFilter
{
    private readonly IAuditLogger _auditLogger;
    private readonly ILogger<SensitiveOperationInterceptor> _logger;

    public SensitiveOperationInterceptor(
        IAuditLogger auditLogger,
        ILogger<SensitiveOperationInterceptor> logger)
    {
        _auditLogger = auditLogger;
        _logger = logger;
    }

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var shouldAudit = ShouldAuditOperation(context);
        
        if (!shouldAudit)
        {
            await next();
            return;
        }

        var startTime = DateTime.UtcNow;
        var operationDetails = ExtractOperationDetails(context);

        try
        {
            // 执行操作
            var resultContext = await next();

            // 记录成功的敏感操作
            await LogSensitiveOperation(context, resultContext, operationDetails, startTime, true);
        }
        catch (Exception ex)
        {
            // 记录失败的敏感操作
            await LogSensitiveOperation(context, null, operationDetails, startTime, false, ex);
            throw;
        }
    }

    private bool ShouldAuditOperation(ActionExecutingContext context)
    {
        // 检查是否有敏感操作标记
        var hasSensitiveAttribute = context.ActionDescriptor.EndpointMetadata
            .Any(m => m is SensitiveOperationAttribute);
        
        if (hasSensitiveAttribute)
            return true;

        // 检查控制器和操作名称
        var controllerName = context.Controller.GetType().Name;
        var actionName = context.ActionDescriptor.RouteValues["action"] ?? "";

        // 自动检测敏感操作
        return IsSensitiveController(controllerName) || 
               IsSensitiveAction(actionName) || 
               ContainsSensitiveParameters(context);
    }

    private bool IsSensitiveController(string controllerName)
    {
        var sensitiveControllers = new[]
        {
            "PaymentController",
            "RefundController", 
            "AdminUserController",
            "PermissionController",
            "RoleController",
            "SystemConfigController",
            "DataExportController"
        };

        return sensitiveControllers.Any(sc => controllerName.Contains(sc, StringComparison.OrdinalIgnoreCase));
    }

    private bool IsSensitiveAction(string actionName)
    {
        var sensitiveActions = new[]
        {
            "Delete", "Remove", "Purge",
            "Grant", "Revoke", "Assign",
            "ChangePassword", "ResetPassword",
            "ProcessPayment", "IssueRefund",
            "Export", "Download",
            "UpdateConfig", "UpdateSettings"
        };

        return sensitiveActions.Any(sa => actionName.Contains(sa, StringComparison.OrdinalIgnoreCase));
    }

    private bool ContainsSensitiveParameters(ActionExecutingContext context)
    {
        var sensitiveParams = new[] { "password", "token", "apikey", "secret", "payment", "card" };
        
        return context.ActionArguments.Keys
            .Any(key => sensitiveParams.Any(sp => key.Contains(sp, StringComparison.OrdinalIgnoreCase)));
    }

    private SensitiveOperationDetails ExtractOperationDetails(ActionExecutingContext context)
    {
        var details = new SensitiveOperationDetails
        {
            ControllerName = context.Controller.GetType().Name,
            ActionName = context.ActionDescriptor.RouteValues["action"] ?? "",
            HttpMethod = context.HttpContext.Request.Method,
            RequestPath = context.HttpContext.Request.Path,
            RequestId = context.HttpContext.TraceIdentifier
        };

        // 提取参数（脱敏处理）
        details.Parameters = new Dictionary<string, object?>();
        foreach (var (key, value) in context.ActionArguments)
        {
            details.Parameters[key] = SanitizeParameterValue(key, value);
        }

        // 提取实体信息
        var entityParam = context.ActionArguments.FirstOrDefault(a => 
            a.Value != null && IsEntityType(a.Value.GetType()));
        
        if (entityParam.Value != null)
        {
            details.EntityType = entityParam.Value.GetType().Name;
            details.EntityId = ExtractEntityId(entityParam.Value);
        }

        return details;
    }

    private async Task LogSensitiveOperation(
        ActionExecutingContext context,
        ActionExecutedContext? resultContext,
        SensitiveOperationDetails operationDetails,
        DateTime startTime,
        bool isSuccess,
        Exception? exception = null)
    {
        var executionTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
        var user = context.HttpContext.User;
        
        var module = DetermineModule(operationDetails.ControllerName);
        var action = DetermineAction(operationDetails.ActionName, operationDetails.HttpMethod);
        var description = GenerateDescription(operationDetails, isSuccess);

        // 记录审计日志
        await _auditLogger.LogActionAsync(
            action,
            module,
            description,
            new
            {
                operationDetails,
                ExecutionTime = executionTime,
                IsSuccess = isSuccess,
                ErrorMessage = exception?.Message,
                ResponseStatus = resultContext?.HttpContext.Response.StatusCode,
                IsSensitiveOperation = true,
                AutoAudited = true
            });

        // 如果是高风险操作，记录安全事件
        if (IsHighRiskOperation(operationDetails))
        {
            await _auditLogger.LogSecurityEventAsync(
                "HighRiskOperation",
                $"执行高风险操作: {action}",
                "High",
                new
                {
                    operationDetails,
                    User = user.Identity?.Name,
                    IsSuccess = isSuccess
                });
        }

        _logger.LogInformation(
            "Sensitive operation audited: {Action} by {User}, Success: {IsSuccess}, Time: {Time}ms",
            action, user.Identity?.Name ?? "Anonymous", isSuccess, executionTime);
    }

    private string DetermineModule(string controllerName)
    {
        if (controllerName.Contains("Payment") || controllerName.Contains("Refund"))
            return "Finance";
        if (controllerName.Contains("User") || controllerName.Contains("Admin"))
            return "UserManagement";
        if (controllerName.Contains("Permission") || controllerName.Contains("Role"))
            return "Security";
        if (controllerName.Contains("System") || controllerName.Contains("Config"))
            return "System";
        
        return "General";
    }

    private string DetermineAction(string actionName, string httpMethod)
    {
        // 标准化操作名称
        return $"{actionName}_{httpMethod}";
    }

    private string GenerateDescription(SensitiveOperationDetails details, bool isSuccess)
    {
        var status = isSuccess ? "成功" : "失败";
        return $"敏感操作{status}: {details.ActionName} on {details.ControllerName}";
    }

    private bool IsHighRiskOperation(SensitiveOperationDetails details)
    {
        var highRiskPatterns = new[]
        {
            "Delete", "Purge", "Payment", "Refund", "Grant", "Revoke", "SystemConfig"
        };

        return highRiskPatterns.Any(pattern => 
            details.ActionName.Contains(pattern, StringComparison.OrdinalIgnoreCase) ||
            details.ControllerName.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }

    private object? SanitizeParameterValue(string paramName, object? value)
    {
        if (value == null) return null;

        var sensitiveFields = new[] { "password", "token", "apikey", "secret", "cvv", "cardnumber" };
        
        if (sensitiveFields.Any(sf => paramName.Contains(sf, StringComparison.OrdinalIgnoreCase)))
        {
            return "***MASKED***";
        }

        // 对于复杂对象，进行深度脱敏
        if (value.GetType().IsClass && value.GetType() != typeof(string))
        {
            return SanitizeObject(value);
        }

        return value;
    }

    private object SanitizeObject(object obj)
    {
        var sanitized = new Dictionary<string, object?>();
        var properties = obj.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var prop in properties)
        {
            var propName = prop.Name;
            var propValue = prop.GetValue(obj);
            
            sanitized[propName] = SanitizeParameterValue(propName, propValue);
        }

        return sanitized;
    }

    private bool IsEntityType(Type type)
    {
        // 检查是否是实体类型（通常有Id属性）
        return type.GetProperty("Id") != null && 
               !type.IsPrimitive && 
               type != typeof(string);
    }

    private string? ExtractEntityId(object entity)
    {
        var idProperty = entity.GetType().GetProperty("Id");
        return idProperty?.GetValue(entity)?.ToString();
    }
}

/// <summary>
/// 敏感操作标记特性
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class SensitiveOperationAttribute : Attribute
{
    public string? Description { get; set; }
    public string RiskLevel { get; set; } = "Medium";
    public bool RequireApproval { get; set; } = false;
    public bool NotifyAdmins { get; set; } = false;
}

/// <summary>
/// 敏感操作详情
/// </summary>
public class SensitiveOperationDetails
{
    public string ControllerName { get; set; } = string.Empty;
    public string ActionName { get; set; } = string.Empty;
    public string HttpMethod { get; set; } = string.Empty;
    public string RequestPath { get; set; } = string.Empty;
    public string RequestId { get; set; } = string.Empty;
    public Dictionary<string, object?> Parameters { get; set; } = new();
    public string? EntityType { get; set; }
    public string? EntityId { get; set; }
}