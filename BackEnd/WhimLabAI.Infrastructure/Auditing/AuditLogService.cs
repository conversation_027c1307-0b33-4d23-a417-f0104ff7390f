using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.IdentityModel.Tokens.Jwt;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WhimLabAI.Domain.Entities.Audit;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Infrastructure.Extensions;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Auditing;

/// <summary>
/// 审计日志服务
/// </summary>
public class AuditLogService : IAuditLogger
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<AuditLogService> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly AuditOptions _options;
    private readonly Channel<AuditLog> _auditChannel;
    
    // Internal property for AuditLogBackgroundService access
    internal Channel<AuditLog> AuditChannel => _auditChannel;

    public AuditLogService(
        IServiceScopeFactory scopeFactory,
        ILogger<AuditLogService> logger,
        IHttpContextAccessor httpContextAccessor,
        IOptions<AuditOptions> options)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _options = options.Value;
        
        // 创建审计日志队列
        _auditChannel = Channel.CreateUnbounded<AuditLog>(new UnboundedChannelOptions
        {
            SingleReader = true,
            SingleWriter = false
        });
    }

    /// <summary>
    /// 记录审计日志
    /// </summary>
    public async Task LogAsync(object auditLog)
    {
        if (auditLog is AuditLog log)
        {
            await LogAuditLogAsync(log);
        }
    }
    
    /// <summary>
    /// 记录审计日志实体
    /// </summary>
    private async Task LogAuditLogAsync(AuditLog auditLog)
    {
        try
        {
            // 补充HTTP上下文信息
            EnrichWithHttpContext(auditLog);
            
            // 异步写入队列
            await _auditChannel.Writer.WriteAsync(auditLog);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to write audit log to channel");
        }
    }

    /// <summary>
    /// 记录操作审计
    /// </summary>
    public async Task LogActionAsync(
        string action,
        string module,
        string description,
        object? additionalData = null)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        var user = httpContext?.User;
        
        var auditLog = AuditLog.Create(
            action,
            module,
            description,
            GetUserId(user),
            GetUserName(user),
            GetUserType(user)
        );

        if (additionalData != null)
        {
            auditLog.AdditionalData = JsonSerializer.Serialize(additionalData);
        }

        await LogAuditLogAsync(auditLog);
    }

    /// <summary>
    /// 记录实体变更审计
    /// </summary>
    public async Task LogEntityChangeAsync<TEntity>(
        string action,
        TEntity entity,
        TEntity? oldEntity = default,
        string? description = null) where TEntity : class
    {
        var entityType = typeof(TEntity).Name;
        var entityId = GetEntityId(entity);
        
        var auditLog = AuditLog.Create(
            action,
            entityType,
            description ?? $"{action} {entityType}",
            GetUserId(),
            GetUserName(),
            GetUserType()
        );

        auditLog.SetEntityInfo(entityType, entityId);

        // 记录变更信息
        if (oldEntity != null)
        {
            var changes = GetChanges(oldEntity, entity);
            auditLog.SetChangeInfo(
                JsonSerializer.Serialize(oldEntity),
                JsonSerializer.Serialize(entity),
                string.Join(", ", changes.Keys)
            );
        }
        else
        {
            auditLog.NewValues = JsonSerializer.Serialize(entity);
        }

        await LogAuditLogAsync(auditLog);
    }

    /// <summary>
    /// 记录安全事件
    /// </summary>
    public async Task LogSecurityEventAsync(
        string eventType,
        string description,
        string riskLevel = "High",
        object? details = null)
    {
        var auditLog = AuditLog.Create(
            eventType,
            "Security",
            description,
            GetUserId(),
            GetUserName(),
            GetUserType()
        );

        auditLog.RiskLevel = riskLevel;
        auditLog.IsSensitive = true;
        
        if (details != null)
        {
            auditLog.AdditionalData = JsonSerializer.Serialize(details);
        }

        await LogAuditLogAsync(auditLog);
    }

    /// <summary>
    /// 记录数据访问
    /// </summary>
    public async Task LogDataAccessAsync(
        string dataType,
        string operation,
        string? criteria = null,
        int? recordCount = null)
    {
        var auditLog = AuditLog.Create(
            operation,
            "DataAccess",
            $"Accessed {dataType}",
            GetUserId(),
            GetUserName(),
            GetUserType()
        );

        var additionalData = new Dictionary<string, object?>
        {
            ["DataType"] = dataType,
            ["Criteria"] = criteria,
            ["RecordCount"] = recordCount
        };

        auditLog.AdditionalData = JsonSerializer.Serialize(additionalData);
        
        // 敏感数据访问标记
        if (IsSensitiveData(dataType))
        {
            auditLog.IsSensitive = true;
            auditLog.RiskLevel = "Medium";
        }

        await LogAuditLogAsync(auditLog);
    }


    private void EnrichWithHttpContext(AuditLog auditLog)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext == null) return;

        // 请求信息
        auditLog.SetRequestInfo(
            httpContext.Request.Method,
            httpContext.Request.Path + httpContext.Request.QueryString,
            null, // 请求参数需要在中间件中设置，避免重复读取请求体
            GetClientIpAddress(httpContext),
            httpContext.Request.Headers["User-Agent"].FirstOrDefault()
        );

        // 控制器和操作信息
        var routeData = httpContext.GetRouteData();
        auditLog.ControllerName = routeData?.Values["controller"]?.ToString();
        auditLog.ActionName = routeData?.Values["action"]?.ToString();

        // 请求ID
        auditLog.RequestId = httpContext.TraceIdentifier;
        
        // 获取关联ID和JWT ID
        auditLog.CorrelationId = httpContext.Request.Headers["X-Correlation-ID"].FirstOrDefault();
        auditLog.JwtId = httpContext.User.FindFirst(JwtRegisteredClaimNames.Jti)?.Value;

        // 客户端信息
        auditLog.ClientInfo = JsonSerializer.Serialize(new
        {
            Browser = GetBrowserInfo(httpContext),
            Platform = GetPlatformInfo(httpContext),
            IsMobile = IsMobileDevice(httpContext)
        });
    }

    private Guid? GetUserId(ClaimsPrincipal? user = null)
    {
        user ??= _httpContextAccessor.HttpContext?.User;
        var userIdClaim = user?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    private string GetUserName(ClaimsPrincipal? user = null)
    {
        user ??= _httpContextAccessor.HttpContext?.User;
        return user?.FindFirst(ClaimTypes.Name)?.Value ?? 
               user?.FindFirst(ClaimTypes.Email)?.Value ?? 
               "Anonymous";
    }

    private string GetUserType(ClaimsPrincipal? user = null)
    {
        user ??= _httpContextAccessor.HttpContext?.User;
        var roles = user?.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
        
        if (roles?.Contains("Admin") == true)
            return "Admin";
        if (roles?.Contains("Customer") == true)
            return "Customer";
            
        return user?.Identity?.IsAuthenticated == true ? "User" : "Anonymous";
    }

    private string GetEntityId<TEntity>(TEntity entity) where TEntity : class
    {
        var idProperty = entity.GetType().GetProperty("Id");
        return idProperty?.GetValue(entity)?.ToString() ?? "Unknown";
    }

    private Dictionary<string, object?> GetChanges<TEntity>(TEntity oldEntity, TEntity newEntity) where TEntity : class
    {
        var changes = new Dictionary<string, object?>();
        var properties = typeof(TEntity).GetProperties()
            .Where(p => p.CanRead && p.CanWrite);

        foreach (var property in properties)
        {
            var oldValue = property.GetValue(oldEntity);
            var newValue = property.GetValue(newEntity);

            if (!Equals(oldValue, newValue))
            {
                changes[property.Name] = new { OldValue = oldValue, NewValue = newValue };
            }
        }

        return changes;
    }

    private bool IsSensitiveData(string dataType)
    {
        var sensitiveTypes = new[]
        {
            "CustomerUser", "PaymentTransaction", "RefundRecord",
            "PersonalInfo", "BankAccount", "CreditCard"
        };

        return sensitiveTypes.Any(t => dataType.Contains(t, StringComparison.OrdinalIgnoreCase));
    }

    private string GetClientIpAddress(HttpContext context)
    {
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }

    private string GetBrowserInfo(HttpContext context)
    {
        var userAgent = context.Request.Headers["User-Agent"].FirstOrDefault() ?? "";
        
        if (userAgent.Contains("Chrome")) return "Chrome";
        if (userAgent.Contains("Firefox")) return "Firefox";
        if (userAgent.Contains("Safari")) return "Safari";
        if (userAgent.Contains("Edge")) return "Edge";
        if (userAgent.Contains("Opera")) return "Opera";
        
        return "Unknown";
    }

    private string GetPlatformInfo(HttpContext context)
    {
        var userAgent = context.Request.Headers["User-Agent"].FirstOrDefault() ?? "";
        
        if (userAgent.Contains("Windows")) return "Windows";
        if (userAgent.Contains("Mac")) return "macOS";
        if (userAgent.Contains("Linux")) return "Linux";
        if (userAgent.Contains("Android")) return "Android";
        if (userAgent.Contains("iOS") || userAgent.Contains("iPhone") || userAgent.Contains("iPad")) return "iOS";
        
        return "Unknown";
    }

    private bool IsMobileDevice(HttpContext context)
    {
        var userAgent = context.Request.Headers["User-Agent"].FirstOrDefault() ?? "";
        return userAgent.Contains("Mobile") || 
               userAgent.Contains("Android") || 
               userAgent.Contains("iPhone") || 
               userAgent.Contains("iPad");
    }
}

/// <summary>
/// 审计日志后台服务 - 负责批量写入数据库
/// </summary>
public class AuditLogBackgroundService : BackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<AuditLogBackgroundService> _logger;
    private readonly Channel<AuditLog> _auditChannel;
    private readonly AuditOptions _options;
    private readonly Timer _flushTimer;
    private readonly List<AuditLog> _buffer;
    private readonly SemaphoreSlim _semaphore;

    public AuditLogBackgroundService(
        IServiceScopeFactory scopeFactory,
        ILogger<AuditLogBackgroundService> logger,
        IAuditLogger auditLogger,
        IOptions<AuditOptions> options)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
        _auditChannel = ((AuditLogService)auditLogger).AuditChannel;
        _options = options.Value;
        _buffer = new List<AuditLog>();
        _semaphore = new SemaphoreSlim(1, 1);
        
        // 定期刷新缓冲区
        _flushTimer = new Timer(async _ => await FlushBufferAsync(), null, 
            TimeSpan.FromSeconds(_options.FlushIntervalSeconds), 
            TimeSpan.FromSeconds(_options.FlushIntervalSeconds));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await foreach (var auditLog in _auditChannel.Reader.ReadAllAsync(stoppingToken))
        {
            await _semaphore.WaitAsync(stoppingToken);
            try
            {
                _buffer.Add(auditLog);
                
                // 如果缓冲区已满，立即刷新
                if (_buffer.Count >= _options.BufferSize)
                {
                    await FlushBufferAsync();
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }
    }

    private async Task FlushBufferAsync()
    {
        if (_buffer.Count == 0) return;

        await _semaphore.WaitAsync();
        try
        {
            var logsToWrite = _buffer.ToList();
            _buffer.Clear();

            using var scope = _scopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<WhimLabAIDbContext>();
            
            await dbContext.Set<AuditLog>().AddRangeAsync(logsToWrite);
            await dbContext.SaveChangesAsync();
            
            _logger.LogInformation("Flushed {Count} audit logs to database", logsToWrite.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to flush audit logs to database");
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _flushTimer?.Dispose();
        await FlushBufferAsync();
        await base.StopAsync(cancellationToken);
    }

    public override void Dispose()
    {
        _flushTimer?.Dispose();
        _semaphore?.Dispose();
        base.Dispose();
    }

}

/// <summary>
/// 审计配置选项
/// </summary>
public class AuditOptions
{
    public bool Enabled { get; set; } = true;
    public bool EnableBackgroundFlush { get; set; } = true;
    public int BufferSize { get; set; } = 100;
    public int FlushIntervalSeconds { get; set; } = 10;
    public bool LogSensitiveData { get; set; } = false;
    public List<string> ExcludedPaths { get; set; } = new()
    {
        "/health",
        "/health/ready",
        "/health/live",
        "/swagger"
    };
    
    // 性能优化选项
    public bool EnableBatchProcessing { get; set; } = true;
    public int MaxBatchSize { get; set; } = 500;
    public int MaxQueueSize { get; set; } = 10000;
    public bool EnableCompression { get; set; } = true;
    public int CompressionThreshold { get; set; } = 1024; // bytes
    
    // 索引优化选项
    public bool AutoCreateIndexes { get; set; } = true;
    public List<string> IndexedFields { get; set; } = new()
    {
        "CreatedAt",
        "UserId",
        "Action",
        "Module",
        "RiskLevel",
        "IsSuccess"
    };
    
    // 清理选项
    public bool EnableAutoCleanup { get; set; } = true;
    public int CleanupRetentionDays { get; set; } = 90;
    public string CleanupSchedule { get; set; } = "0 3 * * *"; // 每天凌晨3点
}

