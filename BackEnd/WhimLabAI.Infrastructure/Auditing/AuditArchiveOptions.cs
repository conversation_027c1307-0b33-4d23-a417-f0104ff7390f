namespace WhimLabAI.Infrastructure.Auditing;

/// <summary>
/// 审计归档配置选项
/// </summary>
public class AuditArchiveOptions
{
    public int RetentionDays { get; set; } = 90;
    public string DefaultArchiveLocation { get; set; } = "./archives/audit";
    public bool DeleteAfterArchive { get; set; } = true;
    public bool AutoArchiveEnabled { get; set; } = true;
    public string AutoArchiveSchedule { get; set; } = "0 2 * * *"; // 每天凌晨2点
}