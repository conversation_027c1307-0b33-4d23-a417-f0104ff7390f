using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IO;
using WhimLabAI.Domain.Entities.Audit;
using WhimLabAI.Infrastructure.Extensions;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Auditing;

/// <summary>
/// 审计日志中间件
/// </summary>
public class AuditLogMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IAuditLogger _auditLogger;
    private readonly ILogger<AuditLogMiddleware> _logger;
    private readonly AuditOptions _options;
    private readonly RecyclableMemoryStreamManager _recyclableMemoryStreamManager;

    public AuditLogMiddleware(
        RequestDelegate next,
        IAuditLogger auditLogger,
        ILogger<AuditLogMiddleware> logger,
        IOptions<AuditOptions> options)
    {
        _next = next;
        _auditLogger = auditLogger;
        _logger = logger;
        _options = options.Value;
        _recyclableMemoryStreamManager = new RecyclableMemoryStreamManager();
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 检查是否需要审计
        if (!ShouldAudit(context))
        {
            await _next(context);
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        var originalBodyStream = context.Response.Body;
        
        string? requestBody = null;
        Exception? exception = null;
        
        try
        {
            // 读取请求体（如果需要）
            if (ShouldCaptureRequestBody(context))
            {
                requestBody = await ReadRequestBodyAsync(context);
            }

            // 使用内存流替换响应流以捕获响应
            using var responseBody = _recyclableMemoryStreamManager.GetStream();
            context.Response.Body = responseBody;

            // 执行下一个中间件
            await _next(context);

            // 记录成功的请求
            stopwatch.Stop();
            await LogRequestAsync(context, requestBody, stopwatch.ElapsedMilliseconds, true);

            // 将响应写回原始流
            responseBody.Seek(0, SeekOrigin.Begin);
            await responseBody.CopyToAsync(originalBodyStream);
        }
        catch (Exception ex)
        {
            exception = ex;
            stopwatch.Stop();
            
            // 记录失败的请求
            await LogRequestAsync(context, requestBody, stopwatch.ElapsedMilliseconds, false, ex);
            
            throw;
        }
        finally
        {
            context.Response.Body = originalBodyStream;
        }
    }

    private bool ShouldAudit(HttpContext context)
    {
        if (!_options.Enabled)
            return false;

        var path = context.Request.Path.Value?.ToLower() ?? "";
        
        // 排除特定路径
        if (_options.ExcludedPaths.Any(excludedPath => path.StartsWith(excludedPath.ToLower())))
            return false;

        // 排除静态文件
        if (IsStaticFile(path))
            return false;

        // 只审计API和重要页面
        return path.StartsWith("/api/") || 
               path.StartsWith("/admin/") ||
               IsImportantEndpoint(path);
    }

    private bool ShouldCaptureRequestBody(HttpContext context)
    {
        // 不捕获GET请求的body
        if (context.Request.Method == HttpMethods.Get)
            return false;

        // 不捕获大文件上传
        if (context.Request.ContentLength > 1024 * 1024) // 1MB
            return false;

        // 不捕获文件上传
        if (context.Request.HasFormContentType && context.Request.Form.Files.Any())
            return false;

        return true;
    }

    private async Task<string?> ReadRequestBodyAsync(HttpContext context)
    {
        context.Request.EnableBuffering();

        try
        {
            using var reader = new StreamReader(
                context.Request.Body,
                encoding: Encoding.UTF8,
                detectEncodingFromByteOrderMarks: false,
                bufferSize: 1024,
                leaveOpen: true);

            var body = await reader.ReadToEndAsync();
            context.Request.Body.Position = 0;

            // 脱敏处理
            if (!string.IsNullOrEmpty(body))
            {
                body = MaskSensitiveData(body);
            }

            return body;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading request body for audit");
            return null;
        }
    }

    private async Task LogRequestAsync(
        HttpContext context,
        string? requestBody,
        long elapsedMilliseconds,
        bool isSuccess,
        Exception? exception = null)
    {
        try
        {
            var action = DetermineAction(context);
            var module = DetermineModule(context);
            var description = GenerateDescription(context);

            var auditLog = AuditLog.Create(
                action,
                module,
                description
            );

            // 设置请求参数（脱敏处理）
            var requestParameters = new
            {
                Query = MaskSensitiveQueryParameters(context.Request.Query),
                Body = requestBody,
                Headers = GetSafeHeaders(context.Request.Headers)
            };

            auditLog.RequestParameters = JsonSerializer.Serialize(requestParameters);

            // 设置响应信息
            auditLog.SetResponseInfo(context.Response.StatusCode, elapsedMilliseconds);

            // 处理失败情况
            if (!isSuccess || exception != null)
            {
                auditLog.MarkAsFailed(
                    exception?.Message ?? "Unknown error",
                    _options.LogSensitiveData ? exception?.StackTrace : null
                );
            }

            // 判断是否敏感操作
            auditLog.IsSensitive = IsSensitiveOperation(context);

            await _auditLogger.LogAsync(auditLog);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging audit entry");
        }
    }

    private string DetermineAction(HttpContext context)
    {
        var method = context.Request.Method;
        var action = context.GetRouteValue("action")?.ToString() ?? "";

        return method switch
        {
            "GET" => $"Read{action}",
            "POST" => $"Create{action}",
            "PUT" => $"Update{action}",
            "PATCH" => $"Patch{action}",
            "DELETE" => $"Delete{action}",
            _ => $"{method}{action}"
        };
    }

    private string DetermineModule(HttpContext context)
    {
        var controller = context.GetRouteValue("controller")?.ToString() ?? "";
        var path = context.Request.Path.Value ?? "";

        if (path.StartsWith("/api/auth")) return "Authentication";
        if (path.StartsWith("/api/users")) return "UserManagement";
        if (path.StartsWith("/api/agents")) return "AgentManagement";
        if (path.StartsWith("/api/conversations")) return "Conversations";
        if (path.StartsWith("/api/subscriptions")) return "Subscriptions";
        if (path.StartsWith("/api/payments")) return "Payments";
        if (path.StartsWith("/api/admin")) return "Administration";

        return string.IsNullOrEmpty(controller) ? "General" : controller;
    }

    private string GenerateDescription(HttpContext context)
    {
        var method = context.Request.Method;
        var path = context.Request.Path.Value ?? "";
        var controller = context.GetRouteValue("controller")?.ToString() ?? "";
        var action = context.GetRouteValue("action")?.ToString() ?? "";

        if (!string.IsNullOrEmpty(controller) && !string.IsNullOrEmpty(action))
        {
            return $"{method} {controller}/{action}";
        }

        return $"{method} {path}";
    }

    private bool IsSensitiveOperation(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLower() ?? "";
        var method = context.Request.Method;

        // 敏感路径
        var sensitivePaths = new[]
        {
            "/api/auth/",
            "/api/users/",
            "/api/payments/",
            "/api/admin/",
            "/api/permissions/"
        };

        if (sensitivePaths.Any(sp => path.StartsWith(sp)))
            return true;

        // 删除操作通常是敏感的
        if (method == "DELETE")
            return true;

        // 特定的敏感操作
        var sensitiveActions = new[]
        {
            "reset", "password", "token", "key", "secret",
            "grant", "revoke", "approve", "reject"
        };

        return sensitiveActions.Any(sa => path.Contains(sa, StringComparison.OrdinalIgnoreCase));
    }

    private bool IsStaticFile(string path)
    {
        var staticExtensions = new[]
        {
            ".js", ".css", ".png", ".jpg", ".jpeg", ".gif", ".ico",
            ".svg", ".woff", ".woff2", ".ttf", ".eot", ".map"
        };

        return staticExtensions.Any(ext => path.EndsWith(ext, StringComparison.OrdinalIgnoreCase));
    }

    private bool IsImportantEndpoint(string path)
    {
        var importantPaths = new[]
        {
            "/login", "/logout", "/register", "/payment", "/checkout"
        };

        return importantPaths.Any(ip => path.Contains(ip, StringComparison.OrdinalIgnoreCase));
    }

    private string MaskSensitiveData(string data)
    {
        if (string.IsNullOrEmpty(data) || !_options.LogSensitiveData)
            return data;

        try
        {
            // 尝试解析为JSON并脱敏
            var jsonDoc = JsonDocument.Parse(data);
            var maskedData = MaskJsonDocument(jsonDoc.RootElement);
            return JsonSerializer.Serialize(maskedData);
        }
        catch
        {
            // 如果不是JSON，进行基本脱敏
            return MaskStringData(data);
        }
    }

    private object MaskJsonDocument(JsonElement element)
    {
        switch (element.ValueKind)
        {
            case JsonValueKind.Object:
                var obj = new Dictionary<string, object>();
                foreach (var property in element.EnumerateObject())
                {
                    var key = property.Name.ToLower();
                    if (IsSensitiveField(key))
                    {
                        obj[property.Name] = "***MASKED***";
                    }
                    else
                    {
                        obj[property.Name] = MaskJsonDocument(property.Value);
                    }
                }
                return obj;

            case JsonValueKind.Array:
                return element.EnumerateArray().Select(MaskJsonDocument).ToList();

            default:
                return element.ToString();
        }
    }

    private string MaskStringData(string data)
    {
        // 基本的敏感数据模式匹配和替换
        // 信用卡号
        data = System.Text.RegularExpressions.Regex.Replace(data, 
            @"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b", "****-****-****-****");
        
        // 邮箱
        data = System.Text.RegularExpressions.Regex.Replace(data,
            @"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", "***@***.***");
        
        // 手机号
        data = System.Text.RegularExpressions.Regex.Replace(data,
            @"\b1[3-9]\d{9}\b", "1**********");

        return data;
    }

    private bool IsSensitiveField(string fieldName)
    {
        var sensitiveFields = new[]
        {
            "password", "pwd", "secret", "token", "key", "apikey",
            "creditcard", "cardnumber", "cvv", "ssn", "idnumber",
            "email", "phone", "mobile", "address", "bankaccount"
        };

        return sensitiveFields.Any(sf => fieldName.Contains(sf, StringComparison.OrdinalIgnoreCase));
    }

    private Dictionary<string, string> MaskSensitiveQueryParameters(IQueryCollection query)
    {
        var masked = new Dictionary<string, string>();
        
        foreach (var kvp in query)
        {
            if (IsSensitiveField(kvp.Key))
            {
                masked[kvp.Key] = "***MASKED***";
            }
            else
            {
                masked[kvp.Key] = kvp.Value.ToString();
            }
        }

        return masked;
    }

    private Dictionary<string, string> GetSafeHeaders(IHeaderDictionary headers)
    {
        var safeHeaders = new Dictionary<string, string>();
        var allowedHeaders = new[]
        {
            "Accept", "Accept-Language", "Content-Type", "Content-Length",
            "User-Agent", "Referer", "Origin", "X-Requested-With"
        };

        foreach (var header in headers)
        {
            if (allowedHeaders.Contains(header.Key, StringComparer.OrdinalIgnoreCase))
            {
                safeHeaders[header.Key] = header.Value.ToString();
            }
        }

        return safeHeaders;
    }
}