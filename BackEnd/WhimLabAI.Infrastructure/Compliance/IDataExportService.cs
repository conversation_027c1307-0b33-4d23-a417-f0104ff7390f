using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.Compliance;

/// <summary>
/// 数据导出服务接口
/// </summary>
public interface IDataExportService
{
    Task<Result<DataExportResult>> ExportUserDataAsync(Guid userId, string format, List<string> dataTypes, CancellationToken cancellationToken = default);
}

/// <summary>
/// 数据导出结果
/// </summary>
public class DataExportResult
{
    public string FileUrl { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileHash { get; set; } = string.Empty;
}

/// <summary>
/// 数据导出服务实现
/// </summary>
public class DataExportService : IDataExportService
{
    private readonly WhimLabAIDbContext _dbContext;
    private readonly IStorageService _storageService;
    private readonly ILogger<DataExportService> _logger;

    public DataExportService(
        WhimLabAIDbContext dbContext,
        IStorageService storageService,
        ILogger<DataExportService> logger)
    {
        _dbContext = dbContext;
        _storageService = storageService;
        _logger = logger;
    }

    public async Task<Result<DataExportResult>> ExportUserDataAsync(Guid userId, string format, List<string> dataTypes, CancellationToken cancellationToken = default)
    {
        try
        {
            var exportData = new Dictionary<string, object>();

            // 导出用户基本信息
            if (dataTypes.Contains("profile") || dataTypes.Contains("all"))
            {
                var user = await _dbContext.CustomerUsers
                    .Include(u => u.Profile)
                    .Include(u => u.NotificationSetting)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user != null)
                {
                    exportData["profile"] = new
                    {
                        user.Id,
                        user.Username,
                        Email = user.Email?.Value,
                        Phone = user.Phone?.Value,
                        user.Nickname,
                        user.Gender,
                        user.Birthday,
                        user.Region,
                        user.Industry,
                        user.Position,
                        user.Bio,
                        user.CreatedAt,
                        Profile = user.Profile != null ? new
                        {
                            user.Profile.Company,
                            user.Profile.Department,
                            user.Profile.JobTitle,
                            user.Profile.WorkEmail,
                            user.Profile.WorkPhone,
                            user.Profile.Website,
                            LinkedIn = user.Profile.LinkedIn,
                            GitHub = user.Profile.GitHub,
                            Twitter = user.Profile.Twitter,
                            user.Profile.Address,
                            user.Profile.City,
                            user.Profile.Province,
                            user.Profile.PostalCode,
                            user.Profile.Country,
                            user.Profile.CustomFields
                        } : null
                    };
                }
            }

            // 导出对话历史
            if (dataTypes.Contains("conversations") || dataTypes.Contains("all"))
            {
                var conversations = await _dbContext.Conversations
                    .Include(c => c.Messages)
                    .Where(c => c.CustomerUserId == userId)
                    .Select(c => new
                    {
                        c.Id,
                        c.Title,
                        c.StartedAt,
                        c.LastMessageAt,
                        c.MessageCount,
                        c.TotalTokens,
                        Messages = c.Messages.Select(m => new
                        {
                            m.Id,
                            m.Role,
                            m.Content,
                            m.TokenCount,
                            m.CreatedAt
                        })
                    })
                    .ToListAsync(cancellationToken);

                exportData["conversations"] = conversations;
            }

            // 导出订阅信息
            if (dataTypes.Contains("subscriptions") || dataTypes.Contains("all"))
            {
                var subscriptions = await _dbContext.Subscriptions
                    .Include(s => s.Plan)
                    .Where(s => s.CustomerUserId == userId)
                    .Select(s => new
                    {
                        s.Id,
                        PlanName = s.Plan.Name,
                        s.Status,
                        s.StartDate,
                        s.EndDate,
                        TokenLimit = s.Plan != null ? s.Plan.MonthlyTokens : 0,
                        TokenUsed = s.Plan != null && s.Plan.MonthlyTokens > 0 ? s.Plan.MonthlyTokens - s.RemainingTokens : 0,
                        Price = s.PaidAmount,
                        s.CreatedAt
                    })
                    .ToListAsync(cancellationToken);

                exportData["subscriptions"] = subscriptions;
            }

            // 导出支付记录
            if (dataTypes.Contains("payments") || dataTypes.Contains("all"))
            {
                var orders = await _dbContext.Orders
                    .Include(o => o.Transactions)
                    .Where(o => o.CustomerUserId == userId)
                    .Select(o => new
                    {
                        o.Id,
                        o.OrderNo,
                        OrderType = o.Type,
                        TotalAmount = o.Amount,
                        o.Status,
                        o.CreatedAt,
                        CompletedAt = o.PaidAt,
                        Transactions = o.Transactions.Select(t => new
                        {
                            TransactionNo = t.TransactionId,
                            t.PaymentMethod,
                            t.Amount,
                            t.Status,
                            t.CreatedAt
                        })
                    })
                    .ToListAsync(cancellationToken);

                exportData["payments"] = orders;
            }

            // 生成导出文件
            var fileName = $"user_data_export_{userId}_{DateTime.UtcNow:yyyyMMddHHmmss}.{format}";
            var filePath = Path.Combine(Path.GetTempPath(), fileName);

            byte[] fileBytes;
            if (format.ToLower() == "json")
            {
                var json = JsonSerializer.Serialize(exportData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                fileBytes = Encoding.UTF8.GetBytes(json);
            }
            else if (format.ToLower() == "zip")
            {
                using var memoryStream = new MemoryStream();
                using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
                {
                    foreach (var kvp in exportData)
                    {
                        var entry = archive.CreateEntry($"{kvp.Key}.json");
                        using var entryStream = entry.Open();
                        var json = JsonSerializer.Serialize(kvp.Value, new JsonSerializerOptions
                        {
                            WriteIndented = true,
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                        });
                        var bytes = Encoding.UTF8.GetBytes(json);
                        await entryStream.WriteAsync(bytes, 0, bytes.Length);
                    }
                }
                fileBytes = memoryStream.ToArray();
            }
            else
            {
                return Result<DataExportResult>.Failure($"Unsupported export format: {format}");
            }

            // 计算文件哈希
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(fileBytes);
            var fileHash = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();

            // 上传到存储服务
            using var stream = new MemoryStream(fileBytes);
            var uploadRequest = new UploadFileRequest
            {
                FileStream = stream,
                FileName = fileName,
                ContentType = format.ToLower() == "json" ? "application/json" : "application/zip",
                ObjectPath = $"exports/{userId}",
                Metadata = new Dictionary<string, string>
                {
                    ["UserId"] = userId.ToString(),
                    ["ExportDate"] = DateTime.UtcNow.ToString("O")
                }
            };
            var uploadResult = await _storageService.UploadFileAsync(uploadRequest, cancellationToken);

            if (!uploadResult.Success)
            {
                return Result<DataExportResult>.Failure($"Failed to upload export file: {uploadResult.ErrorMessage}");
            }

            _logger.LogInformation("Successfully exported user data for user {UserId}, format: {Format}, size: {Size} bytes",
                userId, format, fileBytes.Length);

            return Result<DataExportResult>.Success(new DataExportResult
            {
                FileUrl = uploadResult.FileUrl ?? string.Empty,
                FileSize = fileBytes.Length,
                FileHash = fileHash
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export user data for user {UserId}", userId);
            return Result<DataExportResult>.Failure($"Failed to export user data: {ex.Message}");
        }
    }
}