using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Entities.Compliance;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Infrastructure.Security;
using WhimLabAI.Shared.Dtos.Compliance;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.Compliance;

/// <summary>
/// 合规性服务实现
/// </summary>
public class ComplianceService : IComplianceService
{
    private readonly WhimLabAIDbContext _dbContext;
    private readonly ILogger<ComplianceService> _logger;
    private readonly IDataExportService _dataExportService;
    private readonly IDataAnonymizationService _anonymizationService;
    private readonly IDataEncryptionService _encryptionService;

    public ComplianceService(
        WhimLabAIDbContext dbContext,
        ILogger<ComplianceService> logger,
        IDataExportService dataExportService,
        IDataAnonymizationService anonymizationService,
        IDataEncryptionService encryptionService)
    {
        _dbContext = dbContext;
        _logger = logger;
        _dataExportService = dataExportService;
        _anonymizationService = anonymizationService;
        _encryptionService = encryptionService;
    }

    #region 用户同意管理

    public async Task<Result<UserConsentDto>> RecordConsentAsync(
        Guid userId, 
        string consentType, 
        bool isGranted, 
        string ipAddress, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取同意文本
            var consentText = GetConsentText(consentType);
            
            // 创建同意记录
            var consent = isGranted 
                ? UserConsent.CreateForCustomerUser(
                    userId,
                    consentType,
                    "1.0", // 版本号应从配置获取
                    ipAddress,
                    null, // userAgent
                    consentText)
                : UserConsent.CreateForCustomerUser(
                    userId,
                    consentType,
                    "1.0",
                    ipAddress,
                    null,
                    consentText);
            
            // If not granted, immediately revoke it
            if (!isGranted)
            {
                consent.Revoke();
            }

            _dbContext.UserConsents.Add(consent);
            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Recorded consent for user {UserId}, type: {ConsentType}, granted: {IsGranted}", 
                userId, consentType, isGranted);

            var dto = MapToUserConsentDto(consent);
            return Result<UserConsentDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record consent for user {UserId}", userId);
            return Result<UserConsentDto>.Failure($"Failed to record consent: {ex.Message}");
        }
    }

    public async Task<Result<List<UserConsentDto>>> GetUserConsentsAsync(
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var consents = await _dbContext.UserConsents
                .Where(c => c.CustomerUserId == userId)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync(cancellationToken);

            var dtos = consents.Select(MapToUserConsentDto).ToList();
            return Result<List<UserConsentDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get consents for user {UserId}", userId);
            return Result<List<UserConsentDto>>.Failure($"Failed to get consents: {ex.Message}");
        }
    }

    public async Task<Result> RevokeConsentAsync(
        Guid userId, 
        string consentType, 
        string ipAddress, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var consent = await _dbContext.UserConsents
                .Where(c => c.CustomerUserId == userId && c.ConsentType == consentType && !c.IsRevoked)
                .OrderByDescending(c => c.CreatedAt)
                .FirstOrDefaultAsync(cancellationToken);

            if (consent == null)
            {
                return Result.Failure("Active consent not found");
            }

            consent.Revoke();
            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Revoked consent for user {UserId}, type: {ConsentType}", 
                userId, consentType);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to revoke consent for user {UserId}", userId);
            return Result.Failure($"Failed to revoke consent: {ex.Message}");
        }
    }

    public async Task<Result<bool>> CheckConsentAsync(
        Guid userId, 
        string consentType, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var hasValidConsent = await _dbContext.UserConsents
                .Where(c => c.CustomerUserId == userId && c.ConsentType == consentType)
                .OrderByDescending(c => c.CreatedAt)
                .Select(c => !c.IsRevoked && (c.ConsentedAt > DateTime.UtcNow.AddYears(-2)))
                .FirstOrDefaultAsync(cancellationToken);

            return Result<bool>.Success(hasValidConsent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check consent for user {UserId}", userId);
            return Result<bool>.Failure($"Failed to check consent: {ex.Message}");
        }
    }

    #endregion

    #region 隐私设置管理

    public async Task<Result<PrivacySettingsDto>> GetPrivacySettingsAsync(
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var settings = await _dbContext.PrivacySettings
                .FirstOrDefaultAsync(s => s.UserId == userId, cancellationToken);

            if (settings == null)
            {
                // 创建默认设置
                settings = PrivacySettings.CreateDefault(userId);
                _dbContext.PrivacySettings.Add(settings);
                await _dbContext.SaveChangesAsync(cancellationToken);
            }

            var dto = MapToPrivacySettingsDto(settings);
            return Result<PrivacySettingsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get privacy settings for user {UserId}", userId);
            return Result<PrivacySettingsDto>.Failure($"Failed to get privacy settings: {ex.Message}");
        }
    }

    public async Task<Result> UpdatePrivacySettingsAsync(
        Guid userId, 
        WhimLabAI.Shared.Dtos.Compliance.PrivacySettingsUpdateRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var settings = await _dbContext.PrivacySettings
                .FirstOrDefaultAsync(s => s.UserId == userId, cancellationToken);

            if (settings == null)
            {
                return Result.Failure("Privacy settings not found");
            }

            // Convert DTO to domain request
            var domainRequest = new Domain.Entities.Compliance.PrivacySettingsUpdateRequest
            {
                AllowPublicProfile = request.ShowProfilePublicly,
                AllowAnalytics = request.AllowAnalytics,
                AllowMarketing = request.AllowMarketing,
                AllowPersonalization = request.AllowPersonalization,
                AllowThirdPartySharing = request.AllowDataSharing,
                AllowSearchEngineIndexing = request.AllowSearchEngineIndexing,
                // Map remaining properties to defaults since they don't exist in DTO
                AllowLocationAccess = null,
                AllowConversationHistory = null,
                DataRetentionDays = null,
                TwoFactorEnabled = null,
                LoginNotificationEnabled = null,
                PrivacyMode = request.DataRetentionPreference
            };
            
            settings.UpdateSettings(domainRequest);
            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated privacy settings for user {UserId}", userId);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update privacy settings for user {UserId}", userId);
            return Result.Failure($"Failed to update privacy settings: {ex.Message}");
        }
    }

    public async Task<Result> EnableStrictPrivacyAsync(
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var settings = await _dbContext.PrivacySettings
                .FirstOrDefaultAsync(s => s.UserId == userId, cancellationToken);

            if (settings == null)
            {
                return Result.Failure("Privacy settings not found");
            }

            settings.EnableStrictPrivacy();
            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Enabled strict privacy for user {UserId}", userId);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to enable strict privacy for user {UserId}", userId);
            return Result.Failure($"Failed to enable strict privacy: {ex.Message}");
        }
    }

    #endregion

    #region 数据导出

    public async Task<Result<DataExportRequestDto>> CreateDataExportRequestAsync(
        Guid userId, 
        string exportFormat, 
        List<string> dataTypes, 
        string ipAddress, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查是否有未完成的请求
            var pendingRequest = await _dbContext.DataExportRequests
                .AnyAsync(r => r.UserId == userId && 
                    (r.Status == ExportStatus.Pending || r.Status == ExportStatus.Processing), 
                    cancellationToken);

            if (pendingRequest)
            {
                return Result<DataExportRequestDto>.Failure("You already have a pending export request");
            }

            // 创建导出请求
            var request = DataExportRequest.Create(
                userId,
                ExportRequestTypes.CustomExport,
                exportFormat,
                dataTypes,
                ipAddress);

            _dbContext.DataExportRequests.Add(request);
            await _dbContext.SaveChangesAsync(cancellationToken);

            // 异步处理导出
            _ = Task.Run(async () => await ProcessDataExportAsync(request.Id), cancellationToken);

            _logger.LogInformation("Created data export request {RequestId} for user {UserId}", 
                request.Id, userId);

            // Map to DTO
            var dto = new DataExportRequestDto
            {
                Id = request.Id,
                UserId = request.UserId,
                RequestNumber = request.Id.ToString(),
                ExportFormat = request.ExportFormat,
                DataTypes = request.IncludedDataTypes,
                Status = request.Status.ToString(),
                RequestedAt = request.RequestedAt,
                CompletedAt = request.CompletedAt,
                FileUrl = request.FileUrl,
                FileSize = request.FileSize,
                ExpiresAt = request.ExpiresAt,
                IpAddress = request.IpAddress,
                FailureReason = request.ErrorMessage,
                CreatedAt = request.CreatedAt,
                UpdatedAt = request.UpdatedAt
            };

            return Result<DataExportRequestDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create data export request for user {UserId}", userId);
            return Result<DataExportRequestDto>.Failure($"Failed to create export request: {ex.Message}");
        }
    }

    public async Task<Result<DataExportRequestDto>> GetDataExportRequestAsync(
        Guid requestId, 
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = await _dbContext.DataExportRequests
                .FirstOrDefaultAsync(r => r.Id == requestId && r.UserId == userId, cancellationToken);

            if (request == null)
            {
                return Result<DataExportRequestDto>.Failure("Export request not found");
            }

            var dto = MapToDataExportRequestDto(request);
            return Result<DataExportRequestDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get data export request {RequestId}", requestId);
            return Result<DataExportRequestDto>.Failure($"Failed to get export request: {ex.Message}");
        }
    }

    public async Task<Result<List<DataExportRequestDto>>> GetUserDataExportRequestsAsync(
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var requests = await _dbContext.DataExportRequests
                .Where(r => r.UserId == userId)
                .OrderByDescending(r => r.RequestedAt)
                .ToListAsync(cancellationToken);

            // Map to DTOs
            var dtos = requests.Select(request => new DataExportRequestDto
            {
                Id = request.Id,
                UserId = request.UserId,
                RequestNumber = request.Id.ToString(),
                ExportFormat = request.ExportFormat,
                DataTypes = request.IncludedDataTypes,
                Status = request.Status.ToString(),
                RequestedAt = request.RequestedAt,
                CompletedAt = request.CompletedAt,
                FileUrl = request.FileUrl,
                FileSize = request.FileSize,
                ExpiresAt = request.ExpiresAt,
                IpAddress = request.IpAddress,
                FailureReason = request.ErrorMessage,
                CreatedAt = request.CreatedAt,
                UpdatedAt = request.UpdatedAt
            }).ToList();

            return Result<List<DataExportRequestDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get data export requests for user {UserId}", userId);
            return Result<List<DataExportRequestDto>>.Failure($"Failed to get export requests: {ex.Message}");
        }
    }

    public async Task<Result<string>> DownloadDataExportAsync(
        Guid requestId, 
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = await _dbContext.DataExportRequests
                .FirstOrDefaultAsync(r => r.Id == requestId && r.UserId == userId, cancellationToken);

            if (request == null)
            {
                return Result<string>.Failure("Export request not found");
            }

            if (request.Status != ExportStatus.Completed)
            {
                return Result<string>.Failure("Export is not ready for download");
            }

            if (request.IsExpired())
            {
                return Result<string>.Failure("Export has expired");
            }

            request.RecordDownload();
            await _dbContext.SaveChangesAsync(cancellationToken);

            return Result<string>.Success(request.FileUrl!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download data export {RequestId}", requestId);
            return Result<string>.Failure($"Failed to download export: {ex.Message}");
        }
    }

    #endregion

    #region 数据删除（被遗忘权）

    public async Task<Result> DeleteUserDataAsync(
        Guid userId, 
        string reason, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogWarning("Starting data deletion for user {UserId}, reason: {Reason}", userId, reason);

            // 开启事务
            using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);

            try
            {
                // 删除对话历史
                var conversations = await _dbContext.Conversations
                    .Where(c => c.CustomerUserId == userId)
                    .ToListAsync(cancellationToken);
                _dbContext.Conversations.RemoveRange(conversations);

                // 删除订阅信息
                var subscriptions = await _dbContext.Subscriptions
                    .Where(s => s.CustomerUserId == userId)
                    .ToListAsync(cancellationToken);
                _dbContext.Subscriptions.RemoveRange(subscriptions);

                // 删除支付记录
                var orders = await _dbContext.Orders
                    .Where(o => o.CustomerUserId == userId)
                    .ToListAsync(cancellationToken);
                _dbContext.Orders.RemoveRange(orders);

                // 删除API密钥
                var apiKeys = await _dbContext.ApiKeys
                    .Where(k => k.CustomerUserId == userId)
                    .ToListAsync(cancellationToken);
                _dbContext.ApiKeys.RemoveRange(apiKeys);

                // 删除设备授权 - 通过CustomerUser的导航属性删除
                var userWithDevices = await _dbContext.CustomerUsers
                    .Include(u => u.DeviceAuthorizations)
                    .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
                    
                if (userWithDevices?.DeviceAuthorizations?.Any() == true)
                {
                    _dbContext.DeviceAuthorizations.RemoveRange(userWithDevices.DeviceAuthorizations);
                }

                // 删除用户账户
                var user = await _dbContext.CustomerUsers
                    .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
                if (user != null)
                {
                    _dbContext.CustomerUsers.Remove(user);
                }

                await _dbContext.SaveChangesAsync(cancellationToken);
                await transaction.CommitAsync(cancellationToken);

                _logger.LogWarning("Completed data deletion for user {UserId}", userId);
                return Result.Success();
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete user data for {UserId}", userId);
            return Result.Failure($"Failed to delete user data: {ex.Message}");
        }
    }

    public async Task<Result> AnonymizeUserDataAsync(
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting data anonymization for user {UserId}", userId);

            var user = await _dbContext.CustomerUsers
                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);

            if (user == null)
            {
                return Result.Failure("User not found");
            }

            // 匿名化用户数据
            await _anonymizationService.AnonymizeUserAsync(userId, cancellationToken);

            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Completed data anonymization for user {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to anonymize user data for {UserId}", userId);
            return Result.Failure($"Failed to anonymize user data: {ex.Message}");
        }
    }

    #endregion

    #region 数据保留策略

    public async Task<Result<List<DataRetentionPolicyDto>>> GetDataRetentionPoliciesAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var policies = await _dbContext.DataRetentionPolicies
                .Where(p => p.IsEnabled)
                .OrderBy(p => p.Priority)
                .ToListAsync(cancellationToken);

            var dtos = policies.Select(MapToDataRetentionPolicyDto).ToList();
            return Result<List<DataRetentionPolicyDto>>.Success(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get data retention policies");
            return Result<List<DataRetentionPolicyDto>>.Failure($"Failed to get policies: {ex.Message}");
        }
    }

    public async Task<Result> ApplyDataRetentionPoliciesAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var policies = await _dbContext.DataRetentionPolicies
                .Where(p => p.IsEnabled && p.ShouldExecute())
                .OrderBy(p => p.Priority)
                .ToListAsync(cancellationToken);

            foreach (var policy in policies)
            {
                try
                {
                    await ApplyRetentionPolicyAsync(policy, cancellationToken);
                    policy.RecordExecution();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to apply retention policy {PolicyId}", policy.Id);
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Applied {Count} data retention policies", policies.Count);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply data retention policies");
            return Result.Failure($"Failed to apply policies: {ex.Message}");
        }
    }

    #endregion

    #region 合规性报告

    public async Task<Result<ComplianceReportDto>> GenerateComplianceReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var report = new ComplianceReportDto
            {
                GeneratedAt = DateTime.UtcNow,
                StartDate = startDate,
                EndDate = endDate
            };

            // 用户统计
            report.TotalUsers = await _dbContext.CustomerUsers.CountAsync(cancellationToken);
            report.ActiveUsers = await _dbContext.CustomerUsers
                .CountAsync(u => u.LastLoginAt >= startDate && u.LastLoginAt <= endDate, cancellationToken);

            // 同意统计
            var consentGroups = await _dbContext.UserConsents
                .Where(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate)
                .GroupBy(c => new { c.ConsentType, IsGranted = !c.IsRevoked })
                .Select(g => new { g.Key.ConsentType, g.Key.IsGranted, Count = g.Count() })
                .ToListAsync(cancellationToken);

            foreach (var group in consentGroups)
            {
                var key = $"{group.ConsentType}_{(group.IsGranted ? "granted" : "revoked")}";
                report.ConsentStatistics[key] = group.Count;
            }

            // 数据导出请求
            var exportGroups = await _dbContext.DataExportRequests
                .Where(r => r.RequestedAt >= startDate && r.RequestedAt <= endDate)
                .GroupBy(r => r.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync(cancellationToken);

            foreach (var group in exportGroups)
            {
                report.DataExportRequests[group.Status] = group.Count;
            }

            // 检查合规问题
            report.Issues = await DetectComplianceIssuesAsync(startDate, endDate, cancellationToken);

            report.Summary = GenerateComplianceSummary(report);

            return Result<ComplianceReportDto>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate compliance report");
            return Result<ComplianceReportDto>.Failure($"Failed to generate report: {ex.Message}");
        }
    }

    public async Task<Result<UserDataReportDto>> GenerateUserDataReportAsync(
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var report = new UserDataReport
            {
                UserId = userId,
                GeneratedAt = DateTime.UtcNow
            };

            // 获取用户基本信息
            var user = await _dbContext.CustomerUsers
                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);

            if (user != null)
            {
                report.Data["Username"] = user.Username;
                report.Data["Email"] = user.Email?.Value ?? "N/A";
                report.Data["PhoneNumber"] = user.Phone?.Value ?? "N/A";
                report.Data["RegisteredAt"] = user.CreatedAt;
                report.Data["LastLoginAt"] = user.LastLoginAt?.ToString() ?? "Never";
            }

            // Create proper UserDataReportDto with matching properties
            var dto = new UserDataReportDto
            {
                UserId = userId,
                GeneratedAt = DateTime.UtcNow,
                PersonalData = report.Data,
                DataCategories = new List<string> 
                { 
                    "Personal Information",
                    "Account Data",
                    "Conversation History",
                    "Usage Data",
                    "Payment Information"
                },
                DataCounts = new Dictionary<string, int>
                {
                    ["Conversations"] = await _dbContext.Conversations
                        .CountAsync(c => c.CustomerUserId == userId, cancellationToken),
                    ["Messages"] = await _dbContext.ConversationMessages
                        .CountAsync(m => m.Conversation.CustomerUserId == userId, cancellationToken),
                    ["Orders"] = await _dbContext.Orders
                        .CountAsync(o => o.CustomerUserId == userId, cancellationToken),
                    ["Devices"] = await _dbContext.Set<CustomerUser>()
                        .Where(u => u.Id == userId)
                        .SelectMany(u => u.DeviceAuthorizations)
                        .CountAsync(cancellationToken)
                },
                Consents = await _dbContext.UserConsents
                    .Where(c => c.CustomerUserId == userId)
                    .OrderByDescending(c => c.CreatedAt)
                    .Select(c => MapToUserConsentDto(c))
                    .ToListAsync(cancellationToken),
                ProcessingActivities = GetDataProcessingActivities(userId)
            };
            
            // Set privacy settings
            var privacySettings = await _dbContext.PrivacySettings
                .FirstOrDefaultAsync(p => p.UserId == userId, cancellationToken);
            if (privacySettings != null)
            {
                dto.PrivacySettings = MapToPrivacySettingsDto(privacySettings);
            }

            return Result<UserDataReportDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate user data report for {UserId}", userId);
            return Result<UserDataReportDto>.Failure($"Failed to generate report: {ex.Message}");
        }
    }

    #endregion

    #region 安全合规检查

    public async Task<Result<SecurityComplianceCheckDto>> RunSecurityComplianceCheckAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var dto = new SecurityComplianceCheckDto
            {
                CheckedAt = DateTime.UtcNow,
                SecurityFeatures = new Dictionary<string, bool>
                {
                    ["HTTPS"] = true, // 应从配置检查
                    ["HSTS"] = true, // 已通过SecurityHeadersMiddleware启用
                    ["CSP"] = true, // 已通过SecurityHeadersMiddleware启用
                    ["Encryption"] = _encryptionService != null,
                    ["2FA"] = true, // 应检查是否启用
                    ["RateLimiting"] = true,
                    ["AuditLogging"] = true
                },
                CheckItems = new List<SecurityCheckItemDto>
                {
                    new SecurityCheckItemDto
                    {
                        CheckName = "Password Policy",
                        Passed = true,
                        Details = "Strong password requirements enforced"
                    },
                    new SecurityCheckItemDto
                    {
                        CheckName = "Data Encryption",
                        Passed = _encryptionService != null,
                        Details = "Sensitive data encrypted at rest"
                    },
                    new SecurityCheckItemDto
                    {
                        CheckName = "API Security",
                        Passed = true,
                        Details = "API key authentication and rate limiting enabled"
                    },
                    new SecurityCheckItemDto
                    {
                        CheckName = "Session Management",
                        Passed = true,
                        Details = "Secure session handling with timeout"
                    }
                }
            };

            // 检查漏洞
            dto.Vulnerabilities = await DetectSecurityVulnerabilitiesAsync(cancellationToken);

            dto.IsCompliant = !dto.Vulnerabilities.Any(v => v.Severity == "Critical" || v.Severity == "High");

            dto.Recommendations = GenerateSecurityRecommendations(dto);

            return Result<SecurityComplianceCheckDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to run security compliance check");
            return Result<SecurityComplianceCheckDto>.Failure($"Failed to run check: {ex.Message}");
        }
    }

    public async Task<Result<PrivacyComplianceCheckDto>> RunPrivacyComplianceCheckAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var dto = new PrivacyComplianceCheckDto
            {
                CheckedAt = DateTime.UtcNow,
                PrivacyControls = new Dictionary<string, bool>
                {
                    ["ConsentManagement"] = true,
                    ["DataExport"] = true,
                    ["DataDeletion"] = true,
                    ["PrivacySettings"] = true,
                    ["DataRetention"] = true,
                    ["Anonymization"] = true
                },
                CheckItems = new List<PrivacyCheckItemDto>
                {
                    new PrivacyCheckItemDto
                    {
                        CheckName = "Consent Collection",
                        Passed = true,
                        Details = "Proper consent collection mechanisms in place"
                    },
                    new PrivacyCheckItemDto
                    {
                        CheckName = "Data Minimization",
                        Passed = true,
                        Details = "Only necessary data collected"
                    },
                    new PrivacyCheckItemDto
                    {
                        CheckName = "User Rights",
                        Passed = true,
                        Details = "Data export and deletion rights implemented"
                    },
                    new PrivacyCheckItemDto
                    {
                        CheckName = "Privacy Policy",
                        Passed = await CheckPrivacyPolicyAsync(cancellationToken),
                        Details = "Privacy policy up to date"
                    }
                }
            };

            // 检查风险
            dto.Risks = await DetectPrivacyRisksAsync(cancellationToken);

            dto.IsCompliant = !dto.Risks.Any(r => r.Severity == "High");

            dto.Recommendations = GeneratePrivacyRecommendations(dto);

            return Result<PrivacyComplianceCheckDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to run privacy compliance check");
            return Result<PrivacyComplianceCheckDto>.Failure($"Failed to run check: {ex.Message}");
        }
    }

    #endregion

    #region 私有方法

    private string GetConsentText(string consentType)
    {
        return consentType switch
        {
            ConsentTypes.TermsOfService => "I agree to the Terms of Service",
            ConsentTypes.PrivacyPolicy => "I agree to the Privacy Policy",
            ConsentTypes.DataProcessing => "I consent to the processing of my personal data",
            ConsentTypes.Marketing => "I consent to receive marketing communications",
            ConsentTypes.Cookies => "I consent to the use of cookies",
            ConsentTypes.ThirdPartySharing => "I consent to sharing data with third parties",
            _ => "I give my consent"
        };
    }

    private DateTime? GetConsentExpiration(string consentType)
    {
        return consentType switch
        {
            ConsentTypes.Marketing => DateTime.UtcNow.AddYears(2),
            ConsentTypes.Cookies => DateTime.UtcNow.AddYears(1),
            _ => null
        };
    }

    private async Task ProcessDataExportAsync(Guid requestId)
    {
        try
        {
            var request = await _dbContext.DataExportRequests
                .FirstOrDefaultAsync(r => r.Id == requestId);

            if (request == null) return;

            request.StartProcessing();
            await _dbContext.SaveChangesAsync();

            // 执行导出
            var result = await _dataExportService.ExportUserDataAsync(
                request.UserId, 
                request.ExportFormat, 
                request.IncludedDataTypes);

            if (result.IsSuccess)
            {
                request.Complete(result.Value!.FileUrl, result.Value.FileSize, result.Value.FileHash);
            }
            else
            {
                request.MarkAsFailed(result.Error);
            }

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process data export {RequestId}", requestId);
        }
    }

    private async Task ApplyRetentionPolicyAsync(DataRetentionPolicy policy, CancellationToken cancellationToken)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-policy.RetentionDays);

        switch (policy.DataType)
        {
            case DataTypes.LoginHistory:
                var loginHistories = await _dbContext.LoginHistories
                    .Where(l => l.LoginAt < cutoffDate)
                    .ToListAsync(cancellationToken);
                _dbContext.LoginHistories.RemoveRange(loginHistories);
                break;

            case DataTypes.AuditLogs:
                var auditLogs = await _dbContext.AuditLogs
                    .Where(a => a.CreatedAt < cutoffDate)
                    .ToListAsync(cancellationToken);
                _dbContext.AuditLogs.RemoveRange(auditLogs);
                break;

            case DataTypes.ApiKeyUsage:
                var apiKeyUsages = await _dbContext.ApiKeyUsages
                    .Where(u => u.UsedAt < cutoffDate)
                    .ToListAsync(cancellationToken);
                _dbContext.ApiKeyUsages.RemoveRange(apiKeyUsages);
                break;

            case DataTypes.InactiveUsers:
                if (policy.DeletionMethod == DeletionMethods.Anonymize)
                {
                    var inactiveUsers = await _dbContext.CustomerUsers
                        .Where(u => u.LastLoginAt < cutoffDate)
                        .Select(u => u.Id)
                        .ToListAsync(cancellationToken);
                    
                    foreach (var userId in inactiveUsers)
                    {
                        await _anonymizationService.AnonymizeUserAsync(userId, cancellationToken);
                    }
                }
                break;
        }

        await _dbContext.SaveChangesAsync(cancellationToken);
    }

    private async Task<List<ComplianceIssueDto>> DetectComplianceIssuesAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken)
    {
        var issues = new List<ComplianceIssueDto>();

        // 检查未处理的数据导出请求
        var pendingExports = await _dbContext.DataExportRequests
            .CountAsync(r => r.Status == ExportStatus.Pending && 
                r.RequestedAt < DateTime.UtcNow.AddDays(-2), cancellationToken);

        if (pendingExports > 0)
        {
            issues.Add(new ComplianceIssueDto
            {
                IssueType = "DataExport",
                Description = $"{pendingExports} data export requests pending for more than 48 hours",
                Severity = "High",
                DetectedAt = DateTime.UtcNow
            });
        }

        // 检查过期的同意
        var expiredConsents = await _dbContext.UserConsents
            .CountAsync(c => !c.IsRevoked && c.ConsentedAt < DateTime.UtcNow.AddYears(-2), cancellationToken);

        if (expiredConsents > 0)
        {
            issues.Add(new ComplianceIssueDto
            {
                IssueType = "Consent",
                Description = $"{expiredConsents} expired consents need renewal",
                Severity = "Medium",
                DetectedAt = DateTime.UtcNow
            });
        }

        return issues;
    }

    private string GenerateComplianceSummary(ComplianceReportDto report)
    {
        var summary = $"Compliance Report for {report.StartDate:yyyy-MM-dd} to {report.EndDate:yyyy-MM-dd}\n";
        summary += $"Total Users: {report.TotalUsers}, Active Users: {report.ActiveUsers}\n";
        
        if (report.Issues.Any())
        {
            summary += $"\nIssues Found: {report.Issues.Count}";
            var highSeverity = report.Issues.Count(i => i.Severity == "High");
            if (highSeverity > 0)
            {
                summary += $" ({highSeverity} high severity)";
            }
        }
        else
        {
            summary += "\nNo compliance issues detected.";
        }

        return summary;
    }

    private List<DataProcessingActivityDto> GetDataProcessingActivities(Guid userId)
    {
        return new List<DataProcessingActivityDto>
        {
            new DataProcessingActivityDto
            {
                ActivityType = "Authentication",
                Purpose = "User identification and access control",
                ProcessedAt = DateTime.UtcNow,
                LegalBasis = "Contract performance"
            },
            new DataProcessingActivityDto
            {
                ActivityType = "Service Delivery",
                Purpose = "Providing AI chat services",
                ProcessedAt = DateTime.UtcNow,
                LegalBasis = "Contract performance"
            },
            new DataProcessingActivityDto
            {
                ActivityType = "Analytics",
                Purpose = "Service improvement and usage analysis",
                ProcessedAt = DateTime.UtcNow,
                LegalBasis = "Legitimate interest"
            }
        };
    }

    private async Task<List<SecurityVulnerabilityDto>> DetectSecurityVulnerabilitiesAsync(
        CancellationToken cancellationToken)
    {
        var vulnerabilities = new List<SecurityVulnerabilityDto>();

        // 检查弱密码
        var weakPasswords = await _dbContext.CustomerUsers
            .CountAsync(u => u.PasswordHash.Hash.Length < 60, cancellationToken); // BCrypt 哈希长度

        if (weakPasswords > 0)
        {
            vulnerabilities.Add(new SecurityVulnerabilityDto
            {
                Type = "WeakPassword",
                Severity = "Medium",
                Description = $"{weakPasswords} users with potentially weak passwords",
                Mitigation = "Enforce password reset with stronger requirements"
            });
        }

        // 检查过期的API密钥
        var expiredApiKeys = await _dbContext.ApiKeys
            .CountAsync(k => k.IsActive && k.ExpiresAt < DateTime.UtcNow, cancellationToken);

        if (expiredApiKeys > 0)
        {
            vulnerabilities.Add(new SecurityVulnerabilityDto
            {
                Type = "ExpiredApiKeys",
                Severity = "Low",
                Description = $"{expiredApiKeys} expired API keys still active",
                Mitigation = "Deactivate expired API keys"
            });
        }

        return vulnerabilities;
    }

    private async Task<List<PrivacyRiskDto>> DetectPrivacyRisksAsync(
        CancellationToken cancellationToken)
    {
        var risks = new List<PrivacyRiskDto>();

        // 检查未加密的敏感数据
        var unencryptedData = await _dbContext.CustomerUsers
            .CountAsync(u => u.Phone != null && u.Phone.Value != null && !u.Phone.Value.StartsWith("enc_"), cancellationToken);

        if (unencryptedData > 0)
        {
            risks.Add(new PrivacyRiskDto
            {
                RiskType = "UnencryptedData",
                Severity = "High",
                Description = "Some sensitive data may not be encrypted",
                Mitigation = "Enable field-level encryption for all PII"
            });
        }

        // 检查过长的数据保留
        var oldData = await _dbContext.ConversationMessages
            .CountAsync(m => m.CreatedAt < DateTime.UtcNow.AddYears(-2), cancellationToken);

        if (oldData > 10000)
        {
            risks.Add(new PrivacyRiskDto
            {
                RiskType = "ExcessiveDataRetention",
                Severity = "Medium",
                Description = "Large amount of old conversation data retained",
                Mitigation = "Apply data retention policies"
            });
        }

        return risks;
    }

    private async Task<bool> CheckPrivacyPolicyAsync(CancellationToken cancellationToken)
    {
        // 检查隐私政策版本和更新时间
        var latestConsent = await _dbContext.UserConsents
            .Where(c => c.ConsentType == ConsentTypes.PrivacyPolicy)
            .OrderByDescending(c => c.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);

        return latestConsent != null && latestConsent.Version == "1.0"; // 应从配置获取当前版本
    }

    private string GenerateSecurityRecommendations(SecurityComplianceCheckDto check)
    {
        var recommendations = new List<string>();

        if (!check.SecurityFeatures["2FA"])
        {
            recommendations.Add("Enable two-factor authentication for all users");
        }

        if (check.Vulnerabilities.Any(v => v.Type == "WeakPassword"))
        {
            recommendations.Add("Implement stronger password policies");
        }

        if (!check.IsCompliant)
        {
            recommendations.Add("Address high and critical severity vulnerabilities immediately");
        }

        return string.Join("; ", recommendations);
    }

    private string GeneratePrivacyRecommendations(PrivacyComplianceCheckDto check)
    {
        var recommendations = new List<string>();

        if (check.Risks.Any(r => r.RiskType == "UnencryptedData"))
        {
            recommendations.Add("Encrypt all personal identifiable information");
        }

        if (check.Risks.Any(r => r.RiskType == "ExcessiveDataRetention"))
        {
            recommendations.Add("Implement automated data retention policies");
        }

        if (!check.IsCompliant)
        {
            recommendations.Add("Review and update privacy practices");
        }

        recommendations.Add("Conduct regular privacy impact assessments");

        return string.Join("; ", recommendations);
    }

    #endregion

    #region Mapping Methods

    private UserConsentDto MapToUserConsentDto(UserConsent consent)
    {
        return new UserConsentDto
        {
            Id = consent.Id,
            UserId = consent.CustomerUserId ?? consent.AdminUserId ?? Guid.Empty,
            ConsentType = consent.ConsentType,
            IsGranted = !consent.IsRevoked,
            ConsentDate = consent.ConsentedAt,
            RevokedDate = consent.RevokedAt,
            IpAddress = consent.IpAddress ?? string.Empty,
            UserAgent = consent.UserAgent,
            CreatedAt = consent.CreatedAt,
            UpdatedAt = consent.UpdatedAt
        };
    }

    private PrivacySettingsDto MapToPrivacySettingsDto(PrivacySettings settings)
    {
        return new PrivacySettingsDto
        {
            Id = settings.Id,
            UserId = settings.UserId,
            AllowDataCollection = settings.AllowAnalytics, // Map analytics to data collection
            AllowDataSharing = settings.AllowThirdPartySharing,
            AllowMarketing = settings.AllowMarketing,
            AllowAnalytics = settings.AllowAnalytics,
            AllowPersonalization = settings.AllowPersonalization,
            ShowProfilePublicly = settings.AllowPublicProfile,
            AllowSearchEngineIndexing = settings.AllowSearchEngineIndexing,
            DataRetentionPreference = $"{settings.DataRetentionDays} days",
            CreatedAt = settings.CreatedAt,
            UpdatedAt = settings.UpdatedAt
        };
    }

    private DataExportRequestDto MapToDataExportRequestDto(DataExportRequest request)
    {
        return new DataExportRequestDto
        {
            Id = request.Id,
            UserId = request.UserId,
            RequestedAt = request.RequestedAt,
            Status = request.Status,
            ExportFormat = request.ExportFormat,
            DataTypes = request.IncludedDataTypes,
            CompletedAt = request.CompletedAt,
            FileUrl = request.FileUrl,
            ExpiresAt = request.ExpiresAt,
            FileSize = request.FileSize,
            FailureReason = request.ErrorMessage,
            IpAddress = request.IpAddress,
            CreatedAt = request.CreatedAt,
            UpdatedAt = request.UpdatedAt
        };
    }

    private DataRetentionPolicyDto MapToDataRetentionPolicyDto(DataRetentionPolicy policy)
    {
        return new DataRetentionPolicyDto
        {
            Id = policy.Id,
            DataType = policy.DataType,
            RetentionDays = policy.RetentionDays,
            IsActive = policy.IsEnabled,
            AutoDelete = true, // Assuming auto-delete for now
            RequiresApproval = false,
            LastExecutedAt = policy.LastExecutedAt,
            Description = policy.Description,
            CreatedAt = policy.CreatedAt,
            UpdatedAt = policy.UpdatedAt,
            RetentionBasis = "CreatedDate"
        };
    }

    // Removed MapToUserDataReportDto as UserDataReport entity doesn't exist

    // Removed MapToSecurityComplianceCheckDto as SecurityComplianceCheck entity doesn't exist

    // Removed MapToPrivacyComplianceCheckDto as PrivacyComplianceCheck entity doesn't exist

    #endregion
}